package com.lecent.park.vo;

import com.lecent.park.entity.DeviceVolume;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceVolumeVO对象", description = "DeviceVolumeVO对象")
public class DeviceVolumeVO extends DeviceVolume {
	private static final long serialVersionUID = 1L;

	/**
	 * 车场id
	 */
	@ApiModelProperty(value = "车场id")
	private String parklotId;

	/**
	 * 楼层id
	 */
	@ApiModelProperty(value = "楼层id")
	private String floorId;

	/**
	 * 区域id
	 */
	@ApiModelProperty(value = "区域id")
	private String regionId;

	private List<DeviceVolume> deviceVolume;

}
