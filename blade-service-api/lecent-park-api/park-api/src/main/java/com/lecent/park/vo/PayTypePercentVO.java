package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Data
@ApiModel(value = "DeviceInfoVO对象", description = "DeviceInfoVO对象")
public class PayTypePercentVO {

	@ApiModelProperty("支付方式数量")
	private Integer count;

	@ApiModelProperty("支付方式")
	private Integer payType;

	@ApiModelProperty("支付方式名称")
	private String payTypeName;
}
