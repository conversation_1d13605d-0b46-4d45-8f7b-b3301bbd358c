package com.lecent.park.en.coupon;

/**
 * 优惠劵状态
 *
 * <AUTHOR>
 * @date 2020-06-03 09:52
 */
public enum CouponStatusEnum {

	UN_USED(1, "待使用"),

	USED(2, "已使用"),

	OVERDUE(3, "已过期"),

	GIVEN_OTHER(4, "已转赠");

	Integer value;
	String name;

	CouponStatusEnum(Integer value, String name) {
		this.value = value;
		this.name = name;
	}

	public Integer getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public static String getName(Integer value){
		if (value == 1){
			return UN_USED.getName();
		}
		else if (value == 2){
			return USED.getName();
		}
		else if (value == 3){
			return OVERDUE.getName();
		}
		else if (value == 4){
			return GIVEN_OTHER.getName();
		}
		return null;
	}
}
