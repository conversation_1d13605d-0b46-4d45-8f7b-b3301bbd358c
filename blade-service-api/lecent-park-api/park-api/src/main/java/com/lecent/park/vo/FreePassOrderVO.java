package com.lecent.park.vo;

import com.lecent.park.entity.FreeCard;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: TODO
 * @author: scott
 * @date: 2021年05月19日 14:15
 */
@Data
@ApiModel(value = "免费放行订单信息",description = "免费放行的订单信息")
public class FreePassOrderVO extends FreeCard {

	@ApiModelProperty("停车记录id")
	private Long parkingId;

	@ApiModelProperty("车牌")
	private String plate;

	@ApiModelProperty("车场名")
	private String parklotName;

	@ApiModelProperty("通道名称")
	private String channelName;

	@ApiModelProperty("进场时间")
	private Date enterTime;

	@ApiModelProperty("出场时间")
	private Date exitTime;

	@ApiModelProperty("免费原因")
	private String freeCause;

	@ApiModelProperty("出口通道名称")
	private String exitChannelName;

	@ApiModelProperty("车场id")
	private Long parklotId;

	@ApiModelProperty("导出设置")
	private String exportSetting;
	/**
	 * 用户车场ids
	 */
	private List<Long> userParklots;

	@ApiModelProperty(value = "进场开始时间")
	private Date enterStartTime;

	@ApiModelProperty(value = "进场结束时间")
	private Date enterEndTime;

	@ApiModelProperty(value = "出场开始时间")
	private Date exitStartTime;

	@ApiModelProperty(value = "出场结束时间")
	private Date exitEndTime;
}
