package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.entity.Coupon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CouponVO对象", description = "CouponVO对象")
public class CouponVO extends Coupon {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "领取人数")
	private Integer receiveUsers;


	@ApiModelProperty(value = "已领取")
	private Integer receiveNums;

	@ApiModelProperty(value = "已使用")
	private Integer useNums;


	@ApiModelProperty(value = "优惠券类型名称")
	private String couponTypeName;

	@ApiModelProperty(value = "优惠详情")
	private String couponDetail;

	@ApiModelProperty(value = "车场名称限制列表")
	private String parklotNameList;


	@ApiModelProperty(value = "车场id列表")
	private List<String> parklotIdList;

	@ApiModelProperty(value = "用户id")
	private Long userId;

	@ApiModelProperty(value = "用户手机号")
	private String userPhone;

	@ApiModelProperty(value = "优惠券编号")
	private String couponNo;

	@ApiModelProperty(value = "活动名称")
	private String activityName;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "领取时间")
	private Date receiveTime;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "开始时间")
	private Date startDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "结束时间")
	private Date endDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "失效时间")
	private Date invalidTime;

	@ApiModelProperty(value = "优惠券使用状态(标识)")
	private Integer useStatus;

	@ApiModelProperty(value = "订单编号")
	private String tradeNo;

	@ApiModelProperty(value = "优惠金额")
	private BigDecimal couponDiscountAmount;

	@ApiModelProperty(value = "使用车场")
	private String parklotName;

	@ApiModelProperty(value = "车牌")
	private String plate;

	@ApiModelProperty(value = "使用时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date useTime;

	@ApiModelProperty(value = "核销方式")
	private String checkWay;

	@ApiModelProperty(value = "优惠券使用状态")
	private String useStatusName;

	@ApiModelProperty(value = "车场id")
	private Long parklotId;

	@ApiModelProperty(value = "停车记录id")
	private String parkingId;

	private List<String> couponIds;

}
