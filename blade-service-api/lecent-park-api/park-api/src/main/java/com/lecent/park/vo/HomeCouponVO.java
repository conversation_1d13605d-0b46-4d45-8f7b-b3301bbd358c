package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lecent.park.entity.CouponSendHistory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 立减金首页返回
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CouponSendHistoryVO对象", description = "立减金发送记录")
public class HomeCouponVO extends CouponSendHistory {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("优惠劵名称")
	private String couponName;

	@ApiModelProperty("满减金额")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal fullAmount;

	@ApiModelProperty("满减金额")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal reduceAmount;

	@ApiModelProperty("转赠次数")
	private Integer couponType;

	@ApiModelProperty("转赠次数")
	private Integer giveOtherTimes;

	private String endDateStr;
}
