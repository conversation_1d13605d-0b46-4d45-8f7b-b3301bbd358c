package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/15 9:57
 */
@Getter
@Setter
@Builder
@ApiModel(value = "今日进出车辆详细信息展示", description = "今日进出车辆详细信息展示")
public class InAndOutCarVO {

	@ApiModelProperty(name = "parkingCarAmount", value = "停车数量")
	private Integer parkingCarAmount = 0;

	@ApiModelProperty(name = "totalReceiveAmount", value = "累计收费总额")
	private BigDecimal totalReceiveAmount = BigDecimal.ZERO;

	@ApiModelProperty(name = "inOutAmount", value = "今日进出车辆")
	private Integer inOutAmount = 0;

	@ApiModelProperty(name = "openCloseAmount", value = "今日开关闸次数")
	private Integer openCloseAmount = 0;

	@Override
	public String toString() {
		return "InAndOutCarVO{" +
			"parkingCarAmount=" + parkingCarAmount +
			", totalReceiveAmount=" + totalReceiveAmount +
			", inOutAmount=" + inOutAmount +
			", openCloseAmount=" + openCloseAmount +
			'}';
	}
}
