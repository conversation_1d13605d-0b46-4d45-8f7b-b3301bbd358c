package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-04-07 11:54
 */
@Data
@AllArgsConstructor
public class TodoItemVO implements Serializable {
	private String parklotId;

	private String plate;

	private String parklotName;
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date enterTime;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date exitTime;

	private BigDecimal cost;

	/**
	 * 是否需要去支付
	 */
	private Integer needPay;

	private String duration;

	/**
	 * 总费用
	 */
	private BigDecimal totalAmount;

	/**
	 * 实付金额
	 */
	private BigDecimal paidAmount;

	/**
	 * 优惠金额
	 */
	private BigDecimal discountAmount;

	private Long id;

	/**
	 * 停车订单状态（1-待支付；2-超时补缴；3已完成）
	 */
	private Integer status;

}
