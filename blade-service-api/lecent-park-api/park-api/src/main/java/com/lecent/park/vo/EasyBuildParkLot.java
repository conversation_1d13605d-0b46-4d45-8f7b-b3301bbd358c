package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "EasyBuildParkLot对象", description = "轻松建车场实体")
public class EasyBuildParkLot {

	//车场类型
	@ApiModelProperty(value = "车场类型")
	private Integer parkLotType;

	@NotBlank(message = "车场名称不能为空", groups = {saveValid.class})
	private String parkLotName;

	@ApiModelProperty(value = "车场地址")
	private String parkLotAddr;

	@ApiModelProperty(value = "车位数")
	private Integer placeNumber;

	@NotBlank(message = "租户ID不能为空", groups = {saveValid.class})
	private String tenantId;

	@NotNull(message = "通道出口数不能为空", groups = {saveValid.class})
	private Integer exitNum;

	@NotNull(message = "通道入口数不能为空", groups = {saveValid.class})
	private Integer enterNum;

	@NotNull(message = "管理员账号不能为空", groups = {saveValid.class})
	private String adminAccount;

	@NotNull(message = "岗亭账号不能为空", groups = {saveValid.class})
	private String sentryAccount;

	public interface saveValid {
	}
}
