package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 欠费统计vo
 */
@Data
@ApiModel("欠费统计vo")
public class ArrearsOrderVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("车牌")
	private String plate;

	@ApiModelProperty("手机号码")
	private String phone;

	@ApiModelProperty("欠费总额")
	private BigDecimal totalArrearsMoney;

	@ApiModelProperty("欠费单数")
	private String totalArrearsCount;

	@ApiModelProperty("最近缴费金额")
	private BigDecimal arrearsMoney;

	@ApiModelProperty("最近欠费时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date billStartTime;

	@ApiModelProperty("在场状态")
	private String enterStatus;
}
