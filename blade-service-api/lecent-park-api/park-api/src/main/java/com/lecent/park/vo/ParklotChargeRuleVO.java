package com.lecent.park.vo;

import com.lecent.park.entity.ParklotChargeRule;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公司员工车场资源授权表视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParklotChargeRuleVO对象", description = "公司员工车场资源授权表")
public class ParklotChargeRuleVO extends ParklotChargeRule {
	private static final long serialVersionUID = 1L;

}
