package com.lecent.park.vo;

import com.lecent.park.entity.EmployeeCarParklot;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工车辆关联车场表视图实体类
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EmployeeCarParklotVO对象", description = "员工车辆关联车场表")
public class EmployeeCarParklotVO extends EmployeeCarParklot {
	private static final long serialVersionUID = 1L;


	private String parklotName;
	private String ruleName;
}
