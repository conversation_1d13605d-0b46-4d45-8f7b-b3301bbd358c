package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 建行优惠劵详情
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@ApiModel(value = "建行优惠劵详情", description = "建行优惠劵详情")
public class CCBCouponGivenVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("接受人")
	private String receiveUser;

	@ApiModelProperty("转赠时间")
	private Date giveTime;

	@ApiModelProperty("优惠劵名称")
	private String couponName;

}
