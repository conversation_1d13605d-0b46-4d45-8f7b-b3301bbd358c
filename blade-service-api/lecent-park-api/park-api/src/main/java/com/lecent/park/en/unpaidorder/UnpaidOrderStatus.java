package com.lecent.park.en.unpaidorder;




/**
 * 付款状态
 * <AUTHOR>
 */

public enum UnpaidOrderStatus {

	STATUS_CREATE(1, "创建"),
	STATUS_EXPIRE(-1, "过期"),

	PAY_STATUS_0(0, "未缴"),
	PAY_STATUS_1(1, "已补交"),

	AUDIT_STATUS_0(0, "未稽核"),
	AUDIT_STATUS_1(1, "稽核正常"),
	AUDIT_STATUS_2(2, "稽核异常"),
	AUDIT_STATUS_3(3, "审核中"),



	;

	private int value;
	private String name;

	UnpaidOrderStatus(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}


	public String getName() {
		return name;
	}
}
