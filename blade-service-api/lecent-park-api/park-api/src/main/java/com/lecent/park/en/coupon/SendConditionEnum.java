package com.lecent.park.en.coupon;

/**
 * 优惠劵发放条件枚举
 *
 * <AUTHOR>
 * @date 2021-05-20 10:16
 */
public enum SendConditionEnum {
	USER_ACTION(1, "用户属性"),

	TIME(2, "时间"),

//	NEW_USER(3, "新用户"),

	PAY_SITUATION(4, "支付场景");




	int value;
	String name;

	SendConditionEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

}
