package com.lecent.park.en.device;

import lombok.Getter;

/**
 * 视频装状态枚举
 * <AUTHOR>
 */
@Getter
public enum VideoPileDump {

	//NoMPUDev("NoMPUDev","未检测到6050芯片"),
	DevTouched("DevTouched","设备被触碰"),
	DevInclined("DevInclined","设备倾倒"),
	;
	private String code;
	private String name;

	VideoPileDump(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public static String getNameByCode(String code){
		for (VideoPileDump p : VideoPileDump.values()) {
			if (p.getCode().equals(code)){
				return p.getName();
			}
		}
		return null;
	}
}
