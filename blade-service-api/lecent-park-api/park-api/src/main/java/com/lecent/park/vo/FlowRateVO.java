package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车流量返回类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@ApiModel(value = "车流量返回类", description = "车流量返回类")
@NoArgsConstructor
public class FlowRateVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "统计类型")
	private String statisticsType;

	@ApiModelProperty(value = "统计时间")
	private String time;

	@ApiModelProperty(value = "数量")
	private Integer num;


}
