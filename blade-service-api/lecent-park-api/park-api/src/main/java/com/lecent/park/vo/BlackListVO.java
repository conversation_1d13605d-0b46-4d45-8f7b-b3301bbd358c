package com.lecent.park.vo;

import com.lecent.park.entity.BlackList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆黑名单视图实体类
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BlackListVO对象", description = "车辆黑名单")
public class BlackListVO extends BlackList {
	private static final long serialVersionUID = 1L;

	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	/**
	 * 是否允许出场
	 */
	@ApiModelProperty(value = "是否允许出场")
	private String allowOutStr;

	private String updateUserName;

	@ApiModelProperty(value = "移除原因")
	private String removeReason;
}
