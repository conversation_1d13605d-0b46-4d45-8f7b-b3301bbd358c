package com.lecent.park.vo;

import com.lecent.park.entity.CouponActivity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 优惠劵活动视图实体类
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CouponActivityVO对象", description = "优惠劵活动")
public class CouponActivityVO extends CouponActivity {
	private static final long serialVersionUID = 1L;


	/**
	 * 优惠券列表
	 */
	private List<CouponVO> couponVOList;
	/**
	 * 已领取数量
	 */
	@ApiModelProperty(value = "已领取数量")
	private Integer receivedAmount;

	/**
	 * 已使用数量
	 */
	@ApiModelProperty(value = "已使用数量")
	private Integer usedAmount;


	/**
	 * 用户行为列表
	 */
	@ApiModelProperty(value = "用户行为列表")
	private List<Integer> userActionList;

	/**
	 * 时间列表
	 */
	@ApiModelProperty(value = "时间列表")
	private List<Integer> timeList;

	@ApiModelProperty(value = "操作人")
	private String optUser;


}
