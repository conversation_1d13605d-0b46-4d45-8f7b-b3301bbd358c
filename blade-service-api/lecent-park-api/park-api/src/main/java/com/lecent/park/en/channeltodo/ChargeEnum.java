package com.lecent.park.en.channeltodo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 计费类型类型
 *
 * <AUTHOR>
 */

@Slf4j
@Getter
@AllArgsConstructor
public enum ChargeEnum {

	//临停车
	TEMP_STOP("临停"),
	ENTER_NO_CARD("临停"),
	FREE_TIME("免费时长内"),
	MODIFY_PLATE("临停(修改车牌)"),
	NO_CHARGE_RULE("无收费规则"),


	//月卡车
	MONTH_CARD("月卡"),
	MONTH_CARD_NO_RENEWAL("月卡(未续费)"),
	MONTH_CARD_INEFFECTIVE("月卡(未生效进场)"),
	MONTH_CARD_STOP("月卡(禁用/暂停)"),
	MONTH_CARD_EXPIRED("月卡(过期)"),
	MONTH_CARD_PLACE_FULL("月卡(一卡多车|车位满)"),
	MONTH_CARD_TIME_LIMIT("月卡(限时卡)"),


	//免费车
	FREE_CARD("免费卡"),
	FREE_CARD_INEFFECTIVE("免费卡(未生效进场)"),
	FREE_CARD_STOP("免费卡(暂停/禁用/无效)"),
	FREE_CARD_EXPIRED("免费卡(过期)"),


	//授权车辆
	AUTH_INEFFECTIVE("访客授权(未生效进场)"),
	AUTH_TIMEOUT("访客授权(超时)"),
	AUTH_EFFECTIVE("访客授权(有效)"),


	//授权车辆
	MERCHANT_AUTH_ADVANCE("商户授权(未生效进场)"),
	MERCHANT_AUTH_EFFECTIVE("商户授权(有效)"),
	MERCHANT_AUTH_TIMEOUT("商户授权(超时)"),
	MERCHANT_AUTH_PLACE_FULL("商户授权(车位已满)"),


	STAFF_CARD("员工车"),

	SUPPLEMENT_CARD("亲情卡车"),

	RESERVE_CARD("预约车"),

	SPECIAL_CARD("特殊车"),

	;

	private final String msg;

	public static ChargeEnum resolve(String name) {
		try {
			return ChargeEnum.valueOf(name);
		} catch (Exception ignored) {
			log.warn("invalid ChargeEnum name: {}", name);
			return ChargeEnum.TEMP_STOP;
		}
	}


	public static String getMsg(String name) {
		try {
			return ChargeEnum.valueOf(name).msg;
		} catch (Exception ignored) {
			log.warn("invalid ChargeEnum name: {}", name);
		}
		return "";
	}

}
