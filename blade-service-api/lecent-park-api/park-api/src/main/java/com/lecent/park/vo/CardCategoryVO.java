package com.lecent.park.vo;

import com.lecent.park.entity.CardCategory;
import com.lecent.park.entity.CardCategoryDiscount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 套餐卡类型视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CardCategoryVO对象", description = "套餐卡类型")
public class CardCategoryVO extends CardCategory {
	private static final long serialVersionUID = 1L;

	private String beginTimeStr;

	private String endTimeStr;

	@ApiModelProperty("月卡套餐折扣信息")
	private List<CardCategoryDiscount> cardCategoryDiscountList;
}
