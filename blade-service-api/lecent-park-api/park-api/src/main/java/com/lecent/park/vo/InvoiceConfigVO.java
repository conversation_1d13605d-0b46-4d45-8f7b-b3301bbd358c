package com.lecent.park.vo;

import com.lecent.park.entity.InvoiceConfig;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车场-开票相关配置表视图实体类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InvoiceConfigVO对象", description = "车场-开票相关配置表")
public class InvoiceConfigVO extends InvoiceConfig {
	private static final long serialVersionUID = 1L;

}
