package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StatisticsVO {
    @ApiModelProperty("累计停车次数")
    private Integer numberOfStop;
    @ApiModelProperty("累计停车辆数")

    private Integer numberOfCar;
    @ApiModelProperty("未缴费次数")
    private Integer numberOfUnpaid;
    @ApiModelProperty("未缴费车辆数")

    private Integer numberOfUnpaidCar;
    @ApiModelProperty("应收金额统计")
    private BigDecimal receivableAmount;
    @ApiModelProperty("实收金额统计")
    private BigDecimal paidAmount;
    @ApiModelProperty("未收统计")
    private BigDecimal unpaidAmount;




    @ApiModelProperty(value = "未缴次数")
    private int unpaidNumberOfTimes;
    @ApiModelProperty(value = "未缴订单ID")
    private String orderIds;
    @ApiModelProperty(value = "未缴总金额")
    private BigDecimal unpaidTotalAmount;
    @ApiModelProperty(value = "总时长")
    private Long unpaidTotalTimes;



    public StatisticsVO() {
        this.receivableAmount = BigDecimal.ZERO;
        this.paidAmount = BigDecimal.ZERO;
        this.unpaidAmount = BigDecimal.ZERO;
    }
}
