package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 停车大屏停车记录统计VO
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Data
@ApiModel(value = "停车大屏停车记录统计VO", description = "停车大屏停车记录统计VO")
public class JHParkingOrderVO {

	@ApiModelProperty("停车记录类型（1-进场；2-出场）")
	private Integer channelType;

	@ApiModelProperty("车牌号")
	private String plate;

	@ApiModelProperty("车场名称")
	private String parklotName;

	@ApiModelProperty("通道名称")
	private String channelName;

	@ApiModelProperty("进出场时间")
	private String date;
}
