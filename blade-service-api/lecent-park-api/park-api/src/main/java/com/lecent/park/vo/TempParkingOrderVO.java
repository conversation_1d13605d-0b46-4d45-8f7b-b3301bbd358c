package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lecent.park.entity.TempParkingOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 临停缴费订单表视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParkingOrderVO对象", description = "临停缴费订单表")
public class TempParkingOrderVO extends TempParkingOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 总现金
	 */
	@ApiModelProperty(value = "总现金")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal allReceiveAmount;
	/**
	 * 支付方式
	 */
	@ApiModelProperty(value = "支付方式")
	private String payTypeName;
	/**
	 * 通道名称
	 */
	private String channelName;

	/**
	 * 进场时间
	 */
	private Date enterTime;

	/**
	 * 出场时间
	 */
	private Date exitTime;

	/**
	 * 时长
	 */
	private String duration;

	/**
	 * 时长
	 */
	private String timeInterval;

	/**
	 * 是否超时
	 */
	private boolean isOvertime = false;
	/**
	 * 退款金额
	 */
	private BigDecimal refundAmount;
}
