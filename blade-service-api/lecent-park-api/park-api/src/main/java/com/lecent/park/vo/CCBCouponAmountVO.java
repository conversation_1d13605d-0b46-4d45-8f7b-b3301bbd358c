package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 建行优惠劵详情
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@ApiModel(value = "优惠劵最大优惠详情", description = "优惠劵最大优惠详情")
@Builder
public class CCBCouponAmountVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("最大优惠金额")
	private BigDecimal maxDiscountAmount;

	@ApiModelProperty("优惠劵id")
	private Long couponId;




}
