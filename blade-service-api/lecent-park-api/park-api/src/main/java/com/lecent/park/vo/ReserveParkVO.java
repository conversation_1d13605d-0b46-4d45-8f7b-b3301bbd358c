package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.entity.ReservePark;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 预约车位表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ReserveParkVO对象", description = "预约车位表")
public class ReserveParkVO extends ReservePark {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "进场时间")
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date enterTime;

	@ApiModelProperty(value = "出场时间")
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date exitTime;

	/**
	 *
	 * 停车时长（仅当离场且有进场记录）
	 */
	@ApiModelProperty(value = "停车时长（仅当离场且有进场记录）")
	private String duration;

	/**
	 * 实收金额
	 */
	@ApiModelProperty(value = "应付金额")
	private BigDecimal receiveAmount;

	@ApiModelProperty(value = "停车场名称")
	private String parkName;

	@ApiModelProperty(value = "纬度")
	private BigDecimal lng;

	@ApiModelProperty(value = "经度")
	private BigDecimal lat;

	@ApiModelProperty(value = "过期时间")
	private Integer overdueTime;

	@ApiModelProperty(value = "截至时间")
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date endDate;

	/**
	 * 退款状态
	 */
	@ApiModelProperty(value = "退款状态（0：无，1：成功， 2：失败。3：退款中）")
	private Integer refundStatus;

	/**
	 * 退款金额
	 */
	@ApiModelProperty(value = "退款金额")
	private BigDecimal refundMoney;

	/**
	 * 预约金支付状态（0：未支付，1：已支付）
	 */
	@ApiModelProperty(value = "预约金支付状态（0：未支付，1：已支付）")
	private Integer payStatus;

	/**
	 * 支付时间
	 */
	@ApiModelProperty(value = "支付时间")
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private LocalDateTime payTime;

	/**
	 * 车场地址
	 */
	@ApiModelProperty(value = "车场详细地址")
	private String address;

	/**
	 * 车场临停禁止时段
	 */
	@ApiModelProperty(value = "车场临停禁止时段")
	private String forbidTime;

	@ApiModelProperty(value = "车牌-车位对应集合")
	private List<String> platePlaces;
	@ApiModelProperty(value = "预约金")
	private String reservePayMoneyStr;

	private String nickname;

	private String phone;
}
