package com.lecent.park.vo;

import com.lecent.park.entity.ParkingPlaceOwner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 我的车位视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParkingPlaceOwnerVO对象", description = "我的车位")
public class ParkingPlaceOwnerVO extends ParkingPlaceOwner {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	@ApiModelProperty(value = "车位编号")
	private String placeCode;

	@ApiModelProperty(value = "用户名称")
	private String userName;

	@ApiModelProperty(value = "月卡订单")
	private List<CardOrderVO> orders;
}
