package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 累计进出场车辆
 *
 * <AUTHOR>
 */
@Data
public class TotalInAndOutCarVO {

	@ApiModelProperty(value = "开始时间")
	@DateTimeFormat(
		pattern = "yyyy-MM-dd"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd"
	)
	private Date createTime;

	@ApiModelProperty("进入次数")
	private Integer enterCount;

	@ApiModelProperty("星期")
	private Integer week;

	@ApiModelProperty("离开次数")
	private Integer exitCount;
}
