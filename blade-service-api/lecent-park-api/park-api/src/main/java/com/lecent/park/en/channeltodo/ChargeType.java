package com.lecent.park.en.channeltodo;

import java.util.ArrayList;
import java.util.List;

/**
 * 计费类型类型
 *
 * <AUTHOR>
 */

public enum ChargeType {

	/**
	 * 进场
	 *******************************************************************/
	//普通车TEMP_STOP
	ENTER_CARD("ENTER_CARD", "月卡"),
	TEMP_STOP("TEMP_STOP", "临停"),
	ENTER_NO_CARD("ENTER_NO_CARD", "临停"),
	ENTER_CARD_NO_RENEWAL("ENTER_CARD_NO_RENEWAL", "月卡（未续费）"),
	ENTER_CARD_EXPIRED("ENTER_CARD_EXPIRED", "月卡（过期）"),
	ENTER_PLACE_ERROR("ENTER_PLACE_ERROR", "临停（多车多位车已满）"),

	//免费车
	FREE_ENTER_CARD("FREE_ENTER_CARD", "免费卡"),
	FREE_ENTER_CARD_DISABLED("FREE_ENTER_CARD_DISABLED", "免费卡(未授权)"),
	FREE_ENTER_CARD_NO_RENEWAL("FREE_ENTER_CARD_NO_RENEWAL", "免费卡（未续费）"),
	FREE_ENTER_CARD_EXPIRED("FREE_ENTER_CARD_EXPIRED", "免费卡（过期）"),



	/**出场*******************************************************************/
	//普通车
	LEAVE_CARD("LEAVE_CARD", "月卡"),
	LEAVE_NO_CARD("LEAVE_NO_CARD", "临停"),
	LEAVE_CARD_NO_RENEWAL("LEAVE_CARD_NO_RENEWAL", "新开月卡（未续费）"),
	LEAVE_CARD_EXPIRED("LEAVE_CARD_EXPIRED", "月卡（过期）"),
	LEAVE_CARD_DISABLED("LEAVE_CARD_DISABLED", "月卡（禁用）"),
	LEAVE_CARD_REMOVED("LEAVE_CARD_REMOVED", "月卡（未生效进场）"),
	LEAVE_PLACE_ERROR("LEAVE_PLACE_ERROR", "临停（多车多位车已满）"),
	LEAVE_CYCLE_FRONT("LEAVE_CYCLE_FRONT", "月卡（前车计费）"),
	LEAVE_CYCLE_AFTER("LEAVE_CYCLE_AFTER", "月卡（后车收费）"),
	LEAVE_CYCLE_FRONT_AND_AD("LEAVE_CYCLE_FRONT_AND_AD", "月卡（前车+过期费）"),
	LEAVE_CYCLE_AFTER_AND_AD("LEAVE_CYCLE_AFTER_AND_AD", "月卡（后车+过期费）"),
	LEAVE_TIME_LIMITED_CARD("LEAVE_TIME_LIMITED_CARD", "月卡（限时卡）"),

	//免费车
	FREE_LEAVE_CARD("FREE_LEAVE_CARD", "免费卡"),
	FREE_LEAVE_CARD_DISABLED("FREE_LEAVE_CARD_DISABLED", "免费卡(未授权)"),
	FREE_LEAVE_CARD_REMOVED("FREE_LEAVE_CARD_REMOVED", "免费卡（未生效进场）"),
	FREE_LEAVE_CARD_EXPIRED("FREE_LEAVE_CARD_EXPIRED", "免费卡（过期）"),

	//授权车辆
	AUTH_INEFFECTIVE("AUTH_INEFFECTIVE","访客授权(未生效)"),
	AUTH_TIMEOUT("AUTH_TIMEOUT","访客授权(超时)"),
	AUTH_EFFECTIVE("AUTH_EFFECTIVE","访客授权(有效)"),


	//授权车辆
	MERCHANT_AUTH_ADVANCE("MERCHANT_AUTH_ADVANCE","商户授权(未生效进场)"),
	MERCHANT_AUTH_EFFECTIVE("MERCHANT_AUTH_EFFECTIVE","商户授权(有效)"),
	MERCHANT_AUTH_TIMEOUT("MERCHANT_AUTH_TIMEOUT","商户授权(超时)"),
	MERCHANT_AUTH_PLACE_FULL("MERCHANT_AUTH_PLACE_FULL", "商户授权（车位已满）"),


	;



	private String name;
	private String msg;

	ChargeType(String name, String msg) {
		this.name = name;
		this.msg = msg;
	}


	public String getName() {
		return this.name;
	}


	public String getMsg() {
		return this.msg;
	}


	public static String getMsg(String name){
		for (ChargeType en : ChargeType.values()) {
			if (en.getName().equals(name)){
				return en.getMsg();
			}
		}
		return null;
	}


	/**
	 * 获取临停枚举
	 *
	 * @param name
	 * @return
	 */
	public static List<String> findTempStopEnum(String name) {
		if (name != null && (name.equals(ENTER_NO_CARD.name) || name.equals(TEMP_STOP.name))) {
			List<String> list = new ArrayList<>();
			list.add(LEAVE_NO_CARD.name);
			list.add(TEMP_STOP.name);
			return list;
		}
		return null;
	}

	/**
	 * 返回临停类型给前端
	 *
	 * @return
	 */
	public static String returnTempStopEnum(String name) {
		if (name != null && (name.equals(ENTER_NO_CARD.name) || name.equals(TEMP_STOP.name))) {
			return ENTER_NO_CARD.name;
		}
		return name;
	}
}
