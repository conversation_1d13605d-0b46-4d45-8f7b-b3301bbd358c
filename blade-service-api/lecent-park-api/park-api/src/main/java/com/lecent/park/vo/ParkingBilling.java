package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ParkingBilling {
	/**
	 * 车牌号
	 */
	@ApiModelProperty(value = "车牌号")
	private String plate;

	private List<UnitParkingBilling> unitParkingBillings;

	/**
	 * 累计欠费时长
	 */
	@ApiModelProperty(value = "累计欠费时长")
	private String delayTimeAmount;
	/**
	 * 累计未缴费
	 */
	@ApiModelProperty(value = "累计未缴费")
	private BigDecimal unpaidAmount = BigDecimal.ZERO;
	/**
	 * 累计欠费次数
	 */
	@ApiModelProperty(value = "累计欠费次数")
	private int delayNum = 0;
}
