package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.dto.PayWayDTO;
import com.lecent.park.entity.ChannelTodo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 车场通道视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChannelTodoVO对象", description = "车场通道")
public class ChannelTodoVO extends ChannelTodo {
	private static final long serialVersionUID = 1L;

	public static final int TYPE1_ENTER = 1;
	public static final int TYPE2_LEAVE = 2;

	/**
	 * 车场id列表
	 */
	private List<Long> parklotIdList;

	/**
	 * 通道名称
	 */
	@ApiModelProperty(value = "通道名称")
	private String channelName;


	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	/**
	 * 通道ID
	 */
	@ApiModelProperty(value = "通道ID")
	private List<Long> channelIds;

	/**
	 * 显示数量
	 */
	@ApiModelProperty(value = "显示数量")
	private Integer num;

	@ApiModelProperty(value = "进场时间")
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date enterTime;

	@ApiModelProperty(value = "出场时间")
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date exitTime;

	@ApiModelProperty(value = "通道值班人")
	private Long dutyUserId;

	@ApiModelProperty(value = "超时时间段")
	private String timeOutPeriod;

	@ApiModelProperty(value = "缴费时间")
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date paymentTime;


	/**
	 * 收费类型描述
	 */
	private String chargeTypeDes;

	/**
	 * 时间轴
	 */
	private List<Map<String, Object>> dateList;

	/**
	 * 返回状态
	 */
	private Integer codeStatus;

	/**
	 * 订单编号
	 */
	private String tradeNo;

	/**
	 * 是否走场中场逻辑
	 */
	private Boolean isNest;

	/**
	 * 优惠券
	 */
	private CCBPayCouponVO coupon;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endDate;

	/**
	 * 扫码端是否跳转小程序，true跳转、false不跳转
	 */
	@ApiModelProperty(value = "扫码端是否跳转小程序")
	private Boolean scanJumpMini;

	/**
	 * 扫码端跳转小程序Url地址(相对路径)
	 */
	@ApiModelProperty(value = "扫码端跳转小程序Url地址(相对路径)")
	private String scanJumpMiniUrl;

	/**
	 * 扫码端跳转小程序的ID
	 */
	@ApiModelProperty(value = "扫码端跳转小程序的ID")
	private String scanJumpMiniId;

	/**
	 * 支付方式列表
	 */
	@ApiModelProperty(value = "支付方式列表")
	private List<PayWayDTO> payWayList;
}
