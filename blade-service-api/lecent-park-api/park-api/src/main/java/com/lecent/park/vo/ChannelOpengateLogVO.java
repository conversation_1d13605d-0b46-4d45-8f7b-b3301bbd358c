package com.lecent.park.vo;

import com.lecent.park.entity.ChannelOpengateLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 异常开闸日志记录表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChannelOpengateLogVO对象", description = "异常开闸日志记录表")
public class ChannelOpengateLogVO extends ChannelOpengateLog {
	private static final long serialVersionUID = 1L;

	/**
	 * 待办ID
	 */
	private long todoId;


}
