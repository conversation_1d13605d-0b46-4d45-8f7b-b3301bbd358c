package com.lecent.park.vo;

import com.lecent.park.entity.CarBrand;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CarBrandVO对象", description = "CarBrandVO对象")
public class CarBrandVO extends CarBrand {
	private static final long serialVersionUID = 1L;

}
