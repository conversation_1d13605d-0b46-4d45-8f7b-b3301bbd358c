package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lecent.park.core.tool.databind.annottion.Dict;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/3 14:05
 */
@Data
public class CardRuleVO {

	@JsonSerialize(using = ToStringSerializer.class)
	private Long cardRuleId;

	private String cardRuleName;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long parklotId;

	private String parklotName;

	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal unitPrice;

	private Integer periodType;

	@Dict(dictKey = "period_type", bindField = "periodType")
	private String periodTypeDesc;
}
