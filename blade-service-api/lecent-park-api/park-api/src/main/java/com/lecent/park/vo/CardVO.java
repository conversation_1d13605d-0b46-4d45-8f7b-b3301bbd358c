package com.lecent.park.vo;

import com.lecent.park.entity.Card;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.File;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 套餐卡信息表视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CardVO对象", description = "套餐卡信息表")
public class CardVO extends Card {

	private static final long serialVersionUID = 1L;

	/**
	 * 套餐详情
	 */
	private CardCategoryVO category;

	/**
	 * 套餐名称
	 */
	private String categoryNames;

	private String parklotNames;

	/**
	 * 是否启用
	 */
	private boolean disable;

	@ApiModelProperty(value = "缓冲天数")
	private int cacheDay;

	@ApiModelProperty(value = "循环车计费类型")
	private int multipleChargeType;

	@ApiModelProperty(value = "车位分类 0-租赁车位 1-产权车位")
	private String classify;

	@ApiModelProperty(value = "车位分类 0-租赁车位 1-产权车位")
	private String regionName;

	@ApiModelProperty(value = "车位分类 0-租赁车位 1-产权车位")
	private String floorName;

	/**
	 * 车位编号
	 */
	private String placeCode;

	@ApiModelProperty(value = "月卡过期时间")
	private String endTimeStr;

	@ApiModelProperty(value = "剩余天数")
	private Long remainingDays;

	@ApiModelProperty(value = "订单编号")
	private String tradeNo;


	@ApiModelProperty(value = "单价")
	private BigDecimal unitPrice;


	@ApiModelProperty(value = "是否失效")
	private Boolean isInvalid;

	@ApiModelProperty(value = "缴费月份")
	private List<PayMonth> monthList;

	@ApiModelProperty(value = "剩余天数缴费")
	private Map<String,Object> surplusDay;

	@ApiModelProperty(value = "过期天数")
	private String nearEndDate;
	private String placeTypeName;

	@ApiModelProperty(value = "是否第一次续费（0-否;1-是）")
	private Integer firstPay;

	@ApiModelProperty(value = "续费方式  1，按月续费 2 按天续费")
	private Integer payStyle;


	/**
	 * 冗余字段，供前端展示
	 */
	private String regionNames;

	private Integer periodType;

	private Integer customStartTimeEnabled;

	private File[] files;

	@ApiModelProperty("月卡状态：0-失效 1-有效 2-禁用 3-暂停")
	private Integer cardStatus;
	/**
	 * 导出-月卡状态
	 */
	private String cardStatusDesc;

	/**
	 * 欠缴信息
	 */
	private List<CardTempUnpaidOrderVO> tempUnpaidOrders;
}
