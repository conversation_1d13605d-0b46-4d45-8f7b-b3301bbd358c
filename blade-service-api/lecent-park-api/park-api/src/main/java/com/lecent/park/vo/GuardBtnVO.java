package com.lecent.park.vo;

import com.lecent.park.entity.GuardBtn;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗亭端按钮表视图实体类
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GuardBtnVO对象", description = "岗亭端按钮表")
public class GuardBtnVO extends GuardBtn {
	private static final long serialVersionUID = 1L;

}
