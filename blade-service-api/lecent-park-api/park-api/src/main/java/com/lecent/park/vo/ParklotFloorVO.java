package com.lecent.park.vo;

import com.lecent.park.entity.ParklotFloor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParklotFloorVO对象", description = "ParklotFloorVO对象")
public class ParklotFloorVO extends ParklotFloor {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("车场名称")
	private String parklotName;

	private Long userId;

	private List<ParklotRegionVO> parklotRegionVO;

}
