package com.lecent.park.vo;

import com.lecent.park.entity.WechatUserPlate;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车位预约配置表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WechatUserPlateVO对象", description = "车位预约配置表")
public class WechatUserPlateVO extends WechatUserPlate {
	private static final long serialVersionUID = 1L;

}
