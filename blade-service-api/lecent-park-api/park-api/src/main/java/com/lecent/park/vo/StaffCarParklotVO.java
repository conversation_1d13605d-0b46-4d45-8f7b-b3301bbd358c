package com.lecent.park.vo;

import com.lecent.park.entity.StaffCarParklot;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工车辆与车场关联信息视图实体类
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StaffCarParklotVO对象", description = "员工车辆与车场关联信息")
public class StaffCarParklotVO extends StaffCarParklot {
	private static final long serialVersionUID = 1L;

	/**
	 * 车场
	 */
	@ApiModelProperty(value = "车场")
	private String parklotName;
	/**
	 * 临停规则
	 */
	@ApiModelProperty(value = "临停规则")
	private String ruleName;

}
