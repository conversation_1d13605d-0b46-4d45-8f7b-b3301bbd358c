package com.lecent.park.vo.vehiclePhoneRelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 车主信息
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
@ApiModel(value = "车辆信息编辑")
public class UpdateVehiclePhoneVO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "车辆信息 id")
	@NotNull(message = "车辆信息 id 不能为空")
	private Long vehicleId;
	@ApiModelProperty(value = "修改手机号信息列表")
	private List<UpdatePhoneItem> phoneRelationList;

	@Data
	public static class UpdatePhoneItem {

		@ApiModelProperty("id")
		private Long id;
		@ApiModelProperty(value = "电话号码")
		@NotBlank(message = "电话号码不能为空")
		private String phone;
		@ApiModelProperty(value = "来源 1: 微信授权 2：app补录 3：车辆绑定")
		@NotNull(message = "来源不能为空")
		private Integer source;
	}

}
