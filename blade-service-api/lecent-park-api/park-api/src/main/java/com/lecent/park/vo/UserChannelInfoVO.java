package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.entity.ChannelAbnormalReason;
import lombok.Data;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description： 用户登录后返回用户登录信息和停车场通道等信息
 * <AUTHOR>
 * @Date: 2020/6/4 18:49
 */
@Data
public class UserChannelInfoVO {

	private Boolean duting;
	private List<ChannelAbnormalReason> enterReasons = new ArrayList<>();
	private List<ChannelAbnormalReason> leaveReasons = new ArrayList<>();
	private List<ChannelAbnormalReason> feeReasons = new ArrayList<>();
	private LocalDateTime currentTime;
	private String realName;
	private String loginName;
	private List<ParklotInfoVO> parklots;

	private Long userId;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private LocalDateTime dutyTime;

}
