package com.lecent.park.en.invoice;

import lombok.Getter;

/**
 * 开票状态码
 *
 * <AUTHOR>
 */
@Getter
public enum InvoiceReturnStateCode {
	/**
	 * 成功
	 */
	SUCCESS("0000", "成功"),
	/**
	 * 开票失败
	 */
	FAIL("9999", "开票失败"),
	/**
	 * 开票错误，未找到此纳税人注册信息，或传入注册信息有误！
	 */
	INVOICE_ERROR("9995", "开票错误，未找到此纳税人注册信息，或传入注册信息有误！"),
	/**
	 * 重新签章失败 不做任何处理
	 */
	RE_SIGNATURE_FAIL("9994", "重新签章失败 不做任何处理"),
	/**
	 * 开票成功，签章成功 ，获取二维码失败， 二维码为空
	 */
	QQRCODE_FAIL("9993", "开票成功，签章成功 ，获取二维码失败， 二维码为空"),
	/**
	 * 发票号码或发票代码有误
	 */
	NUMBER_INVALID("9992 ", "发票号码或发票代码有误"),
	/**
	 * 开票成功，签章失败二维码为空
	 */
	SIGNATURE_FAIL("9991", "开票成功，签章失败二维码为空"),
	/**
	 * 数据格式错误
	 */
	DATA_FORMAT_INVALID("8882", "数据格式错误"),
	/**
	 * 未找到主控纳税人信息，无法进行开票
	 */
	UNKNOWN_INFO("8881", "未找到主控纳税人信息，无法进行开票"),
	;


	/**
	 * 状态码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String desc;

	InvoiceReturnStateCode(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}
}
