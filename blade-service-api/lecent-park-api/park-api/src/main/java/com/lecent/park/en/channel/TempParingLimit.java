package com.lecent.park.en.channel;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通道临停限制
 *
 * <AUTHOR> zxr
 * @date : 2021/12/14
 */
@Getter
@AllArgsConstructor
public enum TempParingLimit {

	/**
	 * 启用，临停限制(禁止所有临停)
	 */
	ENABLE(0),

	/**
	 * 禁用，允许临停(允许所有类型车辆临停)
	 */
	DISABLE(1) {
		@Override
		public boolean isLimit() {
			return false;
		}
	},

	/**
	 * 临停限制(只允许新能源车辆临停)
	 */
	PASS_NEW_ENERGY(2);

	private Integer value;

	/**
	 * 获取对应状态
	 *
	 * @param value 状态值
	 * @return en
	 */
	public static TempParingLimit verify(Integer value) {
		for (TempParingLimit en : values()) {
			if (en.getValue().equals(value)) {
				return en;
			}
		}
		return TempParingLimit.ENABLE;
	}

	/**
	 * 通道是否限制
	 *
	 * @return true临停限制、false允许临停
	 */
	public boolean isLimit() {
		return true;
	}
}
