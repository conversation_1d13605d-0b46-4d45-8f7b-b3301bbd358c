package com.lecent.park.vo;

import com.lecent.park.entity.BroadcastTemp;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置设置表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceSettingVO对象", description = "设置设置表")
public class BroadcastTempVO extends BroadcastTemp {
	private static final long serialVersionUID = 1L;


	private String userName;

}
