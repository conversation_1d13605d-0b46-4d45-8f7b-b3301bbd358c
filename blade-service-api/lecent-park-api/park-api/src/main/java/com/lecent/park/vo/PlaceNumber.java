package com.lecent.park.vo;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
public class PlaceNumber {

	/**
	 * 总数量
	 */
	private Integer totalNumber;
	/**
	 * vip数量
	 */
	private Integer vipNumber;

	/**
	 * 可用数量
	 */
	private Integer remainNumber;
	/**
	 * 可预约数量
	 */
	private Integer reservableNumber;

	public PlaceNumber(Integer totalNumber, Integer vipNumber, Integer remainNumber, Integer reservableNumber) {
		this.totalNumber = totalNumber;
		this.vipNumber = vipNumber;
		this.remainNumber = remainNumber;
		this.reservableNumber = reservableNumber;
	}
}
