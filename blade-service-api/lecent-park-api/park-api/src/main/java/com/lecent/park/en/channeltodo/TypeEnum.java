package com.lecent.park.en.channeltodo;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 待办类型
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum TypeEnum {

	ENTER_STOP_NO_PLATE("ENTER_STOP_NO_PLATE", "无牌车"),

	NO_PLATE("NO_PLATE", "无牌车"),

	ENTER_STOP_TIME_LIMIT("ENTER_STOP_TIME_LIMIT", "临停时段限制"),

	ENTER_STOP_CONFIRMING("ENTER_STOP_CONFIRMING", "等待确认"),

	ENTER_STOP_BLACK_LIST("ENTER_STOP_BLACK_LIST", "黑名单"),

	ENTER_STOP_CHANNEL_LIMIT("ENTER_STOP_CHANNEL_LIMIT", "禁止外来车辆"),

	ENTER_UNKNOWN("ENTER_UNKNOWN", "未知"),

	PLACE_FULL("PLACE_FULL", "车位已满"),

	ENTER_CARD_TYPE_CHANNEL_LIMIT("ENTER_CARD_TYPE_CHANNEL_LIMIT", "非专用车辆禁止入场"),



	LEAVE_STOP_NO_PLATE("LEAVE_STOP_NO_PLATE", "无牌车"),

	LEAVE_STOP_BLACK_LIST("LEAVE_STOP_BLACK_LIST", "黑名单"),

	LEAVE_STOP_YELLOW_LIST("LEAVE_STOP_YELLOW_LIST", "黄名单"),

	LEAVE_STOP_NO_ENTER_RECORD("LEAVE_STOP_NO_ENTER_RECORD", "无进场记录"),

	NO_ENTER_RECORD("NO_ENTER_RECORD", "无进场记录"),

	LEAVE_STOP_PAYING("LEAVE_STOP_PAYING", "等待支付"),

	LEAVE_STOP_CONFIRMING("LEAVE_STOP_CONFIRMING", "等待确认"),

	LEAVE_STOP_UNKNOWN("LEAVE_STOP_UNKNOWN", "未知"),

	LEAVE_CARD_TYPE_CHANNEL_LIMIT("LEAVE_CARD_TYPE_CHANNEL_LIMIT", "禁止类型车辆进场");



	private final String name;
	private final String msg;


	public static String getMsg(String name){
		for (TypeEnum en : TypeEnum.values()) {
			if (en.getName().equals(name)){
				return en.getMsg();
			}
		}
		return "";
	}






}
