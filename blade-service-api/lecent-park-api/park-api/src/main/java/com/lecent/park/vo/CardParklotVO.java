package com.lecent.park.vo;

import com.lecent.park.entity.CardParklot;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 套餐车场表视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CardParklotVO对象", description = "套餐车场表")
public class CardParklotVO extends CardParklot {
	private static final long serialVersionUID = 1L;

}
