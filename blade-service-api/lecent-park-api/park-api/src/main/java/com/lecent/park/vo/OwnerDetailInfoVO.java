package com.lecent.park.vo;

import com.lecent.park.entity.OwnerDetailInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.node.INode;

import java.util.List;

/**
 * 楼层业主信息表视图实体类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OwnerDetailInfoVO对象", description = "楼层业主信息表")
public class OwnerDetailInfoVO extends OwnerDetailInfo implements INode {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "子类对象")
	private List<OwnerDetailInfoVO> children;

	@ApiModelProperty(value = "组团数量")
	private Integer groupNum;

	@ApiModelProperty(value = "楼栋数量")
	private Integer towerNum;

	@ApiModelProperty(value = "单元数量")
	private Integer apartmentNum;

	@ApiModelProperty(value = "房间数量")
	private Integer roomNum;

	@ApiModelProperty(value = "车辆数")
	private Integer carNum;

	@ApiModelProperty(value = "月卡数量")
	private Integer cardNum;

	@ApiModelProperty(value = "业主信息车辆信息")
	private List<CardVO> carInfoList;

	@ApiModelProperty(value = "车牌")
	private String plates;

	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;


}
