package com.lecent.park.vo;

import com.lecent.park.entity.DeviceSetting;
import com.lecent.park.entity.DeviceVolume;
import com.lecent.park.entity.PaceDevice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceInfoVO对象", description = "DeviceInfoVO对象")
public class PaceDeviceVO extends PaceDevice {
	private static final long serialVersionUID = 1L;

	/**
	 * 设置内容列表
	 */
	private List<DeviceSetting> deviceSettingList;

	/**
	 * 音量列表
	 */
	private List<DeviceVolume> deviceVolumeList;

	@ApiModelProperty("车场名称")
	private String parklotName;

	@ApiModelProperty("车场类型")
	private String parklotType;

	@ApiModelProperty("楼层名称")
	private String floorName;
	@ApiModelProperty("区域名称")
	private String regionName;

	@ApiModelProperty(value = "音量大小")
	private String volumeSize;

	/**
	 * 扩展id  用于前端展示
	 */

	private String sn;
	private String extId;

	private String placeType;

	private Integer isFree;
}
