package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/5 15:32
 */
@Data
@Builder
@ApiModel("订单笔数统计")
public class OrderStatisticsVO {
	@ApiModelProperty("笔数")
	private Integer count;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("应收合计")
	private BigDecimal totalAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("提前付款合计")
	private BigDecimal paidAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠合计")
	private BigDecimal discountAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(优惠券)")
	private BigDecimal couponDiscountAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(商户)")
	private BigDecimal merchantAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(充电)")
	private BigDecimal chargingDiscountAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(未交|稽核)")
	private BigDecimal unpaidDiscountAmount;

	@ApiModelProperty("优惠(新能源车优惠)")
	private BigDecimal nevsDiscountAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("实收合计")
	private BigDecimal receiveAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("多收合计")
	private BigDecimal moreAmount;

	@ApiModelProperty("少收合计")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal lessAmount;

	@ApiModelProperty("通道缴费")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal channelPayAmount;

	@ApiModelProperty("现金缴费")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal cashAmount;
}
