package com.lecent.park.en.coupon;

/**
 * 170平台立减金状态
 *
 * <AUTHOR>
 * @date 2020-06-03 09:52
 */
public enum Platform170CouponStatusEnum {

	UN_USED(0, "待使用"),

	USED(1, "已使用"),

	OVERDUE(2, "已过期");


	Integer value;
	String name;

	Platform170CouponStatusEnum(Integer value, String name) {
		this.value = value;
		this.name = name;
	}

	public Integer getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public static String getName(Integer value) {
		if (value == 1) {
			return UN_USED.getName();
		} else if (value == 2) {
			return USED.getName();
		} else if (value == 3) {
			return OVERDUE.getName();
		}
		return null;
	}
}
