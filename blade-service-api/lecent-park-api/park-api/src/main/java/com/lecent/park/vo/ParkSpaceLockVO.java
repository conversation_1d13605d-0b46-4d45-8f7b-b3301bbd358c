package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 车位锁视图类
 *
 * <AUTHOR>
 * @since 2022/12/15
 */
@Data
@ApiModel(value = "ParkSpaceLockVO对象", description = "车位锁视图类")
public class ParkSpaceLockVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("车位锁序列号")
    private String sn;

    @ApiModelProperty("车位编号")
    private String placeCode;

    @ApiModelProperty("通信信道")
    private Integer channel;

    @ApiModelProperty("设备类型")
    private Integer deviceType;

    @ApiModelProperty("mac地址")
    private String mac;
}
