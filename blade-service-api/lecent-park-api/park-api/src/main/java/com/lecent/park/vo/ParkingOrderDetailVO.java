package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *  用户端停车订单详情
 * <AUTHOR>
 * @date 2021-04-07 11:54
 */
@Data
public class ParkingOrderDetailVO implements Serializable {


	/**
	 * 车牌号
	 */
	private String plate;

	/**
	 * 车场名称
	 */
	private String parklotName;

	/**
	 * 入场时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date enterTime;

	/**
	 * 车场时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date exitTime;

	/**
	 * 停车费用
	 */
	private BigDecimal totalAmount;


	/**
	 * 停车时长
	 */
	private String duration;

	/**
	 * 优惠金额
	 */
	private BigDecimal discountAmount;

	/**
	 * 缴费时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private Date payTime;

	/**
	 * 车牌类型（1-临停车；2-月卡过期）
	 */
	private Integer plateType;

	/**
	 * 缴费信息
	 */
	private String chargeData;

	/**
	 * 车场id
	 */
	private Long parklotId;

}
