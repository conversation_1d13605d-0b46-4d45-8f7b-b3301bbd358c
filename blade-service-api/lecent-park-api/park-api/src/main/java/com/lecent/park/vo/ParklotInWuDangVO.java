package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 车场信息表实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@ApiModel(value = "Parklot对象", description = "车场信息表")
public class ParklotInWuDangVO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "车场id")
	private Long id;

	@ApiModelProperty(value = "车场编号")
	private String parklotNo;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 全称
	 */
	@ApiModelProperty(value = "全称")
	private String fullName;
	/**
	 * 备注，描述
	 */
	@ApiModelProperty(value = "备注，描述")
	private String memo;

	@ApiModelProperty(value = "地址")
	private String address;

	@ApiModelProperty(value = "总层数")
	private Integer floorAmount;

	@ApiModelProperty(value = "独立产权车位数")
	private Integer independentOwnershipAmount;

	@ApiModelProperty(value = "临停车位数")
	private Integer tempLotAmount;

	@ApiModelProperty(value = "vip车位")
	private Integer vipLotAmount;

	@ApiModelProperty(value = "无障碍车位数")
	private Integer accessibilityLotAmount;

	@ApiModelProperty(value = "字母车位数")
	private Integer letterLotAmount;

	@ApiModelProperty(value = "总面积")
	private BigDecimal totalAreaAmount;





}
