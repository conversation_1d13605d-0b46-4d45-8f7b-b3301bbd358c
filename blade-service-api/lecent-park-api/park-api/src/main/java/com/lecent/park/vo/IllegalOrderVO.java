package com.lecent.park.vo;

import com.lecent.park.entity.IllegalOrder;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆违法记录视图实体类
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "IllegalOrderVO对象", description = "车辆违法记录")
public class IllegalOrderVO extends IllegalOrder {
	private static final long serialVersionUID = 1L;

	private String parklotName;
	private String regionName;
	private String placeName;
	private String parklotType;
	private String illegalTypeName;
	private String isUploadName;
}
