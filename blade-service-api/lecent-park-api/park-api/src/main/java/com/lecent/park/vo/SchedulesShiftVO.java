package com.lecent.park.vo;

import com.lecent.park.entity.SchedulesShift;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 排班班次表视图实体类
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SchedulesShiftVO对象", description = "排班班次表")
public class SchedulesShiftVO extends SchedulesShift {
	private static final long serialVersionUID = 1L;

}
