package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lecent.park.entity.Schedules;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.utils.Func;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 排班表视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@JsonIgnoreProperties({
        "tenantId", "createUser", "createDept", "createTime", "updateUser", "updateTime", "status", "isDeleted"
})
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SchedulesVO对象", description = "排班表")
public class SchedulesVO extends Schedules {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("车场名称")
    private String parklotName;

    @ApiModelProperty("班次名称")
    private String shiftName;

    @ApiModelProperty("排班日志")
    private List<SchedulingLogsVO> logs;

    @ApiModelProperty("车场ids")
    private String parkLotIdStr;

    @ApiModelProperty("班次ids")
    private String shiftIds;

    @ApiModelProperty("角色")
    private String role;

    @ApiModelProperty("车场ids")
    private List<Long> parkLotIds;

    public List<Long> parklotIds() {
        if (this.parkLotIds == null) {
            this.parkLotIds = Optional.ofNullable(this.parkLotIdStr)
                    .filter(Func::isNotBlank)
                    .map(Func::toLongList)
                    .orElseGet(ArrayList::new);
        }

        return this.parkLotIds;
    }
}
