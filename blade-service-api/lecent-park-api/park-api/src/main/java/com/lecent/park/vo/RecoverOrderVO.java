package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.utils.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 追缴订单vo
 *
 * <AUTHOR>
 * @date 2023/07/31
 */
@Data
@ApiModel("追缴订单vo")
public class RecoverOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "订单号")
    @ApiModelProperty("订单号")
    private String tradeNo;
	@ApiModelProperty("停车记录编号")
	@Excel(name = "停车记录编号")
	private Long parkingId;

    @Excel(name = "车牌")
    @ApiModelProperty("车牌")
    private String plate;

    @Excel(name = "总金额")
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    @Excel(name = "支付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("支付时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "收费员")
    @ApiModelProperty("收费员")
    private String tollCollector;

    @ApiModelProperty("收费员id")
    private Long tollCollectorId;

    @Excel(name = "佣金比例")
    @ApiModelProperty("佣金比例")
    private BigDecimal commissionRate;

    @Excel(name = "佣金")
    @ApiModelProperty("佣金")
    private BigDecimal commission;

    @ApiModelProperty("合并订单id")
    private Long mergeOrderId;

	@ApiModelProperty("追缴类型 1 系统追缴 2人工追缴")
	private Integer recoveryType;

	@Excel(name = "追缴类型")
	@ApiModelProperty("追缴类型")
	private String recoveryTypeName;
}
