package com.lecent.park.vo;

import com.lecent.park.entity.ParklotSmsSetting;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;

/**
 * 车场移动短信配置表视图实体类
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DParklotSmsSettingVO对象", description = "车场移动短信配置表")
public class ParklotSmsSettingVO extends ParklotSmsSetting {
	private static final long serialVersionUID = 1L;
	/**
	 * 移车提醒时间点List
	 */
	private ArrayList<ParklotSmsSettingVO> nonStopPeriodTimes;
	/**
	 *移车的时间（具体数值。**分钟，**小时）
	 */
	private Integer nonParkingTime;

	/**
	 * 历史催缴时间点List
	 */
	private ArrayList<ParklotSmsSettingVO> historyNonStopPeriodTimes;
	/**
	 *催缴短信下发的时间（具体数值。**分钟，**小时）
	 */
	private Integer historyNonParkingTime;

	/**
	 * 历史催缴当天时间点
	 */
	private String historyPointOfTime;

	/**
	 * 当天催缴时间点List
	 */
	private ArrayList<ParklotSmsSettingVO> callNonStopPeriodTimes;
	/**
	 *当天催缴短信下发的时间（具体数值。**分钟，**小时）
	 */
	private Integer callNonParkingTime;
	/**
	 * 时段类型单位(1.分钟，2.小时，3.天，4.周)
	 */
	private Integer timeType;
	/**
	 * 移车短信模板
	 */
	private String smTextarea;

	/**
	 * 催缴短信模板
	 */
	private String historyCallSmTextarea;

	/**
	 * 移车短信模板
	 */
	private ArrayList disturbTimes;


}
