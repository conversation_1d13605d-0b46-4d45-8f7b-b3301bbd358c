package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 追缴支付dto
 *
 * <AUTHOR>
 * @date 2023/07/17
 */
@Data
public class RecoveredPayVO {

    @ApiModelProperty("车牌")
    private String plate;

    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty("订单数量")
    private Integer orderSize;

    @ApiModelProperty("未缴订单")
    private List<UserUnpaidOrderByParklotIdListVo> unpaidOrder;

    @ApiModelProperty("待办ids")
    private List<Long> todoIds;
}
