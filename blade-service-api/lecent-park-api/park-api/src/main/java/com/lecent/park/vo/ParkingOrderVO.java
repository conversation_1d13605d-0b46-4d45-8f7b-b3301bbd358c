package com.lecent.park.vo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.entity.ParkingImageDO;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.ParklotDeviceRet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 停场场订单视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParkingVO对象", description = "停场场订单")
public class ParkingOrderVO extends ParkingOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 进场通道名称
	 */
	@ApiModelProperty(value = "进场通道名称")
	private String enterChannelName;

	/**
	 * 出场通道名称
	 */
	@ApiModelProperty(value = "出场通道名称")
	private String exitChannelName;

	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	@ApiModelProperty(value = "收费明细")
	List<TempParkingOrderVO> orders;

	/**
	 * 月卡部分信息
	 */
	@ApiModelProperty(value = "月卡部分信息")
	private String setMealInfo;

	/**
	 * 月卡部分信息
	 */
	@ApiModelProperty(value = "月卡部分信息")
	private JSONObject setNewMealInfo;

	/**
	 * 付款状态 0:待付， 1：已付
	 */
	@TableField(exist = false)
	private Integer paymentStatus;

	/**
	 * 支付状态 0未支付 1支付成功
	 */
	@TableField(exist = false)
	private Integer payStatus;

	/**
	 * 应收
	 */
	@ApiModelProperty(value = "应收")
	private BigDecimal receiveAmount;
	@ApiModelProperty(value = "欠缴")
	private BigDecimal unPaidAmount;

	@ApiModelProperty(value = "历史欠缴金额")
	private BigDecimal historyUnpaidAmount;

	/**
	 * 停车时段
	 */
	private String parkingPeriod;

	private String parkingStatusName;

	private String enterWayName;
	private String exitWayName;

	private boolean isOvertime=false;

	private List<Date> enterTimes;

	private String cardNo;

	private List<Date> exitTimes;

	/**
	 * 车场类型（1-路外；2-路内）
	 */
	@ApiModelProperty(value = "车场类型（1-路外；2-路内）")
	private Integer parklotType;
	/**
	 * 区域名称
	 */
	@ApiModelProperty(value = "区域名称")
	private String regionName;

	@ApiModelProperty(value = "停车详细地址")
	private String addr;
	@ApiModelProperty(value = "未缴次数")
	private int unpaidNumberOfTimes;
	@ApiModelProperty(value = "未缴订单ID")
	private String orderIds;
	@ApiModelProperty(value = "未缴总金额")
	private BigDecimal unpaidTotalAmount;
	@ApiModelProperty(value = "总时长")
	private String totalTimesStr;

	/**
	 * 车位编号
	 */
	@ApiModelProperty(value = "车位编号")
	private String placeCode;

	@ApiModelProperty(value = "支付码")
	private String payCode;

	/**
	 * 欠费时长
	 */
	@ApiModelProperty(value = "欠费时长")
	private String delayTime;

	/**
	 * 场中场停车轨迹
	 */
	private List<ParkingOrderVO> locus;

	/**
	 * 最后离场时间
	 */
	private Date lastExitTime;

	/**
	 * 是否场中场
	 */
	private Boolean isNesting;

	/**
	 * 进场地点
	 */
	@ApiModelProperty(value = "进场地点")
	private String enterAddr;

	/**
	 * 车牌号（仅供前端冗余展示）
	 */
	@ApiModelProperty(value = "车牌号")
	private String plates;

	/**
	 * 计费开始时间
	 */
	@ApiModelProperty(value = "计费开始时间")
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private Date billingStartTime;
	/**
	 * 计费截至时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "计费截至时间")
	private Date billingEndTime;

	@ApiModelProperty(value = "小票打印记录")
	private List<ReceiptPrintLogVO> receiptPrintLogVOS;

	@ApiModelProperty(value = "设备信息")
	private Map<Integer, ParklotDeviceRet> deviceMap;

	/**
	 * 停车进出场图片Map
	 * key=2, 进场图片集合
	 * key=4, 出场图片集合
	 */
	private Map<Integer, List<ParkingImageDO>> imageUrlMap;

	@ApiModelProperty("车辆类型")
	private String carType;

	/**
	 * 异常订单  异常；类型
	 */
	@ApiModelProperty("异常订单  异常类型 0没有异常 1 进场覆盖 2 进出场不一致 3无牌车有进场图片 4无进出场图片")
	private Integer abnormalType;

	/**
	 * 异常订单处理人
	 */
	@ApiModelProperty("异常订单处理人")
	private Long handlerPersonId;

	/**
	 * 异常订单处理人
	 */
	@ApiModelProperty("异常订单处理人")
	private String handlerPersonName;

	/**
	 * 异常订单处理时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty("异常订单处理时间")
	private Date handlerDate;

	/**
	 * 异常订单 处理备注
	 */
	@ApiModelProperty("异常订单 处理备注")
	private String handlerRemark;
	/**
	 * 异常订单处理状态 0 待处理 1 已处理
	 */
	@ApiModelProperty("异常订单处理状态 0 待处理 1 已处理")
	private Integer handlerStatus;

	@ApiModelProperty("异常原因")
	private String reason;

	@ApiModelProperty("退款金额")
	private BigDecimal refundAmount;

}
