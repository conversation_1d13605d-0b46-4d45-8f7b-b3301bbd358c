package com.lecent.park.vo;

import com.lecent.park.en.invoice.InvoiceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 开具发票记录实体
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
public class InvoiceCardOrderVO {
	private static final long serialVersionUID = 1L;

	/**
	 * 电话号码
	 */
	@ApiModelProperty(value = "电话号码")
	private String phone;

	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String name;

	@ApiModelProperty(value = "收费金额")
	private BigDecimal receiveAmount;

	@ApiModelProperty(value = "支付时间")
	private Date payTime;
	/**
	 * 车牌
	 */
	@ApiModelProperty(value = "车牌")
	private String plate;
	/**
	 * 车场ID
	 */
	@ApiModelProperty(value = "车场ID")
	private String parklotId;

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 订单类型
	 * {@link InvoiceType}
	 */
	@ApiModelProperty(value = "订单类型")
	private Integer invoiceType;

	@ApiModelProperty(value = "开始时间")
	private String startTime;

	@ApiModelProperty(value = "结束时间")
	private String endTime;

	@ApiModelProperty(value = "支持开票的车场")
	private List<Long> parklotCollect;

	@ApiModelProperty(value = "用户绑定车牌")
	private List<String> plateCollect;

}
