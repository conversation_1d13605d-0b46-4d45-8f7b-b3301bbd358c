package com.lecent.park.vo;

import com.lecent.park.entity.VisitorAuth;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 访客授权表视图实体类
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "VisitorAuthVO对象", description = "访客授权表")
public class VisitorAuthVO extends VisitorAuth {
	private static final long serialVersionUID = 1L;

	/**
	 * 车场名称
	 */
	private String parklotName;
	/**
	 * 微信uid
	 */
	private String uid;
}
