package com.lecent.park.vo;

import com.lecent.park.entity.ReserveParkConfig;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 月卡变更日志表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ReserveParkConfigVO对象", description = "月卡变更日志表")
public class ReserveParkConfigVO extends ReserveParkConfig {
	private static final long serialVersionUID = 1L;

}
