package com.lecent.park.en.merchant;

/**
 * 酒店授权车辆的授权状态
 *
 * <AUTHOR>
 * @date 2021-11-30 09:52
 */
public enum MerchantParingStatusEnum {

	INVALID(0, "授权无效"),
	VALID(1, "授权有效"),
	DELETED(-1, "授权已移除"),
	WAIT_ENTER(2, "授权后车辆未进出");

	private final int value;
	private final String name;

	MerchantParingStatusEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}


	public String getName() {
		return name;
	}
}
