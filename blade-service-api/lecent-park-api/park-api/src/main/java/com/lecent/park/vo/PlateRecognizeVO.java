package com.lecent.park.vo;

import com.lecent.park.entity.PlateRecognize;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 对象存储表视图实体类
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlateRecognizeVO对象", description = "对象存储表")
public class PlateRecognizeVO extends PlateRecognize {
	private static final long serialVersionUID = 1L;

}
