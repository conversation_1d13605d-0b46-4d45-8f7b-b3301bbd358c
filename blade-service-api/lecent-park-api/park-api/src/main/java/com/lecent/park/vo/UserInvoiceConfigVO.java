package com.lecent.park.vo;

import com.lecent.park.entity.UserInvoiceConfig;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开发票抬头配置视图实体类
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserInvoiceConfigVO对象", description = "开发票抬头配置")
public class UserInvoiceConfigVO extends UserInvoiceConfig {
	private static final long serialVersionUID = 1L;

}
