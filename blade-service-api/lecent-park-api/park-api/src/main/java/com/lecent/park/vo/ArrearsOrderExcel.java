package com.lecent.park.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.excel.ExcelData;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 欠费统计vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("欠费统计vo")
public class ArrearsOrderExcel extends ExcelData {
	private static final long serialVersionUID = 1L;

	@ExcelProperty(value = "车牌")
	private String plate;

	@ExcelProperty(value = "手机号码")
	private String phone;

	@ExcelProperty(value = "欠费总额")
	private BigDecimal totalArrearsMoney;

	@ExcelProperty(value = "欠费单数")
	private String totalArrearsCount;

	@ExcelProperty(value = "最近缴费金额")
	private BigDecimal arrearsMoney;

	@ColumnWidth(25)
	@ExcelProperty(value = "最近欠费时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date billStartTime;

	@ExcelProperty("是否在场")
	private String enterStatus;
}
