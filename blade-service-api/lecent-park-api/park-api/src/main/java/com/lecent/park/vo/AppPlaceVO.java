package com.lecent.park.vo;

import com.lecent.park.entity.ParkingPlace;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AppPlaceVO", description = "AppPlaceVO")
public class AppPlaceVO extends ParkingPlace {

	@ApiModelProperty("车位绑定状态（0-未绑定；1-已绑定）")
	private Integer bindStatus;

	@ApiModelProperty("车位绑定的mac地址")
	private String macAddress;
}
