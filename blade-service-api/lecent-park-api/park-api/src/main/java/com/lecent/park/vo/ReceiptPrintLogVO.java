package com.lecent.park.vo;

import com.lecent.park.entity.ReceiptPrintLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 小票打印记录视图实体类
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ReceiptPrintLogVO对象", description = "小票打印记录")
public class ReceiptPrintLogVO extends ReceiptPrintLog {
	private static final long serialVersionUID = 1L;

}
