package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@ApiModel("缴费码")
@AllArgsConstructor
public class PayQrCode {

	/**
	 * {@link com.lecent.park.common.enums.parkinglot.PayQrCodeType}
	 * 缴费码类型
	 */
	@ApiModelProperty(value = "缴费码类型")
	private Integer payCodeType;
	@ApiModelProperty("二维码链接")
	private String url;
}
