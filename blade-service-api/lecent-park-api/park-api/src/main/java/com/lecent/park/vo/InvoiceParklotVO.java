package com.lecent.park.vo;

import com.lecent.park.entity.InvoiceParklot;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车场和开票信息关联表视图实体类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InvoiceParklotVO对象", description = "车场和开票信息关联表")
public class InvoiceParklotVO extends InvoiceParklot {
	private static final long serialVersionUID = 1L;

}
