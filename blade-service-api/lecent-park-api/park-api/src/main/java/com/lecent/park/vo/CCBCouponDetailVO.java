package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户端我的优惠劵详情
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@ApiModel(value = "用户端我的优惠劵详情", description = "用户端我的优惠劵详情")
public class CCBCouponDetailVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("用户优惠劵id")
	private Long userCouponId;

	@ApiModelProperty("优惠劵名称")
	private String couponName;

	@ApiModelProperty("优惠劵编号")
	private String couponNo;

	@ApiModelProperty("优惠劵来源(1-用户注册;2-7天未登录;3-节假日或者周末发放;4-好友转赠;5-首次绑定车牌)")
	private Integer couponSource;

	@ApiModelProperty("好友手机号")
	private String friendMobile;

	@ApiModelProperty("好友赠送时间")
	private Date fiendGiveTime;

	@ApiModelProperty("优惠劵类型")
	private Integer couponType;

	@ApiModelProperty("可转赠次数")
	private Integer giveOtherTimes;

	@ApiModelProperty("开始时间")
	private Date startDate;

	@ApiModelProperty("结束时间")
	private Date endDate;

	@ApiModelProperty("满多少元")
	private String fullAmount;

	@ApiModelProperty("减多少元")
	private String reduceAmount;

	@ApiModelProperty("折扣数")
	private String discountAmount;

	@ApiModelProperty("使用说明")
	private String remark;

	@ApiModelProperty("抵扣时长")
	private Integer reduceHour;

	@ApiModelProperty("优惠劵状态")
	private Integer status;

	@ApiModelProperty("适用车场")
	private List<CCBCouponParklotVO> availableParklot;


}
