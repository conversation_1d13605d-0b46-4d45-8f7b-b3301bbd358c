package com.lecent.park.en.invoice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum InvoiceMessageEnum {

	// 空闲中
	MESSAGE_PHONE("1","手机"),
	MESSAGE_EMAIL("0","邮件"),
	MESSAGE_PHONE_EMAIL("2","手机和邮件")
	;
	public String code ;

	public String value;

	InvoiceMessageEnum(String code, String value) {
		this.code = code;
		this.value = value;
	}

	public static String getValueByCode(Integer code){
		for (InvoiceMessageEnum stateEnum : InvoiceMessageEnum.values()) {
			if (stateEnum.code.equals(code)){
				return stateEnum.value;
			}
		}
		return "";
	}

	public static List<Map> enumToDict(){
		List<Map> listDict = new ArrayList<>();
		for (InvoiceMessageEnum stateEnum : InvoiceMessageEnum.values()) {
			HashMap<String, Object> dict = new HashMap<>();
			dict.put("code",stateEnum.code);
			dict.put("value",stateEnum.value);
			listDict.add(dict);
		}
		return listDict;
	}

}
