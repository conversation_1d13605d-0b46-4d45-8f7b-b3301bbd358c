package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 建行优惠劵信息
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@ApiModel(value = "建行优惠劵信息", description = "建行优惠劵信息")
public class CCBPayCouponVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("最大优惠金额")
	private BigDecimal maxDiscountAmount;

	@ApiModelProperty("优惠劵id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long couponId;

	private List<CCBCouponVO> list;


}
