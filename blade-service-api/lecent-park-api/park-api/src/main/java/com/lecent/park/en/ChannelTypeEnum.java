package com.lecent.park.en;

/**
 * 通道类型
 *
 * <AUTHOR>
 * @date 2020-06-03 09:52
 */
public enum ChannelTypeEnum {
	/**
	 * 入口
	 */
	ENTER(1),

	/**
	 * 出口
	 */
	EXIT(2);

	Integer value;

	ChannelTypeEnum(Integer value) {
		this.value = value;
	}

	public Integer getValue() {
		return value;
	}

	/**
	 * 判断是否是进场
	 *
	 * @param value c
	 * @return t
	 */
	public static boolean isEnter(Integer value) {
		return ENTER.getValue().equals(value);
	}
}
