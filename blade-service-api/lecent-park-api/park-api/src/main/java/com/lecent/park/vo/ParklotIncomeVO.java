package com.lecent.park.vo;

import lombok.Data;
import org.nustaq.serialization.annotations.Serialize;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 统计车场VO
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@Serialize
public class ParklotIncomeVO {

	/**
	 * 临时车场统计
	 */
	private BigDecimal tempIncomeAmount = BigDecimal.ZERO;
	/**
	 * 车场统计
	 */
	private BigDecimal cardIncomeAmount = BigDecimal.ZERO;
	/**
	 * 返回日期
	 */
	private String duration;

	private List<ParklotIncomeDataVO> parklotIncomeList = new ArrayList<>();

}
