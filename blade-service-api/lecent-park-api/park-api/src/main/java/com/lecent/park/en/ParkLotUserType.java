package com.lecent.park.en;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车场管理系统登录用户类型
 *
 * <AUTHOR> zxr
 * @date : 2021/12/3
 */
@Getter
@AllArgsConstructor
public enum ParkLotUserType {

	/**
	 * 无类型
	 */
	NONE(-1),

	/**
	 * 物业端用户
	 */
	PM(1) {
		@Override
		public boolean isPm() {
			return true;
		}
	},

	/**
	 * 商家端用户
	 */
	MERCHANT(2) {
		@Override
		public boolean isMerchant() {
			return true;
		}
	};

	private Integer code;

	/**
	 * 获取对应用户端类型
	 *
	 * @param code code
	 * @return userType
	 */
	public static ParkLotUserType verify(int code) {
		for (ParkLotUserType userType : values()) {
			if (userType.getCode().equals(code)) {
				return userType;
			}
		}
		return NONE;
	}


	/**
	 * 物业端
	 *
	 * @return 是否是物业端
	 */
	public boolean isPm() {
		return false;
	}

	/**
	 * 商户端
	 *
	 * @return 是否是商户端
	 */
	public boolean isMerchant() {
		return false;
	}

}
