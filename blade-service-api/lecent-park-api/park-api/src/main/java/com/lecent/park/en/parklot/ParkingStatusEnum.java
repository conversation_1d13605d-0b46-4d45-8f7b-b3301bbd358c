package com.lecent.park.en.parklot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 停车状态
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ParkingStatusEnum {

	/**
	 * 在场
	 */
	PARK_IN(2, "在场"),

	/**
	 * 出场
	 */
	PARK_OUT(4, "出场"),

	/**
	 * 重复进场
	 */
	PARK_REPEAT(10, "重复进场");

	private final int value;
	private final String name;

	public static ParkingStatusEnum resolve(Integer status) {
		return Optional.ofNullable(status)
			.flatMap(s ->
				Stream.of(values())
					.filter(v -> v.value == s)
					.findFirst()
			).orElse(null);
	}

	public boolean isPresent() {
		return this == PARK_IN;
	}


	public boolean isExited() {
		return this == PARK_OUT || this == PARK_REPEAT;
	}

	public static boolean isPresent(Integer value) {
		return Objects.nonNull(value) && value == PARK_IN.value;
	}

	public static boolean isExited(Integer value) {
		return Objects.nonNull(value) && (value == PARK_OUT.value || value == PARK_REPEAT.value);
	}
}
