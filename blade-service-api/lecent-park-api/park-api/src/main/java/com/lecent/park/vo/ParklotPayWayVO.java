package com.lecent.park.vo;

import com.lecent.park.core.tool.databind.annottion.Dict;
import com.lecent.park.entity.ParklotPayWay;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付渠道配置表视图实体类
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PayWayVO对象", description = "支付渠道配置表")
public class ParklotPayWayVO extends ParklotPayWay {
	private static final long serialVersionUID = 1L;

	/**
	 * 商户类型名称
	 */
	@Dict(dictKey = "pay_merchant_type", bindField = "merchantType")
	@ApiModelProperty(value = "商户类型名称")
	private String merchantTypeName;
}
