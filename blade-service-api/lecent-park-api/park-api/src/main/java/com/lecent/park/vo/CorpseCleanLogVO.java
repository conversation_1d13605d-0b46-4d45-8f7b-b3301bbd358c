package com.lecent.park.vo;

import com.lecent.park.entity.CorpseCleanLog;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 *僵尸车清理记录VO
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "僵尸车清理记录VO", description = "僵尸车清理记录VO")
public class CorpseCleanLogVO extends CorpseCleanLog {

	private static final long serialVersionUID = 1L;

	/**
	 * 相差天数
	 */
	private Long diffDay;

	/**
	 * 是否是月卡车
	 */
	private String monthCard;

	/**
	 * 关联类型
	 * {@link com.lecent.park.common.enums.plate.PlatePropertyType}
	 */
	@ApiModelProperty(value = "关联类型")
	private Integer relationType;
	private String relationTypeName;
}
