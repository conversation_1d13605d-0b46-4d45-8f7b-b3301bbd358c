package com.lecent.park.en;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderAbnormalTypeEnum {

	ABNORMAL_TYPE_0(0,"没有差异"),
	ABNORMAL_TYPE_1(1,"进场覆盖"),
	ABNORMAL_TYPE_2(2,"进出场不一致"),
	ABNORMAL_TYPE_3(3,"无牌车有进场图片"),
	ABNORMAL_TYPE_4(4,"无进出场图片"),
	ABNORMAL_TYPE_5(5,"有地锁未缴费");
	@EnumValue
	final Integer value;
	final String desc;

	/**
	 * 根据name获取枚举
	 *
	 * @param value 枚举名称
	 * @return 枚举
	 */
	public static OrderAbnormalTypeEnum resolve(Integer value) {
		for (OrderAbnormalTypeEnum e :OrderAbnormalTypeEnum.values()){
			if(e.value.equals(value)){
				return e;
			}
		}
		return ABNORMAL_TYPE_0;
	}
}
