package com.lecent.park.en.reservepark;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约停车类型
 *
 * <AUTHOR>
 * @date 2021/12/29
 **/
@Getter
@AllArgsConstructor
public enum ReserveParkType {

	/**
	 * 预约未支付预约金
	 */
	NOR_PAY_SERVE_MONEY(0),

	/**
	 * 预约成功
	 */
	SUCCESS(1),

	/**
	 * 已完成
	 */
	OVER(2),

	/**
	 * 取消预约
	 */
	CANCEL(3),

	/**
	 * 已失效
	 */
	OVERDUE(4),

	;
	private Integer code;
}
