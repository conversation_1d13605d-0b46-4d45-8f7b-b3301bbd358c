package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description： 用户端缴费记录VO
 * <AUTHOR>
 * @Date: 2020/6/3 14:05
 */
@Data
public class UserCardOrderVO {

	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	@ApiModelProperty(value = "卡号")
	private String cardNo;


	@ApiModelProperty(value = "交易流水号")
	private String tradeNo;

	@ApiModelProperty(value = "续费时长")
	private String reChargeTimeLength;

	@ApiModelProperty(value = "支付金额")
	private String payAmount;

	@ApiModelProperty(value = "支付时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date payTime;

	@ApiModelProperty(value = "续费开始时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startDate;

	@ApiModelProperty(value = "到期时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endDate;

	@ApiModelProperty(value = "车位类型")
	private String parkPlaceType;

	@ApiModelProperty(value = "描述")
	private String memo;
}
