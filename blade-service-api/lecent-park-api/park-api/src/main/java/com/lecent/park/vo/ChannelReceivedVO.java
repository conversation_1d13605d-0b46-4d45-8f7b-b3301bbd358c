package com.lecent.park.vo;

import com.lecent.park.entity.ChannelReceived;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通道设备识别结果视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChannelReceivedVO对象", description = "通道设备识别结果")
public class ChannelReceivedVO extends ChannelReceived {
	private static final long serialVersionUID = 1L;

}
