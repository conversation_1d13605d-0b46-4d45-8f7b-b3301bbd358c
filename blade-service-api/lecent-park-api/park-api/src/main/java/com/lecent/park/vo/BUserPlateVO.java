package com.lecent.park.vo;

import com.lecent.park.entity.BUserPlate;
import com.lecent.park.entity.TagDict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 微信用户车牌视图实体类
 *
 * <AUTHOR>
 * @since 2020-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BUserPlateVO对象", description = "微信用户车牌")
public class BUserPlateVO extends BUserPlate {
	private static final long serialVersionUID = 1L;


	/**
	 * 车辆标签列表
	 */
	private List<String> tagNames;

	/**
	 * 标签列表
	 */
	private List<TagDict> tagDicts;


	/**
	 * 月卡结束时间
	 */
	private Date cardEndTime;
	/**
	 * 停车场名称
	 */
	private String parklotName;

	/**
	 * 进场时间
	 */
	private Date enterTime;

	/**
	 * 停车时长
	 */
	private String parkTime;


	/**
	 * 是否需要去缴费
	 */
	private int needPay;

	/**
	 * 用户名
	 */
	@ApiModelProperty("用户姓名")
	private String userName;

	@ApiModelProperty("车牌绑定月卡数量")
	private Integer cardCount;

}
