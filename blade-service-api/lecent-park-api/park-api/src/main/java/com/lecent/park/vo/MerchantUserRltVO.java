package com.lecent.park.vo;

import com.lecent.park.entity.MerchantUserRlt;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 酒店商户与blade_user用户关联表视图实体类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MerchantUserRltVO对象", description = "酒店商户与blade_user用户关联表")
public class MerchantUserRltVO extends MerchantUserRlt {
	private static final long serialVersionUID = 1L;

}
