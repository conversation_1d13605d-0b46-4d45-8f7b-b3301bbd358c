package com.lecent.park.vo;

import com.lecent.park.entity.ParkMerchantRule;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParkMerchantRuleVO对象", description = "ParkMerchantRuleVO对象")
public class ParkMerchantRuleVO extends ParkMerchantRule {
	private static final long serialVersionUID = 1L;

	private String deptNames;

	private List<Long> deptIdList;

}
