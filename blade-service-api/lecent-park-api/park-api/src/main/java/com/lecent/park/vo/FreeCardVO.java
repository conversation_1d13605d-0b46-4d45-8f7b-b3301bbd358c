package com.lecent.park.vo;

import com.lecent.park.entity.FreeCard;
import com.lecent.park.entity.FreeCardAuth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 公司角色表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FreeCardVO对象", description = "公司角色表")
public class FreeCardVO extends FreeCard {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("卡id")
	private Long cardId;
	@ApiModelProperty("车类型")
	private String typeName;
	@ApiModelProperty("绑定车场数量")
	private Integer authParkAmount;

	@ApiModelProperty("绑定车场")
	private List<FreeCardAuth> freeAuthList;

}
