package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 月卡时段计算VO
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "月卡时段计算VO", description = "月卡时段计算VO ")
public class CardDurationVO  {
	private static final long serialVersionUID = 1L;

	private Date startDate;

	private Date endDate;

	private BigDecimal amount;

}
