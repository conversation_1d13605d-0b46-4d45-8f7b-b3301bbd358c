package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户填写开发票
 *
 * <AUTHOR>
 */
@Data
public class InvoiceParam implements Serializable {



	@ApiModelProperty(value = "金额", hidden = true)
	private BigDecimal amount=BigDecimal.ZERO;

	@ApiModelProperty(value = "购货方名称", required = true)
	private String ghfMc;

	@ApiModelProperty("税号/纳税人识别码")
	private String ghfNsrsbh = "";

	@ApiModelProperty("付款方银行及账号")
	private String fkfkhyhFkfyhzh = "";

	@ApiModelProperty("付款方地址/电话")
	private String fkfdzFkfdh = "";

	@ApiModelProperty("开票流水号")
	private String fplsh;

	/**
	 * 默认是蓝票
	 */
	@ApiModelProperty(value = "开票类型（0-蓝票 1-红票）")
	private String kplx = "0";

	/**
	 * 蓝票不填，红票必填 默认空（蓝票）
	 */
	@ApiModelProperty(value = "原发票代码")
	private String yfpDm = "";

	/**
	 * 蓝票不填，红票必填 默认空（蓝票）
	 */
	@ApiModelProperty(value = "原发票号码")
	private String yfpHm = "";

	@ApiModelProperty(value = "备注")
	private String remark;

}
