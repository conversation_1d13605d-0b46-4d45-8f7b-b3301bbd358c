package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 首次关注公众号返回优惠劵vo
 *
 * <AUTHOR>
 * @since 2021-07-15
 */
@Data
@ApiModel(value = "首次关注公众号返回优惠劵vo", description = "首次关注公众号返回优惠劵vo")
public class FirstFocusCouponVO {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty("优惠劵名称")
	private String couponName;

	@ApiModelProperty("优惠劵类型")
	private Integer couponType;

	@ApiModelProperty(value = "满多少元")
	private BigDecimal fullAmount;

	@ApiModelProperty(value = "减多少元")
	private BigDecimal reduceAmount;

	@ApiModelProperty(value = "折扣数")
	private Double discountAmount;

	@ApiModelProperty(value = "优惠时长")
	private Integer reduceHour;

	@ApiModelProperty(value = "是否登录")
	private String remark;

	private String startDate;

	private String endDate;

	@ApiModelProperty(value = "发送数量")
	private Integer sendCount;

}
