package com.lecent.park.vo;

import com.lecent.park.entity.CardLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 月卡变更日志表视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CardLogVO对象", description = "月卡变更日志表")
public class CardLogVO extends CardLog {
	private static final long serialVersionUID = 1L;

}
