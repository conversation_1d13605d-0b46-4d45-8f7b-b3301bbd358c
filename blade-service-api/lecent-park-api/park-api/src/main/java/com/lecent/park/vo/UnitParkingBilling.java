package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class UnitParkingBilling {
	/**
	 * 停车记录id
	 */
	@ApiModelProperty(value = "停车记录id")
	private Long parkingId;

	/**
	 * 车辆状态（0：已入场，1：已离场）
	 */
	@ApiModelProperty(value = "车辆状态（2：已入场，4：已离场）")
	private Integer carStatus;
	/**
	 * 入场时间
	 */
	@ApiModelProperty(value = "入场时间")
	private Date enterTime;
	/**
	 * 出场时间
	 */
	@ApiModelProperty(value = "出场时间")
	private Date exitTime;
	/**
	 * 本次停车时长
	 */
	@ApiModelProperty(value = "本次停车时长")
	private String durationTime;
	/**
	 * 本次未支付金额
	 */
	@ApiModelProperty(value = "本次未支付金额")
	private BigDecimal unpaidAmount = BigDecimal.ZERO;

	/**
	 * 欠费时长
	 */
	@ApiModelProperty(value = "欠费时长")
	private String delayTime;
	/**
	 * 滞纳金
	 */
	@ApiModelProperty(value = "滞纳金")
	private BigDecimal overdueFine = BigDecimal.ZERO;
	/**
	 * 车位编号
	 */
	@ApiModelProperty(value = "车位编号")
	private String placeCode;
	/**
	 * 车位位置
	 */
	@ApiModelProperty(value = "车位位置")
	private String placeInfo;
	/**
	 * 车牌
	 */
	@ApiModelProperty(value = "车牌")
	private String plate;
}
