package com.lecent.park.vo;

import com.lecent.park.entity.InvoiceTitle;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票抬头表视图实体类
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InvoiceTitleVO对象", description = "发票抬头表")
public class InvoiceTitleVO extends InvoiceTitle {
	private static final long serialVersionUID = 1L;

}
