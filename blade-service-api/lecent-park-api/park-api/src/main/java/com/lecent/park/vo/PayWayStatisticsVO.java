package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 缴费方式返回类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@ApiModel(value = "缴费方式返回类", description = "缴费方式返回类")
@Builder
public class PayWayStatisticsVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "统计类型")
	private String statisticsType;

	@ApiModelProperty(value = "总计")
	private String total;

	@ApiModelProperty(value = "占比")
	private String percentRate;


}
