package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lecent.park.en.SchedulesRole;
import com.lecent.park.entity.SchedulingLogs;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 排班日志表视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@JsonIgnoreProperties({
		"tenantId", "createUser", "createDept", "createTime", "updateUser", "updateTime", "status", "isDeleted"
})
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SchedulingLogsVO对象", description = "排班日志表")
public class SchedulingLogsVO extends SchedulingLogs {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("用户名称")
	private String userName;

	@ApiModelProperty("车场名称")
	private String parklotName;

	@ApiModelProperty("角色名称")
	private String roleName;

	@ApiModelProperty("用户id")
	private Long userId;

	@ApiModelProperty("车场id")
	private String parklotIds;

	@ApiModelProperty("车场id")
	private Long parklotId;

	@ApiModelProperty("分单时间")
	private Date dispatchTime;

	@ApiModelProperty("开始日期")
	private LocalDate startDate;

	@ApiModelProperty("结束日期")
	private LocalDate endDate;

	@ApiModelProperty(value = "多角色")
	private List<SchedulesRole> roles;
}
