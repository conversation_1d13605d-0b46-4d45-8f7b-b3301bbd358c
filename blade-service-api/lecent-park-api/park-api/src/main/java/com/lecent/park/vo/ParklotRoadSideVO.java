package com.lecent.park.vo;

import com.lecent.park.entity.ParklotSmsSetting;
import com.lecent.park.entity.ParklotTimeDuration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ParklotRoadSideVO {

	private String parklotName;
	private String fullName;
	private String address;
	private String totalArea;
	private Integer tempNum;
	private String chargeRuleName;
	private Integer isAutoUpload;
	private List<ParklotTimeDuration> workDurations;
	private List<ParklotTimeDuration> festivalDurations;
	List<ParklotSmsSetting> smsSettings;

	/**
	 * 非停车时段超时限制
	 */
	@ApiModelProperty(value = "非停车时段超时限制")
	private Integer disableOverTime;
	/**
	 * 超过停车时段限制
	 */
	@ApiModelProperty(value = "超过停车时段限制")
	private Integer overAllowTime;
	/**
	 * 移动短信模板
	 */
	@ApiModelProperty(value = "移动短信模板")
	private String mobileSmsTemplate;
	@ApiModelProperty(value = "催缴模版")
	private String smsPaymentTemplate;
}
