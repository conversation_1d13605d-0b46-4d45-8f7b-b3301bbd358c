package com.lecent.park.vo;

import com.lecent.park.entity.CardPlate;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 套餐车牌表视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CardPlateVO对象", description = "套餐车牌表")
public class CardPlateVO extends CardPlate {
	private static final long serialVersionUID = 1L;

}
