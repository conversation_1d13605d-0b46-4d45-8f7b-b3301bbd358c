package com.lecent.park.vo;

import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.ParklotRegion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 车场区域信息表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParklotRegionVO对象", description = "车场区域信息表")
public class ParklotRegionVO extends ParklotRegion {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("车场名称")
	private String parklotName;

	@ApiModelProperty("车场类型")
	private Integer parklotType;

	@ApiModelProperty("楼层代号")
	private String floorName;

	private Long userId;

	private List<ParkingPlace> parkingPlaceList;

}
