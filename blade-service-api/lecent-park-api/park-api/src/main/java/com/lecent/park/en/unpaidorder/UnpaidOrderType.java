package com.lecent.park.en.unpaidorder;




/**
 * 付款状态
 * <AUTHOR>
 */

public enum UnpaidOrderType {
	TYPE01(1, "循环车-后车缴费，特殊放行"),
	TYPE02(2, "循环车-前车缴费，特殊放行"),
	TYPE03(3, "循环车-后车缴费，等待缴费"),
	TYPE04(4, "循环车-后车缴费+临停，特殊放行"),
	TYPE05(5, "循环车-前车缴费+临停，特殊放行"),
	TYPE06(6, "循环车-前车缴费，等待缴费"),
	TYPE11(11, "无牌车，特殊放行"),
	TYPE12(12, "无进场，特殊放行"),
	TYPE20(20, "月卡过期，特殊放行"),
	TYPE21(21, "月卡过期，自动放行"),
	TYPE23(23, "月卡被禁，特殊放行"),
	TYPE24(24, "月卡已注销，特殊放行"),
	TYPE25(25, "月卡车位异常，特殊放行"),
	TYPE26(26, "月卡刚开未续费，特殊放行"),
	TYPE99(99, "其他有产生费用，异常放行"),
	TYPE100(100, "路边停车-未缴"),
	TYPE101(101, "路边停车-进出场车辆信息不一致"),

	;

	private final int value;
	private final String name;

	UnpaidOrderType(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}


	public String getName() {
		return name;
	}

	public static String getNameByValue(Integer value) {
		for (UnpaidOrderType unpaidOrderType : values()) {
			if (value != null && unpaidOrderType.value == value) {
				return unpaidOrderType.name;
			}
		}
		return "";
	}
}
