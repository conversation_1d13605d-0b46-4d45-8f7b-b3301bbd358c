package com.lecent.park.vo;

import com.lecent.park.entity.SupplementCard;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 附属卡(亲情卡)表视图实体类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplementCardVO对象", description = "附属卡(亲情卡)表")
public class SupplementCardVO extends SupplementCard {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "车场名")
	private String parklotName;

	@ApiModelProperty(value = "业主姓名")
	private String ownerName;

	@ApiModelProperty(value = "辖区全路径地址")
	private  String regionName;

	@ApiModelProperty(value = "业主电话")
	private String ownerPhone;

	@ApiModelProperty(value = "计费规则名")
	private String chargeRuleName;

	private List<Long> parkLotIds;
}
