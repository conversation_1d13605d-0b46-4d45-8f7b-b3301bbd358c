package com.lecent.park.vo;

import com.alibaba.fastjson.JSONArray;
import com.lecent.park.entity.Inspection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检记录表视图实体类
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectionVO对象", description = "巡检记录表")
public class InspectionVO extends Inspection {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String deviceName;
	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	/**
	 * 巡检人姓名
	 */
	@ApiModelProperty(value = "巡检人姓名")
	private String realName;

	/**
	 * 巡检人ids
	 */
	@ApiModelProperty(value = "巡检人ids")
	private List<Long> userIds;

	/**
	 * 设备信息列表
	 */
	@ApiModelProperty(value = "设备信息列表")
	private String deviceList;

	/**
	 * 设备信息列表
	 */
	@ApiModelProperty(value = "设备信息列表")
	private JSONArray deviceArray;


	/**
	 * 巡检状态ids
	 */
	@ApiModelProperty(value = "巡检状态ids")
	private List<Integer> inspectionStatuss;

	/**
	 * 通道名称
	 */
	@ApiModelProperty(value = "通道名称")
	private String channelName;

	/**
	 * 巡检开始时间
	 */
	@ApiModelProperty(value = "巡检开始时间")
	private LocalDateTime inspectionStartDate;

	/**
	 * 巡检结束时间
	 */
	@ApiModelProperty(value = "巡检结束时间")
	private LocalDateTime inspectionEndDate;

}
