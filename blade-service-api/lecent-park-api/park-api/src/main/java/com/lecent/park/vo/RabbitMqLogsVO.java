package com.lecent.park.vo;

import com.lecent.park.entity.RabbitMqLogs;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 接收rabbitmq数据日志视图实体类
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RabbitMqLogsVO对象", description = "接收rabbitmq数据日志")
public class RabbitMqLogsVO extends RabbitMqLogs {
	private static final long serialVersionUID = 1L;

}
