package com.lecent.park.en;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 岗亭枚举类型
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum PassTemplate {

	//进出场(失败)
	NO_PLATE("无牌车", "无牌车,请扫码通行${extendVoice}", "无牌车<br> 请扫码通行"),

	BLACK_LIST("该车辆已被列入黑名单，请联系车场管理员处理！", "您的车辆已进入黑名单，请联系岗亭人员${extendVoice}", "${plate}<br> 黑名单<br> 待岗亭人员确认"),

	YELLOW_LIST("该车辆已被列入黄名单，请联系车场管理员处理！", "您的车辆已进入黄名单，请联系岗亭人员${extendVoice}", "${plate}<br> 黄名单<br> 待岗亭人员确认"),


	//进场(失败)
	TEMP_TIME_LIMIT("临停时段限制", "该时段禁止外部车辆进入${extendVoice}", "${plate}<br> 该时段不允许进入<br> 禁止通行"),

	WAITING_CONFIRM("等待确认", "待岗亭人员确认${extendVoice}", "${plate}<br> 等待确认<br> 待岗亭人员确认"),

	PLACE_FULL("车位已满", "车位已满${extendVoice}", "${plate}<br> 车位已满<br> 禁止通行"),

	NO_PASS("专车通道(禁止通行)", "专车通道，禁止通行${extendVoice}", "${plate}<br>专车通道<br>禁止通行<br>${chargeTypeDes}"),


	//出场(失败)
	NO_ENTER_RECORD("无进场记录", "无进场记录${extendVoice}", "${plate}<br> 无进场记录<br> 待岗亭人员确认"),

	LEAVE_STOP_PAYING("等待支付", "请缴费${receiveAmount}元${extendVoice}", "${plate}<br> 停车费${receiveAmount}元<br> 停车时长:${duration}<br> ${chargeTypeDes}"),

	//进场(成功)
	WELCOME_PASS("欢迎光临", "欢迎光临${extendVoice}", "${plate}<br> ${days}<br> ${chargeTypeDes}<br> 欢迎光临"),


	//出场(成功)
	BYE_PASS("祝您一路平安", "祝您一路平安${extendVoice}", "${plate}<br> ${days}<br> ${chargeTypeDes}<br>  祝您一路平安"),

	PAY_SUCCESS_PASS("已缴费", "祝您一路平安${extendVoice}", "${plate}<br> 已缴费<br> ${chargeTypeDes}<br> 祝您一路平安"),

	NO_CHARGE_RULE("未设置收费规则", "祝您一路平安,车场未开通计费${extendVoice}", "${plate}<br> ${chargeTypeDes}<br> 车场未开通计费<br> 祝您一路平安"),

	;

	private final String msg;
	private final String voice;
	private final String show;

	/**
	 * 根据name获取枚举
	 *
	 * @param name 枚举名称
	 * @return 枚举
	 */
	public static PassTemplate resolve(String name) {
		try {
			return PassTemplate.valueOf(name);
		} catch (Exception ignored) {
			log.error("unknown name: {}", name);
		}
		return null;
	}

	/**
	 * 根据name获取msg
	 *
	 * @param name 枚举名称
	 * @return 枚举
	 */
	public static String getMsg(String name) {
		try {
			return PassTemplate.valueOf(name).msg;
		} catch (Exception ignored) {
		}
		return "";
	}


}
