package com.lecent.park.vo;

import com.lecent.park.entity.ChannelTree;
import com.lecent.park.entity.UserChannel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 公司员工车场资源授权表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserChannelVO对象", description = "公司员工车场资源授权表")
public class UserChannelVO extends UserChannel {
	private static final long serialVersionUID = 1L;

	/**
	 * 通道列表数据
	 */
	private List<ChannelTree> channelTrees;

	/**
	 * 用户已设置的列表
	 */
	private List<String> settingChannelIds;


}
