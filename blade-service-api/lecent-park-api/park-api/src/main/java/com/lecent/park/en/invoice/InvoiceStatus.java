package com.lecent.park.en.invoice;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum InvoiceStatus {
	// 开票状态 0-开票中 1-开票成功 2-开票失败
	KP_PROCEED(0, "开票中"),
	KP_SUCCESS(1, "开票成功"),
	KP_FAIL(2, "开票失败"),
	KP_DISABLED(3, "发票失效");
	public Integer code;

	public String value;

	InvoiceStatus(Integer code, String value) {
		this.code = code;
		this.value = value;
	}

	public static String getValueByCode(Integer code){
		for (InvoiceStatus stateEnum : InvoiceStatus.values()) {
			if (stateEnum.code.equals(code)){
				return stateEnum.value;
			}
		}
		return "";
	}

	public static List<Map> enumToDict(){
		List<Map> listDict = new ArrayList<>();
		for (InvoiceStatus stateEnum : InvoiceStatus.values()) {
			HashMap<String, Object> dict = new HashMap<>();
			dict.put("code",stateEnum.code);
			dict.put("value",stateEnum.value);
			listDict.add(dict);
		}
		return listDict;
	}

}
