package com.lecent.park.en;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合并订单类型
 *
 * <AUTHOR>
 * @date 2023/07/18
 */
@Getter
@AllArgsConstructor
public enum MergeOrderType {
    /**
     * 临停订单
     */
    MINI_TEMP_ORDER(1, "小程序临停订单"),
    /**
     * 追缴订单
     */
    RECOVERED_ORDER(2, "追缴订单"),
    /**
     * 退款订单
     */
    REFUND_ORDER(3, "退款订单"),
    ;

    @EnumValue
    final Integer value;

    final String desc;
}
