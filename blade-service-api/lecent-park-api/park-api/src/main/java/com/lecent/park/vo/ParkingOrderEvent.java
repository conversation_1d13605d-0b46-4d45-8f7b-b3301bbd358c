package com.lecent.park.vo;

import com.lecent.park.entity.ParkingOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParkingOrderEvent extends ParkingOrder {

	@ApiModelProperty("工单id")
	private Long workOrderId;

	@ApiModelProperty("处理人id")
	private Long handleUserId;

	@ApiModelProperty("处理人")
	private String handleUserName;

	@ApiModelProperty("车牌图片")
	private String plateImageUrl;

	@ApiModelProperty("场景图片")
	private String sceneImageUrl;
}
