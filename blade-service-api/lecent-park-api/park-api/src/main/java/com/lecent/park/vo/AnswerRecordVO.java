package com.lecent.park.vo;

import com.lecent.park.entity.AnswerRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 客服接听记录 视图实体类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AnswerRecordVO对象", description = "客服接听记录 ")
public class AnswerRecordVO extends AnswerRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 会话时长
	 */
	@ApiModelProperty(value = "会话时长")
	private String duration;
}
