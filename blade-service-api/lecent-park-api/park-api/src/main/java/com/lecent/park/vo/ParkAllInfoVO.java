package com.lecent.park.vo;

import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotFloor;
import com.lecent.park.entity.ParklotRegion;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springblade.system.entity.Tenant;

/**
 * 车位信息表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Setter
@Getter
@ApiModel(value = "车场所有信息", description = "车场所有信息（车场，楼层，区域，车位）")
@Builder
public class ParkAllInfoVO {
	private static final long serialVersionUID = 1L;

	private Tenant tenant;

	private Parklot parklot;

	private ParklotFloor parklotFloor;

	private ParklotRegion parklotRegion;

	private ParkingPlace parkingPlace;




}
