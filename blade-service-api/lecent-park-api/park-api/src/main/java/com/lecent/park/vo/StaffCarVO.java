package com.lecent.park.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.lecent.park.entity.StaffCar;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 员工车辆视图实体类
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StaffCarVO对象", description = "员工车辆")
public class StaffCarVO extends StaffCar {
	private static final long serialVersionUID = 1L;

	/**
	 * 关联车场数
	 */
	@ExcelIgnore
	private Integer relatedCount;


	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parklotName;


	/**
	 * 规则名称
	 */
	@ApiModelProperty(value = "规则名称")
	private String ruleName;


	@ApiModelProperty(value = "凭证列表")
	private List<String> certificateList;


	@ApiModelProperty(value = "关联车场")
	private List<StaffCarParklotVO> staffCarParklotList;

}
