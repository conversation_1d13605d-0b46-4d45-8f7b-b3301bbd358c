package com.lecent.park.vo;

import com.lecent.device.vo.DeviceVO;
import com.lecent.park.entity.ParkingPlace;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 车位信息表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParkingPlaceVO对象", description = "车位信息表")
public class ParkingPlaceVO extends ParkingPlace {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "区域")
	private String regionName;
	/**
	 * 楼层
	 */
	@ApiModelProperty(value = "楼层")
	private String floorName;
	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	@ApiModelProperty(value = "车场编号")
	private String parklotNo;
	/**
	 * 设备列表
	 */
	@ApiModelProperty(value = "设备列表")
	private List<DeviceVO> deviceList;
	@ApiModelProperty(value = "车主电话，存在多条根据时间取第一条")
	private String carOwnerPhone;
	@ApiModelProperty(value = "无感类型，存在多条根据时间取第一条")
	private String unconsciousType;
	@ApiModelProperty(value = "车辆信息id")
	private Long vehicleId;
	@ApiModelProperty(value = "车牌颜色")
	private String plateColor;
	@ApiModelProperty(value = "车辆类型")
	private Integer vehicleType;
	@ApiModelProperty(value = "占用时长")
	private Long parkingTimestamp;
	@ApiModelProperty(value = "空闲时长")
	private Long idleTimestamp;
	@ApiModelProperty(value = "车位整体状态")
	private Integer overallStatus;
	@ApiModelProperty(value = "小票打印次数")
	private Integer receiptPrintLogNum;
	@ApiModelProperty(value = "入场时间")
	private Date enterTime;
	@ApiModelProperty(value = "入场方式")
	private Integer enterWay;
	@ApiModelProperty(value = "车场ids")
	private String parklotIdStr;

	private List<Long> parklotIds;

	@ApiModelProperty(value = "巡检任务")
	private InspectionTaskDTO inspectionTask;

	@Data
	@Builder
	public static class InspectionTaskDTO {

		@ApiModelProperty(value = "任务ID")
		private Long id;

		@ApiModelProperty(value = "子任务ID")
		private Long subTaskId;

		@ApiModelProperty(value = "子任务状态")
		private Integer subTaskStatus;
	}
}
