package com.lecent.park.vo;

import com.lecent.park.entity.ViolationRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 停车违章记录表视图实体类
 *
 * <AUTHOR>
 * @since 2024-04-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ViolationRecordVO对象", description = "停车违章记录表")
public class ViolationRecordVO extends ViolationRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "车场id")
    private String parkLotIdStr;

    @ApiModelProperty(value = "车场名称")
    private String parklotName;

    @ApiModelProperty(value = "停车时长")
    private Long minuteTime;
}
