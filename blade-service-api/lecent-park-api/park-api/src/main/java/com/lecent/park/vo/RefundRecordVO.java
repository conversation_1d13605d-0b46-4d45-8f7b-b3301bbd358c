package com.lecent.park.vo;

import com.lecent.park.entity.RefundRecord;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 退款记录视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RefundRecordVO对象", description = "退款记录")
public class RefundRecordVO extends RefundRecord {
	private static final long serialVersionUID = 1L;

}
