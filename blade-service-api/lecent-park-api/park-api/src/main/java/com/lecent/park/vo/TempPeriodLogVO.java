package com.lecent.park.vo;

import com.lecent.park.entity.TempPeriodLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 修改车牌临停记录表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TempPeriodLogVO对象", description = "修改车牌临停记录表")
public class TempPeriodLogVO extends TempPeriodLog {
	private static final long serialVersionUID = 1L;

}
