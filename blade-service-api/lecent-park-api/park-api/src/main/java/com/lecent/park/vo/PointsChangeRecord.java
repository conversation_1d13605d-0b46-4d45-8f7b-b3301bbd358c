package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 积分变化记录
 *
 * <AUTHOR>
 * @date 2024/08/15
 */
@Data
@ApiModel("积分变化记录")
public class PointsChangeRecord implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("用户id")
	private Long userId;

	@ApiModelProperty("用户名称")
	private String userName;

	@ApiModelProperty("车牌")
	private String plate;

	@ApiModelProperty("类型：停车、月卡缴费")
	private String changeType;

	@ApiModelProperty("积分")
	private Integer points;

	@ApiModelProperty("积分变化时间")
	private Date changeTime;

	@ApiModelProperty("租户id")
	private String tenantId;
}
