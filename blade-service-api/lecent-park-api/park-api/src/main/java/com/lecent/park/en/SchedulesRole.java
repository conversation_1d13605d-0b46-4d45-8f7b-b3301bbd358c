package com.lecent.park.en;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SchedulesRole {
    /**
     * 收费员
     */
    TOLL("toll", "收费员"),
    /**
     * 运维人员
     */
    OPS("ops", "运维人员"),
    ;

    @JsonValue
    @EnumValue
    private final String code;
    private final String name;

	public static SchedulesRole resolve(String code) {
		for (SchedulesRole value : SchedulesRole.values()) {
			if (value.code.equals(code)) {
				return value;
			}
		}
		return null;
	}
}

