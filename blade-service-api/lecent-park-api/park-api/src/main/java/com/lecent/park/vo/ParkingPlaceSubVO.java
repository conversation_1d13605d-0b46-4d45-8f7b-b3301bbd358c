package com.lecent.park.vo;

import com.lecent.park.entity.ParkingPlaceSub;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 车位预约视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "parkingPlaceSubVO对象", description = "车位预约")
public class ParkingPlaceSubVO extends ParkingPlaceSub {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	@ApiModelProperty(value = "车位编号")
	private String placeCode;

	@ApiModelProperty(value = "用户名称")
	private String userName;

	@ApiModelProperty(value = "预约状态")
	private Boolean subStatus;

	@ApiModelProperty(value = "经度")
	private BigDecimal lng;

	@ApiModelProperty(value = "纬度")
	private BigDecimal lat;
}
