package com.lecent.park.en;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态,请不要修改
 *
 * <AUTHOR>
 * @date 2022/1/5
 **/
@Getter
@AllArgsConstructor
public enum EnableStatus {

	/**
	 * 无效
	 */
	INVALID(0),

	/**
	 * 有效
	 */
	ACTIVE(1),

	/**
	 * 删除
	 */
	DELETE(-1);
	/**
	 * 状态值
	 */
	private Integer code;

	/**
	 * 判断是否有效
	 *
	 * @return true有效、false无效
	 */
	public static boolean isActive(Integer status) {
		return EnableStatus.ACTIVE.getCode().equals(status);
	}

	/**
	 * 判断是否无效
	 *
	 * @return true无效、false有效
	 */
	public static boolean isInvalid(Integer status) {
		if (status == null) {
			return true;
		}
		return !isActive(status);
	}
}
