package com.lecent.park.vo;

import com.lecent.park.entity.Parklot;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ParklotInfo extends Parklot{
    private final static long serialVersionUID = 1L;

    @ApiModelProperty(value = "车位列表")
    private List<ParkingPlaceVO> parkingPlaceList;
}
