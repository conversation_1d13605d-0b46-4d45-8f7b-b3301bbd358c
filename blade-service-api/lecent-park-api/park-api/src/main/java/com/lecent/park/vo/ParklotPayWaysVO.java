package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 支付渠道配置表视图实体类
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Data
@ApiModel(value = "PayWayVO对象", description = "支付渠道配置表")
public class ParklotPayWaysVO {

	private List<ParklotPayWayVO> payWayVOList;
	/**
	 * 商户id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "商户id")
	private Long businessId;
	@ApiModelProperty(value = "商户名称")
	private String businessName;

	private Long parklotId;
}
