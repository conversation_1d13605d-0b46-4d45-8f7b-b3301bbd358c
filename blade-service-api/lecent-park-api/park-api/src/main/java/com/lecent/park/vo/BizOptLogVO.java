package com.lecent.park.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.lecent.park.entity.BizOptLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务操作日志表视图实体类
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizOptLogVO对象", description = "业务操作日志表")
public class BizOptLogVO extends BizOptLog {
	private static final long serialVersionUID = 1L;

	@ExcelProperty(value = "操作终端", index = 1)
	private String OptTerminalStr;

	@ExcelProperty(value = "操作类型", index = 2)
	private String OptTypeStr;

}
