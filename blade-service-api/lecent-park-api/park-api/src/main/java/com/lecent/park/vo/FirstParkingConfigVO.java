package com.lecent.park.vo;

import com.lecent.park.entity.FirstParkingConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 首停配置视图对象
 *
 * <AUTHOR>
 * @since 2024-10-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FirstParkingConfigVO对象", description = "首停配置视图对象")
public class FirstParkingConfigVO extends FirstParkingConfig {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 车场名称列表
     */
    @ApiModelProperty(value = "车场名称列表")
    private List<String> parklotNames;
}
