package com.lecent.park.en.invoice;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/26 9:31
 * @description 发票抬头类型
 */
@Getter
@AllArgsConstructor
public enum InvoiceConfigType {

	PRIVATE(1, "个人/非企业"),
	ENTERPRISE(2, "单位企业"),
	;

	private final Integer key;
	private final String value;

	public static String getValueByKey(Integer key){
		for (InvoiceConfigType invoiceConfigType : InvoiceConfigType.values()) {
			if (invoiceConfigType.key.equals(key)){
				return invoiceConfigType.value;
			}
		}
		return "";
	}
}
