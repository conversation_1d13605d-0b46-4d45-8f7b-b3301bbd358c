package com.lecent.park.vo;

import com.lecent.park.entity.ParkingPlaceStatusDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 停车位状态
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "停车位状态", description = "停车位状态")
public class ParkingPlaceStatusVO extends ParkingPlaceStatusDO {

	/**
	 * 空闲时间（分钟）
	 */
	private Integer freeMinute;
}
