package com.lecent.park.en.merchant;


/**
 * 授权条件限制枚举
 */

public enum AuthConditionLimitEnum {

	// 1-授权车辆数上限；2-车辆入场时长；3-优惠时长超过商家剩余时长；4-授权车辆数超过商家可用车位数

	AUTH_CAR_NUM_ENABLED(1, "授权车辆数上限"),
	ENTER_TIME_ENABLED(2, "车辆入场时长"),
	VALID_PERIOD_ENABLED(3, "优惠时长超过商家剩余时长"),
	VALID_PLACE_ENABLED(4, "授权车辆数超过商家可用车位数"),
	AUTH_NUM_TOP(5, "授权车辆总数(有效授权)"),
	AUTH_TOTAL_NUMBER(6, "授权总次数");

	private int value;
	private String name;

	AuthConditionLimitEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}


	public String getName() {
		return name;
	}

}
