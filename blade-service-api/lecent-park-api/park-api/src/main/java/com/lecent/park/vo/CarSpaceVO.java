package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "车位",description = "车位信息展示vo")
public class CarSpaceVO {

	@ApiModelProperty("独立产权（固定车位）")
	private Integer independentOwnershipAmount;

	@ApiModelProperty("月租车位")
	private Integer lettingLotAmount;

	@ApiModelProperty("临时车位数")
	private Integer tempSpaceAmount;

	@ApiModelProperty("剩余车位数")
	private Integer surplusSpaceAmount;

	@ApiModelProperty("车场数量")
	private Integer  parklotNum;
	@ApiModelProperty("路外车场数量")
	private Integer  parklotOutNum;
	@ApiModelProperty("路内车场数量")
	private Integer  parklotInNum;
	@ApiModelProperty("总车位数")
	private Integer totalPlaceNum;
	@ApiModelProperty("路内车位数")
	private Integer totalPlaceOutNum;
	@ApiModelProperty("路外车位数")
	private Integer totalPlaceInNum;




}
