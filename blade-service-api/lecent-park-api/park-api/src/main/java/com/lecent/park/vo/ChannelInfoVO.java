package com.lecent.park.vo;

import com.lecent.device.entity.DeviceManage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/5 10:09
 */
@Data
public class ChannelInfoVO {

	private Long channelId;
	private Integer channelNo;
	private Long parklotId;
	private String parklotNo;
	private String channelName;
	private Integer type;
	private Boolean hasTodo;
	private ChannelTodoVO todo;
	private Integer deviceStatus = 0;
	/**
	 * 通道图片
	 */
	@ApiModelProperty(value = "通道图片")
	private String channelPicture;

	/**
	 * 摄像头
	 */
	private DeviceManage camera;
}
