package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/28 20:41
 */
@Getter
@Setter
@Builder
public class RemainAmountVO {

	@ApiModelProperty("总车位剩余数")
	private Integer totalRemain;

	@ApiModelProperty("独立产权车位剩余数")
	private Integer independentRemain;

	@ApiModelProperty("子母车位剩余数")
	private Integer letterRemain;

	@ApiModelProperty("临停车位剩余数")
	private Integer tempRemain;
}
