package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 追缴支付dto
 *
 * <AUTHOR>
 * @date 2023/07/17
 */
@Data
public class RecoveredPayDTO {

    @ApiModelProperty("车牌")
    private String plate;

    @ApiModelProperty("商户号")
    private String merchantId;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("是否优惠")
    private Boolean isDiscount;

    @ApiModelProperty("是否支付")
    private Boolean isPay;

    @ApiModelProperty("授权码")
    private String authCode;

    @ApiModelProperty("关闭订单号")
    private String closeTradeNo;
}
