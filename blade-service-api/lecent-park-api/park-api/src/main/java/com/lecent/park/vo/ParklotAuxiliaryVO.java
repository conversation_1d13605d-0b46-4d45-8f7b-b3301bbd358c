package com.lecent.park.vo;

import com.lecent.park.entity.ParklotAuxiliary;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车场信息扩展表视图实体类
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParklotAuxiliaryVO对象", description = "车场信息扩展表")
public class ParklotAuxiliaryVO extends ParklotAuxiliary {
	private static final long serialVersionUID = 1L;

	private String parklotName;

	@ApiModelProperty(value = "车场id")
	private String parklotIds;
}
