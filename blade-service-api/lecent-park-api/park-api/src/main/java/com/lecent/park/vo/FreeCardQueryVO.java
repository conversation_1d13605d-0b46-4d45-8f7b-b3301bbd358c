package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.mp.support.Query;


/**
 * 公司角色表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@ApiModel("授权延长列表查询参数")
public class FreeCardQueryVO extends Query {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("卡id集合，多个逗号隔开")
	private String ids;
}
