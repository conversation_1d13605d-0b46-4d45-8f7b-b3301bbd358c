package com.lecent.park.en.invoice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * @author: cy
 * @date: 2021年10月26日 9:50
 */
public enum InvoiceType {
	// 空闲中
	TEMP_ORDER(1,"临停"),
	CARD_ORDER(2,"月卡")
		;
	public Integer code ;

	public String value;

	InvoiceType(Integer code, String value) {
		this.code = code;
		this.value = value;
	}

	public static String getValueByCode(Integer code){
		for (InvoiceType stateEnum : InvoiceType.values()) {
			if (stateEnum.code.equals(code)){
				return stateEnum.value;
			}
		}
		return "";
	}

	public static List<Map> enumToDict(){
		List<Map> listDict = new ArrayList<>();
		for (InvoiceType stateEnum : InvoiceType.values()) {
			HashMap<String, Object> dict = new HashMap<>();
			dict.put("code",stateEnum.code);
			dict.put("value",stateEnum.value);
			listDict.add(dict);
		}
		return listDict;
	}
}
