/**
 * Copyright 2023-2033, likavn (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lecent.park.vo.place;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车位迭代器
 *
 * <AUTHOR>
 * @date 2025/5/16
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlaceIteratorVO {
	/**
	 * 车场ID
	 */
	private Long currParklotId;
	/**
	 * 车场名称
	 */
	private String currParklotName;
	/**
	 * 当前车位
	 */
	private PlaceIteratorItemVO currPlace;
	/**
	 * 上一个车位
	 */
	private PlaceIteratorItemVO lastPlace;
	/**
	 * 下一个车位
	 */
	private PlaceIteratorItemVO nextPlace;
}
