package com.lecent.park.vo;

import com.lecent.park.entity.Channel;
import com.lecent.park.entity.Parklot;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 车场信息表视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParklotVO对象", description = "车场信息表")
public class ParklotVO extends Parklot {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "租户id")
	private List<String> tenantIds;

	@ApiModelProperty(value = "月卡规则名称")
	private String cardRuleName;
	@ApiModelProperty(value = "楼层列表")
	private List<FloorVO> floorList;

	@ApiModelProperty(value = "月卡类型名称")
	private String cardTypesName;
	@ApiModelProperty(value = "临停收费规则名称")
	private String chargeRuleName;

	@ApiModelProperty(value = "父车场信息")
	private Parklot parentInfo;

	@ApiModelProperty(value = "父车场名称")
	private String parentName;

	@ApiModelProperty(value = "收费规则描述")
	private String chargeRuleDesc;

	/**
	 * 剩余车位数
	 */
	private int parkingNum;
	/**
	 *相距距离
	 */
	private double apart;

	@ApiModelProperty(value = "车场通道信息")
	private List<Channel> channels;

	@ApiModelProperty(value = "标签")
	private String labels;

	@ApiModelProperty(value = "设备数")
	private Integer deviceNum;
	@ApiModelProperty(value = "小票打印配置信息 id")
	private Long receiptPrintConfigId;
	@ApiModelProperty(value = "配置信息名称")
	private String receiptPrintConfigName;

}
