package com.lecent.park.vo;

import com.lecent.park.entity.OpenGateLogs;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开闸日志视图实体类
 *
 * <AUTHOR>
 * @since 2021-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OpenGateLogsVO对象", description = "开闸日志")
public class OpenGateLogsVO extends OpenGateLogs {
	private static final long serialVersionUID = 1L;

}
