package com.lecent.park.vo;

import com.lecent.park.entity.CouponSendHistory;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 立减金发送记录视图实体类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CouponSendHistoryVO对象", description = "立减金发送记录")
public class CouponSendHistoryVO extends CouponSendHistory {
	private static final long serialVersionUID = 1L;

	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 优惠劵名称
	 */
	private String couponName;

}
