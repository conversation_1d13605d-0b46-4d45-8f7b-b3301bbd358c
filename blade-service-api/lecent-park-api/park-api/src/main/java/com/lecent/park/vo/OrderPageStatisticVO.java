package com.lecent.park.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description： 临停订单统计分页
 * <AUTHOR>
 * @Date: 2020/6/5 14:04
 */
@Data
@ApiModel("临停订单返回VO")
@ContentRowHeight(20)
@HeadRowHeight(30)
public class OrderPageStatisticVO implements Serializable {

	@ExcelIgnore
	@ApiModelProperty("id")
	private String id;

	@ApiModelProperty("订单号")
	@ExcelProperty(value = "订单号")
	@ColumnWidth(value = 30)
	private String tradeNo;

	@ApiModelProperty("车牌号")
	@ExcelProperty(value = "车牌号")
	@ColumnWidth(value = 18)
	private String plate;

	@ApiModelProperty("应收")
	@ExcelProperty(value = "应收")
	private String totalAmount;

	@ApiModelProperty("优惠")
	@ExcelProperty(value = "优惠")
	private String discountAmount;

	@ApiModelProperty("优惠信息")
	private List<String> discountInfo;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(优惠券)")
	private BigDecimal couponDiscountAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(商户)")
	private BigDecimal merchantAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(充电)")
	private BigDecimal chargingDiscountAmount;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠(未交|稽核)")
	private BigDecimal unpaidDiscountAmount;

	@ApiModelProperty("优惠(新能源车优惠)")
	private BigDecimal nevsDiscountAmount;

	@ApiModelProperty("已付")
	@ExcelProperty(value = "已付")
	private String paidAmount;

	@ApiModelProperty("实收")
	@ExcelProperty(value = "优惠")
	private String receiveAmount;

	@ApiModelProperty("异常多收")
	@ExcelProperty(value = "异常多收")
	@ColumnWidth(value = 18)
	private String moreAmount;

	@ApiModelProperty("异常少收")
	@ExcelProperty(value = "异常少收")
	@ColumnWidth(value = 18)
	private String lessAmount;

	@ApiModelProperty("支付方式")
	@ExcelProperty(value = "支付方式")
	@ColumnWidth(value = 18)
	private String payType;

	@ApiModelProperty("支付时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "支付时间")
	@ColumnWidth(value = 22)
	private Date payTime;
	@ApiModelProperty("稽核时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "稽核时间")
	@ColumnWidth(value = 22)
	private Date auditTime;

	@ApiModelProperty("操作人真实姓名")
	@ExcelProperty(value = "操作人姓名")
	@ColumnWidth(value = 22)
	private String chargeUserRealName;

	@ApiModelProperty("操作人登录账号")
	@ExcelProperty(value = "操作人账号")
	@ColumnWidth(value = 22)
	private String chargeUserLoginName;

	@ApiModelProperty("订单名称")
	@ExcelProperty(value = "订单状态")
	@ColumnWidth(value = 22)
	private String orderName;

	@ApiModelProperty("订单状态")
	@JsonSerialize(using = ToStringSerializer.class)
	@ExcelIgnore
	private Integer orderStatus;

	@ApiModelProperty("停车记录id")
	@ExcelIgnore
	private Long parkingId;

	@ApiModelProperty("通道id")
	@ExcelIgnore
	private Long channelId;

	@ApiModelProperty("车场id")
	@ExcelIgnore
	private Long parklotId;

	@ExcelProperty(value = "出场通道名称")
	@ColumnWidth(value = 22)
	private String channelName;

	@ApiModelProperty("出场通道名称")
	@ExcelProperty(value = "出场通道名称")
	@ColumnWidth(value = 22)
	private String exitChannelName;

	@ApiModelProperty("进场通道名称")
	@ExcelProperty(value = "进场通道名称")
	@ColumnWidth(value = 22)
	private String enterChannelName;

	@ApiModelProperty("车场名称")
	@ExcelProperty(value = "车场名称")
	@ColumnWidth(value = 22)
	private String parklotName;

	@ApiModelProperty("通道缴费")
	@ExcelIgnore
	private String channelPayAmount;

	@ApiModelProperty("现金缴费")
	@ExcelIgnore
	private String cashAmount;

	/**
	 * 计费开始时间
	 */
	@ApiModelProperty(value = "计费开始时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelIgnore
	private String billingStartTime;

	/**
	 * 计费截至时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "计费截至时间")
	@ExcelIgnore
	private String billingEndTime;

	@ExcelIgnore
	private String imageUrl;

	@JsonSerialize(using = ToStringSerializer.class)
	@ExcelIgnore
	private Long unpaidOrderId;

	@ApiModelProperty(value = "审核状态")
	@ExcelIgnore
	private Integer auditStatus;

	@ApiModelProperty(value = "实收金额")
	@JsonSerialize(using = ToStringSerializer.class)
	@ExcelProperty(value = "实收金额")
	@ColumnWidth(value = 22)
	private BigDecimal realAmount;


	/**
	 * 应收金额
	 */
	@ApiModelProperty(value = "应收金额")
	@JsonSerialize(using = ToStringSerializer.class)
	@ExcelProperty(value = "应收金额")
	@ColumnWidth(value = 22)
	private BigDecimal amount;

	@ExcelIgnore
	private String createTime;

	@ApiModelProperty(value = "缴费类别")
	private String createWay;

	@ApiModelProperty(value = "退款状态")
	private String refundStatus;

	@ApiModelProperty(value = "退款金额")
	private BigDecimal refundAmount;

	@ApiModelProperty("车位ID")
	@ExcelIgnore
	private String placeId;

	@ApiModelProperty("车位编号")
	@ExcelIgnore
	private String placeCode;
	@ApiModelProperty("进场时间")
	@ExcelIgnore
	private Date enterTime;
	@ApiModelProperty("出场时间")
	@ExcelIgnore
	private Date exitTime;

	@ApiModelProperty("停车时长")
	private String durationTime;
}
