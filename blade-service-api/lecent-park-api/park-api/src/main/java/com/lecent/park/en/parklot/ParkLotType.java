package com.lecent.park.en.parklot;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 车场类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ParkLotType {

	@Deprecated
	ROAD_OUT_INDOOR(0, "路外室内停车场"),
	/**
	 * 场内停车场
	 */
	ROAD_OUT_OUTDOOR(1, "场内停车场"),
	/**
	 * 路边停车
	 */
	ROAD_IN(2, "路边停车场");

	private final int value;
	private final String name;

	public static String getNameForValue(int value) {
		for (ParkLotType parkLotType : ParkLotType.values()) {
			if (parkLotType.value == value) {
				return parkLotType.name;
			}
		}
		return "";
	}

	public static boolean contains(Integer value) {
		if (Objects.isNull(value)) {
			return false;
		}

		for (ParkLotType parkLotType : ParkLotType.values()) {
			if (parkLotType.value == value) {
				return true;
			}
		}

		return false;
	}

	/**
	 * 根据值获取枚举
	 *
	 * @param value 枚举值
	 * @return 枚举 {@link ParkLotType}
	 */
	public static ParkLotType resolve(Integer value) {
		return Optional.ofNullable(value)
			.flatMap(v -> Stream.of(values()).filter(t -> t.value == v).findAny())
			.orElse(null);
	}

	/**
	 * 根据传入的值判断是否为场内车场
	 *
	 * @param value 枚举值
	 * @return {@code true} 场内车场 {@code false} 非场内车场
	 */
	public static boolean isRoadOut(Integer value) {
		if (Objects.isNull(value)) {
			return false;
		}

		return value.equals(ParkLotType.ROAD_OUT_INDOOR.getValue()) || value.equals(ParkLotType.ROAD_OUT_OUTDOOR.getValue());
	}

	/**
	 * 根据传入的值判断是否为路边车场
	 *
	 * @param value 枚举值
	 * @return {@code true} 路边车场 {@code false} 非路边车场
	 */
	public static boolean isRoadIn(Integer value) {
		return Objects.equals(ParkLotType.ROAD_IN.getValue(), value);
	}

	/**
	 * 是否为路边车场
	 *
	 * @return {@code true} 路边车场 {@code false} 非路边车场
	 */
	public boolean isRoadside() {
		return this == ROAD_IN;
	}
}
