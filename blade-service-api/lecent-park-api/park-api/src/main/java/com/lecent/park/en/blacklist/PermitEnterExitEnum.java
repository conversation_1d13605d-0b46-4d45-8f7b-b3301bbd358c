package com.lecent.park.en.blacklist;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.log.exception.ServiceException;

/**
 * 允许进出场类型
 *
 * <AUTHOR>
 * @date 2021/12/02 10:15
 */
@Getter
@AllArgsConstructor
public enum PermitEnterExitEnum {

	/**
	 * 黑名单
	 */
	BLACK_LIST(0, "黑名单", "不允许进出场"),

	/**
	 * 其他名单
	 */
	OTHERS_LIST(1, "黑名单", "允许出场,不允许进场"),

	/**
	 * 黄名单
	 */
	YELLOW_LIST(2, "黄名单", "允许进场,不允许出场"),
	;
	private Integer code;
	private String name;
	private String value;

	public static PermitEnterExitEnum of(Integer code) {
		for (PermitEnterExitEnum item : PermitEnterExitEnum.values()) {
			if (item.getCode().equals(code)) {
				return item;
			}
		}
		throw new ServiceException("未知的名单类型");
	}
}



