package com.lecent.park.vo.parkingOrderVO;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/20 11:35
 */
@Data
public class PayCodeByOrderVO {

    /**
     * 停车场id
     */
    @ApiModelProperty("停车场id")
    private Long parklotId;

    /**
     * 车牌
     */
    @ApiModelProperty("车牌")
    private String plate;

    /**
     * 支付码
     */
    @ApiModelProperty("车位支付码")
    private String payCode;

    /**
     * 车辆状态
     * {@link com.lecent.park.en.parklot.ParkingStatusEnum}
     */
    @ApiModelProperty("车辆状态")
    private Integer parkingStatus;

    /**
     * 订单状态： 0-未查询到停车记录；1-需要缴费；2-免费离场
     */
    @ApiModelProperty("订单状态")
    private Integer status;

    /**
     * 应收金额
     */
    @ApiModelProperty("应收金额")
    private BigDecimal receiveAmount;

    public PayCodeByOrderVO() {
        this.receiveAmount = BigDecimal.ZERO;
    }
}
