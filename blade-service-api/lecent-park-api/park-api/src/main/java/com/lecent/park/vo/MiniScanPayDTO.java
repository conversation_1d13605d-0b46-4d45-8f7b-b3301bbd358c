package com.lecent.park.vo;

import com.lecent.park.en.coupon.CouponCategory;
import com.lecent.pay.core.enums.PayScene;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 小程序支付请求参数
 *
 * <AUTHOR>
 */
@Data
public class MiniScanPayDTO {

	/**
	 * 主订单待办ID
	 */
	private Long todoId;

	/**
	 * 所有订单待办IDS
	 */
	private List<Long> todoIds;

	/**
	 * 商户ID
	 */
	private String merchantId;

	/**
	 * 优惠劵ID
	 */
	private Long userCouponId;

	/**
	 * 优惠劵类型
	 */
	private CouponCategory couponCategory;

	/**
	 * openId
	 */
	@NotEmpty(message = "openId不能为空")
	private String openId;

	/**
	 * 回调地址
	 */
	private String returnUrl;

	/**
	 * 回调地址参数
	 */
	private String returnUrlParams;

	/**
	 * 支付平台
	 */
	private String source;

	/**
	 * 用户ID
	 */
	private String userId;

	/**
	 * 支付渠道
	 */
	private String payChannel;

	/**
	 * 支付场景
	 */
	private PayScene payScene;
}
