package com.lecent.park.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/4 15:55
 */
@Data
public class CardVOPageVo {

	@ApiModelProperty(value = "所以人")
	private String roomNum;

	@ApiModelProperty(value = "车场ids")
	private String parklotIds;

	private Integer placeNum;

	private String cardIds;


	@ApiModelProperty(value = "房号")
	private List<CardVO> cardVOList;


}
