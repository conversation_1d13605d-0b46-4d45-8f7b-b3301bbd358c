package com.lecent.park.vo;

import com.lecent.park.entity.Channel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车场通道视图实体类
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChannelVO对象", description = "车场通道")
public class ChannelVO extends Channel {
	private static final long serialVersionUID = 1L;

	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	private Long userId;
	/**
	 * 车场编号
	 */
	private String parklotNo;

}
