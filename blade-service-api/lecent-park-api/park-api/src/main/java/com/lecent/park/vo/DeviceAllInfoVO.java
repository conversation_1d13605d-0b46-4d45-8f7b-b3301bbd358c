package com.lecent.park.vo;

import com.lecent.park.entity.PaceDevice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/23 11:54
 */
@Setter
@Getter
@Builder
@ApiModel("设备信息")
public class DeviceAllInfoVO extends PaceDevice {

	@ApiModelProperty("车场名称")
	private String parklotName;

	@ApiModelProperty("楼层名称")
	private String floorName;

	@ApiModelProperty("区域名称")
	private String regionName;

	@ApiModelProperty("公司名称")
	private String tenantName;
}
