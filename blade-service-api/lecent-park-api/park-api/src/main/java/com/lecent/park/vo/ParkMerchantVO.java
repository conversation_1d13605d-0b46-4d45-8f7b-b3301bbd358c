package com.lecent.park.vo;

import com.lecent.park.entity.ParkMerchant;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 酒店商户表视图实体类
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParkMerchantVO对象", description = "酒店商户表")
public class ParkMerchantVO extends ParkMerchant {
	private static final long serialVersionUID = 1L;

	/**
	 * 车场名称
	 */
	private String parklotNames;

	private String realName;


}
