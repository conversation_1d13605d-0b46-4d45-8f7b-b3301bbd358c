package com.lecent.park.vo.vehiclePhoneRelation;

import com.lecent.pay.core.enums.PayChannel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.tool.utils.Func;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.*;
import java.util.stream.Collectors;


@Data
@ApiModel(value = "车主信息检索参数")
public class VehiclePhoneRelationQueryVO {

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "获取电话时间开始时间")
	private Date phoneBindStartTime;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "获取电话时间结束时间")
	private Date phoneBindEndTime;
	@ApiModelProperty(value = "是否已认证 0：未认证 1：已认证")
	private Integer isAuth;
	@ApiModelProperty(value = "车牌多个逗号隔开")
	private String plates;
	@ApiModelProperty(value = "车牌列表", hidden = true)
	private List<String> plateList;
	@ApiModelProperty(value = "用户车辆 id 集合", hidden = true)
	private List<Long> vehicleIdList;
	@ApiModelProperty(value = "电话号码")
	private String phone;
	@ApiModelProperty(value = "无感开通状态，多个逗号隔开")
	private String unconsciousTypes;
	@ApiModelProperty(value = "无感开通状态列表", hidden = true)
	private List<String> unconsciousTypeList;
	@ApiModelProperty(value = "来源 1: 微信授权 2：app录入 3：用户车辆绑定")
	private Integer source;
	@ApiModelProperty(value = "是否有电话 0：无 1：有")
	private Integer havePhone;
	@ApiModelProperty("当前页")
	private long current;
	@ApiModelProperty("每页的数量")
	private long size;


	public List<String> getPlateList() {
		if (Func.isNotEmpty(this.plateList)) {
			return this.plateList;
		}
		this.plateList = Func.isBlank(this.plates) ? Collections.emptyList() : Func.toStrList(this.plates);
		return this.plateList;
	}

	public List<String> getUnconsciousTypeList() {
		if (Func.isNotEmpty(this.unconsciousTypeList)) {
			return this.unconsciousTypeList;
		}
		this.unconsciousTypeList = Optional.ofNullable(this.unconsciousTypes)
			.map(item -> Func.toStrList(this.unconsciousTypes).stream()
				.map(type -> PayChannel.payChannelValue(Integer.valueOf(type)).getName())
				.collect(Collectors.toList())).orElse(null);
		return this.unconsciousTypeList;
	}
}
