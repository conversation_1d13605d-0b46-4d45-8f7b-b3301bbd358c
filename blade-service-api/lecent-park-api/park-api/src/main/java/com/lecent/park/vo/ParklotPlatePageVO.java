package com.lecent.park.vo;

import com.lecent.park.entity.ParkMerchantParklotPlate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商家车场授权车牌表视图实体类
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParkMerchantParklotPlateVO对象", description = "商家车场授权车牌表")
public class ParklotPlatePageVO extends ParkMerchantParklotPlate {
	private static final long serialVersionUID = 1L;

	/**
	 * 商家名称
	 */
	@ApiModelProperty(value = "授权人")
	private String merName;

	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	@ApiModelProperty(value = "车辆状态（2-在场；4-离场）")
	private Integer carInParkStatus;

	@ApiModelProperty(value = "商家账号")
	private String merAccount;

	@ApiModelProperty(value = "登录人名称")
	private String realName;

	@ApiModelProperty(value = "是否绑定无牌车")
	private  Boolean isBind;

	@ApiModelProperty(value = "进场时间")
	private Date enterTime;


}
