package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 建行优惠劵信息
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@ApiModel(value = "建行优惠劵信息", description = "建行优惠劵信息")
public class CCBCouponVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("用户优惠劵id")
	private Long userCouponId;

	@ApiModelProperty("优惠劵名称")
	private String couponName;

	@ApiModelProperty("优惠劵类型")
	private Integer couponType;

	@ApiModelProperty("转赠次数")
	private Integer giveOtherTimes;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("开始时间")
	private Date startDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("结束时间")
	private Date endDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("失效时间")
	private Date invalidTime;

	@ApiModelProperty("结束时间描述信息")
	private String endDateStr;

	@ApiModelProperty("满多少元")
	private String fullAmount;

	@ApiModelProperty("减多少元")
	private String reduceAmount;

	@ApiModelProperty("折扣数")
	private String discountAmount;

	@ApiModelProperty("抵扣时长（时长劵）")
	private Integer reduceHour;

	@ApiModelProperty("抵扣时长对应的费用（时长劵）")
	private String reduceHourAmount;

	@ApiModelProperty("备注说明")
	private String remark;

	@ApiModelProperty("可用车场id列表")
	private String availableParklotIds;

}
