package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 进出场记录
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@ApiModel(value = "进出场记录", description = "进出场记录")
public class HomeEnterAndExitVO implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 车场名称
	 */
	private String parklotName;


	/**
	 * 车牌
	 */
	private String plate;


	/**
	 * 地址
	 */
	private String addr;


	/**
	 * 出入场类型
	 */
	private String type;


	/**
	 * 通行时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private Date passTime;


}
