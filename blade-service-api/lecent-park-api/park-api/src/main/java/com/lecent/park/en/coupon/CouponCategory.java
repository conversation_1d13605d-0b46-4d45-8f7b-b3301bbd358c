package com.lecent.park.en.coupon;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 优惠卷种类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CouponCategory {

	/**
	 * 优惠券
	 */
	COUPON(0, "优惠券"),

	/**
	 * 商户优惠
	 */
	MERCHANT(1, "商户优惠"),

	/**
	 * 充电优惠
	 */
	CHARGING(2, "充电优惠"),

	/**
	 * 新能源车牌优惠
	 */
	NEW_ENERGY_PLATE(3, "新能源车牌优惠"),

	/**
	 * 追缴优惠
	 */
	RECOVERY_DISCOUNT(4, "追缴优惠"),

	/**
	 * 支付渠道优惠
	 */
	PAY_CHANNEL(5, "支付渠道优惠"),

	/**
	 * 其他
	 */
	OTHER(99, "其他"),
	;

	/**
	 * value
	 */
	@EnumValue
	@JsonValue
	private final int value;

	/**
	 * 描述
	 */
	private final String name;
}
