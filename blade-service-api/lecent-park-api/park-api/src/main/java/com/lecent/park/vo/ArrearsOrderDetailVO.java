package com.lecent.park.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.utils.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.excel.ExcelData;
import org.springblade.common.excel.ExcelTable;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 欠费详情vo
 */
@Data
@ApiModel("欠费详情vo")
public class ArrearsOrderDetailVO extends ExcelData {
	private static final long serialVersionUID = 1L;

	@Excel(name = "订单号")
	@ExcelProperty(value = "订单号")
	@ApiModelProperty("订单号")
	private String id;

	@Excel(name = "车场名称")
	@ExcelProperty(value = "车场名称")
	@ApiModelProperty("车场名称")
	private String name;

	@Excel(name = "进场时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "进场时间")
	@ApiModelProperty("进场时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date enterTime;

	@Excel(name = "出场时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "出场时间")
	@ApiModelProperty("出场时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date exitTime;

	@Excel(name = "停车时长")
	@ExcelProperty(value = "停车时长")
	@ApiModelProperty("停车时长")
	private String durationTime;

	@Excel(name = "欠费金额")
	@ExcelProperty(value = "欠费金额")
	@ApiModelProperty("欠费金额")
	private BigDecimal amount;

	@Excel(name = "车牌")
	@ExcelProperty(value = "车牌")
	@ApiModelProperty("车牌")
	private String plate;
}
