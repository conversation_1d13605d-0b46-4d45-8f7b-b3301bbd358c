package com.lecent.park.en;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;

/**
 * <AUTHOR>
 * @since 2023/1/6
 */
@Getter
@AllArgsConstructor
public enum QRSettingType {
    /**
     * 场内二维码
     */
    FIELD(0, "场内二维码"),
    /**
     * 入口二维码
     */
    ENTRANCE(1, "入口二维码"),
    /**
     * 出口二维码
     */
    EXIT(2, "出口二维码"),
    /**
     * 车位地锁二维码
     */
    LOCK(3, "车位地锁二维码"),
    /**
     * 车位视频桩二维码
     */
    VIDEO_PILE(4, "车位视频桩二维码"),
    ;

    int value;
    String desc;

    /**
     * 获取模板类型枚举
     *
     * @param value 值
     * @return {@link QRSettingType}
     */
    public static QRSettingType getTypeByValue(Integer value) {
        LecentAssert.notNull(value, "模板类型不能为空");
        for (QRSettingType type : values()) {
            if (value.equals(type.getValue())) {
                return type;
            }
        }
        throw new ServiceException("未查询到模板:" + value);
    }
}
