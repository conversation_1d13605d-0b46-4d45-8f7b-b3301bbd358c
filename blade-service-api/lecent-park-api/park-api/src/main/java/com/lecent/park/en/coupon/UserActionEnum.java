package com.lecent.park.en.coupon;

/**
 * 用户行为枚举类
 *
 * <AUTHOR>
 * @date 2021-05-20 10:16
 */
public enum UserActionEnum {

	NEVER_LOGIN_IN_SEVEN_DAY(1, "7天未登录用户"),

	FIRST_FOCUS(2, "已关注公众号"),

	BIND_ETC(3, "绑定ETC"),

	FIRST_BIND_PLATE(4, "首次绑定车辆"),

	USER_PLATE_AUTHENTICATION(5, "用户车辆认证"),

	BIND_NO_SENCE_PAY(6, "绑定无感支付"),

	TEMP_PAY(7, "跳转到临停缴费页面"),

	PAY_SUCCESS(8, "支付完成"),

	NEW_USER(9, "新用户");


	int value;
	String name;

	UserActionEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

}
