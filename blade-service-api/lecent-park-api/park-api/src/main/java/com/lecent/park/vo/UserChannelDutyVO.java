package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lecent.park.entity.UserChannelDuty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 通道值班表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserChannelDutyVO对象", description = "通道值班表")
public class UserChannelDutyVO extends UserChannelDuty {
	private static final long serialVersionUID = 1L;


	/**
	 * 现金
	 */
	@ApiModelProperty(value = "现金")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal receiveAmount;

}
