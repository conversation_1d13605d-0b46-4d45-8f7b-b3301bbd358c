package com.lecent.park.en.merchant;

/**
 * 商家（酒店） 结算频次
 *
 * <AUTHOR>
 * @date 2021-11-30 09:52
 */
public enum SettleFrequencyEnum {

	WEEK(1, "按周结算"),
	MONTH(2, "按月结算"),
	DAY(3, "按日结算"),
	YEAR(4, "按年结算"),
	;

	private Integer value;
	private String name;

	SettleFrequencyEnum(Integer value, String name) {
		this.value = value;
		this.name = name;
	}

	public Integer getValue() {
		return value;
	}


	public String getName() {
		return name;
	}
}
