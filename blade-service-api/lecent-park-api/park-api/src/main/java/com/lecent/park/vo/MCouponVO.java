package com.lecent.park.vo;

import com.lecent.park.entity.MCoupon;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 优惠券明细表视图实体类
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MCouponVO对象", description = "优惠券明细表")
public class MCouponVO extends MCoupon {
	private static final long serialVersionUID = 1L;

}
