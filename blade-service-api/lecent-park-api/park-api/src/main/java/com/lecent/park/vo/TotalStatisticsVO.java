package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020-08-24 13:33
 */
@Data
@Builder
@ApiModel("首页返回数据")
public class TotalStatisticsVO {
	@ApiModelProperty(value = "停车收入")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private BigDecimal parkinMoney;

	@ApiModelProperty(value = "欠费金额")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private BigDecimal nopayMoney;

	@ApiModelProperty(value = "违停次数")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Integer iIllegalCount;

	@ApiModelProperty(value = "剩余泊位")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Integer leftPlace;

	@ApiModelProperty(value = "在场车位数")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Integer parksum;

	@ApiModelProperty(value = "总车位数")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Integer totalPlace;

	@ApiModelProperty(value = "今日过车数量")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Integer passNum;


	@ApiModelProperty(value = "今天的流量")
	private Integer todayTraffic;
	@ApiModelProperty(value = "昨日流量")
	private Integer prevDayTraffic;
	@ApiModelProperty(value = "去年同一天的流量")
	private Integer prevYearTraffic;

	@ApiModelProperty(value = "同比")
	private BigDecimal dayOnDayPercentage;
	@ApiModelProperty(value = "环比")
	private BigDecimal yearOnYearPercentage;

}
