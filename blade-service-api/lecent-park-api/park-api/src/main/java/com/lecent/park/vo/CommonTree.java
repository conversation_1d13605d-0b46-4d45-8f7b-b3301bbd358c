package com.lecent.park.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonTree {
	private Long id;
	private String code;
	private String name;
	private List<CommonTree> children;

	public CommonTree(Long id, String code, String name) {
		this.id = id;
		this.code = code;
		this.name = name;
	}
}
