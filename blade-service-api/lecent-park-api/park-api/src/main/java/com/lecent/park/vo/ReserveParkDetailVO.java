package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预约停车记录返还对象
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@ApiModel(value = "ReserveParkDetailVO对象", description = "预约停车记录返还对象")
public class ReserveParkDetailVO implements Serializable {

	private static final long serialVersionUID = 1777190394390653379L;

	@ApiModelProperty(value = "车牌")
	private String plate;

	@ApiModelProperty(value = "停车场名称")
	private String parkName;

	@ApiModelProperty(value = "预约订单号")
	private String reserveOrderId;

	/**
	 * 出场时间 时间戳
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "出场时间 时间戳")
	private Date exitTime;

	@ApiModelProperty(value = "停车记录id")
	private Long parkId;

	@ApiModelProperty("停车缴费订单记录")
	private List<ReserveTempParkingOrderVO> parkingChargeOrders;

}
