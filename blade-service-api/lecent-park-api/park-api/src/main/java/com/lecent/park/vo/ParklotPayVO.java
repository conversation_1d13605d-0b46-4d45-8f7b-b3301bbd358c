package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lecent.park.entity.ParklotPay;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 车场与商户对应关系表视图实体类
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ParklotPayVO对象", description = "车场与商户对应关系表")
public class ParklotPayVO extends ParklotPay {
	private static final long serialVersionUID = 1L;


	/**
	 * 支付方式列表
	 * 1 微信支付  2 聚合支付
	 */
	private List<String> types;


	/**
	 * 支付渠道列表
	 */
	private List<String> payWayIds;


	@ApiModelProperty("默认支付渠道id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long defaultWayId;

}
