package com.lecent.park.en.temprule;



/**
 * 车型
 * <AUTHOR>
 */

public enum CarType {

	MOTORCYCLE(1, "摩托车"),
	TRICYCLE(2, "三轮车"),
	LIGHT_DUTY_VEHICLE(3, "小型车"),
	MEDIUM_SIZED_CAR(4, "中型车"),
	LARGE_VEHICLE(5, "大型车"),
	VISITOR_AUTH(6, "访客授权车辆"),
	IMMEDIATE_FAMILY(7,"直系亲属车辆"),
	NO_IMMEDIATE_FAMILY(8,"非直系亲属车辆"),
	STAFF(9,"员工车辆")
	;

	private int value;
	private String name;

	CarType(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public static String getName(int value){
		for (CarType c:CarType.values()) {
			if (c.getValue()==value){
				return c.getName();
			}
		}
		return "";
	}

}
