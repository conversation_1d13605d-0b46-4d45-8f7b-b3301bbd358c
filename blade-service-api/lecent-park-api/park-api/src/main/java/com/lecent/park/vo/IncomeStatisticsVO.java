package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 收入统计返回类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@ApiModel(value = "收入统计返回类", description = "收入统计返回类")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IncomeStatisticsVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "停车总收入")
	private BigDecimal totalIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "今日收入")
	private BigDecimal todayIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "今日临停收入")
	private BigDecimal todayTempIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "今日临停线下收入")
	private BigDecimal todayTempOfflineIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "今日临停线上收入")
	private BigDecimal todayTempOnlineIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "今日月卡收入")
	private BigDecimal todayCardIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "今日月卡线下收入")
	private BigDecimal todayCardOfflineIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "今日月卡线上收入")
	private BigDecimal todayCardOnlineIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "昨日收入")
	private BigDecimal lastDayIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "本月收入")
	private BigDecimal thisMonthIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "上月月收入")
	private BigDecimal lastMonthIncome = BigDecimal.ZERO;

	@ApiModelProperty(value = "日环比增长")
	private String growthOfDay;

	@ApiModelProperty(value = "月环比增长")
	private String growthOfMonth;

	public static IncomeStatisticsVO getDefaultIncome() {
		return IncomeStatisticsVO.builder()
			.totalIncome(BigDecimal.ZERO)
			.todayIncome(BigDecimal.ZERO)
			.lastDayIncome(BigDecimal.ZERO)
			.thisMonthIncome(BigDecimal.ZERO)
			.lastMonthIncome(BigDecimal.ZERO)
			.growthOfDay("-")
			.growthOfMonth("-")
			.build();
	}

}
