package com.lecent.park.en;

/**
 * 170平台核销回调接口响应枚举
 *
 * <AUTHOR>
 * @date 2020-06-03 09:52
 */
public enum Platform170ResultEnum {

	PARAM_LOSE(1001, "缺少请求参数"),
	PARAM_ERROR(1002, "加密参数有误"),
	SIGN_ERROR(1003, "签名失败"),
	COUPON_NOT_FOUND(1004, "立减金不存在"),
	ORGANIZATION_ERROR(1005, "organizationId不正确"),
	CONSUME_ERROR(1006, "立减金已核销"),
	OVERDUE_ERROR(1007, "立减金已过期"),
	REQUEST_FAIL(1500, "请求失败！"),
	SUCCESS(1000, "响应成功");


	Integer value;
	String name;

	Platform170ResultEnum(Integer value, String name) {
		this.value = value;
		this.name = name;
	}


	public Integer getValue() {
		return value;
	}

	public String getName() {
		return name;
	}
}
