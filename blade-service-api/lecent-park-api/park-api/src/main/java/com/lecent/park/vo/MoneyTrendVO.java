package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/15 10:36
 */
@Data
@ApiModel("收费金额趋势详细信息")
public class MoneyTrendVO {

	@ApiModelProperty("日期")
	private String createTime;
	@ApiModelProperty("周几（1-星期一2-星期二3星期三4星期四5星期五6星期六0星期日）")
	private Integer week;
	@ApiModelProperty("异常金额")
	private BigDecimal unusualAmount = BigDecimal.ZERO;
	@ApiModelProperty("优惠金额")
	private BigDecimal discountAmount = BigDecimal.ZERO;
	@ApiModelProperty("已付金额")
	private BigDecimal paidAmount = BigDecimal.ZERO;

}
