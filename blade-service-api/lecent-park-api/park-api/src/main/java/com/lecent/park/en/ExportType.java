package com.lecent.park.en;

/**
 * <AUTHOR>
 */
public interface ExportType {

	/**
	 * 停车记录
	 */
	int  PARKING_ORDER = 1;


	/**
	 *临停订单
	 */
	int TEMP_ORDER = 2;


	/**
     * 识别记录
     */
	int CHANNEL_TODO = 3;



	/**
	 * 车位
	 */
	int PLACE_EXPORT=4;

	/**
	 * 车场设备二维码导出
	 */
	int PARK_DEVICE_QR_EXPORT=5;
	/**
	 * 识别记录
	 */
	int CARD = 3;

	/**
	 * 识别记录
	 */
	int CARD_ORDER = 7;

	/**
	 * 识别记录
	 */
	int CARD_ORDER_TOTAL = 8;

	int LOCK_ERROR_ARREARS = 9;


}
