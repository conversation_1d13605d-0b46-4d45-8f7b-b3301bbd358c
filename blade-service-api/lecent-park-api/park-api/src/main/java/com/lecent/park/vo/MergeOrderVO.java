package com.lecent.park.vo;

import com.lecent.park.entity.MergeOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 临停合并支付订单VO
 *
 * <AUTHOR>
 * @date 2023/03/06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TempParkingOrderMergeVO对象", description = "临停合并支付订单VO")
public class MergeOrderVO extends MergeOrder {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("appId")
    private String appId;

    @ApiModelProperty("停车记录id")
    private List<Long> parkingIds;

    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;


	private Date startTime;
	private Date endTime;
	private List<Long> parklotIds;

	private Long  parklotId;
	private String  parklotName;
	private Long  placeId;
	private String  placeCode;
	private Integer  payType;
	private Date payTime;
	private String createUserName;





}
