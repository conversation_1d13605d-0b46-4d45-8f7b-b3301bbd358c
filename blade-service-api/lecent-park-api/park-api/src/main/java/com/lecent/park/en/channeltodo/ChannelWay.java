package com.lecent.park.en.channeltodo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nullable;

/**
 * 停车进出场触发方式
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChannelWay {

	WAY_1(1, "摄像头识别"),
	WAY_2(2, "车主扫码"),
	WAY_3(3, "手动开闸"),
	WAY_4(4, "手动模拟"),
	WAY_5(5, "车位锁地感"),
	WAY_6(6, "地磁"),
	WAY_7(7, "自动抓拍"),
	WAY_8(8, "人工登记"),
	WAY_9(9, "人工补录"),
	WAY_10(10, "进场覆盖"),
	WAY_11(11, "车主通道扫码"),
	WAY_12(12, "巡检车"),
	;

	private final Integer value;
	private final String name;

	public static String getName(Integer value) {
		for (ChannelWay en : ChannelWay.values()) {
			if (en.getValue().equals(value)) {
				return en.getName();
			}
		}
		return null;
	}

	@Nullable
	public static ChannelWay resolve(Integer value) {
		for (ChannelWay way : values()) {
			if (way.value.equals(value)) {
				return way;
			}
		}
		return null;
	}

}
