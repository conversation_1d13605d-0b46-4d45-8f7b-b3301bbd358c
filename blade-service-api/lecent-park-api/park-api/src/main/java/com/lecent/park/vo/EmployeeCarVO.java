package com.lecent.park.vo;

import com.lecent.park.entity.EmployeeCar;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 员工车辆视图实体类
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EmployeeCarVO对象", description = "员工车辆")
public class EmployeeCarVO extends EmployeeCar {
	private static final long serialVersionUID = 1L;


	private List<EmployeeCarParklotVO> employeeCarParklotVOList;

	private List<String> certificateList;

}
