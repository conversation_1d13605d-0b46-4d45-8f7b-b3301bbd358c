package com.lecent.park.en.card;

import org.springblade.core.tool.utils.Func;

public enum Classify {

	PROPERTY_RIGHT(1,"独立产权车位"),
	MOTHER_SON(2,"子母车位"),
	TEMPORARY_STOP(3,"临停车位"),
	VIP(4,"VIP车位"),
	ACCESSIBILITY(5,"无障碍车位"),
	OTHER(6,"其他车位"),
	SHARE(7,"共享车类型"),

	;

	private Integer value;
	private String name;

	Classify(Integer value, String name) {
		this.value = value;
		this.name = name;
	}


	public Integer getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public static String getName(Integer value){
		if (Func.isEmpty(value)){
			return "";
		}
		for (Classify en : Classify.values()) {
			if (en.getValue().equals(value)){
				return en.getName();
			}
		}
		return null;
	}

}
