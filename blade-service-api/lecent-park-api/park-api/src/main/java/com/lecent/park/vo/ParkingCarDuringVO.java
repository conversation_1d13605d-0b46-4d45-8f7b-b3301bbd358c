package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description：
 * <AUTHOR>
 * @Date: 2020/6/15 10:25
 */
@Getter
@Setter
@Builder
@ApiModel("入场停车时长分析详情")
public class ParkingCarDuringVO {

	@ApiModelProperty("小于1h")
	private Integer one;
	@ApiModelProperty("1-6h")
	private Integer six;
	@ApiModelProperty("6-12h")
	private Integer twelve;
	@ApiModelProperty("12-18h")
	private Integer eighteen;
	@ApiModelProperty("18-24h")
	private Integer twentyFour;
}
