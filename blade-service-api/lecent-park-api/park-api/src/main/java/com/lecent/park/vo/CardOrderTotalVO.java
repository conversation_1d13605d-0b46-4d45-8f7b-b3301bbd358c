package com.lecent.park.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020-06-05 14:55
 */
@Data
public class CardOrderTotalVO {

	/**
	 * 车场ID
	 */
	@ApiModelProperty(value = "车场ID")
	private Long parklotId;

	/**
	 * 车场ID
	 */
	@ApiModelProperty(value = "车场名称")
	private String  parklotName;

	/**
	 * 缴费时间
	 */
	@ApiModelProperty(value = "缴费时间")
	private String  payDate;
	/**
	 * 笔数
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("笔数")
	private BigDecimal orderCount;

	/**
	 * 应收合计
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("应收合计")
	private BigDecimal shouldTotal;
	/**
	 * 优惠合计
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("优惠合计")
	private BigDecimal discountTotal;
	/**
	 * 实付金额
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("实付金额")
	private BigDecimal payTotal;

	/**
	 * 线上缴费合计
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("线上缴费合计")
	private BigDecimal onlinePayTotal;

	/**
	 * 线下缴费合计
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("线下缴费合计")
	private BigDecimal offlinePayTotal;

	@ApiModelProperty("欠缴订单数")
	private Integer  recoveryCount;
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("欠缴金额")
	private BigDecimal  recoveryAmount;

}
