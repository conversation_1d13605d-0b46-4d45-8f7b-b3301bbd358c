package com.lecent.park.en;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 停车异常类型枚举
 */
@Getter
@AllArgsConstructor
public enum ParkingAbnormalTypeEnum {
    
    ENTRY_OVERRIDE(1, "进场覆盖"),
    ENTRY_EXIT_INCONSISTENT(2, "进出场不一致"), 
    NO_PLATE_WITH_ENTRY_IMAGE(3, "无牌车有进场图片"),
    NO_ENTRY_EXIT_IMAGE(4, "无进出场图片");

    private final Integer code;
    private final String desc;

    public static ParkingAbnormalTypeEnum getByCode(Integer code) {
        for (ParkingAbnormalTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 