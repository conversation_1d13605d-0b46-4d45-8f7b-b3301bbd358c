package com.lecent.park.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lecent.park.dto.VehicleDTO;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 路边停车请求实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoadSideParkingDTO {

	/**
	 * 车场ID
	 */
	private Long parklotId;
	/**
	 * 车牌
	 */
	private String plate;

	/**
	 * 设备序列号
	 */
	private String sn;

	/**
	 * 支付号
	 */
	private String payCode;

	/**
	 * 车位ID
	 */
	private Long placeId;

	/**
	 * 车位编号
	 */
	private String placeCode;

	/**
	 * 停车状态[2-进场，4-出场]
	 * {@link com.lecent.park.constant}
	 */
	@NotNull(message = "停车状态不能为空")
	private Integer parkingStatus;

	/**
	 * 触发方式 1摄像头识别 2车主扫码 3手动开闸 4模拟入场
	 * {@link com.lecent.park.en.channeltodo.ChannelWay}
	 */
	private Integer triggerType;

	/**
	 * 设备类型
	 */
	private Integer deviceType;

	/**
	 * 车牌图片链接（小图）
	 */
	private String plateImageUrl;

	/**
	 * 场景图片链接
	 */
	private String imageUrl;

	/**
	 * 多种类型停车图片
	 * key=1 车牌图片
	 * key=2 场景图片（多张）
	 */
	private Map<Integer, List<String>> imageUrlMap;

	/**
	 * 触发时间
	 */
	private Date date;

	/**
	 * 车牌颜色
	 */
	private String plateColor;

	/**
	 * 车辆类型
	 */
	private Integer vehicleType;

	/**
	 * 车辆属性id
	 */
	private Long vehicleId;

	/**
	 * 停车记录id
	 */
	private Long parkingId;

	/**
	 * 车辆信息
	 */
	private VehicleDTO vehicleDTO;

	/**
	 * 延迟处理
	 */
	private Boolean delayHandle;

	private String merchantId;

	private String payScene;

	/**
	 * 判断是否为入场
	 *
	 * @return boolean
	 */
	@JsonIgnore
	public boolean isEnter() {
		return ParkingStatusEnum.PARK_IN.getValue() == this.parkingStatus;
	}


}
