package com.lecent.park.en.coupon;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 优惠类型：0：停车场决定、1：根据时长减免、2：根据费用减免、3：根据订单减免，默认0
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChargingFactoryDiscountType {

	/**
	 * 停车场决定
	 */
	DEFAULT(0),

	/**
	 * 根据时长减免
	 */
	FREE_TIMES(1),

	/**
	 * 根据费用减免
	 */
	FREE_COST(2),

	/**
	 * 根据订单减免
	 */
	ORDER_MONEY(3),
	;

	/**
	 * code
	 */
	@EnumValue
	@JsonValue
	private Integer value;

	public static ChargingFactoryDiscountType get(Integer value) {
		for (ChargingFactoryDiscountType discountType : ChargingFactoryDiscountType.values()) {
			if (discountType.value.equals(value)) {
				return discountType;
			}
		}
		return ChargingFactoryDiscountType.DEFAULT;
	}
}
