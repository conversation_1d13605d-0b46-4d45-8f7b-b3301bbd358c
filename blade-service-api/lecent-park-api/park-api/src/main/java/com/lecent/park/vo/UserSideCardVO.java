package com.lecent.park.vo;

import com.lecent.park.entity.Card;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户端月卡详情
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "用户端月卡详情", description = "用户端月卡详情")
public class UserSideCardVO extends Card {

	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "月卡类型id")
	private Long categoryId;

	@ApiModelProperty(value = "车场名称")
	private String parklotName;

	@ApiModelProperty(value = "续费月份列表")
	private List<PayMonth> monthList;

	@ApiModelProperty(value = "车位类型(1独立产权车位;2子母车位;3临停车位;4VIP车位;5无障碍车位)")
	private Integer placeType;

	@ApiModelProperty("月卡状态（1有效；0无效；-1作废；2即将过期）")
	private Integer status;

	@ApiModelProperty(value = "是否第一次续费（0-否;1-是）")
	private Integer firstPay;

	@ApiModelProperty(value = "最少缴费月数")
	private Integer minPayMonth;

	@ApiModelProperty(value = "续费类型")
	private Integer periodType;

	/**
	 * 扫码端小程序ID
	 */
	@ApiModelProperty(value = "扫码端小程序ID")
	private String scanMimiId;

	/**
	 * 扫码端小程序应用原始ID
	 */
	@ApiModelProperty(value = "扫码端小程序应用原始ID")
	private String scanMimiOriginalId;

	/**
	 * 扫码端支付宝小程序ID
	 */
	@ApiModelProperty(value = "扫码端支付宝小程序ID")
	private String scanAliMimiId;
	/**
	 * 是否支持线上续费 1支持 0 不支持
	 */
	@ApiModelProperty(value = "是否支持线上续费 1支持 0 不支持")
	private String supportOnlineRenewal;
}
