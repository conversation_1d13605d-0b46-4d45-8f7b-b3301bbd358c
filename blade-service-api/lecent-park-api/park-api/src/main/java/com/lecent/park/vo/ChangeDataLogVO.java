package com.lecent.park.vo;

import com.lecent.park.entity.ChangeDataLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 更改数据日志视图实体类
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChangeDataLogVO对象", description = "更改数据日志")
public class ChangeDataLogVO extends ChangeDataLog {
	private static final long serialVersionUID = 1L;

}
