package com.lecent.park.vo;

import com.lecent.park.entity.BlackListRemoveLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 车辆黑名单视图实体类
 *
 * <AUTHOR>
 * @since 2021-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BlackListRemoveLogVO对象", description = "车辆黑名单")
public class BlackListRemoveLogVO extends BlackListRemoveLog {
	private static final long serialVersionUID = 1L;

	private Integer parklotCount;

	private String parklotNames;

	private List<String> certificateList;

	private List<String> parklotIdList;
}
