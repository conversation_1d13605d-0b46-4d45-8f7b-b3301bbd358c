package com.lecent.park.en.card;

/**
 * 月卡续费时间段类型枚举
 * <AUTHOR>
 * @date 2020-06-03 09:52
 */
public enum CardPeriodTypeEnum {

	/**
	 * 自然月（月初到月末）
	 */
	NATURAL_MONTH(1),
	/**
	 * 天对天（8.8-9.8）
	 */
	DAY_2_DAY(2),

	/**
	 * 天对天结束少一天（8.8-9.7）
	 */
	DAY_2_DAY_EARLY_END(3),

	/**
	 * 对日
	 */
	MANY_DAYS(4);

	private Integer renewWay;

	CardPeriodTypeEnum(Integer renewWay) {
		this.renewWay = renewWay;
	}

	public Integer getRenewWay() {
		return this.renewWay;
	}


}
