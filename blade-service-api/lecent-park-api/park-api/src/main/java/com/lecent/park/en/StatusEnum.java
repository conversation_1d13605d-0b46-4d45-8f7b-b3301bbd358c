package com.lecent.park.en;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 状态枚举
 */
@AllArgsConstructor
@Getter
public enum StatusEnum {

	CANCEL(-1, "注销"),
	INVALID(0, "无效"),
	VALID(1, "有效"),
	STOP(2, "暂停"),

	//代办状态
	TODO_CREATE_STATUS(0, "创建"),
	TODO_HAND(1, "已处理"),
	TODO_SELF_PAY(2, "自主缴费"),
	TODO_AUTO_PASS(3, "自动通行"),
	TODO_EXPIRE(4, "过期 、清除车辆"),
	TODO_NO_ENTER(5, "禁止入场"),

	//支付状态
	PAY_CANCEL(-1, "取消支付"),
	PAY_CREATE(0, "创建"),
	PAYING(2, "支付中"),
	PAY_SUCCESS(1, "支付成功"),
	PAY_FAIL(4, "支付失败"),

	;
	private Integer code;
	private String desc;

}
