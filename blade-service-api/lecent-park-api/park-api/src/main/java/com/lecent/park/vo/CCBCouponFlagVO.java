package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支付成功页弹出是否有优惠劵需要领取标识
 *
 * <AUTHOR>
 * @since 2021-07-15
 */
@Data
@ApiModel(value = "建行优惠劵信息", description = "支付成功页是否有优惠劵需要领取标识")
public class CCBCouponFlagVO {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty("是否有优惠劵需要领取标识")
	private Boolean couponFlag;




}
