package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户端优惠劵适用的车场信息
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@ApiModel(value = "用户端优惠劵适用的车场信息", description = "用户端优惠劵适用的车场信息")
public class CCBCouponParklotVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("车场名称")
	private String parklotName;

	@ApiModelProperty("车场地址")
	private String parklotAddress;

	@ApiModelProperty("纬度")
	private BigDecimal lng;

	@ApiModelProperty("经度")
	private BigDecimal lat;


}
