package com.lecent.park.vo;

import com.lecent.park.entity.PayOrder;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预约车位表视图实体类
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ReservePayOrderVO对象", description = "预约车位表")
public class PayOrderVO extends PayOrder {
	private static final long serialVersionUID = 1L;

}
