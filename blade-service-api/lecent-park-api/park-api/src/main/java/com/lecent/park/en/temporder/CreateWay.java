package com.lecent.park.en.temporder;


import org.springblade.core.tool.utils.Func;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收费通道
 *
 * <AUTHOR>
 */

public enum CreateWay {
	INIT(0, "待处理"),
	INSIDE(1, "场内缴费"),
	CHANNEL(2, "通道自助缴费"),
	SENTRY_BOX_PERSON(3, "岗亭收费员"),
	SIMULATION(4, "模拟"),
	SYSTEM_TRACE_BACK(5, "系统追缴"),
	ABSENT(6, "自动代扣"),

	;

	private int value;
	private String name;

	CreateWay(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public static final Map<Integer, CreateWay> MAP = Arrays.stream(CreateWay.values())
			.collect(Collectors.toMap(CreateWay::getValue, payChannel -> payChannel));

	public int getValue() {
		return value;
	}


	public String getName() {
		return name;
	}


	public static String getNameByKey(Integer key) {
		if (Func.isEmpty(key)) {
			return null;
		}
		for (CreateWay pc : CreateWay.values()) {
			if (pc.getValue() == key) {
				return pc.getName();
			}
		}
		return null;
	}

	/**
	 * 获取收费通道
	 *
	 * @param value 值
	 * @return {@link CreateWay}
	 */
	public static CreateWay getPayChannel(Integer value){
		CreateWay createWay = MAP.get(value);
		return createWay != null ? createWay : INIT;
	}

}
