package com.lecent.park.vo.vehiclePhoneRelation;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lecent.park.entity.BUserPlate;
import com.lecent.park.vo.BVehicleUnconsciousRelationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 车主信息
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
@ApiModel(value = "车主信息")
public class CSCBVehiclePhoneRelationVO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "车辆信息 id")
	private Long vehicleId;
	@ApiModelProperty(value = "车牌")
	private String plate;
	@ApiModelProperty(value = "车牌颜色")
	private String plateColor;
	@ApiModelProperty(value = "用户车牌绑定关系列表")
	@JsonIgnore
	private List<BUserPlate> bUserPlateList;
	@ApiModelProperty(value = "车牌关联手机号集合")
	private List<BVehiclePhoneRelationVO> phoneRelationList;
	@ApiModelProperty(value = "无感开通类型集合")
	@JsonIgnore
	private List<BVehicleUnconsciousRelationVO> unconsciousRelationList;
	@ApiModelProperty(value = "无感开通类型，多个逗号隔开")
	private String unconsciousTypes;
	@ApiModelProperty(value = "关联用户信息")
	@JsonIgnore
	private List<CUserRel> cUserRelList;
	@ApiModelProperty(value = "关联用户第三方信息")
	@JsonIgnore
	private List<CUserThirdRel> cUserThirdRelList;
	@ApiModelProperty("行驶证图片")
	private String  vehicleLicenseUrl;
	@ApiModelProperty("是否已认证")
	private Boolean  isAuth;
	@ApiModelProperty("用户名")
	private String userName;
	@ApiModelProperty("openId")
	private String openId;

}
