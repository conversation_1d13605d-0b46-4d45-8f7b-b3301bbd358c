package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 排班日历
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@ApiModel(value = "SchedulingLogsVO对象", description = "排班日志表")
public class SchedulesCalendar implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("车场id")
	private Long parklotId;

	@ApiModelProperty("车场名称")
	private String parklotName;

    @ApiModelProperty("排班日历")
	private List<SchedulesCalendar>  schedulesCalendars;

    @ApiModelProperty("日期")
    private LocalDate date;

    @ApiModelProperty("车场ids")
    private String parklotIds;

    @NotNull(message = "开始日期不能为空")
    @ApiModelProperty("开始日期")
    private LocalDateTime startDate;

    @NotNull(message = "结束日期不能为空")
    @ApiModelProperty("结束日期")
    private LocalDateTime endDate;

    @ApiModelProperty("排版表")
    private List<SchedulesVO> schedules;
}
