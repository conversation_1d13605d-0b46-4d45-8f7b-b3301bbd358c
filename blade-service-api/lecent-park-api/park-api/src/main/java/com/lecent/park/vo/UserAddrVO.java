package com.lecent.park.vo;

import com.lecent.park.entity.UserAddr;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票抬头表视图实体类
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserAddrVO对象", description = "发票抬头表")
public class UserAddrVO extends UserAddr {
	private static final long serialVersionUID = 1L;

}
