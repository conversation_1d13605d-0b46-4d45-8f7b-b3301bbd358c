package com.lecent.park.vo;

import com.lecent.park.entity.MessageLog;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户端消息日志表视图实体类
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MessageLogVO对象", description = "用户端消息日志表")
public class MessageLogVO extends MessageLog {
	private static final long serialVersionUID = 1L;

}
