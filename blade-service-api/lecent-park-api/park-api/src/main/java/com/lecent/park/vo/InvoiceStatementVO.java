package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: TODO
 * @author: cy
 * @date: 2021年11月30日 17:18
 */
@Data
@ApiModel(value = "开票报表VO对象")
public class InvoiceStatementVO {

	@ApiModelProperty(value = "记录表id")
	private Long recordId;

	@ApiModelProperty(value = "车场名")
	private String parkLotName;

	@ApiModelProperty(value = "发票号码")
	private String invoiceNum;

	@ApiModelProperty(value = "抬头类型")
	private Integer titleType;

	@ApiModelProperty(value = "抬头名称")
	private String titleName;

	@ApiModelProperty(value = "税号")
	private String taxNum;

	@ApiModelProperty(value = "发票总金额")
	private BigDecimal totalAmount;

	@ApiModelProperty(value = "申请时间")
	private Date commentTime;

	@ApiModelProperty(value = "电子邮件")
	private String email;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "pdf地址")
	private String pdf;

	@ApiModelProperty(value = "开票状态")
	private Integer invoiceStatus;

	/**
	 * 开票返回信息
	 */
	@ApiModelProperty(value = "开票返回信息")
	private String message;
}
