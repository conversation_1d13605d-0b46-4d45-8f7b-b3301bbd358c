package com.lecent.park.vo;

import com.lecent.device.entity.Device;
import com.lecent.park.entity.ChannelDevice;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2021-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChannelDeviceVO对象", description = "ChannelDeviceVO对象")
public class ChannelDeviceVO extends ChannelDevice {
	private static final long serialVersionUID = 1L;

	private List<Device> deviceList;
}
