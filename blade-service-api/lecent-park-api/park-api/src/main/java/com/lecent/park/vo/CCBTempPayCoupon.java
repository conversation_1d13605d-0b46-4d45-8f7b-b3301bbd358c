package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 临停缴费页面弹出的优惠劵信息
 *
 * <AUTHOR>
 * @since 2021-07-15
 */
@Data
@ApiModel(value = "建行优惠劵信息", description = "临停缴费页面弹出的优惠劵信息")
public class CCBTempPayCoupon {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty("优惠劵名称")
	private String couponName;

	@ApiModelProperty("优惠劵类型")
	private Integer couponType;

	@ApiModelProperty(value = "满多少元")
	private BigDecimal fullAmount;

	@ApiModelProperty(value = "减多少元")
	private BigDecimal reduceAmount;

	@ApiModelProperty(value = "折扣数")
	private Double discountAmount;

	@ApiModelProperty(value = "优惠时长")
	private Integer reduceHour;

	@ApiModelProperty(value = "是否登录")
	private Boolean isLogin;


}
