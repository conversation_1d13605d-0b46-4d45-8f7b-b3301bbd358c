package com.lecent.park.vo;

import com.lecent.park.entity.UserCoupon;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户优惠劵视图实体类
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserCouponVO对象", description = "用户优惠劵")
public class UserCouponVO extends UserCoupon {
	private static final long serialVersionUID = 1L;

}
