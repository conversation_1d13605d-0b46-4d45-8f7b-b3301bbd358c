package com.lecent.park.scheduled;

import com.lecent.park.en.coupon.CouponStatusEnum;
import com.lecent.park.entity.UserCoupon;
import com.lecent.park.discount.coupon.service.IUserCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 优惠劵过期的定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponExpireScheduled {

	@Autowired
	private IUserCouponService userCouponService;

	/**
	 * 每隔5分钟执行一次
	 */
	@Scheduled(cron = "${lecent.park.coupon.expire-cron:0 0/5 * * * ?}")
	@RedisLock(value = "lecent:park::timedTask:lock:couponExpireScheduled")
	public void couponExpireScheduled() {

		//获取结束时间已到的待使用优惠劵
		List<UserCoupon> expireCouponList = userCouponService.getUnUsedExpireCoupon();
		if (Func.isEmpty(expireCouponList)) {
			return;
		}

		expireCouponList.forEach(c -> {
			c.setStatus(CouponStatusEnum.OVERDUE.getValue());
			c.setRemark("优惠劵已过期");
			c.setInvalidTime(DateUtil.now());
		});

		log.info("----------优惠劵批量设置过期----------------");
		//优惠劵批量过期
		userCouponService.updateBatchById(expireCouponList, expireCouponList.size());

	}


}
