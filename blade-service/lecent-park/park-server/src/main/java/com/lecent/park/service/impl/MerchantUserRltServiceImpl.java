package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.MerchantUserRlt;
import com.lecent.park.entity.ParkMerchant;
import com.lecent.park.mapper.MerchantUserRltMapper;
import com.lecent.park.service.IMerchantUserRltService;
import com.lecent.park.service.IParkMerchantService;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.BladePage;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.system.entity.Role;
import org.springblade.system.feign.IRoleAndDeptClient;
import org.springblade.system.user.dto.UserDTO;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.stereotype.Service;

/**
 * 酒店商户与blade_user用户关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class MerchantUserRltServiceImpl extends
	BaseServiceImpl<MerchantUserRltMapper, MerchantUserRlt> implements IMerchantUserRltService {
	private final IUserClient userClient;

	private final IParkMerchantService parkMerchantService;
	private IRoleAndDeptClient roleAndDeptClient;

	public MerchantUserRltServiceImpl(IUserClient userClient, IParkMerchantService parkMerchantService,IRoleAndDeptClient roleAndDeptClient) {
		this.userClient = userClient;
		this.parkMerchantService = parkMerchantService;
		this.roleAndDeptClient = roleAndDeptClient;
	}

	@Override
	public boolean addUser(User user) {
		BladeUser hotelAdmin = AuthUtil.getUser();
		// 使用酒店商户绑定的管理员账号查询停车商户
		ParkMerchant parkMerchant = parkMerchantService.getByUserId(hotelAdmin.getUserId());
		LecentAssert.notNull(parkMerchant, "关联商户不存在");
		Role hotelEmployeesRole = getHotelEmployeesRole(hotelAdmin.getTenantId());
		user.setTenantId(hotelAdmin.getTenantId());
		user.setDeptId(hotelAdmin.getDeptId());
		user.setRoleId(hotelEmployeesRole.getId().toString());
		// 设置商户ID
		user.setBusinessId(parkMerchant.getId());

		// 新增用户
		R<User> r = userClient.addUser(user);

		if (!r.isSuccess()) {
			throw new ServiceException(r.getMsg());
		}

		MerchantUserRlt rlt = new MerchantUserRlt();
		rlt.setMerchantId(parkMerchant.getId());
		rlt.setUserId(r.getData().getId());
		return save(rlt);
	}

	@Override
	public BladePage<User> empyInfo(IPage<User> page, User user) {
		String businessId = AuthUtil.getBusinessId();
		Long userId = AuthUtil.getUserId();
		String deptId = AuthUtil.getDeptId();
		String tenantId = AuthUtil.getTenantId();
		LecentAssert.notBlank(businessId, "非商户不允许查询");
		user.setBusinessId(Long.valueOf(businessId));
		user.setDeptId(deptId);
		user.setTenantId(tenantId);
		UserDTO userDTO = new UserDTO();
		BeanUtil.copy(user, userDTO);
		userDTO.setCurrent(page.getCurrent());
		userDTO.setSize(page.getSize());
		BladePage<User> userPage = userClient.queryUserPage(userDTO);
		if (userPage!=null) {
			userPage.setRecords(userPage.getRecords());
		}
		return userPage;
	}

	private Role getHotelEmployeesRole(String tenantId){
		String roleName = "酒店员工";
		String roleAlias = "hotelEpy";
		R<Role> hotelEpyR = roleAndDeptClient.getRole(tenantId, roleName);
		Role epyRole = hotelEpyR.getData();
		if (epyRole != null && epyRole.getId() != null){
			return epyRole;
		}
		// 新创建角色
		Role newRole = new Role();
		R<Role>  hotelMngR = roleAndDeptClient.getRole(tenantId, "酒店管理员");
		Role mngRole = hotelMngR.getData();
		LecentAssert.notNull(mngRole,"先添加酒店管理员账号");
		// 酒店员工
		newRole.setRoleAlias(roleAlias);
		newRole.setRoleName(roleName);
		// 父类是酒店管理员
		newRole.setParentId(mngRole.getId());
		newRole.setTenantId(tenantId);
		newRole.setSort(2);
		newRole.setIsDeleted(0);
		R<Role> roleR = roleAndDeptClient.addRole(newRole);
		return roleR.getData();
	}
}
