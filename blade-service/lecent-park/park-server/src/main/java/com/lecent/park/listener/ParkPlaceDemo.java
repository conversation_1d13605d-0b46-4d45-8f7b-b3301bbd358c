package com.lecent.park.listener;

import com.lecent.park.event.channeltodo.ReloadParkPlaceEvent;
import com.lecent.park.event.channeltodo.ReturnParkPlaceEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ParkPlaceDemo {
	@Autowired
	private ApplicationEventPublisher publisher;

	public void demo() {
		//编辑车位数量等 重新计算
		ReloadParkPlaceEvent reloadParkPlaceEvent = new ReloadParkPlaceEvent();
		publisher.publishEvent(reloadParkPlaceEvent);

		//归还车位
		ReturnParkPlaceEvent returnParkPlaceEvent = new ReturnParkPlaceEvent();
		returnParkPlaceEvent.setParkLotId(123466L);
		returnParkPlaceEvent.setParkPlaceNumber(5);

		publisher.publishEvent(returnParkPlaceEvent);

	}
}
