package com.lecent.park.controller.open.openh5;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.enums.card.CardStatusConstants;
import com.lecent.park.controller.open.openh5.req.ReqOnlineCard;
import com.lecent.park.controller.open.openh5.req.ReqQueryCard;
import com.lecent.park.controller.open.openh5.res.ResCard;
import com.lecent.park.controller.open.openh5.res.ResCardCategory;
import com.lecent.park.controller.open.openh5.res.ResOnlineCard;
import com.lecent.park.controller.open.openh5.res.ResOpenCard;
import com.lecent.park.dto.CardDTO;
import com.lecent.park.dto.UserSideDTO;
import com.lecent.park.entity.*;
import com.lecent.park.service.*;
import com.lecent.park.vo.CardOrderVO;
import com.lecent.park.vo.CardVO;
import com.lecent.park.vo.PayMonth;
import com.lecent.park.vo.UserSideCardVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 月卡开放接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = {
	OpenApiConstant.OPEN_API + "open-h5/card",
	OpenApiConstant.OPEN_API + "card/",
	// 测试环境免授权接口
	"test-open-api/open-h5/card"})
@Api(value = "月卡开放接口", tags = "月卡开放接口")
public class OpenH5CardController extends BladeController {

	@Autowired
	private ICardService cardService;

	@Autowired
	private ICardCategoryService cardCategoryService;

	@Autowired
	private UserSideService userSideService;

	@Autowired
	private IBaseAppConfigService baseAppConfigService;

	@Autowired
	private ICardOrderService orderService;

	@Autowired
	private IUserParklotService userParklotService;


	/**
	 * 月卡详情
	 */
	@PostMapping("/v1/cardByPlate")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "用户端查询月卡", notes = "用户端查询月卡")
	public R<List<ResCard>> cardByPlate(@RequestBody ReqQueryCard reqQueryCard) {
		LecentAssert.notBlank(reqQueryCard.getPlate(), "车牌不能为空");

		// 判断当前是否登录
		boolean isLogin = null != AuthUtil.getUserId();

		// 查询绑定车场信息,已登录未授权直接返回
		Set<Long> parkLotSetIds = userParklotService.getCurrentUserBindParkSetIds();
		if (isLogin && Func.isEmpty(parkLotSetIds)) {
			return R.data(Collections.emptyList());
		}
		List<CardVO> cards = cardService.listUserCard(reqQueryCard.getPlate());
		List<ResCard> resCards = new ArrayList<>();

		cards.forEach(card -> {
			// 已登录未授权车场不返回
			if (isLogin && !parkLotSetIds.contains(Func.toLongArray(card.getParklotIds())[0])) {
				return;
			}

			resCards.add(ResCard.builder()
				.id(card.getId())
				.no(card.getNo())
				.name(card.getRoomNum())
				.phone(card.getPhone())
				.parklotIds(card.getParklotIds())
				.parklotNames(card.getParklotNames())
				.categoryId(card.getCategoryId())
				.categoryName(card.getCategoryNames())
				.plate(card.getPlates())
				.startDate(card.getStartDate())
				.endDate(card.getEndDate())
				.firstPay(card.getFirstPay())
				.build());
		});
		return R.data(resCards);
	}

	/**
	 * 月卡详情
	 */
	@GetMapping("/v1/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "用户端月卡详情", notes = "用户端月卡详情")
	public R<UserSideCardVO> detail(ReqQueryCard reqQueryCard) {
		String cardId = reqQueryCard.getCardId();
		if (Func.isBlank(cardId)) {
			LecentAssert.notBlank(reqQueryCard.getPlate(), "车牌必填");
			LecentAssert.notNull(reqQueryCard.getParklotId(), "车场ID必填");

			String cardIds = cardService.selectAvailableCardIds(null, reqQueryCard.getParklotId(), reqQueryCard.getPlate());
			LecentAssert.notBlank(cardIds, "月卡信息不存在");
			cardIds = cardIds.replaceFirst(",", "");
			cardIds = cardIds.endsWith(",") ? cardIds.substring(0, cardIds.lastIndexOf(",")) : cardIds;
			cardId = String.valueOf(Func.toLong(cardIds));
		}

		UserSideCardVO result = cardService.userSideCardDetail(cardId);
		LecentAssert.notNull(result, "月卡信息不存在");

		if (null == result.getEndDate()) {
			result.setEndDate(DateUtil.toDate(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0)));
		}
		// get parkLot
		Parklot parklot = ParkLotCaches.getParkLot(Func.firstLong(result.getParklotIds()));
		if (null == parklot) {
			return R.data(result);
		}

		// 获取微信支付小程序配置
		BaseAppConfig wxScanMiniConfig = baseAppConfigService.getConfigById(parklot.getScanMimiId());
		if (null != wxScanMiniConfig) {
			result.setScanMimiId(wxScanMiniConfig.getJumpAppId());
			result.setScanMimiOriginalId(wxScanMiniConfig.getAppOriginalId());
		}

		// 获取支付宝小程序配置
		BaseAppConfig aliScanMiniConfig = baseAppConfigService.getConfigById(parklot.getScanAliMimiId());
		if (null != aliScanMiniConfig) {
			result.setScanAliMimiId(aliScanMiniConfig.getAppId());
		}

		return R.data(result);
	}

	/**
	 * 根据套餐ID获取缴费月份价格
	 */
	@PostMapping("/v1/renewalFee")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据套餐ID获取缴费月份价格", notes = "根据套餐ID获取缴费月份价格")
	public R<String> renewalFee(@Valid @RequestBody UserSideDTO userSideDto) {
		CardOrderVO cardOrder = userSideService.createRenewalFeeCardOrder(userSideDto);
		return R.data(cardOrder.getId().toString());
	}

	/**
	 * 根据套餐ID获取缴费月份价格
	 */
	@GetMapping("/v1/getRenewTimeDuration")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据套餐ID获取缴费月份价格", notes = "根据套餐ID获取缴费月份价格")
	public R<Map<String, String>> renewalFee(@RequestParam @NotNull Long categoryId,
											 @RequestParam @NotNull Integer monthNum,
											 @RequestParam String nextStartTime) {
		Date startTime = DateUtil.parse(nextStartTime, DateUtil.PATTERN_DATETIME);
		return R.data(cardService.getRenewTimeDuration(categoryId, startTime, monthNum, 1));
	}

	/**
	 * 根据套餐ID获取缴费月份价格
	 */
	@GetMapping("/v1/onlineOpenCategories")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据套餐ID获取缴费月份价格", notes = "根据套餐ID获取缴费月份价格")
	public R<ResOpenCard> onlineOpenCategories(Long parklotId) {
		List<CardCategory> cardCategories = cardCategoryService.onlineOpenCategories(parklotId);

		List<ResCardCategory> retList = new ArrayList<>(cardCategories.size());
		cardCategories.forEach(cardCategory -> {
			List<PayMonth> monthPrice = cardCategoryService.getMonthPrice(cardCategory);

			ResCardCategory resCardCategory = ResCardCategory.builder()
				.id(cardCategory.getId())
				.name(cardCategory.getName())
				.unitPrice(cardCategory.getUnitPrice())
				// 剩余可开卡数
				.surplusCardNum(cardCategory.getOnlineOpenCardNum() - cardCategory.getOnlineAlreadyOpenCardNum())
				.periodType(cardCategory.getPeriodType())
				.monthPrice(monthPrice)
				.build();
			retList.add(resCardCategory);
		});
		Parklot parkLot = ParkLotCaches.getParkLot(parklotId);
		return R.data(ResOpenCard.builder().parklotName(null == parkLot ? "" : parkLot.getName())
			.addr(parkLot == null ? "" : parkLot.getAddress())
			.startTime(DateUtil.format(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0), DateUtil.PATTERN_DATETIME))
			.cardCategories(retList).build());
	}

	/**
	 * 新开月卡检查月卡是否存在
	 */
	@PostMapping("/v1/checkPlate")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新开月卡检查月卡是否存在", notes = "新开月卡检查月卡是否存在")
	public R<Boolean> onlineNewMonthlyCardPay(ReqQueryCard reqQueryCard) {
		String cardIds = cardService.selectAvailableCardIds(null, reqQueryCard.getParklotId(), reqQueryCard.getPlate());
		if (Func.isBlank(cardIds)) {
			return R.data(Boolean.FALSE);
		}
		cardIds = cardIds.replaceFirst(",", "");
		cardIds = cardIds.endsWith(",") ? cardIds.substring(0, cardIds.lastIndexOf(",")) : cardIds;
		return R.data(Func.isBlank(cardIds) ? Boolean.FALSE : Boolean.TRUE);
	}

	/**
	 * 新开月卡并续费支付
	 */
	@Transactional(rollbackFor = {Exception.class})
	@PostMapping("/v1/onlineNewMonthlyCardPay")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新开月卡并续费支付", notes = "新开月卡并续费支付")
	public R<ResOnlineCard> onlineNewMonthlyCardPay(@RequestBody ReqOnlineCard reqOnlineCard) {
		// get parkLot
		Parklot parklot = ParkLotCaches.existParkLot(reqOnlineCard.getParklotId());

		CardDTO cardDTO = new CardDTO();
		cardDTO.setCategoryId(reqOnlineCard.getCategoryId());
		cardDTO.setRoomNum(reqOnlineCard.getOwnerName());
		cardDTO.setParklotIds(String.valueOf(reqOnlineCard.getParklotId()));
		cardDTO.setPlates(reqOnlineCard.getPlate());
		cardDTO.setUserOpenId(reqOnlineCard.getUserId());
		cardDTO.setPhone(reqOnlineCard.getPhone());
		R r = cardService.saveCard(cardDTO);
		if (r.getMsg().contains("已成功发起审批")) {
			return R.data(null, r.getMsg());
		}

		UserSideDTO userSideDto = new UserSideDTO();
		userSideDto.setCardId(String.valueOf(cardDTO.getId()));
		userSideDto.setRenewalFeeNum(reqOnlineCard.getRenewalFeeNum());
		userSideDto.setRenewalFeeMount(reqOnlineCard.getRenewalFeeMount());
		userSideDto.setReChargeType(1);
		userSideDto.setUserId(reqOnlineCard.getUserId());

		// 发起续费订单
		CardOrderVO renewalFeeCardOrder = userSideService.createRenewalFeeCardOrder(userSideDto);

		// 线上开卡是注销状态
		Card upCard = new Card();
		upCard.setId(cardDTO.getId());
		upCard.setStatus(CardStatusConstants.LOG_OFF);
		cardService.updateById(upCard);

		String scanMimiId = null;
		String scanMimiOriginalId = null;
		String scanAliMimiId = null;
		// 获取微信支付小程序配置
		BaseAppConfig wxScanMiniConfig = baseAppConfigService.getConfigById(parklot.getScanMimiId());
		if (null != wxScanMiniConfig) {
			scanMimiId = wxScanMiniConfig.getJumpAppId();
			scanMimiOriginalId = wxScanMiniConfig.getAppOriginalId();
		}

		// 获取支付宝小程序配置
		BaseAppConfig aliScanMiniConfig = baseAppConfigService.getConfigById(parklot.getScanAliMimiId());
		if (null != aliScanMiniConfig) {
			scanAliMimiId = aliScanMiniConfig.getAppId();
		}
		return R.data(ResOnlineCard.builder().orderId(renewalFeeCardOrder.getId())
			.scanAliMimiId(scanAliMimiId)
			.scanMimiId(scanMimiId)
			.scanMimiOriginalId(scanMimiOriginalId)
			.build());
	}

	/**
	 * 根据月卡ID查询是否支付成功
	 */
	@GetMapping("/v1/paySuccess")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "根据月卡ID查询是否支付成功", notes = "orderId")
	public R<Boolean> paySuccess(@RequestParam("orderId") Long orderId) {
		CardOrder cardOrder = orderService.getById(orderId);
		LecentAssert.notNull(cardOrder, "订单信息不存在");

		return R.data(Integer.valueOf(1).equals(cardOrder.getPayStatus()));
	}
}
