package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.api.client.util.Lists;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.common.constant.AvailableTimeTypeEnum;
import com.lecent.park.discount.coupon.service.ICouponService;
import com.lecent.park.discount.coupon.service.IUserCouponService;
import com.lecent.park.discount.coupon.wrapper.CouponWrapper;
import com.lecent.park.dto.third.ThirdCouponDTO;
import com.lecent.park.en.coupon.CouponProviderTypeEnum;
import com.lecent.park.en.coupon.CouponStatusEnum;
import com.lecent.park.en.coupon.CouponTypeEnum;
import com.lecent.park.entity.Coupon;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.entity.UserCoupon;
import com.lecent.park.discount.coupon.mapper.UserCouponMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.third.ThirdCouponVO;
import com.lecent.park.vo.third.ThirdCouponVerificationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.system.user.entity.CUser;
import org.springblade.system.user.feign.ICUserClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 第三方优惠劵 服务实现类
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ThirdCouponServiceImpl extends BaseServiceImpl<UserCouponMapper, UserCoupon> implements IThirdCouponService {

	@Value("${lecent.park.xingli_parklotNo:1739}")
	private String xlParklotNo;

	private final IUserCouponService userCouponService;
	private final ICouponService couponService;
	private final IParklotService parklotService;
	private final ICUserClient icUserClient;
	private final IBUserPlateService userPlateService;


	@Override
	@Transactional(rollbackFor = Exception.class)
	@RedisLock(value = ParkCacheNames.PARK_THIRD_COUPON, param = "#thirdCouponDTO.couponNo")
	public boolean thirdSendCoupon(ThirdCouponDTO thirdCouponDTO) {

		checkParams(thirdCouponDTO);

		String parklotNo = thirdCouponDTO.getParklotNo();
		Parklot parklot = parklotService.getOne(Wrappers.<Parklot>lambdaQuery().eq(Parklot::getParklotNo, parklotNo));
		LecentAssert.notNull(parklot, "车场编号：" + parklotNo + "没有找到对应的车场！");


		Coupon coupon = couponService.getByNameAndProvider(thirdCouponDTO.getCouponName(), CouponProviderTypeEnum.XING_LI.getValue());
		if (Objects.isNull(coupon)) {
			coupon = CouponWrapper.build().thirdDTO2Entity(thirdCouponDTO, parklot.getId());
			coupon.setProviderType(CouponProviderTypeEnum.XING_LI.getValue());
			coupon.setTenantId(parklot.getTenantId());
			coupon.setCreateUser(0L);
			coupon.setCreateDept(0L);
			coupon.setUpdateUser(0L);
			couponService.save(coupon);
		}

		return thirdSave(coupon, thirdCouponDTO, parklot.getTenantId());
	}

	@Override
	public boolean thirdSave(Coupon coupon, ThirdCouponDTO thirdCouponDTO, String tenantId) {

		//提前注册第三方用户
		R<CUser> userResult = icUserClient.advanceRegister(thirdCouponDTO.getMobile(), tenantId);
		LecentAssert.isTrue(userResult.isSuccess(), "调用用户服务异常");
		Long userId = userResult.getData().getId();

		//提前给第三方用户绑定车牌
		userPlateService.advanceBind(thirdCouponDTO.getPlates(), thirdCouponDTO.getMobile());

		UserCoupon userCoupon = new UserCoupon();
		userCoupon.setRemark(coupon.getRemark());
		if (AvailableTimeTypeEnum.DURATION.getValue().equals(thirdCouponDTO.getAvailableTimeType())) {
			userCoupon.setStartDate(coupon.getStartDate());
			userCoupon.setEndDate(coupon.getEndDate());
		} else {
			userCoupon.setStartDate(DateUtil.now());
			userCoupon.setEndDate(DateUtil.plusDays(DateUtil.now(), thirdCouponDTO.getAvailableDays()));
		}
		userCoupon.setCouponNo(thirdCouponDTO.getCouponNo());
		userCoupon.setProviderType(coupon.getProviderType());
		userCoupon.setUserId(userId);
		userCoupon.setCouponSourceUserId(0L);
		userCoupon.setActivityId(0L);
		userCoupon.setTenantId(tenantId);
		userCoupon.setCouponId(coupon.getId());

		return this.save(userCoupon);
	}

	@Override
	@Async
	public void verificationCoupon(TempParkingOrder tempParkingOrder) {
		String couponId = tempParkingOrder.getCouponIds();

		UserCoupon userCoupon = this.getById(Long.valueOf(couponId));
		if (Objects.isNull(userCoupon)) {
			return;
		}
		//非第三方优惠劵，不需要核销
		if (CouponProviderTypeEnum.CCB.getValue() == userCoupon.getProviderType()) {
			return;
		}
		//todo 核销优惠劵
		String tradeNo = tempParkingOrder.getTradeNo();
		String plate = tempParkingOrder.getPlate();
		BigDecimal couponDiscountAmount = tempParkingOrder.getCouponDiscountAmount();

		//核销成功，将优惠劵改为已核销
		update(Wrappers.<UserCoupon>lambdaUpdate()
			.set(UserCoupon::getHasVerification, Boolean.TRUE)
			.eq(UserCoupon::getId, couponId));


	}

	@Override
	public List<ThirdCouponVerificationVO> unVerificationCoupons(String parklotNo) {

		LecentAssert.isTrue(xlParklotNo.equals(parklotNo), "车场编号不正确！");

		List<ThirdCouponVerificationVO> result = Lists.newArrayList();

		//查询星力集团已使用未稽核的优惠劵
		List<UserCoupon> unVerificationCoupons = this.list(Wrappers.<UserCoupon>lambdaQuery()
			.select(UserCoupon::getId, UserCoupon::getUserId, UserCoupon::getCouponNo, UserCoupon::getUseTime)
			.eq(UserCoupon::getStatus, CouponStatusEnum.USED.getValue())
			.eq(UserCoupon::getProviderType, CouponProviderTypeEnum.XING_LI.getValue())
			.eq(UserCoupon::getHasVerification, Boolean.FALSE));

		if (CollectionUtil.isEmpty(unVerificationCoupons)) {
			return result;
		}

		for (UserCoupon uncheckOffCoupon : unVerificationCoupons) {
			ThirdCouponVerificationVO thirdCouponVerificationVO = new ThirdCouponVerificationVO();
			thirdCouponVerificationVO.setCouponNo(uncheckOffCoupon.getCouponNo());
			if (Objects.nonNull(uncheckOffCoupon.getUseTime())) {
				thirdCouponVerificationVO.setUseTime(DateUtil.formatDateTime(uncheckOffCoupon.getUseTime()));
			}
			R<CUser> r = icUserClient.getById(uncheckOffCoupon.getUserId());
			if (r.isSuccess() && Objects.nonNull(r.getData())) {
				thirdCouponVerificationVO.setMobile(r.getData().getPhone());
			}
			result.add(thirdCouponVerificationVO);
		}

		return result;
	}

	@Override
	public ThirdCouponVO verificationDetail(String couponNo) {
		UserCoupon userCoupon = userCouponService.getByCouponNo(couponNo);
		LecentAssert.notNull(userCoupon, "优惠劵编号：" + couponNo + "不存在，请检查后重试！");


		return ThirdCouponVO.builder()
			.couponNo(couponNo)
			.hasVerification(userCoupon.getHasVerification())
			.status(userCoupon.getStatus())
			.build();
	}

	/**
	 * 参数校验
	 *
	 * @param thirdCouponDTO 请求参数
	 */
	private void checkParams(ThirdCouponDTO thirdCouponDTO) {

		//手动校验参数
		ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
		Validator validator = validatorFactory.getValidator();
		Set<ConstraintViolation<ThirdCouponDTO>> validate = validator.validate(thirdCouponDTO);
		if (CollectionUtil.isNotEmpty(validate)) {
			throw new ServiceException(validate.iterator().next().getMessage());
		}

		LecentAssert.isTrue(xlParklotNo.equals(thirdCouponDTO.getParklotNo()), "车场编号不正确！");

		if (CouponTypeEnum.CASH.getValue().equals(thirdCouponDTO.getCouponType())) {

			LecentAssert.isTrue(Objects.nonNull(thirdCouponDTO.getFullAmount()) && Objects.nonNull(thirdCouponDTO.getReduceAmount()),
				"优惠劵类型为满减劵时，满减金额不能为空");
			LecentAssert.isTrue(thirdCouponDTO.getFullAmount().compareTo(thirdCouponDTO.getReduceAmount()) > 0,
				"优惠劵类型为满减劵时，减的金额不能大于满的金额");
		}

		if (CouponTypeEnum.DISCOUNT.getValue().equals(thirdCouponDTO.getCouponType())) {
			LecentAssert.isTrue(Objects.nonNull(thirdCouponDTO.getDiscountAmount()),
				"优惠劵类型为折扣劵时，折扣率不能为空");
		}

		if (CouponTypeEnum.TIME_LENGTH.getValue().equals(thirdCouponDTO.getCouponType())) {
			LecentAssert.isTrue(Objects.nonNull(thirdCouponDTO.getReduceHour()),
				"优惠劵类型为时长劵时，抵扣时长不能为空");
		}

		if (AvailableTimeTypeEnum.DAYS.getValue().equals(thirdCouponDTO.getAvailableTimeType())) {
			LecentAssert.isTrue(Objects.nonNull(thirdCouponDTO.getAvailableDays()),
				"优惠劵有效期类型为天数时，有效期天数不能为空");
			LecentAssert.isTrue(thirdCouponDTO.getAvailableDays() > 0,
				"优惠劵有效期类型为天数时，有效期天数不能小于等于0");
		}

		if (AvailableTimeTypeEnum.DURATION.getValue().equals(thirdCouponDTO.getAvailableTimeType())) {

			LecentAssert.isTrue(Objects.nonNull(thirdCouponDTO.getStartDate()) && Objects.nonNull(thirdCouponDTO.getEndDate()),
				"优惠劵有效期类型为时间段时，开始结束时间不能为空");

			LecentAssert.isTrue(thirdCouponDTO.getStartDate().getTime() < thirdCouponDTO.getEndDate().getTime(),
				"优惠劵有效期类型为时间段时，开始时间不能小于结束时间");

			LecentAssert.isTrue(thirdCouponDTO.getEndDate().getTime() > System.currentTimeMillis(),
				"优惠劵有效期类型为时间段时，结束时间不能小于当前时间");
		}

		Integer count = userCouponService.countByCouponNo(thirdCouponDTO.getCouponNo());
		LecentAssert.isTrue(count == 0, "优惠劵编号:" + thirdCouponDTO.getCouponNo() + "已存在！");
	}

}
