package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.CarBrand;
import com.lecent.park.vo.CarBrandVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface ICarBrandService extends BaseService<CarBrand> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param carBrand
	 * @return
	 */
	IPage<CarBrandVO> selectCarBrandPage(IPage<CarBrandVO> page, CarBrandVO carBrand);

	List<CarBrand> searchCarBrand(CarBrand carBrand);
}
