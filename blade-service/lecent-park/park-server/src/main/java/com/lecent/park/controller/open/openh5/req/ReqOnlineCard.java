package com.lecent.park.controller.open.openh5.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReqOnlineCard {
	/**
	 * 月卡所有人
	 */
	@NotBlank(message = "月卡所有人必填")
	private String ownerName;

	/**
	 * 手机号码
	 */
	@NotBlank(message = "手机号码必填")
	private String phone;

	/**
	 * 车牌
	 */
	@NotBlank(message = "车牌必填")
	private String plate;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 月卡开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date cardStartTime;

	/**
	 * 车场ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@NotNull(message = "车场ID必填")
	private Long parklotId;

	/**
	 * 套餐ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@NotNull(message = "月卡套餐ID必填")
	private Long categoryId;

	/**
	 * 缴费数量  （单位 月）
	 */
	@Min(value = 1, message = "缴费数量最小值为1")
	private Integer renewalFeeNum;


	/**
	 * 缴费金額  （单位 元）
	 */
	@DecimalMin(value = "0.01", message = "缴费金額最小值为0.01元")
	private BigDecimal renewalFeeMount;
	/**
	 * 渠道
	 */
	private String source;

	/**
	 * 下单人ID
	 */
	private String userId;
}
