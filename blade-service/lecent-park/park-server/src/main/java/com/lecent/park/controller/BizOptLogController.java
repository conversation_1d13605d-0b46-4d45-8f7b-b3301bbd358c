package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.BizOptLog;
import com.lecent.park.service.IBizOptLogService;
import com.lecent.park.vo.BizOptLogVO;
import com.lecent.park.wrapper.BizOptLogWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 业务操作日志表 控制器
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/biz-opt-log")
@Api(value = "业务操作日志表", tags = "业务操作日志表接口")
public class BizOptLogController extends BladeController {

	private IBizOptLogService bizOptLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入bizOptLog")
	public R<BizOptLogVO> detail(BizOptLog bizOptLog) {
		BizOptLog detail = bizOptLogService.getOne(Condition.getQueryWrapper(bizOptLog));
		return R.data(BizOptLogWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 业务操作日志表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入bizOptLog")
	public R<IPage<BizOptLogVO>> list(BizOptLog bizOptLog, Query query) {
		IPage<BizOptLog> pages = bizOptLogService.page(Condition.getPage(query), Condition.getQueryWrapper(bizOptLog));
		return R.data(BizOptLogWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 业务操作日志表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入bizOptLog")
	public R<IPage<BizOptLogVO>> page(BizOptLogVO bizOptLog, Query query) {
		IPage<BizOptLogVO> pages = bizOptLogService.selectBizOptLogPage(Condition.getPage(query), bizOptLog);
		return R.data(pages);
	}

	/**
	 * 新增 业务操作日志表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入bizOptLog")
	public R save(@Valid @RequestBody BizOptLog bizOptLog) {
		return R.status(bizOptLogService.save(bizOptLog));
	}

	/**
	 * 修改 业务操作日志表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入bizOptLog")
	public R update(@Valid @RequestBody BizOptLog bizOptLog) {
		return R.status(bizOptLogService.updateById(bizOptLog));
	}

	/**
	 * 新增或修改 业务操作日志表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入bizOptLog")
	public R submit(@Valid @RequestBody BizOptLog bizOptLog) {
		return R.status(bizOptLogService.saveOrUpdate(bizOptLog));
	}


	/**
	 * 删除 业务操作日志表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(bizOptLogService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 操作日志导出
	 */
	@PostMapping("/export")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "操作日志导出")
	public void export(BizOptLogVO bizOptLog, HttpServletResponse response) {
		bizOptLogService.export(bizOptLog, response);
	}

}
