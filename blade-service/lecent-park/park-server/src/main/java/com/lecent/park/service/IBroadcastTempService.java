package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.BroadcastTemp;
import com.lecent.park.vo.BroadcastTempVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 设置设置表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
public interface IBroadcastTempService extends BaseService<BroadcastTemp> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceSetting
	 * @return
	 */
	IPage<BroadcastTempVO> selectBroadcastTempPage(IPage<BroadcastTempVO> page, BroadcastTempVO deviceSetting);

}
