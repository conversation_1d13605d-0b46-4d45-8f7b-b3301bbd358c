package com.lecent.park.listener;

import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.event.IASyncDataForwardingEvent;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.service.IChannelTodoService;
import com.lecent.park.service.IParkingOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

/**
 * 车场通道消息数据处理监听服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableAsync
public class ChannelDataReceiveListener {
	/**
	 * 车场待办服务
	 */
	@Autowired
	private IChannelTodoService channelTodoService;
	@Autowired
	private IParkingOrderService parkingOrderService;
	/**
	 * 接收车牌识别结果(正常抓拍)
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.ChannelCommandConstant).NORMAL_SNAP" +
		" or " +
		"#event.commands==T(com.lecent.park.device.constant.ChannelCommandConstant).AUTO_SNAP")
	public void receivedPlateResult(IASyncDataForwardingEvent event) {
		ParkChannelMessageEvent message = (ParkChannelMessageEvent) event;
		log.info("解析到上报数据：" + message.toString());
		try {
			message.setTriggerType(ChannelWay.WAY_1.getValue());
			message.setRemark("MQ方式调用");
			channelTodoService.triggerCreateTodo(message);
		} catch (Exception e) {
			log.error("ChannelDataReceiveListener.receivedPlateResult", e);
		}

	}
	/**
	 * 接收 地杆数据
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.ChannelCommandConstant).PASS_COIL")
	public void passCoil(IASyncDataForwardingEvent event) {
		ParkChannelMessageEvent message = (ParkChannelMessageEvent) event;
		log.info("解析到通过地杆上报数据：" + message.toString());
		try {
			if (Func.isEmpty(message.getParkLotId())) {
				message.setParkLotId(message.getParkingId());
			}
			parkingOrderService.confirmEnter(message.getParkLotId(), message.getChannelNo());
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * 接收车牌识别结果(正常抓拍)
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.ChannelCommandConstant).URL_UPLOAD")
	public void asyncUploadPicture(IASyncDataForwardingEvent event) {
		ParkChannelMessageEvent message = (ParkChannelMessageEvent) event;
		log.info("解析到上报数据：" + message.toString());
		try {
			channelTodoService.asyncUploadPicture(message);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}
}
