package com.lecent.park.strategy.cardperiod.abstracts;


import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.entity.CardCategoryDiscount;
import com.lecent.park.service.ICardCategoryDiscountService;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.CardDurationVO;
import org.springblade.common.utils.LecentAssert;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import static com.lecent.park.strategy.cardperiod.CardPeriodHelper.calculateStartDate;
import static com.lecent.park.strategy.cardperiod.CardPeriodHelper.calculateTotalAmount;


/**
 * <AUTHOR>
 * 月卡续费公共方法
 */
@Component
public abstract class AbstractCardPeriodService {

	@Resource
	private ICardCategoryDiscountService cardCategoryDiscountService;


	/**
	 * 构造月卡续费信息
	 *
	 * @param startDate 开始时间
	 * @param num       续费月份/天数
	 * @param unitPrice 套餐单价
	 * @return 月卡续费信息
	 */
	protected CardDurationVO buildCardDuration(Date startDate, Date endDate, int num, BigDecimal unitPrice) {

		return CardDurationVO.builder()
			.startDate(startDate)
			.endDate(endDate)
			.amount(calculateTotalAmount(unitPrice, num))
			.build();
	}

	/**
	 * @param startDate 开始时间
	 * @param card      月卡
	 * @param cardCategory
	 * @return 新的开始时间
	 */
	protected Date getStartDate(Date startDate, Card card, CardCategory cardCategory) {

		LecentAssert.notNull(startDate, "请选择开始时间");

		//首次续费
		if (Objects.isNull(card.getEndDate()) || cardCategory.getCustomStartTimeEnabled()) {
			return DateUtils.getStartTimeOfDay(startDate);
		}

		return calculateStartDate(card.getEndDate());
	}

	/**
	 * @param monthNum     续费月份
	 * @param cardCategory 套餐单价
	 * @return 套餐单价
	 */
	protected BigDecimal getUnitPrice(int monthNum, CardCategory cardCategory) {

		//折扣单价
		CardCategoryDiscount cardCategoryDiscount = cardCategoryDiscountService.discountByDuration(monthNum, cardCategory.getId());
		if (Objects.nonNull(cardCategoryDiscount)) {
			return cardCategoryDiscount.getPrice();
		}

		//原始单价
		return cardCategory.getUnitPrice();
	}
}
