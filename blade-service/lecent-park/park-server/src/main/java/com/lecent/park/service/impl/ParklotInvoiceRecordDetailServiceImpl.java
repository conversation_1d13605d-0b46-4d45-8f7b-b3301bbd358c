package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lecent.park.entity.ParklotInvoiceRecordDetail;
import com.lecent.park.mapper.ParklotInvoiceRecordDetailMapper;
import com.lecent.park.service.IParklotInvoiceRecordDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 发票明细服务
 */
@Service
@Slf4j
public class ParklotInvoiceRecordDetailServiceImpl extends ServiceImpl<ParklotInvoiceRecordDetailMapper, ParklotInvoiceRecordDetail> implements IParklotInvoiceRecordDetailService {

	@Override
	public List<ParklotInvoiceRecordDetail> getRecordDetailList(Long recordId) {
		return this.list(Wrappers.<ParklotInvoiceRecordDetail>lambdaQuery().eq(ParklotInvoiceRecordDetail::getRecordId, recordId));
	}

	@Override
	public List<ParklotInvoiceRecordDetail> isIncludeOrder(List<Long> orderIds, Integer type) {
		if (orderIds.isEmpty()) {
			return new ArrayList<>();
		}
		return this.list(Wrappers.<ParklotInvoiceRecordDetail>lambdaQuery().eq(ParklotInvoiceRecordDetail::getOrderType, type).eq(ParklotInvoiceRecordDetail::getIsDeleted, 0).in(ParklotInvoiceRecordDetail::getOrderId, orderIds));
	}

	@Override
	public Boolean updateBatchBySerialNum(List<ParklotInvoiceRecordDetail> details) {
		if (Func.isEmpty(details)) {
			return false;
		}
		return baseMapper.updateBatchBySerialNum(details);
	}
}
