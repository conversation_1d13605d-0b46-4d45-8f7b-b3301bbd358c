package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.Inspection;
import com.lecent.park.service.IInspectionService;
import com.lecent.park.vo.InspectionVO;
import com.lecent.park.wrapper.InspectionWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.resource.constant.SmsConstant;
import org.springblade.resource.feign.ISmsClient;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 巡检记录表 控制器
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/inspection")
@Api(value = "巡检记录表", tags = "巡检记录表接口")
public class InspectionController extends BladeController {

	private IInspectionService inspectionService;
	private ISmsClient smsClient;
	private IUserClient userClient;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入inspection")
	public R<InspectionVO> detail(Inspection inspection) {
		Inspection detail = inspectionService.getOne(Condition.getQueryWrapper(inspection));
		return R.data(InspectionWrapper.build().entityVO(detail));
	}

	/**
	 * 详情
	 */
	@GetMapping("/getDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入inspection")
	public R<InspectionVO> getDetail(InspectionVO inspection) {
		Query query = new Query();
		query.setCurrent(1);
		query.setSize(1);
		IPage<InspectionVO> pages = inspectionService.selectInspectionPage(Condition.getPage(query), inspection);
		InspectionVO inspectionVO = pages.getRecords().get(0);
		return R.data(InspectionWrapper.build().entityVO(inspectionVO));
	}


	/**
	 * 分页 巡检记录表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入inspection")
	public R<IPage<InspectionVO>> list(Inspection inspection, Query query) {
		IPage<Inspection> pages = inspectionService.page(Condition.getPage(query), Condition.getQueryWrapper(inspection));
		return R.data(InspectionWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 巡检记录表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "自定义分页", notes = "传入inspection")
	public R<IPage<InspectionVO>> page(InspectionVO inspection, Query query) {
		IPage<InspectionVO> pages = inspectionService.selectInspectionPage(Condition.getPage(query), inspection);
		return R.data(pages);
	}

	/**
	 * 新增 巡检记录表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入inspection")
	public R save(@Valid @RequestBody Inspection inspection) {
		return R.status(inspectionService.save(inspection));
	}

	/**
	 * 修改 巡检记录表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入inspection")
	public R update(@Valid @RequestBody Inspection inspection) {
		return R.status(inspectionService.updateById(inspection));
	}

	/**
	 * 新增或修改 巡检记录表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入inspection")
	public R submit(@Valid @RequestBody Inspection inspection) {
		return R.status(inspectionService.saveOrUpdate(inspection));
	}


	/**
	 * 删除 巡检记录表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(inspectionService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 根据通道获取可巡检的设备
	 */
	@GetMapping("/inspectionChannelList")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "根据通道获取可巡检的设备", notes = "channelId")
	public R inspectionChannelList(@ApiParam(value = "通道id", required = true) @NotNull(message = "id 不能为空") Long channelId) {
		return R.data(inspectionService.inspectionChannelList(channelId));
	}


	/**
	 * 完成巡检并上报
	 */
	@PostMapping("/report")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "完成巡检并上报", notes = "传入inspection")
	public R report(@Valid @RequestBody List<InspectionVO> inspections) {
		return inspectionService.report(inspections);
	}


	/**
	 * 更改手机号发送验证码
	 */
	@GetMapping("/sendValidate")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "更改手机号发送验证码", notes = "传入手机号码")
	public R sendValidate(@Valid @ApiParam(value = "密码", required = true) String passWord,
						  @ApiParam(value = "手机号", required = true) String phone) {
		R<User> userR = userClient.userInfoById(SecureUtil.getUserId());
		User user = userR.getData();
		if (!user.getPassword().equals(DigestUtil.encrypt(passWord))) {
			return R.data(1, "", "密码不正确!");
		} else {
			return smsClient.sendValidate(SmsConstant.SEND_VALIDATE, phone);
		}
	}

	/**
	 * 校验手机验证码
	 */
	@GetMapping("/calibration")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "校验手机验证码", notes = "传入id 验证码 手机号")
	public R calibration(@Valid @ApiParam(value = "id", required = true) String id,
						 @ApiParam(value = "验证码", required = true) String value,
						 @ApiParam(value = "手机号", required = true) String phone) {
		Integer integer = userClient.userInfoByPhone(SecureUtil.getTenantId(), phone);
		if (Func.isNull(integer)) {
			return R.data(1, "", "该手机号已被注册绑定到其他用户");
		}
		R sms = smsClient.validateMessage(SmsConstant.SEND_VALIDATE, phone, id, value);
		if (!sms.isSuccess()) {
			return R.data(0, "", "验证码有误！");
		}
		boolean b = false;
		R<User> userR = userClient.userInfoById(SecureUtil.getUserId());
		User user = userR.getData();
		user.setPhone(phone);
		b = userClient.updateInfo(user);
		return R.status(b);
	}

	/**
	 * 巡检人列表
	 */
	@GetMapping("/inspectionUserList")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "巡检人列表")
	public R inspectionUserList() {
		return R.data(inspectionService.inspectionUserList());
	}

}
