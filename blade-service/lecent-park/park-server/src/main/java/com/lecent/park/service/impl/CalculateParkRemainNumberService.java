package com.lecent.park.service.impl;

import com.lecent.park.device.common.ChannelCommandBuilder;
import com.lecent.park.device.utils.GateActionUtils;
import com.lecent.park.entity.Parklot;
import com.lecent.park.event.channeltodo.ReloadParkPlaceEvent;
import com.lecent.park.event.channeltodo.ReturnParkPlaceEvent;
import com.lecent.park.listener.ParkRemainNumberListener;
import com.lecent.park.service.ICalculateParkRemainNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import static com.lecent.park.cache.ParkCacheNames.LECENT_PARKPLACE_INIT_FLAG;
import static com.lecent.park.cache.ParkCacheNames.LECENT_PARK_REMAIN_NUMBER;

/**
 * 停车场剩余车位服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableAsync
public class CalculateParkRemainNumberService implements ICalculateParkRemainNumberService {


	/**
	 * 预定车位脚本 （保证整个判断原子性）
	 */
	private final String bookLockScript =
		"    local remain  =tonumber(redis.call('DECRBY',KEYS[1],tonumber(ARGV[1])))\n" +
			"    if(remain>=0) then\n" +
			"       return remain\n" +
			"    else " +
			"       redis.call('INCRBY',KEYS[1],tonumber(ARGV[1]))" +
			"    end\n" +
			"    return -1\n";
	/**
	 * spring事件发布器
	 */
	@Autowired
	private ApplicationEventPublisher publisher;
	/**
	 * redis工具类
	 */
	@Autowired
	private BladeRedis bladeRedis;

	@Override
	public int getParkRemainNumber(Long parkLotId) {
		try {
			int number = Func.toInt(bladeRedis.get(LECENT_PARK_REMAIN_NUMBER.concat("::").concat(String.valueOf(parkLotId))));
			if (number <= 0) {
				ParkRemainNumberListener parkRemainNumberListener = SpringUtil.getBean("parkRemainNumberListener");
				number = parkRemainNumberListener.calculateParkRemainNumber(parkLotId);
			}
			return number;
		} catch (Exception e) {
			log.error("车位获取异常:{}", e.getMessage());
		}
		return 0;
	}

	@Override
	public Integer getRealTimeParkRemainNumber(Long parkLotId) {
		try {
			ParkRemainNumberListener parkRemainNumberListener = SpringUtil.getBean("parkRemainNumberListener");
			return parkRemainNumberListener.calculateParkRemainNumber(parkLotId);
		} catch (Exception e) {
			log.error("车位获取异常:{}", e.getMessage());
		}
		return 0;
	}

	@Override
	public Boolean parkPlaceBook(Parklot parklot, Integer parkPlaceNumber) {
		try {
			Long remainNumber = this.decrNumber(parkPlaceNumber,parklot);
			ParkRemainNumberListener parkRemainNumberListener = SpringUtil.getBean("parkRemainNumberListener");
			if (remainNumber <= 0) {
				log.info("预定{}个车位失败，当前车位不足!", parkPlaceNumber);
				parkRemainNumberListener.calculateParkRemainNumber(parklot.getId());
				 remainNumber = this.decrNumber(parkPlaceNumber,parklot);
				log.info("剩余车位为0，重新计算后车位数={}",remainNumber);

			}

			Long pushNumber = remainNumber < 0 ? 0 : remainNumber;
			String parkingStr = parklot.getFuzzyDisplay() ? "车位充足" : "剩余车位数"+pushNumber;
			String parkNumberStr = ChannelCommandBuilder.builderRemainParkNumber(pushNumber.intValue(), parkingStr);
			GateActionUtils.execCommandAndVoice(parklot.getId(), parkNumberStr);
			log.info("下发车位数到一体机，parkLotId={},下发内容:{}", parklot.getId(), parkNumberStr);
			parkRemainNumberListener.asyncCalculateBootScreenInfo(parklot.getId(), pushNumber);


			return remainNumber >= 0;
		} catch (Exception e) {
			log.error("车位预定异常：{}", e.getMessage());
			e.printStackTrace();
			//TODO 锁定车位不可用
			ReloadParkPlaceEvent reloadParkPlaceEvent = new ReloadParkPlaceEvent();
			reloadParkPlaceEvent.setParkLotId(parklot.getId());
			publisher.publishEvent(reloadParkPlaceEvent);
			return false;
		}
	}

	private Long decrNumber(Integer parkPlaceNumber, Parklot parklot) {
		String key = LECENT_PARK_REMAIN_NUMBER.concat("::").concat(String.valueOf(parklot.getId()));
		Object o = bladeRedis.get(key);
		if (Func.isNotEmpty(o)){
			long l = Func.toLong(o) - parkPlaceNumber;
			bladeRedis.set(key,l);
			return l;
		}
		return -1L;
	}

	@Override
	public Boolean returnParkPlaceBook(ReturnParkPlaceEvent event) {
		try {
			Long remainNumber = this.incrNumber(event);

			remainNumber = remainNumber < 0 ? 0 : remainNumber;

			String parkingStr = event.getFuzzyDisplay() ? "车位充足" : "剩余车位数"+remainNumber;
			String parkNumberStr = ChannelCommandBuilder.builderRemainParkNumber(remainNumber.intValue(), parkingStr);
			GateActionUtils.execCommandAndVoice(event.getParkLotId(), parkNumberStr);
			log.info("下发车位数到一体机，parkLotId={},下发内容:{}", event.getParkLotId(), parkNumberStr);

			ParkRemainNumberListener parkRemainNumberListener = SpringUtil.getBean("parkRemainNumberListener");
			parkRemainNumberListener.asyncCalculateBootScreenInfo(event.getParkLotId(), remainNumber);
			return true;
		} catch (Exception e) {
			//还车位失败不会出现多定  允许继续预定 但是需要重新计算车位
			log.error("归还车位异常：{}", e.getMessage());
			e.printStackTrace();
			//归还车位异常需要重新计算
			log.error("归还车位异常重新计算车位");
			//归还车位异常
			bladeRedis.set(LECENT_PARKPLACE_INIT_FLAG, 2);
			ReloadParkPlaceEvent reloadParkPlaceEvent = new ReloadParkPlaceEvent();
			reloadParkPlaceEvent.setParkLotId(event.getParkLotId());
			publisher.publishEvent(reloadParkPlaceEvent);
			return false;
		}
	}

	private Long incrNumber(ReturnParkPlaceEvent event) {
		String key = LECENT_PARK_REMAIN_NUMBER.concat("::").concat(String.valueOf(event.getParkLotId()));
		Object o = bladeRedis.get(key);
		if (Func.isNotEmpty(o)){
			long l = Func.toLong(o) + event.getParkPlaceNumber();
			bladeRedis.set(key,l);
			return l;
		}
		return 1L;

	}
}
