package com.lecent.park.scheduled;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.invoice.entity.InvoiceResult;
import com.lecent.invoice.eum.InvoiceStatusEnum;
import com.lecent.invoice.eum.InvoiceTypeEnum;
import com.lecent.invoice.util.InvoiceUtil;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.entity.ParklotInvoiceRecord;
import com.lecent.park.entity.ParklotInvoiceRecordDetail;
import com.lecent.park.service.IParklotInvoiceRecordDetailService;
import com.lecent.park.service.IParklotInvoiceRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 发票开票结果定时查询更新开票记录表
 * @author: cy
 * @date: 2021年11月10日 10:48
 */
@Component
@Slf4j
public class InvoiceResultScheduled {

	private IParklotInvoiceRecordService invoiceRecordService;

	private IParklotInvoiceRecordDetailService invoiceRecordDetailService;

	@Autowired
	public InvoiceResultScheduled(IParklotInvoiceRecordService invoiceRecordService, IParklotInvoiceRecordDetailService invoiceRecordDetailService) {
		this.invoiceRecordService = invoiceRecordService;
		this.invoiceRecordDetailService = invoiceRecordDetailService;
	}

	/**
	 * 定时执行 更新开票记录
	 */
	@Scheduled(fixedRate = 1000 * 60 * 10, initialDelay = 5000)
	@RedisLock(value = ParkCacheNames.INVOICE_ORDER_RESULT_UPDATE, waitTime = 60)
	public void init() {
		log.info("定时刷新开票结果");
		InvoiceResultScheduled bean = SpringUtil.getBean(this.getClass());
		bean.updateInvoiceRecord();
	}


	/**
	 * 更新开票记录
	 */
	@Transactional(rollbackFor = Exception.class)
	public void updateInvoiceRecord() {
		Date now = new Date();
		Date before = DateUtil.offsetDay(now, -1);
		List<String> serialNums = getNotUpdate(before, now);
		log.info("查询到没有更新的订单=======》serialNums={}", serialNums);
		List<InvoiceResult> result = InvoiceUtil.result(serialNums);
		log.info("查询到订单结果=======》result={}", result);
		List<ParklotInvoiceRecord> records = new ArrayList<>();
		// 更新开票状态
		for (InvoiceResult invoiceResult : result) {
//			ParklotInvoiceRecord record = new ParklotInvoiceRecord();
//			record.setFplsh(invoiceResult.getSerialNum());
//			record.setPdfUrl(invoiceResult.getResultUrl());
//			record.setStatus(invoiceResult.getStatus());
//			record.setFpDm(invoiceResult.getInvoiceCode());
//			record.setFpHm(invoiceResult.getInvoiceNum());
//			records.add(record);

			invoiceRecordService.update(Wrappers.<ParklotInvoiceRecord>lambdaUpdate()
				.set(ParklotInvoiceRecord::getPdfUrl, invoiceResult.getResultUrl())
				.set(ParklotInvoiceRecord::getStatus, invoiceResult.getStatus())
				.set(ParklotInvoiceRecord::getFpDm, invoiceResult.getInvoiceCode())
				.set(ParklotInvoiceRecord::getFpHm, invoiceResult.getInvoiceNum())
				.eq(ParklotInvoiceRecord::getFplsh, invoiceResult.getSerialNum()));
		}

//		invoiceRecordService.updateBatchBySerialNum(records);
		// 开红票成功，将原发票状态更新为作废
		List<String> abandonInvoiceNums = new ArrayList<>();
		for (InvoiceResult invoiceResult : result) {
			String invoiceType = invoiceResult.getInvoiceType();
			Integer resultStatus = invoiceResult.getStatus();
			if (InvoiceTypeEnum.INVOICE_TYPE_RED.code.equals(invoiceType) && InvoiceStatusEnum.KP_SUCCESS.code.equals(resultStatus)) {
				abandonInvoiceNums.add(invoiceResult.getOriginNum());
			}
		}
		if (!abandonInvoiceNums.isEmpty()) {
			invoiceRecordService.update(Wrappers.<ParklotInvoiceRecord>lambdaUpdate()
				.set(ParklotInvoiceRecord::getStatus, InvoiceStatusEnum.KP_DISABLED.code)
				.in(ParklotInvoiceRecord::getFpHm, abandonInvoiceNums));
		}
		// 超时情况 更新开票为开票失败
		// updatePastRecord(before)

		// 开票失败的情况 删除掉开票详情记录 允许重新提交订单开票（超时的情况手动处理）
		//updateFailRecord(before);
	}

	/**
	 * 将超过endDate 时间的数据更新为开票失败
	 *
	 * @param endDate
	 */
	private void updatePastRecord(Date endDate) {
		ParklotInvoiceRecord record = new ParklotInvoiceRecord();
		record.setStatus(InvoiceStatusEnum.KP_FAIL.code);
		// 将超时的开票记录 设置为开票失败
		invoiceRecordService.update(record, Wrappers.<ParklotInvoiceRecord>lambdaUpdate()
			.le(ParklotInvoiceRecord::getCreateTime, endDate)
			.eq(ParklotInvoiceRecord::getStatus, InvoiceStatusEnum.KP_PROCEED.code));

		List<ParklotInvoiceRecord> failData = invoiceRecordService
			.list(Wrappers.<ParklotInvoiceRecord>lambdaQuery()
				.le(ParklotInvoiceRecord::getCreateTime, endDate)
				.or().eq(ParklotInvoiceRecord::getStatus, InvoiceStatusEnum.KP_FAIL.code));
		List<Long> recordIds = failData.stream().map(ParklotInvoiceRecord::getId).collect(Collectors.toList());
		// 将超时的开票记录详情删掉，因为查询开票订单列表的时候会关联详情表查询记录
		invoiceRecordDetailService.update(Wrappers.<ParklotInvoiceRecordDetail>lambdaUpdate()
			.set(ParklotInvoiceRecordDetail::getIsDeleted, 1)
			.in(ParklotInvoiceRecordDetail::getRecordId, recordIds)
			.eq(ParklotInvoiceRecordDetail::getIsDeleted, 0));
	}

	/**
	 * 将开票失败的数据 订单详情删除掉 允许重开（超时情况手动处理）
	 */
	private void updateFailRecord(Date endDate) {
		List<ParklotInvoiceRecord> failData = invoiceRecordService
			.list(Wrappers.<ParklotInvoiceRecord>lambdaQuery()
				.ge(ParklotInvoiceRecord::getCreateTime, endDate)
				.eq(ParklotInvoiceRecord::getStatus, InvoiceStatusEnum.KP_FAIL.code));
		List<Long> recordIds = failData.stream().map(ParklotInvoiceRecord::getId).collect(Collectors.toList());
		log.info("删除的开票订单记录ids==========》recordIds={}", recordIds);
		// 将超时的开票记录详情删掉，因为查询开票订单列表的时候会关联详情表查询记录
		if (recordIds.isEmpty()) {
			return;
		}
		invoiceRecordDetailService.update(Wrappers.<ParklotInvoiceRecordDetail>lambdaUpdate()
			.set(ParklotInvoiceRecordDetail::getIsDeleted, 1)
			.in(ParklotInvoiceRecordDetail::getRecordId, recordIds)
			.eq(ParklotInvoiceRecordDetail::getIsDeleted, 0));
	}

	/**
	 * 获取到没有更新的开票记录 流水号
	 *
	 * @return
	 */
	private List<String> getNotUpdate(Date startDate, Date endDate) {
		LambdaQueryWrapper<ParklotInvoiceRecord> queryWrapper = Wrappers.<ParklotInvoiceRecord>lambdaQuery().eq(ParklotInvoiceRecord::getStatus, InvoiceStatusEnum.KP_PROCEED.code);
		if (startDate != null) {
			queryWrapper.ge(ParklotInvoiceRecord::getCreateTime, startDate);
		}
		if (endDate != null) {
			queryWrapper.le(ParklotInvoiceRecord::getCreateTime, endDate);
		}
		// 获取到一天内的还在开票中状态的 开票记录
		List<ParklotInvoiceRecord> records = invoiceRecordService.list(queryWrapper);
		return records.stream().map(ParklotInvoiceRecord::getFplsh).collect(Collectors.toList());
	}

}
