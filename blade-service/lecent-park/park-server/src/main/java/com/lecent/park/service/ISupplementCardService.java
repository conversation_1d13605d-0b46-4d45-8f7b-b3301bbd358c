package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.SupplementCardDTO;
import com.lecent.park.entity.SupplementCard;
import com.lecent.park.vo.SupplementCardVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 附属卡(亲情卡)表 服务类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
public interface ISupplementCardService extends BaseService<SupplementCard> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param supplementCard
	 * @return
	 */
	IPage<SupplementCardVO> selectSupplementCardPage(IPage<SupplementCardVO> page, SupplementCardVO supplementCard);

	/**
	 * 保存或者更新亲情卡信息
	 *
	 * @param supplementCardDTO
	 * @return
	 */
	boolean saveOrUpdateSupplementCar(SupplementCardDTO supplementCardDTO);

	/**
	 * 查询剩余可授权车辆数量
	 *
	 * @param supplementCard
	 * @return
	 */
	Integer selectUsableCarNum(SupplementCard supplementCard);

	/**
	 * 亲情卡信息列表
	 *
	 * @param supplementCardVO
	 * @return
	 */
	IPage<SupplementCardVO> selectSupplementCarList(IPage page, SupplementCardVO supplementCardVO);

	/**
	 * 根据车牌查询亲情卡
	 *
	 * @param plate
	 * @param parklotId
	 */
	SupplementCard selectByPlate(String plate, Long parklotId);

	Boolean updateCard(SupplementCard supplementCard);

	/**
	 * 删除
	 *
	 * @param ids
	 * @return
	 */
	boolean customDeleteLogic(List<Long> ids);
}
