package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.CardLog;
import com.lecent.park.excel.CardLogExcel;
import com.lecent.park.vo.CardLogVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 月卡变更日志表 服务类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface ICardLogService extends BaseService<CardLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cardLog
	 * @return
	 */
	IPage<CardLogVO> selectCardLogPage(IPage<CardLogVO> page, CardLogVO cardLog);

	List<CardLogExcel> getExportData(Long cardId);
	List<CardLogExcel> getListByFreeCardId(Long freeCardId);

	/**
	 * 获取商家授权车牌的变更日志记录
	 *
	 * @param cardId
	 * @return 日志列表
	 */
	List<CardLog> getAuthPlateChangeLogList(Long cardId);
}
