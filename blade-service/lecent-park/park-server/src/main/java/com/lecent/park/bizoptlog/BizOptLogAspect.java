package com.lecent.park.bizoptlog;

import com.lecent.park.service.IBizOptLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-05-25 09:20
 */
@Component
@Aspect
@Slf4j
public class BizOptLogAspect {


	@Autowired
	private IBizOptLogService bizOptLogService;


	@Pointcut("@annotation(com.lecent.park.bizoptlog.AroundOpt)")
	public void matchCondition() {
	}

	//使用matchCondition这个切入点进行增强
	@Around("matchCondition()")
	public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
		if (!bizOptLogService.enableSaveLog()) {
			return joinPoint.proceed(joinPoint.getArgs());
		}
		Object result;
		Long id = getParamId(joinPoint);
		if (Func.isNull(id)) {
			result = joinPoint.proceed(joinPoint.getArgs());
			saveForAddEl(joinPoint);
		} else {
			BaseService baseService = getService(joinPoint);
			BaseEntity beforeValue = getValue(baseService, id);
			String header = getHeader(joinPoint, beforeValue);
			BaseEntity bef = Func.isNull(beforeValue) ? null : Func.readJson(Func.toJson(beforeValue), beforeValue.getClass());
			log.info("=============操作之前====================，操作前值为====={}", Func.isNull(bef) ? "" : bef.toString());
			result = joinPoint.proceed(joinPoint.getArgs());
			BaseEntity afterValue = getValue(baseService, id);
			BaseEntity aft = Func.isNull(afterValue) ? null : Func.readJson(Func.toJson(afterValue), afterValue.getClass());
			log.info("=============操作之后====================，操作后值为====={}", Func.isNull(aft) ? "" : aft.toString());
			Integer type = getType(joinPoint);
			if (Func.notNull(beforeValue) && Func.notNull(afterValue)) {
				//新增和修改共用一个接口的时候类型改为修改类型
				type = OptTypeEnum.ADD.getValue().equals(type) ? OptTypeEnum.EDIT.getValue() : type;
				if (OptTypeEnum.DISABLE.getValue().equals(type) || OptTypeEnum.ENABLE.getValue().equals(type)) {
					saveForDelEl(joinPoint, bef);
				} else {
					String temp = getCustomMsgTemp(joinPoint);
					String recordIdentification = getRecordIdentification(joinPoint);
					if (Func.isNotBlank(temp) && Func.isNotBlank(recordIdentification)) {
						saveForDelEl(joinPoint, bef);
					} else {
						bizOptLogService.saveBizLog(getTitle(joinPoint), bef, aft, SecureUtil.getUser(), type,
							getOptTerminal(joinPoint), header);
					}
				}
			}
			if (Func.notNull(beforeValue) && Func.isNull(afterValue)
				&& OptTypeEnum.DEL.getValue().equals(type)) {
				saveForDelEl(joinPoint, bef);
			}
		}
		return result;
	}

	private String getHeader(ProceedingJoinPoint joinPoint, BaseEntity obj) {
		StringBuilder header = new StringBuilder();
		AroundOpt annotation = getAnnotation(joinPoint);
		String headerSuffix = annotation.headerSuffix();
		AroundOptHeader[] headers = annotation.headers();
		List<String> headerList = Arrays.asList(headers).stream()
			.sorted(Comparator.comparing(AroundOptHeader::order))
			.map(aroundOptHeader -> getHeaderByAnnotation(aroundOptHeader, obj))
			.filter(s -> Func.isNotBlank(s))
			.collect(Collectors.toList());
		header.append(Func.join(headerList, "||")).append(headerSuffix);
		return Func.isBlank(header.toString()) ? "" : header.insert(0, "<<").append(">>").toString();
	}

	private String getHeaderByAnnotation(AroundOptHeader aroundOptHeader, BaseEntity obj) {
		String header = "";
		Class<? extends BaseService> headerClass = aroundOptHeader.headerClass();
		BaseService headerService = SpringUtil.getBean(headerClass);
		String headerId = aroundOptHeader.headerId();
		String headerName = aroundOptHeader.headerName();
		List<Field> fieldList = getAllFields(obj);
		String id = "";
		for (Field declaredField : fieldList) {
			if (declaredField.getName().equals(headerId)) {
				declaredField.setAccessible(true);
				try {
					Object o = declaredField.get(obj);
					id = Func.notNull(o) ? o.toString() : "";
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				}
				break;
			}
		}
		if (Func.isNotBlank(id)) {
			BaseEntity headerEntity = (BaseEntity) headerService.getById(id);
			List<Field> allFields = getAllFields(headerEntity);
			for (Field declaredField : allFields) {
				if (declaredField.getName().equals(headerName)) {
					declaredField.setAccessible(true);
					try {
						Object o = declaredField.get(headerEntity);
						header = Func.notNull(o) ? o.toString() : "";
					} catch (IllegalAccessException e) {
						e.printStackTrace();
					}
					break;
				}
			}

		}
		return header;
	}

	private List<Field> getAllFields(BaseEntity obj) {
		Class clazz = obj.getClass();
		List<Field> fieldList = new ArrayList<>();
		while (clazz != null) {
			fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
			clazz = clazz.getSuperclass();
		}
		return fieldList;
	}

	private void saveForDelEl(ProceedingJoinPoint joinPoint, BaseEntity bef) {
		String customMsgTemp = getCustomMsgTemp(joinPoint);
		if (Func.isNotBlank(customMsgTemp)) {
			String header = getHeader(joinPoint, bef);
			String customMsg = getDelMsg(joinPoint, customMsgTemp, bef, header);
			bizOptLogService.saveLogForFinalParam(SecureUtil.getUser(), getTitle(joinPoint), customMsg,
				getType(joinPoint), getOptTerminal(joinPoint));
		}
	}

	private String getDelMsg(ProceedingJoinPoint joinPoint, String customMsgTemp, BaseEntity bef,
							 String header) {
		String ret = header + "";
		String recordIdentification = getRecordIdentification(joinPoint);
		Class<? extends BaseEntity> aClass = bef.getClass();
		try {
			Field declaredField = aClass.getDeclaredField(recordIdentification);
			declaredField.setAccessible(true);
			Object o = declaredField.get(bef);
			String s = header + o.toString();
			ret = customMsgTemp.replaceAll("\\{\\}", s);
		} catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
		return ret;
	}

	private String getRecordIdentification(ProceedingJoinPoint joinPoint) {
		AroundOpt annotation = getAnnotation(joinPoint);
		return annotation.recordIdentification();
	}


	private void saveForAddEl(ProceedingJoinPoint joinPoint) {
		String elCustomMsg = getElCustomMsg(joinPoint);
		String header = getHeader(joinPoint, (BaseEntity) joinPoint.getArgs()[0]);
		String customMsg = getCustomMsg(joinPoint, elCustomMsg, header);
		bizOptLogService.saveLogForFinalParam(SecureUtil.getUser(), getTitle(joinPoint), customMsg,
			getType(joinPoint), getOptTerminal(joinPoint));
	}

	private String getCustomMsg(ProceedingJoinPoint joinPoint, String key, String header) {
		String value = header + "";
		if (Func.isNotBlank(key)) {
			ExpressionParser parser = new SpelExpressionParser();
			Expression expression = parser.parseExpression(key);
			EvaluationContext context = new StandardEvaluationContext();
			Object[] arguments = joinPoint.getArgs();
			String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
			for (int i = 0; i < arguments.length; i++) {
				context.setVariable(paramNames[i], arguments[i]);
			}
			value = header + expression.getValue(context, String.class);
		}
		String temp = getCustomMsgTemp(joinPoint);
		String ret = temp.replaceAll("\\{\\}", value);
		return ret;
	}

	private String getCustomMsgTemp(ProceedingJoinPoint joinPoint) {
		AroundOpt annotation = getAnnotation(joinPoint);
		return annotation.customMsgTemp();
	}

	private String getElCustomMsg(ProceedingJoinPoint joinPoint) {
		AroundOpt annotation = getAnnotation(joinPoint);
		return annotation.customMsg();
	}


	private String getTitle(ProceedingJoinPoint joinPoint) {
		AroundOpt annotation = getAnnotation(joinPoint);
		return annotation.title();
	}

	private Integer getType(ProceedingJoinPoint joinPoint) {
		AroundOpt annotation = getAnnotation(joinPoint);
		return annotation.optType().value;
	}

	private Integer getOptTerminal(ProceedingJoinPoint joinPoint) {
		Integer ret = 0;
		AroundOpt annotation = getAnnotation(joinPoint);
		boolean terminalFromParam = annotation.getTerminalFromParam();
		if (terminalFromParam) {
			String elCustomMsg = getElCustomMsg(joinPoint);
			ExpressionParser parser = new SpelExpressionParser();
			Expression expression = parser.parseExpression(elCustomMsg);
			EvaluationContext context = new StandardEvaluationContext();
			Object[] arguments = joinPoint.getArgs();
			String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
			for (int i = 0; i < arguments.length; i++) {
				context.setVariable(paramNames[i], arguments[i]);
			}
			ret = expression.getValue(context, Integer.class);
		}
		return Func.isNull(ret) || ret.equals(0) ? annotation.optTerminal().value : ret;
	}

	private BaseEntity getValue(BaseService baseService, Long id) {
		return (BaseEntity) baseService.getById(id);
	}

	private BaseService getService(ProceedingJoinPoint joinPoint) {
		AroundOpt annotation = getAnnotation(joinPoint);
		Class<? extends BaseService> aClass = annotation.serviceClass();
		BaseService baseService = SpringUtil.getBean(aClass);
		return baseService;
	}

	private AroundOpt getAnnotation(ProceedingJoinPoint joinPoint) {
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		AroundOpt annotation = method.getAnnotation(AroundOpt.class);
		return annotation;
	}

	private Long getParamId(ProceedingJoinPoint joinPoint) {
		Long id = null;
		Object[] paramValues = joinPoint.getArgs();
		AroundOpt annotation = getAnnotation(joinPoint);
		String idName = annotation.idName();
		String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
		for (int i = 0; i < paramNames.length; i++) {
			if (paramNames[i].equals(idName)) {
				if (paramValues[i] instanceof String) {
					id = Long.valueOf((String) paramValues[i]);
				} else {
					id = (Long) paramValues[i];
				}
			}
		}
		if (Func.isNull(id)) {
			Object paramValue = paramValues[0];
			if (paramValue instanceof BaseEntity) {
				return ((BaseEntity) paramValue).getId();
			}
		}
		return id;
	}
}
