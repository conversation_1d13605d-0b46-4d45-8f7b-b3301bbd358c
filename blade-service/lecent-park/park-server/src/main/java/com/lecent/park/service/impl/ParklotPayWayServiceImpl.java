package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lecent.park.common.constant.CacheConstant;
import com.lecent.park.core.log.utils.AuditLogger;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.entity.ParklotPayWay;
import com.lecent.park.service.IParklotPayWayService;
import com.lecent.park.vo.ParklotPayWayVO;
import com.lecent.park.wrapper.PayWayWrapper;
import com.lecent.pay.core.enums.PayChannel;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParklotPayWayMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.UserAgent;
import org.springblade.common.utils.CacheUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.api.crypto.config.ApiCryptoProperties;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.AesUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付渠道配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Slf4j
@Service
public class ParklotPayWayServiceImpl extends
    BaseServiceImpl<ParklotPayWayMapper, ParklotPayWay> implements IParklotPayWayService {

    private static final String PAY_WAY_CACHE_NAME = CacheConstant.PARK_CACHE + "payWayByParkLotId:";
    public static final String THIRD_PARTY_APP_ID = "Third-party-app-id";
    private static final String LOG_KEY = "parklotPayWay";
    @Resource
    private ApiCryptoProperties apiCryptoProperties;

    @Override
    public IPage<ParklotPayWayVO> selectPayWayPage(IPage<ParklotPayWayVO> page, ParklotPayWayVO payWay) {
        return page.setRecords(baseMapper.selectPayWayPage(page, payWay));
    }

    @Override
    public List<ParklotPayWayVO> listPays(Long parklotId) {
        return PayWayWrapper.build().listVO(listPayWay(parklotId));
    }

    @Override
    public List<ParklotPayWay> listPayWay(Long parkLotId) {
        return Optional.ofNullable(
                CacheUtils.getList(PAY_WAY_CACHE_NAME + parkLotId,
                    ParklotPayWay.class,
                    () -> list(Wrappers.<ParklotPayWay>lambdaQuery()
                        .eq(ParklotPayWay::getParklotId, parkLotId)
                        .eq(ParklotPayWay::getStatus, EnableStatus.ACTIVE.getCode())
                        .orderByAsc(ParklotPayWay::getPriorityLevel)),
                    CacheUtils.DEFAULT_1_WEEK))
            .orElse(Lists.newArrayList());
    }

    @Override
    public List<ParklotPayWay> listUnconsciousPayWay(Long parkLotId) {
        return listPays(parkLotId).stream()
            .filter(payWay -> Boolean.TRUE.equals(payWay.getNoFeelSupport()))
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean customSaveOrUpdate(List<ParklotPayWay> payWays) {
        if (Func.isEmpty(payWays)) {
            return true;
        }

        List<Integer> priorityLevels = new ArrayList<>();
        for (ParklotPayWay p : payWays) {
            LecentAssert.notNull(p.getPriorityLevel(), "优先级不能为空");
            LecentAssert.isFalse(priorityLevels.contains(p.getPriorityLevel()), "优先级不能重复");
            priorityLevels.add(p.getPriorityLevel());
        }
        LecentAssert.isTrue(priorityLevels.contains(1), "优先级必须有一级");

        Long parklotId = payWays.get(0).getParklotId();
        // 删除
        List<Long> deleteIds = payWays.stream()
            .filter(payWay -> null != payWay.getId() && Integer.valueOf(1).equals(payWay.getIsDeleted()))
            .map(BaseEntity::getId)
            .collect(Collectors.toList());
        if (Func.isNotEmpty(deleteIds)) {
            deleteLogic(deleteIds);
        }

        // 校验
        payWays.forEach(payWay -> {
            LecentAssert.notNull(payWay.getParklotId(), "车场ID不能为空");

            if (null != payWay.getId()) {
                return;
            }
            Integer merchantType = payWay.getMerchantType();
            // 所有聚合支付都互斥
            if (!PayChannel.WX.value().equals(merchantType)
                && !PayChannel.ALI.value().equals(merchantType)
                && !PayChannel.ETC.value().equals(merchantType)) {
//				int count = count(Wrappers.<ParklotPayWay>lambdaQuery()
//					.eq(ParklotPayWay::getParklotId, parklotId)
//					.notIn(ParklotPayWay::getMerchantType, CreateWay.WX.value(), CreateWay.ALI.value(), CreateWay.ETC.value())
//					.eq(ParklotPayWay::getStatus, EnableStatus.ACTIVE.getCode()));
                //LecentAssert.isFalse(count > 0, StringUtil.format("一个车场只能配置一个聚合支付商户,{}重复", payWay.getMerchantName()));
            }
            // 单一支付类型与同类型互斥与聚合支付互斥
            else {
//				int count = count(Wrappers.<ParklotPayWay>lambdaQuery()
//					.eq(ParklotPayWay::getParklotId, parklotId).eq(ParklotPayWay::getStatus, EnableStatus.ACTIVE.getCode())
//					.and(wrapper -> {
//						wrapper.eq(ParklotPayWay::getMerchantType, merchantType)
//							.or()
//							.notIn(ParklotPayWay::getMerchantType, CreateWay.WX.value(), CreateWay.ALI.value(), CreateWay.ETC.value());
//					})
//				);
                //LecentAssert.isFalse(count > 0, StringUtil.format("一个车场只能配置一种支付商户类型,{}重复", payWay.getMerchantName()));
            }
        });

        payWays.forEach(payWay -> {
            // 编辑
            if (null != payWay.getId()) {
                if (Integer.valueOf(1).equals(payWay.getIsDeleted())) {
                    AuditLogger.add(String.valueOf(parklotId), LOG_KEY, "删除支付配置", payWay.getMerchantName());
                } else {
                    // 添加日志
                    AuditLogger.add(String.valueOf(parklotId),
                        LOG_KEY,
                        "支付配置修改",
                        getById(payWay.getId()),
                        payWay);
                }
            }

            // 清除缓存
            CacheUtils.delKey(PAY_WAY_CACHE_NAME + payWay.getParklotId());
            if (!deleteIds.contains(payWay.getId())) {
                saveOrUpdate(payWay);
            }
        });
        return true;
    }


    @Override
    public ParklotPayWay payWayByParkLotId(Long parkLotId) {
        return payWayByParkLotId(parkLotId, null);
    }

    @Override
    public ParklotPayWay existPayWayByParkLotId(Long parkLotId) {
        return existPayWayByParkLotId(parkLotId, null);
    }

    @Override
    public ParklotPayWay existPayWayByParkLotId(Long parkLotId, String authCode) {
        ParklotPayWay parklotPayWay = payWayByParkLotId(parkLotId, authCode);
        LecentAssert.notNull(parklotPayWay, "该车场还未开通线上支付功能，如需支付请联系管理人员。");

        return parklotPayWay;
    }

    @Override
    public ParklotPayWay payWayByParkLotId(Long parkLotId, String authCode) {
        List<ParklotPayWay> payWays = listPayWay(parkLotId);

        if (Func.isEmpty(payWays)) {
            return null;
        }

        if (payWays.size() == 1) {
            return payWays.get(0);
        }

        UserAgent userAgent = getUserAgent(authCode);
        for (ParklotPayWay payWay : payWays) {
            if (UserAgent.WX == userAgent &&
                PayChannel.WX.value().equals(payWay.getMerchantType())) {
                return payWay;
            }
            // 支付宝
            else if (UserAgent.ALI_PAY == userAgent
                && PayChannel.ALI.value().equals(payWay.getMerchantType())) {
                return payWay;
            }
        }
        return payWays.get(0);
    }

    @Override
    public ParklotPayWay getOneByAutomatic(Long parkLotId) {
        return getOneByAutomatic(parkLotId, null);
    }

    @Override
    public ParklotPayWay getOneByAutomatic(Long parkLotId, String thirdPartyAppId) {
        List<ParklotPayWay> payWays = listPayWay(parkLotId);

        if (payWays.isEmpty()) {
            log.error("parklot[id={}] not found pay way.", parkLotId);
            return null;
        }

        ParklotPayWay defaultPayWay = payWays.get(0);
        if (payWays.size() == 1) {
            return defaultPayWay;
        }

        List<ParklotPayWay> waysOfUserAgent = filterByUserAgent(payWays);

        if (waysOfUserAgent.isEmpty()) {
            return defaultPayWay;
        }

        List<ParklotPayWay> waysOfThirdPartyAppId = filterByThirdPartyAppId(waysOfUserAgent, thirdPartyAppId);

        return waysOfThirdPartyAppId.stream().findFirst().orElse(waysOfUserAgent.get(0));
    }

    public List<ParklotPayWay> filterByUserAgent(List<ParklotPayWay> payWays) {
        PayChannel userAgentPayChannel = getPayWayTypeByUserAgent();
        if (Objects.isNull(userAgentPayChannel)) {
            return Collections.emptyList();
        }

        List<ParklotPayWay> collect = payWays.stream()
            .filter(t -> userAgentPayChannel.value().equals(t.getMerchantType()))
            .collect(Collectors.toList());

        if (collect.isEmpty()) {
            if (log.isDebugEnabled()) {
                log.debug("not found pay channel of user agent : {}", userAgentPayChannel.getName());
            }
        }

        return collect;
    }

    public List<ParklotPayWay> filterByThirdPartyAppId(List<ParklotPayWay> payWays, String thirdPartyAppId) {
        String finalThirdPartyAppId = Func.isNotBlank(thirdPartyAppId) ? thirdPartyAppId : getThirdPartyAppId();
        if (Func.isBlank(finalThirdPartyAppId)) {
            return Collections.emptyList();
        }

        List<ParklotPayWay> collect = payWays.stream()
            .filter(t -> t.getMerchantId().startsWith(finalThirdPartyAppId))
            .collect(Collectors.toList());

		if (collect.isEmpty()) {
			if (log.isDebugEnabled()) {
				log.debug("not found pay channel of third party app id : {}", thirdPartyAppId);
			}
		}

        return collect;
    }

    public String getThirdPartyAppId() {
        String thirdPartyAppId = WebUtil.getHeader(THIRD_PARTY_APP_ID);
        if (Func.isBlank(thirdPartyAppId)) {
            return null;
        }

        return CacheUtils.get(
            CacheConstant.Client.Header.THIRD_PARTY_APP_ID + thirdPartyAppId,
            String.class,
            () -> AesUtil.decryptFormHexToString(thirdPartyAppId, apiCryptoProperties.getAesKey()),
            CacheUtils.DEFAULT_1_MONTH
        );
    }

    @Nullable
    private PayChannel getPayWayTypeByUserAgent() {
        UserAgent userAgent = CommonUtil.getUserAgent();
        switch (userAgent) {
            case WX:
                return PayChannel.WX;
            case ALI_PAY:
                return PayChannel.ALI;
            default:
                return null;
        }
    }

    /**
     * 获取发起支付方
     *
     * @param authCode 条形码
     * @return agent
     */
    private UserAgent getUserAgent(String authCode) {
        // wx ali
        UserAgent userAgent = CommonUtil.getUserAgent();
        if (UserAgent.WX == userAgent
            || UserAgent.ALI_PAY == userAgent) {
            return userAgent;
        }

        if (Func.isNotEmpty(authCode)) {
            // 微信条形码1开头
            if (authCode.startsWith("1")) {
                return UserAgent.WX;
            }
            // 支付宝条形码2开头
            else if (authCode.startsWith("2")) {
                return UserAgent.ALI_PAY;
            }
        }
        return UserAgent.OTHER;
    }

    @Override
    public List<ParklotPayWay> listByMerchantId(String merchantId) {
        return lambdaQuery().eq(ParklotPayWay::getMerchantId, merchantId).list();
    }

    @Override
    public ParklotPayWay getByParkLotIdAndMerchantId(Long parkLotId, String merchantId) {
        return lambdaQuery().eq(ParklotPayWay::getParklotId, parkLotId)
            .eq(ParklotPayWay::getMerchantId, merchantId)
            .one();
    }
}
