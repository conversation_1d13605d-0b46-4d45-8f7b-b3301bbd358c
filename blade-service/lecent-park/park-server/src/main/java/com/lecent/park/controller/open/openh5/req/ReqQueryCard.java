package com.lecent.park.controller.open.openh5.req;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询月卡信息
 *
 * <AUTHOR> zxr
 * @date : 2022/8/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReqQueryCard {

	/**
	 * 车场ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("车场ID")
	private Long parklotId;

	/**
	 * 车牌
	 */
	@ApiModelProperty("车牌")
	private String plate;

	/**
	 * 月卡ID
	 */
	@ApiModelProperty("月卡ID")
	private String cardId;
}
