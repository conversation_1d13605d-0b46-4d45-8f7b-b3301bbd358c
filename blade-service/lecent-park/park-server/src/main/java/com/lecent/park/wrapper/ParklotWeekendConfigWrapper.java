package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotWeekendConfig;
import com.lecent.park.vo.ParklotWeekendConfigVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
public class ParklotWeekendConfigWrapper extends BaseEntityWrapper<ParklotWeekendConfig, ParklotWeekendConfigVO>  {

	public static ParklotWeekendConfigWrapper build() {
		return new ParklotWeekendConfigWrapper();
 	}

	@Override
	public ParklotWeekendConfigVO entityVO(ParklotWeekendConfig parklotWeekendConfig) {
		ParklotWeekendConfigVO parklotWeekendConfigVO = BeanUtil.copy(parklotWeekendConfig, ParklotWeekendConfigVO.class);

		//User createUser = UserCache.getUser(parklotWeekendConfig.getCreateUser());
		//User updateUser = UserCache.getUser(parklotWeekendConfig.getUpdateUser());
		//parklotWeekendConfigVO.setCreateUserName(createUser.getName());
		//parklotWeekendConfigVO.setUpdateUserName(updateUser.getName());

		return parklotWeekendConfigVO;
	}

}
