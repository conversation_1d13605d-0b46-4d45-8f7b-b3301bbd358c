package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.EmployeeCarDTO;
import com.lecent.park.entity.EmployeeCar;
import com.lecent.park.vo.EmployeeCarVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 员工车辆 服务类
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
public interface IEmployeeCarService extends BaseService<EmployeeCar> {

	/**
	 * 自定义分页
	 *
	 * @param page           分页信息
	 * @param employeeCarDto 员工车辆DTO
	 * @return 分页结果
	 */
	IPage<EmployeeCarVO> selectEmployeeCarPage(IPage<EmployeeCarVO> page, EmployeeCarDTO employeeCarDto);

	/**
	 * 员工车辆修改和新增
	 *
	 * @param employeeCarDto 员工车辆DTO
	 * @return 是否成功提交
	 */
	boolean submit(EmployeeCarDTO employeeCarDto);

	/**
	 * 删除员工车辆
	 *
	 * @param id 员工车辆ID
	 * @return 是否成功删除
	 */
	boolean customRemove(String id);

	/**
	 * 获取员工车辆详情
	 *
	 * @param id 员工车辆ID
	 * @return 员工车辆详情
	 */
	EmployeeCarVO detail(String id);

	/**
	 * 移除停车场
	 *
	 * @param contactId 停车场ID
	 * @return 是否成功移除
	 */
	boolean removeParklot(String contactId);
}
