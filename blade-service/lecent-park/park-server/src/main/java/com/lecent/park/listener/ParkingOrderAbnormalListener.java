package com.lecent.park.listener;

import com.lecent.park.core.mq.rabbitmq.MessageConstant;
import com.lecent.park.core.mq.rabbitmq.utils.ObjUtil;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.service.IParkingOrderAbnormalService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_EXCHANGE_NAME;

/**
 * 异常订单监听器
 */
@Slf4j
@Component
@Order
public class ParkingOrderAbnormalListener {

    @Resource
    private IParkingOrderAbnormalService parkingOrderAbnormalService;

    /**
     * 处理异常订单消息
     *
     * @param message 消息
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            value = @Queue(value = MessageConstant.LECENT_PARK_ORDER_ABNORMAL_QUEUE, durable = "true"),
            exchange = @Exchange(value = LECENT_PARK_EXCHANGE_NAME, type = ExchangeTypes.TOPIC),
            key = MessageConstant.LECENT_PARK_ORDER_ABNORMAL_KEY
        )
    })
    public void handleAbnormalOrder(Message message) {
        try {
            ParkingOrder parkingOrder = ObjUtil.toObjectMessage(message, ParkingOrder.class, "utf-8");
            log.info("收到异常订单处理消息, order: {}", JsonUtil.toJson(parkingOrder));
            // 处理异常订单
            parkingOrderAbnormalService.handleAbnormalOrders(parkingOrder);
        } catch (Exception e) {
            log.error("处理异常订单失败", e);
        }
    }
}
