package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.ParklotPayWay;
import com.lecent.park.service.IParklotPayWayService;
import com.lecent.park.vo.ParklotPayWayVO;
import com.lecent.park.wrapper.PayWayWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 支付渠道配置表 控制器
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("/payway")
@Api(value = "支付渠道配置表", tags = "支付渠道配置表接口")
public class PayWayController extends BladeController {

	private IParklotPayWayService payWayService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入payWay")
	public R<ParklotPayWayVO> detail(ParklotPayWay payWay) {
		ParklotPayWay detail = payWayService.getOne(Condition.getQueryWrapper(payWay));
		return R.data(PayWayWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 支付渠道配置表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入payWay")
	public R<IPage<ParklotPayWayVO>> list(ParklotPayWay payWay, Query query) {
		IPage<ParklotPayWay> pages = payWayService.page(Condition.getPage(query), Condition.getQueryWrapper(payWay));
		return R.data(PayWayWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 支付渠道配置表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入payWay")
	public R<IPage<ParklotPayWayVO>> page(ParklotPayWayVO payWay, Query query) {
		IPage<ParklotPayWayVO> pages = payWayService.selectPayWayPage(Condition.getPage(query), payWay);
		return R.data(pages);
	}

	/**
	 * 新增 支付渠道配置表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入payWay")
	public R save(@Valid @RequestBody ParklotPayWay payWay) {
		return R.status(payWayService.save(payWay));
	}

	/**
	 * 修改 支付渠道配置表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入payWay")
	public R update(@Valid @RequestBody ParklotPayWay payWay) {
		return R.status(payWayService.updateById(payWay));
	}

	/**
	 * 新增或修改 支付渠道配置表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入payWay")
	public R submit(@Valid @RequestBody ParklotPayWay payWay) {
		return R.status(payWayService.saveOrUpdate(payWay));
	}


	/**
	 * 删除 支付渠道配置表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(payWayService.deleteLogic(Func.toLongList(ids)));
	}
}
