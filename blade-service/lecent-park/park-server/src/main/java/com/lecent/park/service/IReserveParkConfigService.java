package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ReserveParkConfig;
import com.lecent.park.vo.ReserveParkConfigVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 月卡变更日志表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
public interface IReserveParkConfigService extends BaseService<ReserveParkConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param reserveParkConfig
	 * @return
	 */
	IPage<ReserveParkConfigVO> selectReserveParkConfigPage(IPage<ReserveParkConfigVO> page, ReserveParkConfigVO reserveParkConfig);

	ReserveParkConfig getConfig(Long parkId, String tenantId);

	boolean saveConfig(ReserveParkConfig config);
}
