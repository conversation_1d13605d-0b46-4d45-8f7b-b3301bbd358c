package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.StaffCarParklot;
import com.lecent.park.vo.StaffCarParklotVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 员工车辆与车场关联信息 服务类
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
public interface IStaffCarParklotService extends BaseService<StaffCarParklot> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param staffCarParklot
	 * @return
	 */
	IPage<StaffCarParklotVO> selectStaffCarParklotPage(IPage<StaffCarParklotVO> page, StaffCarParklotVO staffCarParklot);

	/**
	 * 根据车牌查询员工车辆
	 *
	 * @param plate
	 * @param parkLotId
	 * @return
	 */
	StaffCarParklot selectByPlate(String plate, Long parkLotId);

	/**
	 * 获取员工信息
	 *
	 * @param id id
	 * @return bean
	 */
	StaffCarParklot getStaffCarById(Long id);

	/**
	 * 删除
	 *
	 * @param relatedId
	 * @return
	 */
	boolean customDelete(Long relatedId);

	/**
	 * 统一新增修改
	 *
	 * @param upBean upBean
	 */
	void unifySaveOrUpdate(StaffCarParklot upBean);

	/**
	 * 获取实体
	 *
	 * @param id
	 * @return
	 */
	StaffCarParklot getBeanById(Long id);
}
