package com.lecent.park.service;

import com.lecent.park.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 首页统计service
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface IIndexStatisticsService {

	/**
	 * 最近一周收费金额趋势
	 *
	 * @return
	 */
	List<MoneyTrendVO> lastWeekMoney();

	/**
	 * 今日进出车辆
	 *
	 * @return
	 */
	InAndOutCarVO inAndOutCarAmount();

	/**
	 * 收费金额分布占比
	 *
	 * @return
	 */
	ReceivedMoneyDistributeVO receivedMoney();

	/**
	 * 累计进出场车辆
	 *
	 * @return
	 */
	List<TotalInAndOutCarVO> totalInAndOutCar();

	/**
	 * 入场停车时长分析
	 *
	 * @return
	 */
	ParkingCarDuringVO parkingCarDuring();

	/**
	 * 车辆入场时间分布
	 *
	 * @return
	 */
	List<Map> parkingEnterTimeDistribute();

	/**
	 * 获取车位信息统计
	 *
	 * @param tenantId
	 * @param parklotId
	 * @return
	 */
	CarSpaceVO getCarSpaceInfo(String tenantId, Long parklotId);

	/**
	 * 获取车辆进去统计根据租户或者车场筛选
	 *
	 * @param tenantId
	 * @param parklotId
	 * @return
	 */
	Map<String, Integer> getEnterAndExitTotal(String tenantId, Long parklotId);

	Map<String, long[]> getCarFlow(String tenantId, Long parklotId, String type);

	/**
	 * 获取缴费方式占比
	 *
	 * @param parklotId
	 * @return
	 */
	List<GuardPayWayStatisticsVO> payWayRatio(Long parklotId);
}
