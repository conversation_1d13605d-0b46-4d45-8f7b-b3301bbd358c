package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.OpenGateLogs;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.OpenGateLogsVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 开闸日志 服务类
 *
 * <AUTHOR>
 * @since 2021-02-09
 */
public interface IOpenGateLogsService extends BaseService<OpenGateLogs> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param openGateLogs
	 * @return
	 */
	IPage<OpenGateLogsVO> selectOpenGateLogsPage(IPage<OpenGateLogsVO> page, OpenGateLogsVO openGateLogs);

	/**
	 * 开闸保存日志
	 * @param channelTodoVO
	 */
	void saveLogs(ChannelTodoVO channelTodoVO);
}
