package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.FreeCardAuth;
import com.lecent.park.service.IFreeCardAuthService;
import com.lecent.park.vo.FreeCardAuthVO;
import com.lecent.park.vo.FreeCardBatchExtendEndTime;
import com.lecent.park.wrapper.FreeCardAuthWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 免费车授权 控制器
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/freecardauth")
@Api(value = "免费车授权", tags = "免费车授权")
public class FreeCardAuthController extends BladeController {

	private IFreeCardAuthService freeCardAuthService;

	/**
	 * 根据免费车id获取授权的车场列表
	 */
	@GetMapping("/getListByFreeCardId")
	@ApiOperation(value = "根据免费车id获取授权的车场列表", notes = "根据免费车id获取授权的车场列表")
	public R<List<FreeCardAuthVO>> getListByFreeCardId(@NotNull(message = "免费车id不能为空") Long freeCardId) {
		List<FreeCardAuthVO> authList = freeCardAuthService.getFreeCardAuthVOList(freeCardId);
		return R.data(authList);
	}

	/**
	 * 根据车场Id卡ID查详情
	 */
	@GetMapping("/freeCardByIdAndParkLotId")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据车场Id卡ID查详情", notes = "传入cardAuth")
	public R<FreeCardAuth> freeCardByIdAndParkLotId(FreeCardAuth cardAuth) {
		return R.data(freeCardAuthService.selectByCardId(cardAuth.getFreeCardId(), cardAuth.getParklotId()));
	}

	/**
	 * 取消授权
	 */
	@GetMapping("/cancelAuth")
	@ApiOperation(value = "取消授权", notes = "取消授权")
	public R cancelAuth(@NotNull(message = "授权id不能为空") Long id, String memo) {
		return R.status(freeCardAuthService.cancelAuth(id, memo));
	}

	/**
	 * 恢复授权
	 */
	@GetMapping("/recoverAuth")
	@ApiOperation(value = "恢复授权", notes = "恢复授权")
	public R recoverAuth(@NotNull(message = "授权id不能为空") Long id, String memo) {
		return R.status(freeCardAuthService.recoverAuth(id, memo));
	}

//	/**
//	 * 批量给车场取消授权
//	 */
//	@GetMapping("/batchUpdateStatus")
//	@ApiOperation(value = "批量给车场取消授权", notes = "批量给车场取消授权")
//	public R<List<FreeCardAuth>> batchCancelAuth(@NotEmpty(message = "授权id不能为空") String authIds) {
//		return R.status(freeCardAuthService.batchCancelAuth(Func.toLongList(authIds)));
//	}

	/**
	 * 延长授权时间
	 */
	@PostMapping("/extendEndTime")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "延长授权时间", notes = "传入freeCardAuth")
	public R extendEndTime(@Valid @RequestBody FreeCardAuth freeCardAuth) {
		return R.status(freeCardAuthService.extendEndTime(freeCardAuth));
	}

	/**
	 * 批量延长授权时间
	 */
	@PostMapping("/batchExtendEndTime")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "批量延长授权时间", notes = "传入freeCardAuth")
	public R batchExtendEndTime(@Valid @RequestBody FreeCardBatchExtendEndTime extendEndTime) {
		return R.status(freeCardAuthService.batchExtendEndTime(extendEndTime));
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入freeCardAuth")
	public R<FreeCardAuthVO> detail(FreeCardAuth freeCardAuth) {
		FreeCardAuth detail = freeCardAuthService.getOne(Condition.getQueryWrapper(freeCardAuth));
		return R.data(FreeCardAuthWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 公司角色表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入freeCardAuth")
	public R<IPage<FreeCardAuthVO>> list(FreeCardAuth freeCardAuth, Query query) {
		IPage<FreeCardAuth> pages = freeCardAuthService.page(Condition.getPage(query),
			Condition.getQueryWrapper(freeCardAuth));
		return R.data(FreeCardAuthWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 公司角色表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入freeCardAuth")
	public R<IPage<FreeCardAuthVO>> page(FreeCardAuthVO freeCardAuth, Query query) {
		IPage<FreeCardAuthVO> pages = freeCardAuthService.selectFreeCardAuthPage(Condition.getPage(query),
			freeCardAuth);
		return R.data(pages);
	}

	/**
	 * 新增 公司角色表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入freeCardAuth")
	public R save(@Valid @RequestBody FreeCardAuth freeCardAuth) {
		return R.status(freeCardAuthService.save(freeCardAuth));
	}

	/**
	 * 修改 公司角色表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入freeCardAuth")
	public R update(@Valid @RequestBody FreeCardAuth freeCardAuth) {
		return R.status(freeCardAuthService.updateById(freeCardAuth));
	}

	/**
	 * 新增或修改 公司角色表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入freeCardAuth")
	public R submit(@Valid @RequestBody FreeCardAuth freeCardAuth) {
		return R.status(freeCardAuthService.saveOrUpdate(freeCardAuth));
	}

	/**
	 * 删除 公司角色表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(freeCardAuthService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 批量取消授权
	 */
	@GetMapping("/batchCancelAuth")
	@ApiOperation(value = "批量取消授权", notes = "批量取消授权")
	public R batchCancelAuth(@NotNull(message = "authIds不能为空") @NotEmpty String authIds) {
		return R.status(freeCardAuthService.batchCancelAuth(Func.toLongList(authIds), null));
	}

	/**
	 * 批量恢复授权
	 */
	@GetMapping("/batchRecoverAuth")
	@ApiOperation(value = "批量恢复授权", notes = "批量恢复授权")
	public R batchRecoverAuth(@NotNull(message = "authIds不能为空") String authIds) {
		return R.status(freeCardAuthService.batchRecoverAuth(Func.toLongList(authIds)));
	}

	@PostMapping("/batchDelete")
	@ApiOperation(value = "批量删除免费车", notes = "批量删除免费车")
	public R batchDelete(@NotEmpty(message = "免费车id不能为空") @RequestParam String ids,
						 @NotNull(message = "车场id不能为空") @RequestParam Long parkLotId) {
		return R.status(freeCardAuthService.batchDelete(ids, parkLotId));
	}

}
