package com.lecent.park.service;


import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.entity.*;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.pay.core.enums.PayChannel;

import java.util.List;

/**
 * 无感支付Service
 * <AUTHOR>
 */
public interface INoTouchService{

	/**
	 * 路测无感/ETC支付
	 * @param resultTodo
	 * @param  isDropLock 是否需要降锁
	 * @return 是否扣费成功
	 */
	Boolean roadSideNoTouchPay(ChannelTodoVO resultTodo,boolean isDropLock);


	/**
	 * 室内室外无感/ETC支付
	 * @param parkLot
	 * @param channelTodo
	 * @param parkingOrder
	 * @return
	 */
	void inOutTouchPay(Parklot parkLot, ChannelTodo channelTodo, ParkingOrder parkingOrder);


	/**
	 * 无感支付下单
	 * @param todo
	 * @param payChannel
	 * @return
	 */
	Boolean noTouchUnifiedOrder(ChannelTodoVO todo, ParklotPayWay payChannel);

	/**
	 * 路测无感支付
	 *
	 * @param noTouchPayWays 无感支付方式
	 * @param createWay      创建方式
	 * @param resultTodo     待办
	 * @return {@link Boolean}
	 */
	Boolean roadSideNoTouchPay(List<PayChannel> noTouchPayWays, CreateWay createWay, ChannelTodoVO resultTodo);
}
