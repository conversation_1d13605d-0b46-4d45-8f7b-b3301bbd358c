package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.vo.CCBParkingOrderDetailVO;
import com.lecent.park.vo.TodoItemVO;
import org.springblade.core.mp.support.Query;

/**
 * <AUTHOR>
 * @date 2021-04-07 15:37
 */
public interface JHTodoItemService {

	/**
	 * 用户端停车订单
	 *
	 * @param query           query
	 * @param parkingOrderDTO dto
	 * @return page
	 */
	IPage<TodoItemVO> todoItem(Query query, ParkingOrderDTO parkingOrderDTO);

	/**
	 * 根据停车订单id获取订单详情
	 *
	 * @param parkingId 停车记录id
	 * @return ParkingOrderDetailVO
	 */
	CCBParkingOrderDetailVO getOrderDetail(Long parkingId);


}
