package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.IllegalOrderDTO;
import com.lecent.park.entity.IllegalOrder;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.vo.IllegalOrderVO;
import com.lecent.park.vo.IllegalStopPeriod;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 车辆违法记录 服务类
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
public interface IIllegalOrderService extends BaseService<IllegalOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param illegalOrder
	 * @return
	 */
	IPage<IllegalOrderVO> selectIllegalOrderPage(IPage<IllegalOrderVO> page, IllegalOrderDTO illegalOrder);

	IllegalOrderVO getDetail(Long id);

	/**
	 * 新增违停记录
	 * @param parking
     * @param periodList
     * @return
	 */
    IllegalOrder insertIllegalOrderInfo(ParkingOrder parking, List<IllegalStopPeriod> periodList, Integer illegalType);

}
