package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.ExcelConstant;
import com.lecent.park.core.log.utils.AuditLogger;
import com.lecent.park.dto.ParkMerchantCarStatisticsDTO;
import com.lecent.park.dto.ParkMerchantDTO;
import com.lecent.park.dto.ParkMerchantStatisticDataDTO;
import com.lecent.park.en.ParkLotUserType;
import com.lecent.park.en.merchant.MerchantRuleTypeEnum;
import com.lecent.park.entity.*;
import com.lecent.park.excel.ExportExcelUtils;
import com.lecent.park.mapper.ParkMerchantMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.CarDetailStatisticsVO;
import com.lecent.park.vo.ParkMerchantParklotVO;
import com.lecent.park.vo.ParkMerchantVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springblade.common.utils.DateUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.Role;
import org.springblade.system.feign.IRoleAndDeptClient;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 酒店商户表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Service
@Slf4j
public class ParkMerchantServiceImpl extends BaseServiceImpl<ParkMerchantMapper, ParkMerchant> implements IParkMerchantService {

	@Autowired
	private IUserClient userClient;
	@Resource
	private IRoleAndDeptClient roleAndDeptClient;
	@Autowired
	private ITempParkingOrderService tempOrderService;
	@Lazy
	@Autowired
	private IParkingOrderService parkingOrderService;
	@Lazy
	@Autowired
	private IParkMerchantParklotPlateService authPlateService;
	@Lazy
	@Autowired
	private IParkMerchantParklotService merchantParklotService;

	@Value("${user.defaultPassword:123456}")
	private String defaultPassword;

	@Autowired
	private IUserParklotService userParkLotService;

	@Autowired
	private IParkMerchantRuleService merchantRuleService;

	private static   String log_type="Merchant";

	@Override
	public IPage<ParkMerchantVO> selectParkMerchantPage(IPage<ParkMerchantVO> page, ParkMerchantDTO parkMerchantDto) {
		List<Long> parkLotIds = userParkLotService.getCurrentUserBindParkLotIds(parkMerchantDto.getParklotId());
		// 无关联车场直接返回空
		if (Func.isEmpty(parkLotIds)) {
			return page.setRecords(Collections.emptyList());
		}
		parkMerchantDto.setParklotIdList(parkLotIds);
		List<ParkMerchantVO> list = baseMapper.selectParkMerchantPage(page, parkMerchantDto);

		for (ParkMerchantVO parkMerchantVO : list) {
			parkMerchantVO.setParklotNames(this.getParkNames(parkMerchantVO.getId()));
		}

		return page.setRecords(list);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addParkMerchant(ParkMerchantDTO parkMerchantDto) {
		String tenantId = SecureUtil.getTenantId();
		// 获取角色
		Role hotelMngRole = getHotelMngRole(tenantId);


		User user = new User();
		user.setAccount(parkMerchantDto.getAccount());
		user.setRealName(parkMerchantDto.getRealName());
		user.setPassword(defaultPassword);
		user.setName(parkMerchantDto.getRealName());
		user.setTenantId(tenantId);
		user.setRoleId(hotelMngRole.getId().toString());
		user.setDeptId(SecureUtil.getDeptId());
		user.setPhone(parkMerchantDto.getPhone());
		R<User> userR = userClient.addUser(user);
		LecentAssert.isTrue(Func.notNull(userR)
			&& userR.getCode() == HttpStatus.SC_OK, "生成用户失败," + userR.getMsg());

		User userRData = userR.getData();
		parkMerchantDto.setUserId(userRData.getId());
		save(parkMerchantDto);
		userRData.setBusinessId(parkMerchantDto.getId());
		userClient.updateInfo(userRData);

		return true;
	}

	/**
	 * 修改商户信息
	 * @param parkMerchantDto
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(ParkMerchantDTO parkMerchantDto){
		LecentAssert.notNull(parkMerchantDto.getId(), "id不能为空");
		ParkMerchant oldparkMerchant =this.getById(parkMerchantDto.getId());
		if(oldparkMerchant ==null){
			throw new ServiceException("商户信息不存在！");
		}
		updateUser(oldparkMerchant,parkMerchantDto);
		ParkMerchant newParkMerchant = Func.copy(parkMerchantDto,ParkMerchant.class);
		Map<String,String> fieldsMap =new HashMap<>();
		fieldsMap.put("merName","商户名称");
		fieldsMap.put("manager","商户负责人");
		fieldsMap.put("phone","手机号码");
		AuditLogger.add(oldparkMerchant.getId().toString(), log_type, "基础信息修改", oldparkMerchant, newParkMerchant,  fieldsMap);
		return this.saveOrUpdate(newParkMerchant);
	}


	/**
	 * 更新商户管理员信息
	 * @param parkMerchantDto
	 * @return
	 */
	@Override
	public boolean updateMerchantUser(ParkMerchantDTO parkMerchantDto){
		ParkMerchant oldparkMerchant =this.getById(parkMerchantDto.getId());
		Role hotelMngRole = getHotelMngRole(oldparkMerchant.getTenantId());
		Long oldUserId= oldparkMerchant.getUserId();
		User user = new User();
		user.setAccount(parkMerchantDto.getAccount());
		user.setRealName(parkMerchantDto.getRealName());
		user.setPassword(defaultPassword);
		user.setName(parkMerchantDto.getRealName());
		user.setTenantId(oldparkMerchant.getTenantId());
		user.setRoleId(hotelMngRole.getId().toString());
		user.setDeptId(SecureUtil.getDeptId());
		user.setPhone(parkMerchantDto.getPhone());
		user.setBusinessId(oldparkMerchant.getId());
		R<User> userR = userClient.addUser(user);
		LecentAssert.isTrue(Func.notNull(userR)
			&& userR.getCode() == HttpStatus.SC_OK, "生成用户失败," + userR.getMsg());
		userClient.removeUserBusinessId(oldUserId);
		User userRData = userR.getData();
		parkMerchantDto.setUserId(userRData.getId());
		ParkMerchant newParkMerchant = Func.copy(parkMerchantDto,ParkMerchant.class);
		Map<String,String> fieldsMap =new HashMap<>();
		fieldsMap.put("account","登陆账号");
		fieldsMap.put("phone","手机号码");
		AuditLogger.add(oldparkMerchant.getId().toString(), log_type, "更换商户管理员", oldparkMerchant, newParkMerchant,  fieldsMap);
		return this.saveOrUpdate(newParkMerchant);


	}

	private void updateUser(ParkMerchant oldparkMerchant,ParkMerchantDTO parkMerchantDto){
		if(!oldparkMerchant.getAccount().equals(parkMerchantDto.getAccount())|| !oldparkMerchant.getPhone().equals(parkMerchantDto.getPhone())){
			R<User> userR = userClient.userByAccount(oldparkMerchant.getTenantId(),oldparkMerchant.getAccount());
			if(userR ==null|| userR.getData()==null){
				throw new ServiceException("商户管理员信息不存在！");
			}
			User user = userR.getData();
			user.setAccount(parkMerchantDto.getAccount());
			user.setPhone(parkMerchantDto.getPhone());
			user.setRealName(parkMerchantDto.getRealName());
			user.setName(parkMerchantDto.getRealName());
			boolean b=userClient.updateInfo(user);
			if(!b){
				throw new ServiceException("修改商户管理员信息失败");
			}
		}
	}

	private Role getHotelMngRole(String tenantId) {
		String roleName = "酒店管理员";
		String roleAlias = "hotelMng";
		R<Role> mngRoleR = roleAndDeptClient.getRole(tenantId, roleName);
		LecentAssert.isTrue(Func.notNull(mngRoleR), "服务调用失败");
		Role mngRole = mngRoleR.getData();
		if (mngRole != null && mngRole.getId() != null) {
			return mngRole;
		}
		// 新创建角色
		Role role = new Role();
		role.setTenantId(tenantId);
		role.setParentId(BladeConstant.TOP_PARENT_ID);
		role.setRoleName(roleName);
		role.setRoleAlias(roleAlias);
		role.setSort(2);
		role.setIsDeleted(0);
		R<Role> roleR = roleAndDeptClient.addRole(role);
		return roleR.getData();
	}

	@Override
	public ParkMerchant getByUserId(Long userId) {
		List<ParkMerchant> list = this.list(Wrappers.<ParkMerchant>lambdaQuery()
			.eq(ParkMerchant::getUserId, userId)
			.eq(ParkMerchant::getStatus, 1)
			.orderByDesc(ParkMerchant::getCreateTime));

		if (Func.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean customDel(String ids) {
		List<Long> idList = Func.toLongList(ids);
		List<Long> userIds = new ArrayList<>();

		idList.forEach(id -> {

			ParkMerchant parkMerchant = getById(id);

			ParkMerchantParklot merchantParkLot = merchantParklotService.getOne(Wrappers.<ParkMerchantParklot>lambdaQuery()
				.eq(ParkMerchantParklot::getParkMerchantId, id)
				.eq(ParkMerchantParklot::getIsDeleted, 0).last("limit 1"));
			if (Func.notNull(merchantParkLot)) {
				Parklot parklot = ParkLotCaches.getParkLot(merchantParkLot.getParklotId());
				LecentAssert.isNull(parklot, "[" + parkMerchant.getMerName() + "]已授权[" + parklot.getName() + "],请先取消车场授权!");
			}

			LecentAssert.notNull(parkMerchant, parkMerchant.getMerName() + "商户不存在");
			userIds.add(parkMerchant.getUserId());

		});


		R<Boolean> booleanR = userClient.removeUserById(Func.join(userIds));
		LecentAssert.isTrue(Func.notNull(booleanR) && booleanR.getCode() == HttpStatus.SC_OK, "删除用户失败," + booleanR.getMsg());
		boolean b=  deleteLogic(idList);
		if(b){
			for (Long id:idList){
				AuditLogger.add(id.toString(), log_type, "删除商户", "", "", "删除商户");
			}
		}

		return  b;
	}

	@Override
	public IPage<MerchantOrder> merchantReportPage(IPage<MerchantOrder> page, ParkMerchantCarStatisticsDTO statisticsDTO) {
		putQueryParameter(statisticsDTO);

		if (!isQuery(statisticsDTO)) {
			return page.setRecords(Collections.emptyList());
		}

		List<MerchantOrder> list = tempOrderService.merchantReportPage(page, statisticsDTO);
		list.forEach(e -> {
			Date enterDate = DateUtils.parse(e.getEnterTime(), DateUtils.yMdHms);
			Date exitDate = DateUtils.parse(e.getExitTime(), DateUtils.yMdHms);

			e.setDuration(DateUtils.getDuration(enterDate, exitDate));

			Date authStartDate = DateUtils.parse(e.getAuthStartTime(), DateUtils.yMdHms);
			Date authEndDate = DateUtils.parse(e.getAuthEndTime(), DateUtils.yMdHms);

			if (Func.isNotEmpty(exitDate)) {
				authStartDate = enterDate.getTime() >= authStartDate.getTime() ? enterDate : authStartDate;
				authEndDate = exitDate.getTime() <= authEndDate.getTime() ? exitDate : authEndDate;
				e.setAuthDuration(DateUtils.getDuration(authStartDate, authEndDate));
			}
		});

		return page.setRecords(list);
	}

	private void resolveWeekOrMonth(ParkMerchantCarStatisticsDTO statisticsDTO) {
		Date week = statisticsDTO.getWeek();
		if (Func.isNotEmpty(week)) {
			Date weekend = DateUtils.sundayEnd(week);
			Date weekStart = DateUtils.mondayStart(week);
			statisticsDTO.setPayTimeStart(weekStart);
			statisticsDTO.setPayTimeEnd(weekend);
			statisticsDTO.setWeekStart(weekStart);
			statisticsDTO.setWeekEnd(weekend);
		}
		Date month = statisticsDTO.getMonth();
		if (Func.isNotEmpty(month)) {
			Date monthBegin = DateUtils.getMonthBegin(month);
			Date monthEnd = DateUtils.getMonthEnd(month);
			statisticsDTO.setMonthStart(monthBegin);
			statisticsDTO.setMonthEnd(monthEnd);
			statisticsDTO.setPayTimeStart(monthBegin);
			statisticsDTO.setPayTimeEnd(monthEnd);
		}

		//时间未选择默认本周
		if (Func.isEmpty(statisticsDTO.getWeek()) && Func.isEmpty(statisticsDTO.getMonth())
			&& Func.isEmpty(statisticsDTO.getPayTimeStart()) && Func.isEmpty(statisticsDTO.getPayTimeEnd())) {
			statisticsDTO.setPayTimeStart(DateUtils.mondayStart(new Date()));
			statisticsDTO.setPayTimeEnd(DateUtils.sundayEnd(new Date()));
		}
	}

	@Override
	public CarDetailStatisticsVO merchantReportStatistics(ParkMerchantCarStatisticsDTO statisticsDTO) {
		putQueryParameter(statisticsDTO);

		if (!isQuery(statisticsDTO)) {
			return new CarDetailStatisticsVO();
		}
		CarDetailStatisticsVO orderStatisticsVO = tempOrderService.merchantReportStatistics(statisticsDTO);

		Date payTimeStart = statisticsDTO.getPayTimeStart();
		Date payTimeEnd = statisticsDTO.getPayTimeEnd();

		List<Long> parklotIds = null != statisticsDTO.getParklotId() ? Collections.singletonList(statisticsDTO.getParklotId()) : null;

		List<ParkMerchantParklotVO> merchantParkLotList = merchantParklotService.getMerchantParkLotList(statisticsDTO.getMerchantId(), parklotIds);
		log.info("查询结果为 merchantParkLotList={}", merchantParkLotList);
		if (Func.isEmpty(merchantParkLotList) || merchantParkLotList.size() <= 0) {
			return orderStatisticsVO;
		}

		List<Long> cardIds = new ArrayList<>();
		Integer placeNumber = 0;
		Long totalTimeLength = 0L;

		for (ParkMerchantParklotVO mp : merchantParkLotList) {
			if (mp.getRuleType().equals(statisticsDTO.getType())) {
				cardIds.add(mp.getId());
				placeNumber += mp.getPlaceNum();

				Date preSettlementTime = statisticsDTO.getPayTimeStart() != null ? statisticsDTO.getPayTimeStart() : merchantParklotService.preSettlementTime(mp);
				Date settlementStartTime = statisticsDTO.getPayTimeEnd() != null ? statisticsDTO.getPayTimeEnd() : merchantParklotService.settlementStartTime(mp.getSettleFrequency(), preSettlementTime, mp.getSettlementCycle());

				totalTimeLength += authPlateService.cycleTotalTimeLength(mp.getPlaceNum(), preSettlementTime, settlementStartTime);
			}
		}

		Long usedTimeLength = parkingOrderService.getUsedTimeLength(cardIds, statisticsDTO.getParklotId(), payTimeStart, payTimeEnd, statisticsDTO.getPlate());

		String usedDuration = DateUtils.getDuration(usedTimeLength);
		String totalDuration = DateUtils.getDuration(totalTimeLength);
		orderStatisticsVO.setUsedDuration(usedDuration);
		orderStatisticsVO.setTotalDuration(totalDuration);
		orderStatisticsVO.setPlaceNumber(placeNumber);

		if (totalTimeLength < usedTimeLength) {
			String overflowDuration = DateUtils.getDuration(usedTimeLength - totalTimeLength);
			orderStatisticsVO.setOverflowDuration(overflowDuration);
		}
		return orderStatisticsVO;
	}

	/**
	 * 是否可以查询
	 *
	 * @return true / false
	 */
	private boolean isQuery(ParkMerchantCarStatisticsDTO queryBean) {
		// 物业端空车场直接返回
		if (ParkLotUserType.verify(queryBean.getUserType()).isPm()
			&& null != queryBean.getParklotId()) {
			return true;
		}
		return ParkLotUserType.verify(queryBean.getUserType()).isMerchant()
			&& null != queryBean.getMerchantId();
	}

	/**
	 * 初始化查询参数
	 *
	 * @param statisticsDTO bean
	 */
	private void putQueryParameter(ParkMerchantCarStatisticsDTO statisticsDTO) {
		//计算周或者月的开始结束时间
		resolveWeekOrMonth(statisticsDTO);
		if (Func.isEmpty(statisticsDTO.getType())) {
			statisticsDTO.setType(2);
		}
		ParkMerchant parkMerchant = this.getByUserId(SecureUtil.getUserId());
		if (Func.isNotEmpty(parkMerchant)) {
			statisticsDTO.setMerchantId(parkMerchant.getId());
			statisticsDTO.setUserType(ParkLotUserType.MERCHANT.getCode());
			//查询商户绑定的车场
			if(statisticsDTO.getParklotId() == null){
				if(Func.isBlank(parkMerchant.getParklotIds())){
					throw  new ServiceException("请选择车场!");
				}
				List<Long> parkLotIds= Func.toLongList(",",parkMerchant.getParklotIds());
				if(Func.isEmpty(parkLotIds)){
					throw  new ServiceException("请选择车场!");
				}
				statisticsDTO.setParkLotIdList(parkLotIds);
			}
		} else {
			// 物业端查询绑定车场
			statisticsDTO.setUserType(ParkLotUserType.PM.getCode());
			List<Long> parkLotIds = userParkLotService.getCurrentUserBindParkLotIds(statisticsDTO.getParklotId());

			// 物业端车场为空直接返回空
			if (!parkLotIds.isEmpty()) {
				statisticsDTO.setParklotId(parkLotIds.get(0));
			}
		}
	}

	@Override
	public void merchantReportExport(ParkMerchantCarStatisticsDTO statisticsDTO, HttpServletResponse response) {
		putQueryParameter(statisticsDTO);
		if (!isQuery(statisticsDTO)) {
			LecentAssert.alertException("导出数据不存在");
		}

		String exportSetting = statisticsDTO.getExportSetting();
		List<Map<String, String>> heaederList = Func.readJson(exportSetting, List.class);

		String fileName = "车辆报表列表.xls";
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setHeader("content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			ServletOutputStream outputStream = response.getOutputStream();
			HSSFWorkbook wb = export(heaederList, statisticsDTO);
			wb.write(outputStream);
			outputStream.flush();
			outputStream.close();
		} catch (Exception ex) {
			log.error("", ex);
		}
	}

	private void setTime(MerchantOrder e) {
		Date enterDate = DateUtil.parse(e.getEnterTime(), DateUtil.PATTERN_DATETIME);
		Date exitDate = DateUtil.parse(e.getExitTime(), DateUtil.PATTERN_DATETIME);
		e.setDuration(DateUtils.getDuration(enterDate, exitDate));
		Date authStartDate = DateUtils.parse(e.getAuthStartTime(), DateUtils.yMdHms);
		Date authEndDate = DateUtils.parse(e.getAuthEndTime(), DateUtils.yMdHms);
		e.setAuthDuration(DateUtils.getDuration(authStartDate, authEndDate));
		e.setEnterTime("进场时间：" + e.getEnterTime() + "；出场时间：" + e.getExitTime());
	}

	private HSSFWorkbook export(List<Map<String, String>> heaederList, ParkMerchantCarStatisticsDTO statisticsDTO) {
		int current = 1;
		IPage page = new Page();
		page.setSize(500);
		page.setCurrent(current);
		List<MerchantOrder> list = tempOrderService.merchantReportPage(page, statisticsDTO);
		//要导出的统计数据
		String exportStatisticsData = statisticsDTO.getExportStatisticsData();
		ParkMerchantStatisticDataDTO statisticsData = JsonUtil.parse(exportStatisticsData, ParkMerchantStatisticDataDTO.class);
		if (Func.isEmpty(list)) {
			list = new ArrayList();
			MerchantOrder merchantOrder = new MerchantOrder();
			fillOrderStatisticsInfo(statisticsData, merchantOrder);
			list.add(merchantOrder);
		} else {
			MerchantOrder merchantOrder = list.get(0);
			fillOrderStatisticsInfo(statisticsData, merchantOrder);
		}
		HSSFWorkbook wb = new HSSFWorkbook();
		String sheetName = "车辆报表列表";
		while (Func.isNotEmpty(list)) {
			//设置停车时长
			list.forEach(e -> {
				if (Func.isNotEmpty(e.getEnterTime()) && Func.isNotEmpty(e.getExitTime())) {
					setTime(e);
				}
			});
			wb = ExportExcelUtils.exportExcelWithBean(wb, sheetName, MerchantOrder.class,
				heaederList, list);
			current++;
			page.setCurrent(current);
			list = tempOrderService.merchantReportPage(page, statisticsDTO);
		}
		ExportExcelUtils.addPicture(wb, ExcelConstant.EXCEL_PIC_NAME, MerchantOrder.class);
		return wb;
	}

	@Override
	public List<ParkMerchant> getOptions(Integer ruleType) {

		return baseMapper.getOptions(ruleType);
	}

	@Override
	public List<Parklot> getParkOptions(Integer ruleType) {
		return baseMapper.getParkOptions(ruleType, SecureUtil.getUserId());
	}

	@Override
	public String getParkNames(Long merchantId) {
		return baseMapper.getParkNames(merchantId);
	}

	@Override
	public List<Parklot> getParkListById(Long id) {

		ParkMerchant parkMerchant = this.getById(id);
		if (null == parkMerchant) {
			return Collections.emptyList();
		}

		String parklotIdsStr = parkMerchant.getParklotIds();
		if (StringUtil.isBlank(parklotIdsStr)) {
			return Collections.emptyList();
		}

		List<Parklot> parklotList = new ArrayList<>();
		List<Long> parklotIds = Func.toLongList(parklotIdsStr);
		for (Long parklotId : parklotIds) {
			Parklot parklot = ParkLotCaches.getParkLot(parklotId);
			parklotList.add(parklot);
		}

		return parklotList;
	}

	@Override
	public List<ParkMerchant> getMerchantByParklotId(Long parklotId) {
		return baseMapper.getMerchantByParklotId(parklotId);
	}

	@Override
	public Boolean updateMerchant(ParkMerchant parkMerchant) {

		LecentAssert.notNull(parkMerchant.getId(), "id不能为空");
		ParkMerchant merchant = this.getById(parkMerchant.getId());
		LecentAssert.notNull(merchant, "商户不存在");


		User user = new User();
		user.setPhone(parkMerchant.getPhone());
		user.setId(merchant.getUserId());
		//user.setAccount(parkMerchant.getAccount());
		boolean success = userClient.updateInfo(user);
		LecentAssert.isTrue(success, "更新用户信息失败");

		return updateById(parkMerchant);
	}

	private void fillOrderStatisticsInfo(ParkMerchantStatisticDataDTO statisticsData, MerchantOrder merchantOrder) {
		merchantOrder.setCount(statisticsData.getCount());
		merchantOrder.setTotalAmount(statisticsData.getTotalAmount());
		merchantOrder.setOverflowDuration(statisticsData.getOverflowDuration());
		merchantOrder.setPlaceNumber(statisticsData.getPlaceNumber());
		merchantOrder.setTotalDiscountAmount(statisticsData.getDiscountAmount());
		merchantOrder.setTotalPaidAmount(statisticsData.getPaidAmount());
		merchantOrder.setUsedDuration(statisticsData.getUsedDuration());
		merchantOrder.setTotalDuration(statisticsData.getTotalDuration());
	}

	@Override
	public List<Integer> getMerchantMealType() {
		ParkMerchant merchant = getById(SecureUtil.getBusinessId());
		List<Integer> list = new ArrayList();

		if (merchant != null) {
			List<ParkMerchantParklot> merchantParkLotList = merchantParklotService.list(Wrappers.<ParkMerchantParklot>lambdaQuery().eq(ParkMerchantParklot::getParkMerchantId, merchant.getId()));
			if (Func.isNotEmpty(merchantParkLotList)) {
				merchantParkLotList.forEach(m->{
					ParkMerchantRule merchantRule = merchantRuleService.getById(m.getMerchantRuleId());
					if (merchantRule!=null) {
						list.add(merchantRule.getRuleType());
					}
				});
			}
		}

		if (Func.isEmpty(list)) {
			for (MerchantRuleTypeEnum anEnum : MerchantRuleTypeEnum.values()) {
				list.add(anEnum.getValue());
			}
		}
		return list.stream().distinct().collect(Collectors.toList());
	}
}
