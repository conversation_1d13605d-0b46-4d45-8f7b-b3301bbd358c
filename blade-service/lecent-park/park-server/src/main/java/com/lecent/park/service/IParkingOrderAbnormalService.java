package com.lecent.park.service;

import com.lecent.park.dto.ParkingOrderAbnormalDTO;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.ParkingOrderAbnormal;
import org.springblade.core.mp.base.BaseService;
import java.util.Date;

public interface IParkingOrderAbnormalService extends BaseService<ParkingOrderAbnormal> {

	ParkingOrderAbnormal findParkingOrderAbnormal(Long parkingId);
	/**
	 * 保存异常订单
	 *
	 * @param parkingOrderAbnormalDTO 异常订单DTO
	 * @return 异常订单ID
	 */
	Long saveParkingOrderAbnormal(ParkingOrderAbnormalDTO parkingOrderAbnormalDTO);

	/**
     * 批量处理历史异常订单
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 处理结果
     */
    boolean handleHistoryAbnormalOrders(Date startTime, Date endTime);

	/**
	 * 处理异常订单
	 * @param parkingOrder 停车订单
	 */
	void handleAbnormalOrders(ParkingOrder parkingOrder);

}
