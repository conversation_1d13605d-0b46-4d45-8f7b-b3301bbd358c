package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.SchedulingLogs;
import com.lecent.park.service.ISchedulingLogsService;
import com.lecent.park.vo.SchedulingLogsVO;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.SchedulingLogsMapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 排班日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Service
public class SchedulingLogsServiceImpl extends BaseServiceImpl<SchedulingLogsMapper, SchedulingLogs> implements ISchedulingLogsService {

    @Override
    public IPage<SchedulingLogsVO> selectSchedulingLogsPage(IPage<SchedulingLogsVO> page, SchedulingLogsVO schedulingLogs) {
        return page.setRecords(baseMapper.selectSchedulingLogsPage(page, schedulingLogs));
    }

    @Override
    public List<SchedulingLogsVO> getByScheduleId(Long scheduleId) {
        List<SchedulingLogsVO> list = baseMapper.getByScheduleId(scheduleId);
        list.forEach(s -> s.setRoleName(s.getRole().getName()));
        return list;
    }

    @Override
    public Boolean deleteLogsByScheduleIds(List<Long> scheduleIds) {
        return lambdaUpdate()
                .set(SchedulingLogs::getIsDeleted, 1)
                .in(SchedulingLogs::getScheduleId, scheduleIds)
                .update();
    }

    @Override
    public List<SchedulingLogsVO> getWatchkeeper(SchedulingLogsVO schedulingLogs) {
        if (schedulingLogs.getDispatchTime() == null) {
            schedulingLogs.setDispatchTime(new Date());
        }
        return baseMapper.getWatchkeeper(schedulingLogs);
    }

    @Override
    public List<SchedulingLogsVO> getByScheduleIds(List<Long> scheduleIds) {
        List<SchedulingLogsVO> list = baseMapper.getByScheduleIds(scheduleIds);
        list.forEach(s -> s.setRoleName(s.getRole().getName()));
        return list;
    }
}
