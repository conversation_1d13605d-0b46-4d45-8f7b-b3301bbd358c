package com.lecent.park.wrapper;

import com.lecent.park.entity.VisitorAuth;
import com.lecent.park.vo.VisitorAuthVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 访客授权表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
public class VisitorAuthWrapper extends BaseEntityWrapper<VisitorAuth, VisitorAuthVO>  {

	public static VisitorAuthWrapper build() {
		return new VisitorAuthWrapper();
 	}

	@Override
	public VisitorAuthVO entityVO(VisitorAuth visitorAuth) {
		VisitorAuthVO visitorAuthVO = BeanUtil.copy(visitorAuth, VisitorAuthVO.class);
		return visitorAuthVO;
	}

}
