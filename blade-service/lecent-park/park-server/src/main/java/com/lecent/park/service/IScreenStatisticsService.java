package com.lecent.park.service;

import com.lecent.park.entity.ScreenParklot;
import com.lecent.park.vo.*;

import java.util.List;

/**
 *
 * 盘州大屏展示
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface IScreenStatisticsService {

	/**
	 * 近7天流量分析
	 *
	 * @param parklotId 车场id
	 * @return List<ParkingOrderStatisticsVO>
	 */
	ParkLastWeekVO lastWeekParkingOrder(String parklotId);

	/**
	 * 停车场剩余车位top5
	 *
	 * @return PlaceRemainNumVO
	 */
	List<PlaceRemainNumVO> remainPlaceTopFive();

	/**
	 * 停车时段分析
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	List<StopDurationVO> stopDuration(String parklotId);

	/**
	 * 收入环比
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	FinancialRate financialRate(String parklotId);

	/**
	 * 泊位停车饱和度
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	ParkStopRateVO parkStopRate(String parklotId);

	/**
	 * 泊位利用率分析
	 *
	 * @param parklotId 车场id
	 * @return ParkLastWeekRateVO
	 */
	ParkLastWeekRateVO parkUseRate(String parklotId);


	/**
	 * 财务分析
	 *
	 * @param parklotId 车场id
	 * @return 财务分析
	 */
	List<List<String>> financialAnalysis(String parklotId);

	/**
	 * 大屏所有车场数据  包含经纬度
	 */
	List<ScreenParklot> screenParklots();

	/**
	 * 大屏首页总统计数据
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	TotalStatisticsVO totalStatistics(String parklotId);

}
