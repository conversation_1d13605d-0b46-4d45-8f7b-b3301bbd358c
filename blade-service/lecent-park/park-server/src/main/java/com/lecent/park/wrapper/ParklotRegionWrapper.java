package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotRegion;
import com.lecent.park.vo.ParklotRegionVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场区域信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public class ParklotRegionWrapper extends BaseEntityWrapper<ParklotRegion, ParklotRegionVO>  {

	public static ParklotRegionWrapper build() {
		return new ParklotRegionWrapper();
 	}

	@Override
	public ParklotRegionVO entityVO(ParklotRegion parklotRegion) {
		ParklotRegionVO parklotRegionVO = BeanUtil.copy(parklotRegion, ParklotRegionVO.class);

		//User createUser = UserCache.getUser(parklotRegion.getCreateUser());
		//User updateUser = UserCache.getUser(parklotRegion.getUpdateUser());
		//parklotRegionVO.setCreateUserName(createUser.getName());
		//parklotRegionVO.setUpdateUserName(updateUser.getName());

		return parklotRegionVO;
	}

}
