package com.lecent.park.controller.open.openh5.req;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 支付回调
 *
 * <AUTHOR>
 */
@Data
public class ReqPayOrder {

	/**
	 * 待缴费订单ID
	 */
	private Long todoId;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 第三方订单支付号
	 */
	private String thirdTradeNo;

	/**
	 * 优惠金额
	 */
	private BigDecimal discountAmount;

	/**
	 * 支付状态
	 */
	private Integer payStatus;

	ReqPayOrder() {
		this.discountAmount = BigDecimal.ZERO;
	}
}
