package com.lecent.park.strategy.cardperiod.impl;

import cn.hutool.core.date.DateUtil;
import com.lecent.park.dto.CardDurationDTO;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.strategy.cardperiod.CardPeriodPeriodStrategy;
import com.lecent.park.strategy.cardperiod.abstracts.AbstractCardPeriodService;
import com.lecent.park.vo.CardDurationVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

import static com.lecent.park.strategy.cardperiod.CardPeriodHelper.getRenewalMonths;

/**
 * <AUTHOR>
 * 月卡自然月续费
 */
@Component
@AllArgsConstructor
@Slf4j
public class CardPeriodNaturalMonthStrategy extends AbstractCardPeriodService implements CardPeriodPeriodStrategy {


	@Override
	public CardDurationVO getCardPeriod(CardDurationDTO cardDurationDTO, Card card, CardCategory cardCategory) {


		Date startDate = cardDurationDTO.getStartDate();
		Date endDate = cardDurationDTO.getEndDate();
		LecentAssert.notNull(endDate, "请选择结束时间！");
		LecentAssert.notNull(startDate, "请选择开始时间时间！");
		LecentAssert.isTrue(startDate.getTime() <= endDate.getTime(), "结束时间必须大于开始时间");
		//首次续费
		if (null == card.getEndDate()) {
			//开始时间为当月开始时间，结束时间为当月结束时间
			startDate = DateUtil.beginOfMonth(cardDurationDTO.getStartDate());
		} else {
			startDate = getStartDate(cardDurationDTO.getStartDate(), card, cardCategory);
		}
		endDate = DateUtil.endOfMonth(cardDurationDTO.getEndDate());

		//缴费月数
		int monthNum = getRenewalMonths(startDate, endDate);

		BigDecimal unitPrice = getUnitPrice(monthNum, cardCategory);
		return buildCardDuration(startDate, endDate, monthNum, unitPrice);
	}

}
