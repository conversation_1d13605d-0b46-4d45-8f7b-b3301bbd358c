package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.entity.PlateProperty;
import com.lecent.park.mapper.PlatePropertyMapper;
import com.lecent.park.service.IPlatePropertyService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CacheUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 智慧停车-车场车辆属性表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08
 */
@Slf4j
@Service
public class PlatePropertyServiceImpl extends
	BaseServiceImpl<PlatePropertyMapper, PlateProperty> implements IPlatePropertyService {
	/**
	 * 默认开始时间
	 */
	public static final Date DEFAULT_START_TIME =
		DateUtil.parse("1998-01-24 00:00:00", "yyyy-MM-dd HH:mm:ss");
	/**
	 * 默认结束时间
	 */
	public static final Date DEFAULT_END_TIME =
		DateUtil.parse("2099-01-01 23:59:59", "yyyy-MM-dd HH:mm:ss");

	/**
	 * 查询用户过期天数的卡，默认十五天
	 */
	@Value("${park.card.expiryDay:15}")
	private Integer expiryDay;

	@Override
	public boolean update(String tenantId, Long parkLotId, String plates, Long cardId, PlatePropertyType type) {
		return update(tenantId, parkLotId, plates, cardId, type, DEFAULT_START_TIME, DEFAULT_END_TIME);
	}

	@Override
	public boolean update(String tenantId, Long parkLotId, String plates, Long cardId, PlatePropertyType type, Date startTime, Date endTime) {
		// status 默认为1
		return update(tenantId, parkLotId, plates, cardId, type, startTime, endTime, EnableStatus.ACTIVE.getCode());
	}

	@Override
	public boolean update(String tenantId, String parkLotIds, String plates, Long cardId, PlatePropertyType type, Date startTime, Date endTime, Integer status) {
		for (Long parkLotId : Func.toLongList(parkLotIds)) {
			update(tenantId, parkLotId, plates, cardId, type, startTime, endTime, status);
		}
		return true;
	}

	@Override
	public boolean update(String tenantId, Long parkLotId, String plates, Long cardId, PlatePropertyType type, Date startTime, Date endTime, Integer status) {
		LecentAssert.notBlank(tenantId, "更新聚合表失败，租户不能为空");
		LecentAssert.notNull(parkLotId, "更新聚合表失败，车场ID不能为空");
		LecentAssert.notBlank(plates, "更新聚合表失败，车牌不能为空");
		LecentAssert.notNull(cardId, "更新聚合表失败，carId不能为空");
		LecentAssert.notNull(type, "更新聚合表失败，类型不能为空");

		// 清除缓存
		CacheUtils.delKey(type.getCacheName() + cardId);
		// 获取车牌属性
		Map<String, PlateProperty> mapPlateProperty = getMapPlateProperty(parkLotId, cardId, type);
		// 考虑变更车牌情况
		mapPlateProperty.values().removeIf(plateProperty -> {
			if (!plates.contains(plateProperty.getPlate())) {
				removeById(plateProperty.getId());
				return true;
			}
			return false;
		});

		// 处理多车牌问题
		for (String plate : plates.split(StringPool.COMMA)) {
			PlateProperty bean = new PlateProperty();

			PlateProperty plateProperty = mapPlateProperty.get(plate);
			if (null != plateProperty) {
				bean.setId(plateProperty.getId());
			}
			bean.setTenantId(tenantId);
			bean.setParklotId(parkLotId);
			bean.setPlate(plate);
			bean.setCardId(cardId);
			bean.setType(type);
			bean.setStartTime(startTime);
			bean.setEndTime(endTime);
			bean.setStatus(status);
			saveOrUpdate(bean);

			// 删除当前待办缓存
			ParkLotCaches.delChannelTodo(parkLotId, plate);
		}

		// 清除缓存
		CacheUtils.delKeyAfterTxCommitted(type.getCacheName() + cardId);

		return true;
	}

	/**
	 * 通过属性查询实体
	 *
	 * @param cardId 卡ID
	 * @param type   type
	 * @return p
	 */
	private Map<String, PlateProperty> getMapPlateProperty(Long parkLotId, Long cardId, PlatePropertyType type) {
		return list(Wrappers.<PlateProperty>lambdaQuery()
			.select(PlateProperty::getId, PlateProperty::getPlate)
			.eq(PlateProperty::getParklotId, parkLotId)
			.eq(PlateProperty::getCardId, cardId)
			.eq(PlateProperty::getType, type))
			.stream()
			.collect(Collectors.toMap(PlateProperty::getPlate, Function.identity(), (a, b) -> a));
	}

	@Override
	public List<PlateProperty> queryPlateProperties(Long parkLotId, String plate, Date enterDate, Date exitDate) {
		return baseMapper.queryPlateProperties(parkLotId, plate, enterDate, exitDate);
	}

	@Override
	public boolean removeByCardIds(PlatePropertyType type, Long... cardIds) {
		return removeByCardIds(type, Arrays.asList(cardIds));
	}

	@Override
	public boolean removeByCardIds(PlatePropertyType type, List<Long> cardIds) {
		log.info("删除车辆属性 by cardId={}", cardIds);
		// 删除缓存
		CacheUtils.delKeys(type.getCacheName(), cardIds.stream().map(Object::toString).collect(Collectors.toList()));

		// delete
		List<PlateProperty> list = list(Wrappers.<PlateProperty>lambdaQuery().eq(PlateProperty::getType, type)
			.in(PlateProperty::getCardId, cardIds));

		if (list.isEmpty()) {
			return true;
		}
		for (PlateProperty p : list) {
			removeById(p.getId());
		}

		// 删除缓存
		CacheUtils.delKeyAfterTxCommitted(
			type.getCacheName(),
			cardIds.stream().map(Object::toString).collect(Collectors.toList())
		);
		return true;
	}
}
