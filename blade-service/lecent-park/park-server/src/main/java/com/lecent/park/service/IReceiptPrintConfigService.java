package com.lecent.park.service;

import com.lecent.park.dto.ReceiptPrintDTO;
import com.lecent.park.entity.ReceiptPrintConfig;
import com.lecent.park.vo.PdaReceiptPrintConfigVO;
import com.lecent.park.vo.ReceiptPrintConfigVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 小票打印配置 服务类
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
public interface IReceiptPrintConfigService extends BaseService<ReceiptPrintConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param receiptPrintConfig
	 * @return
	 */
	IPage<ReceiptPrintConfigVO> selectReceiptPrintConfigPage(IPage<ReceiptPrintConfigVO> page, ReceiptPrintConfigVO receiptPrintConfig);

	PdaReceiptPrintConfigVO getPrintContentByParkingId(Long parkingId);

	/**
	 * 查询可用的小票配置
	 * @param configId 配置
	 * @return
	 */
	ReceiptPrintConfig queryPrintConfigByConfigId(Long configId);

	/**
	 * 保存小票配置
	 * @param receiptPrintConfig 配置
	 * @return
	 */
	Boolean customSaveOrUpdate(ReceiptPrintConfig receiptPrintConfig);

	/**
	 * 小票打印
	 * @param printDTO
	 */
	void receiptPrint(ReceiptPrintDTO printDTO);
}
