package com.lecent.park.wrapper;

import com.lecent.park.entity.BUserPlate;
import com.lecent.park.vo.BUserPlateVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 微信用户车牌包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-07-06
 */
public class BUserPlateWrapper extends BaseEntityWrapper<BUserPlate, BUserPlateVO>  {

	public static BUserPlateWrapper build() {
		return new BUserPlateWrapper();
 	}

	@Override
	public BUserPlateVO entityVO(BUserPlate bUserPlate) {
		BUserPlateVO bUserPlateVO = BeanUtil.copy(bUserPlate, BUserPlateVO.class);

		//User createUser = UserCache.getUser(bUserPlate.getCreateUser());
		//User updateUser = UserCache.getUser(bUserPlate.getUpdateUser());
		//bUserPlateVO.setCreateUserName(createUser.getName());
		//bUserPlateVO.setUpdateUserName(updateUser.getName());

		return bUserPlateVO;
	}

}
