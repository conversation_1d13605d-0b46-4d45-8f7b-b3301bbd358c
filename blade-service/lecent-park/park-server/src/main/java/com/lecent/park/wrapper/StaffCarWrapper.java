package com.lecent.park.wrapper;

import com.lecent.park.entity.StaffCar;
import com.lecent.park.vo.StaffCarVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 员工车辆包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
public class StaffCarWrapper extends BaseEntityWrapper<StaffCar, StaffCarVO>  {

	public static StaffCarWrapper build() {
		return new StaffCarWrapper();
 	}

	@Override
	public StaffCarVO entityVO(StaffCar staffCar) {
		StaffCarVO staffCarVO = Objects.requireNonNull(BeanUtil.copy(staffCar, StaffCarVO.class));

		//User createUser = UserCache.getUser(staffCar.getCreateUser());
		//User updateUser = UserCache.getUser(staffCar.getUpdateUser());
		//staffCarVO.setCreateUserName(createUser.getName());
		//staffCarVO.setUpdateUserName(updateUser.getName());

		return staffCarVO;
	}

}
