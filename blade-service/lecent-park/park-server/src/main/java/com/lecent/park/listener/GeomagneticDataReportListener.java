package com.lecent.park.listener;

import com.lecent.park.event.IASyncDataForwardingEvent;
import com.lecent.park.event.geomagnetic.GeomagneticHeartbeatEvent;
import com.lecent.park.event.geomagnetic.GeomagneticManageHeartbeatEvent;
import com.lecent.park.event.geomagnetic.GeomagneticParkPlaceChangeEvent;
import com.lecent.park.event.parkplace.GeomagneticRegisterEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

/**
 * 地磁数据上报监听
 *
 * <AUTHOR>
 */
@Slf4j
@EnableAsync
@Component
public class GeomagneticDataReportListener {

    /**
	 * 管理器上发检测器心跳数据帧
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.GeomagneticCommandConstant).DEVICE_HEARTBEAT")
	public void geomagneticHeartbeat(IASyncDataForwardingEvent event) {
		GeomagneticHeartbeatEvent geomagneticHeartbeatEvent = (GeomagneticHeartbeatEvent) event;
		log.info("接收到地磁心跳数据={}", geomagneticHeartbeatEvent);
	}

	/**
	 * 管理器上发自身心跳数据帧
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.GeomagneticCommandConstant).DEVICE_MANAGE_HEARTBEAT")
	public void geomagneticContainerHeartbeat(IASyncDataForwardingEvent event) {
		GeomagneticManageHeartbeatEvent containerHeartbeatEvent = (GeomagneticManageHeartbeatEvent) event;
		log.info("接收到管理器上发自身心跳数据帧={}", containerHeartbeatEvent);
	}

	/**
	 * 管理器向平台注册数据帧
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.GeomagneticCommandConstant).DEVICE_REGISTER")
	public void geomagneticRegister(IASyncDataForwardingEvent event) {
		GeomagneticRegisterEvent registerEvent = (GeomagneticRegisterEvent) event;
		log.info("接收到管理器向平台注册数据帧={}", registerEvent);
	}

	/**
	 * 管理器上发检测器车位变化数据 帧
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.GeomagneticCommandConstant).DEVICE_PARKPLACE_CHANGE")
	public void geomagneticParkPlaceChange(IASyncDataForwardingEvent event) {
		GeomagneticParkPlaceChangeEvent placeChangeEvent = (GeomagneticParkPlaceChangeEvent) event;
		log.info("管理器上发检测器车位变化数据帧={}", placeChangeEvent);
	}

}
