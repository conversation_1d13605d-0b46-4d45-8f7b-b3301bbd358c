package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParklotInvoiceConfig;
import com.lecent.park.vo.ParklotInvoiceConfigVO;
import org.springblade.core.mp.base.BaseService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
public interface IParklotInvoiceConfigService extends BaseService<ParklotInvoiceConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotInvoiceConfig
	 * @return
	 */
	IPage<ParklotInvoiceConfigVO> selectParklotInvoiceConfigPage(IPage<ParklotInvoiceConfigVO> page, ParklotInvoiceConfigVO parklotInvoiceConfig);

	/**
	 *
	 * 根据车场id获取配置
	 * @param parkLotId
	 * @return
	 */
	ParklotInvoiceConfig getConfig(String parkLotId);
}
