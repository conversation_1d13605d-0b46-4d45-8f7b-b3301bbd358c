package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotInvoiceRecord;
import com.lecent.park.vo.ParklotInvoiceRecordVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场-发票开票记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-09-17
 */
public class ParklotInvoiceRecordWrapper extends BaseEntityWrapper<ParklotInvoiceRecord, ParklotInvoiceRecordVO>  {

	public static ParklotInvoiceRecordWrapper build() {
		return new ParklotInvoiceRecordWrapper();
 	}

	@Override
	public ParklotInvoiceRecordVO entityVO(ParklotInvoiceRecord parklotInvoiceRecord) {
		ParklotInvoiceRecordVO parklotInvoiceRecordVO = BeanUtil.copy(parklotInvoiceRecord, ParklotInvoiceRecordVO.class);

		//User createUser = UserCache.getUser(parklotInvoiceRecord.getCreateUser());
		//User updateUser = UserCache.getUser(parklotInvoiceRecord.getUpdateUser());
		//parklotInvoiceRecordVO.setCreateUserName(createUser.getName());
		//parklotInvoiceRecordVO.setUpdateUserName(updateUser.getName());

		return parklotInvoiceRecordVO;
	}

}
