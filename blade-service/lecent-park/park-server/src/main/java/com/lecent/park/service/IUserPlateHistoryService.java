package com.lecent.park.service;

import com.lecent.park.entity.UserPlateHistory;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 微信用户历史缴费车牌
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
public interface IUserPlateHistoryService extends BaseService<UserPlateHistory> {

	/**
	 * 根据用户id查询车牌
	 *
	 * @param userId 用户id
	 * @return {@link List}<{@link String}>
	 */
	List<String> queryPlateListByUserId(Long userId);

	/**
	 * 是否记录用户车牌
	 *
	 * @param plate  车牌
	 * @param userId 开放id
	 * @return {@link Boolean}
	 */
	Boolean haveUserPlate(String plate, String userId);

	/**
	 * 保存用户缴费车牌历史记录
	 *
	 * @param tenantId 承租者id
	 * @param openId   开放id
	 * @param plate    车牌
	 */
	void saveUserPlateHistory(String tenantId, String openId, String plate);
}
