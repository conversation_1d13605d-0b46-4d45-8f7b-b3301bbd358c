package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.MergeOrderDTO;
import com.lecent.park.dto.RecoverOrderDTO;
import com.lecent.park.service.IMergeOrderService;
import com.lecent.park.vo.MergeOrderVO;
import com.lecent.park.vo.RecoverOrderStatistics;
import com.lecent.park.vo.RecoverOrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 合并支付订单
 *
 * <AUTHOR>
 * @date 2023/07/31
 */
@RestController
@AllArgsConstructor
@RequestMapping("/merge-order")
@Api(value = "合并支付订单表", tags = "合并支付订单表接口")
public class MergeOrderController extends BladeController {

    private final IMergeOrderService mergeOrderService;


    /**
     * 追缴订单页
     */
    @GetMapping("/recover-order/page")
    @ApiOperation(value = "追缴订单页", notes = "传入recoverOrder")
    public R<IPage<RecoverOrderVO>> pageRecoverOrder(RecoverOrderDTO recoverOrder, Query query) {
		if(Func.isBlank(query.getAscs()) && Func.isBlank(query.getDescs())){
			query.setDescs("payTime");
		}
        return R.data(mergeOrderService.pageRecoverOrder(Condition.getPage(query), recoverOrder));
    }

    /**
     * 收费员列表
     */
    @GetMapping("/toll-collector")
    @ApiOperation(value = "收费员列表")
    public R<List<RecoverOrderVO>> listTollCollector() {
        return R.data(mergeOrderService.listTollCollector());
    }

    /**
     * 统计追缴订单
     */
    @GetMapping("/recover-order/count")
    @ApiOperation(value = "统计追缴订单", notes = "传入recoveredOrder")
    public R<RecoverOrderStatistics> countRecoverOrder(RecoverOrderDTO recoveredOrder) {
        return R.data(mergeOrderService.countRecoverOrder(recoveredOrder));
    }

    /**
     * 导出追缴订单
     */
    @PostMapping("/recover-order/export")
    @ApiOperation(value = "导出追缴订单", notes = "传入recoveredOrder")
    public void exportRecoverOrder(HttpServletResponse response, RecoverOrderDTO recoverOrder) {
        mergeOrderService.exportRecoverOrder(response, recoverOrder);
    }

    /**
     * 合并退款
     */
    @PostMapping("/refund")
    @ApiOperation(value = "合并退款", notes = "传入recoveredOrder")
    public R<Boolean> mergeRefund(@RequestBody MergeOrderDTO mergeOrderDTO) {
        return R.data(mergeOrderService.mergeRefund(mergeOrderDTO));
    }

    /**
     * 合并退款详情
     */
    @GetMapping("/refund/detail")
    @ApiOperation(value = "合并退款详情", notes = "传入recoveredOrder")
    public R<MergeOrderVO> refundDetail(@RequestParam String orderIds) {
        return R.data(mergeOrderService.refundDetail(Func.toLongList(orderIds)));
    }

	/**
	 * 追缴订单页
	 */
	@GetMapping("/page")
	@ApiOperation(value = "查询合并订单", notes = "传入mergeOrderVO")
	public R<IPage<MergeOrderVO>> findMergeOrderPage(MergeOrderVO mergeOrderVO, Query query) {
		return R.data(mergeOrderService.findMergeOrderList(Condition.getPage(query), mergeOrderVO));
	}
}
