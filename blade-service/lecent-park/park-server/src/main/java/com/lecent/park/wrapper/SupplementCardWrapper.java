package com.lecent.park.wrapper;

import com.lecent.park.entity.SupplementCard;
import com.lecent.park.vo.SupplementCardVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 附属卡(亲情卡)表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
public class SupplementCardWrapper extends BaseEntityWrapper<SupplementCard, SupplementCardVO>  {

	public static SupplementCardWrapper build() {
		return new SupplementCardWrapper();
 	}

	@Override
	public SupplementCardVO entityVO(SupplementCard supplementCard) {
		SupplementCardVO supplementCardVO = BeanUtil.copy(supplementCard, SupplementCardVO.class);

		//User createUser = UserCache.getUser(supplementCard.getCreateUser());
		//User updateUser = UserCache.getUser(supplementCard.getUpdateUser());
		//supplementCardVO.setCreateUserName(createUser.getName());
		//supplementCardVO.setUpdateUserName(updateUser.getName());

		return supplementCardVO;
	}

}
