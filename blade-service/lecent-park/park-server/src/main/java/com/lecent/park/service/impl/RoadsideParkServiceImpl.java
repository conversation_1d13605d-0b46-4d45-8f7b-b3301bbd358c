package com.lecent.park.service.impl;

import com.alibaba.fastjson.JSON;
import com.lecent.park.access.ParkAccessClient;
import com.lecent.park.access.ParkTodoContext;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.ParkingStatus;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.constant.ChannelConstant;
import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.en.channeltodo.ChargeEnum;
import com.lecent.park.entity.*;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.service.*;
import com.lecent.park.vo.RoadSideParkingDTO;
import com.leliven.park.domain.order.parking.event.ParkingOrderEventType;
import com.leliven.park.domain.order.parking.support.ParkingOrderDomainService;
import com.leliven.park.infrastructure.gateway.persistence.order.converter.ParkingOrderConverter;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 路边停车服务类
 */
@Service
@Slf4j
public class RoadsideParkServiceImpl implements IRoadsideParkService {

	@Resource
	private ParkingOrderConverter parkingOrderConverter;
	@Resource
	private ParkingOrderDomainService parkingOrderDomainService;
	@Resource
	private IParkingOrderService parkingService;
	@Resource
	private ITempParkingOrderService tempParkingOrderService;
	@Resource
	private ITempParkingUnpaidOrderService unpaidOrderService;
    @Lazy
	@Resource
	private IChannelTodoService todoService;
    @Resource
	private ParkAccessClient parkAccessClient;

    @Override
	@Transactional(rollbackFor = Exception.class)
	public boolean absentPaymentSuccessfulCallback(TempParkingOrder tempParkingOrder) {
		return updateTempParkingOrder(tempParkingOrder, null);
	}

	@Override
	public Boolean updateTempParkingOrder(TempParkingOrder tempParkingOrder, ChannelTodo todo) {
		if (ObjectUtil.isNotEmpty(tempParkingOrder)) {
			if (tempParkingOrder.getPayStatus().equals(org.springblade.common.en.payment.PayStatus.SUCCESS.getKey())) {
				return true;
			}

			//更新订单
			tempParkingOrder.setPaidAmount(tempParkingOrder.getReceiveAmount());
			tempParkingOrder.setPayStatus(org.springblade.common.en.payment.PayStatus.SUCCESS.getKey());
			tempParkingOrderService.saveOrUpdate(tempParkingOrder);


			//更新停车记录
			ParkingOrder parkingOrder = parkingService.getById(tempParkingOrder.getParkingId());
			if (Func.isNull(parkingOrder)) {
				return false;
			}
			Parklot parklot = ParkLotCaches.getParkLot(parkingOrder.getParklotId());

			ParkingOrderEventType parkingOrderEventType = null;
			//路内
			if (parklot.isRoadSide()) {
				ProjectCost projectCost = tempParkingOrderService.selectPaidByParkingId(tempParkingOrder.getParkingId());
				log.info("updateTempParkingOrder projectCost={}", Func.toJson(projectCost));
				parkingOrder.setTotalAmount(projectCost.getTotalAmount());
				parkingOrder.setPaidAmount(projectCost.getPaidAmount());
				parkingOrder.setDiscountAmount(projectCost.getDiscountAmount());
				parkingOrder.setCouponDiscountAmount(projectCost.getCouponDiscountAmount());
				parkingOrder.setMerchantAmount(projectCost.getMerchantAmount());
				parkingOrder.setUnusualAmount(projectCost.getUnusualAmount());
				// 停车订单车牌为非正常车牌，且临停订单中车牌为正常车牌时，则替换停车订单车牌为正常车牌
				if (!PlateValidator.isPlate(parkingOrder.getPlate()) && PlateValidator.isPlate(tempParkingOrder.getPlate())) {
					parkingOrder.setPlate(tempParkingOrder.getPlate());
				}
			}else{
				parkingOrder.setTotalAmount(tempParkingOrder.getTotalAmount());
				parkingOrder.setPaidAmount(tempParkingOrder.getPaidAmount());
				parkingOrder.setDiscountAmount(tempParkingOrder.getDiscountAmount());
				parkingOrder.setUnusualAmount(tempParkingOrder.getUnusualAmount());
			}


			if (ParkingStatus.SPRKING_PRESENT.equals(parkingOrder.getParkingStatus())) {
				parkingOrderEventType = ParkingOrderEventType.EXITED;
				parkingOrder.setExitTime(tempParkingOrder.getPayTime());
				parkingOrder.setParkingStatus(ParkingStatus.SPRKING_APPEARED);
				parkingOrder.setExitTodoId(tempParkingOrder.getTodoId());
				todo = Optional.ofNullable(todo).orElseGet(() -> this.todoService.getById(tempParkingOrder.getTodoId()));
				if (todo != null) {
					parkingOrder.setRelationId(todo.getRelationId());
					parkingOrder.setRelationType(todo.getRelationType());
					parkingOrder.setCardId(todo.getCardId());
					parkingOrder.setUnusualAmountType(todo.getErrorAmountType());
					parkingOrder.setExitChannelId(todo.getChannelId());
					parkingOrder.setExitWay(todo.getTriggerType());
					parkingOrder.setExitTime(todo.getDate());
					parkingOrder.setExitImageUrl(todo.getImageUrl());
					parkingOrder.setExitTodoId(todo.getId());
					parkingOrder.setChargeRuleId(todo.getChargeRuleId());
					parkingOrder.setPayTypes(todo.getChargeType());
					parkingOrder.setReasonIds(String.valueOf(todo.getReasonId()));
					parkingOrder.setTimeNodeDetail(todo.getTimeNodeDetail());
				}
				parkingOrder.setDurationTime(DateUtils.getDuration(parkingOrder.getEnterTime(), parkingOrder.getExitTime()));
			}

			List<TimeNode> timeNodeList = parkingService.getParkingTimeNodeList(parkingOrder.getParkingTimeNode());
			timeNodeList.add(new TimeNode(DateUtils.format(tempParkingOrder.getPayTime()), "离场缴费", null, true));
			parkingOrder.setParkingTimeNode(JSON.toJSONString(timeNodeList));

			this.parkingOrderDomainService.update(parkingOrderConverter.fromDO(parkingOrder), parkingOrderEventType);
			//删除代缴费的表
			unpaidOrderService.updatePaidByParkingId(tempParkingOrder.getParkingId());
		}

		return true;

	}

	@Override
	public ParkTodoContext getRoadSideTodoContext(ParkingPlace place) {
		RoadSideParkingDTO build = RoadSideParkingDTO.builder().placeId(place.getId()).build();

		ChannelTodo todo = buildRoadSideTodo(build);
		todo.setPlaceId(place.getId());
		todo.setPlate(place.getPlaceCode());
		todo.setParklotId(place.getParklotId());
		todo.setTriggerType(ChannelWay.WAY_2.getValue());
		ParkTodoContext context = ParkTodoContext.builder(new ParkChannelMessageEvent(todo));
		//创建订单
		parkAccessClient.getParkingCost(context);
		return context;
	}

	private ChannelTodo buildRoadSideTodo(RoadSideParkingDTO req) {
		ChannelTodo todo = new ChannelTodo();
		Date date = req.getDate() != null ? req.getDate() : new Date();
		todo.setPlate(req.getPlate());
		todo.setStatus(ChannelConstant.TODO_STATUS_AUTO_PASS);
		todo.setImageUrl(req.getImageUrl());
		todo.setEnterImageUrl(req.getImageUrl());
		todo.setTriggerType(req.getTriggerType());
		todo.setChargeType(ChargeEnum.TEMP_STOP.name());
		todo.setDate(date);
		todo.setEnterTime(date);
		todo.setOccupied(false);
		todo.setIsMqOpenGate(false);
		todo.setVehicleId(req.getVehicleId());
		return todo;
	}
}
