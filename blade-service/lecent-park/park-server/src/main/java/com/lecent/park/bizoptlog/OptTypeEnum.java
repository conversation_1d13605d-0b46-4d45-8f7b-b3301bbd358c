package com.lecent.park.bizoptlog;

/**
 * 操作类型
 *
 * <AUTHOR>
 * @date 2020-06-03 09:52
 */
public enum OptTypeEnum {
	EDIT(0, "修改"),

	DEL(1, "删除"),

	ADD(2, "新增"),

	DISABLE(3, "禁用"),
	ENABLE(4, "启用"),

	EDIT_PLATE(5, "修改车牌"),

	MATCH_ENTER_RECORD(6, "匹配进场记录"),

	EDIT_AMOUNT(7, "修改金额"),

	MANUAL_REGISTER(8, "手动登记"),

	DESTROY(9, "注销");

	Integer value;
	String name;

	OptTypeEnum(Integer value, String name) {
		this.value = value;
		this.name = name;
	}

	public Integer getValue() {
		return value;
	}

	public String getName() {
		return name;
	}


	public static String getNameByKey(Integer key) {
		String ret = "";
		OptTypeEnum[] values = values();
		for (OptTypeEnum optTypeEnum : values) {
			if (optTypeEnum.getValue().equals(key)) {
				ret = optTypeEnum.getName();
			}
		}
		return ret;
	}

}
