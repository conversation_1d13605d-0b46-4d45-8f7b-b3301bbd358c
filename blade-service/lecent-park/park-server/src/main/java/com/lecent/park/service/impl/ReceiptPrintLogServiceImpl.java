package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lecent.park.entity.ReceiptPrintLog;
import com.lecent.park.vo.ReceiptPrintLogVO;
import com.lecent.park.mapper.ReceiptPrintLogMapper;
import com.lecent.park.service.IReceiptPrintLogService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 小票打印记录 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
public class ReceiptPrintLogServiceImpl extends BaseServiceImpl<ReceiptPrintLogMapper, ReceiptPrintLog> implements IReceiptPrintLogService {

	@Override
	public IPage<ReceiptPrintLogVO> selectReceiptPrintLogPage(IPage<ReceiptPrintLogVO> page, ReceiptPrintLogVO receiptPrintLog) {
		return page.setRecords(baseMapper.selectReceiptPrintLogPage(page, receiptPrintLog));
	}

	@Override
	public List<ReceiptPrintLogVO> selectReceiptPrintLogByParkingId(Long parkingId) {
		ReceiptPrintLogVO logVO = new ReceiptPrintLogVO();
		logVO.setParkingId(parkingId);
		return baseMapper.selectReceiptPrintLogPage(new Page(), logVO);
	}
}
