package com.lecent.park.wrapper;

import com.lecent.park.entity.PlateRecognize;
import com.lecent.park.vo.PlateRecognizeVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 对象存储表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
public class PlateRecognizeWrapper extends BaseEntityWrapper<PlateRecognize, PlateRecognizeVO> {

	public static PlateRecognizeWrapper build() {
		return new PlateRecognizeWrapper();
	}

	@Override
	public PlateRecognizeVO entityVO(PlateRecognize plateRecognize) {
		PlateRecognizeVO plateRecognizeVO = Objects.requireNonNull(BeanUtil.copy(plateRecognize, PlateRecognizeVO.class));

		//User createUser = UserCache.getUser(plateRecognize.getCreateUser());
		//User updateUser = UserCache.getUser(plateRecognize.getUpdateUser());
		//plateRecognizeVO.setCreateUserName(createUser.getName());
		//plateRecognizeVO.setUpdateUserName(updateUser.getName());

		return plateRecognizeVO;
	}

}
