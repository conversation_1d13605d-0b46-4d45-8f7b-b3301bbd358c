package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.EmployeeCarParklot;
import com.lecent.park.mapper.EmployeeCarParklotMapper;
import com.lecent.park.service.IEmployeeCarParklotService;
import com.lecent.park.vo.EmployeeCarParklotVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工车辆关联车场表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Service
public class EmployeeCarParklotServiceImpl extends BaseServiceImpl<EmployeeCarParklotMapper, EmployeeCarParklot> implements IEmployeeCarParklotService {

	@Override
	public IPage<EmployeeCarParklotVO> selectEmployeeCarParklotPage(IPage<EmployeeCarParklotVO> page, EmployeeCarParklotVO employeeCarParklot) {
		return page.setRecords(baseMapper.selectEmployeeCarParklotPage(page, employeeCarParklot));
	}

	@Override
	public List<EmployeeCarParklotVO> employeeCarContact(Long id) {
		return baseMapper.employeeCarContact(id);
	}

}
