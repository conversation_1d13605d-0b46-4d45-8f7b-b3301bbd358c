package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.IllegalOrderDTO;
import com.lecent.park.entity.IllegalOrder;
import com.lecent.park.service.IIllegalOrderService;
import com.lecent.park.vo.IllegalOrderVO;
import com.lecent.park.wrapper.IllegalOrderWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 车辆违法记录 控制器
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
@RestController
@AllArgsConstructor
@RequestMapping("/illegalorder")
@Api(value = "车辆违法记录", tags = "车辆违法记录接口")
public class IllegalOrderController extends BladeController {

	private IIllegalOrderService illegalOrderService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入illegalOrder")
	public R<IllegalOrderVO> detail(IllegalOrderDTO illegalOrderDTO) {
		return R.data(illegalOrderService.getDetail(illegalOrderDTO.getId()));
}

	/**
	 * 分页 车辆违法记录
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入illegalOrder")
	public R<IPage<IllegalOrderVO>> list(IllegalOrderDTO illegalOrder, Query query) {
		IPage<IllegalOrder> pages = illegalOrderService.page(Condition.getPage(query), Condition.getQueryWrapper(illegalOrder));
		return R.data(IllegalOrderWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 车辆违法记录
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入illegalOrder")
	public R<IPage<IllegalOrderVO>> page(IllegalOrderDTO illegalOrderDTO, Query query) {
		IPage<IllegalOrderVO> pages = illegalOrderService.selectIllegalOrderPage(Condition.getPage(query), illegalOrderDTO);
		return R.data(pages);
	}

	/**
	 * 新增 车辆违法记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入illegalOrder")
	public R save(@Valid @RequestBody IllegalOrder illegalOrder) {
		return R.status(illegalOrderService.save(illegalOrder));
	}

	/**
	 * 修改 车辆违法记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入illegalOrder")
	public R update(@Valid @RequestBody IllegalOrder illegalOrder) {
		return R.status(illegalOrderService.updateById(illegalOrder));
	}

	/**
	 * 新增或修改 车辆违法记录
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入illegalOrder")
	public R submit(@Valid @RequestBody IllegalOrder illegalOrder) {
		return R.status(illegalOrderService.saveOrUpdate(illegalOrder));
	}


	/**
	 * 删除 车辆违法记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(illegalOrderService.deleteLogic(Func.toLongList(ids)));
	}


}
