package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.charge.ProjectCalculate;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.entity.BUserPlate;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.service.*;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DiscountVersatileServiceImpl implements IDiscountVersatileService {

	@Autowired
	private ITempParkingOrderService tempParkingOrderService;

	@Autowired
	private IParkingOrderService parkingOrderService;
	@Autowired
	private ITempParkingChargeRuleService chargeRuleService;
	@Autowired
	private IBUserPlateService userPlateService;

	@Override
	public List<String> getPaidByParklotId(Long parklotId) {
		return tempParkingOrderService.getPaidByParklotId(parklotId);
	}

	@Override
	public TempParkingOrder getPaidByCouponId(String couponId) {
		return tempParkingOrderService.getPaidByCouponId(couponId);
	}

	@Override
	public List<TempParkingOrder> getByParkingId(Long id) {
		return tempParkingOrderService.getByParkingId(id);
	}

	@Override
	public ParkingOrder getLatestRecord(String plate) {
		return parkingOrderService.getLatestRecord(plate);
	}

	@Override
	public ProjectCost calculateCost(Long parkLotId, Date startDate, Date endDate) {
		TempParkingChargeRuleVO chargeRule = chargeRuleService.selectOneCarTypeByParkLotId(parkLotId);
		ProjectCost cost = new ProjectCost();
		return ProjectCalculate.calculate(cost, "临停时段计费", chargeRule, startDate, endDate);
	}

	@Override
	public List<BUserPlate> getByPhone(Long phone) {
		return userPlateService.getByPhone(phone);
	}

	@Override
	public Parklot getParkLot(Long parkLotId) {
		return ParkLotCaches.getParkLot(parkLotId);
	}

	@Override
	public TempParkingOrder autoCreateTempOrder(TempParkingOrder order) {
		TempParkingOrder one = tempParkingOrderService.getOne(Wrappers.<TempParkingOrder>lambdaQuery()
			.eq(TempParkingOrder::getParkingId, order.getParkingId())
			.eq(TempParkingOrder::getTotalAmount, order.getTotalAmount())
			.eq(TempParkingOrder::getDiscountAmount, order.getDiscountAmount())
			.eq(TempParkingOrder::getPayStatus, 1).last("limit 1")
		);
		if (one == null) {
			tempParkingOrderService.save(order);
			one = order;
		}
		return one;
	}

	@Override
	public ParkingOrder queryPresentRecord(Long parklotId, String plate) {
		return parkingOrderService.queryPresentRecord(parklotId, plate);
	}
}
