package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParkMerchantRule;
import com.lecent.park.vo.ParkMerchantRuleOptionVO;
import com.lecent.park.vo.ParkMerchantRuleVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
public interface IParkMerchantRuleService extends BaseService<ParkMerchantRule> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parkMerchantRule
	 * @return
	 */
	IPage<ParkMerchantRuleVO> selectParkMerchantRulePage(IPage<ParkMerchantRuleVO> page, ParkMerchantRuleVO parkMerchantRule);

	/**
	 * 逻辑删除
	 *
	 * @param ruleIdList
	 * @return
	 */
	Boolean removeLogic(List<Long> ruleIdList);

	/**
	 * 提交商户套餐
	 *
	 * @param parkMerchantRule 商户套餐
	 * @return true/false
	 */
	Boolean submit(ParkMerchantRule parkMerchantRule);

	/**
	 * 商家套餐下拉框
	 *
	 * @return ParkMerchantRuleOptionVO
	 */
	List<ParkMerchantRuleOptionVO> ruleOptions();

	/**
	 * @param ruleType 套餐类型
	 * @return 套餐列表
	 */
	List<ParkMerchantRule> getByRuleType(int ruleType);
}
