package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.app.vo.AppFreeCardVO;
import com.lecent.park.entity.FreeCardAuth;
import com.lecent.park.vo.FreeCardAuthVO;
import com.lecent.park.vo.FreeCardBatchExtendEndTime;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;
import java.util.List;

/**
 * 公司角色表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface IFreeCardAuthService extends BaseService<FreeCardAuth> {

	/**
	 * 自定义分页
	 *
	 * @param page         分页对象
	 * @param freeCardAuth 查询条件
	 * @return 分页结果
	 */
	IPage<FreeCardAuthVO> selectFreeCardAuthPage(IPage<FreeCardAuthVO> page, FreeCardAuthVO freeCardAuth);

	/**
	 * 根据免费车id获取授权的车场列表 带创建人信息
	 *
	 * @param freeCardId 免费车id
	 * @return 授权的车场列表
	 */
	public List<FreeCardAuthVO> getFreeCardAuthVOList(Long freeCardId);

	/**
	 * 根据免费车id获取授权的车场列表
	 *
	 * @param freeCardId 免费车id
	 * @return 授权的车场列表
	 */
	List<FreeCardAuth> getListByFreeCardId(Long freeCardId);

	/**
	 * 批量给车场取消授权
	 *
	 * @param ids  授权id列表
	 * @param memo 备注
	 * @return 取消授权结果
	 */
	boolean batchCancelAuth(List<Long> ids, String memo);

	/**
	 * 取消授权
	 *
	 * @param id   授权id
	 * @param memo 备注
	 * @return 取消授权结果
	 */
	boolean cancelAuth(Long id, String memo);

	/**
	 * 恢复授权
	 *
	 * @param id   授权id
	 * @param memo 备注
	 * @return 恢复授权结果
	 */
	boolean recoverAuth(Long id, String memo);

	/**
	 * 批量恢复授权
	 *
	 * @param authIds 授权id列表
	 * @return 批量恢复授权结果
	 */
	boolean batchRecoverAuth(List<Long> authIds);

	/**
	 * 更新免费车授权结束时间
	 *
	 * @param freeCardAuth 免费车授权对象
	 * @return 更新结果
	 */
	boolean extendEndTime(FreeCardAuth freeCardAuth);

	/**
	 * 批量更新免费车授权结束时间
	 *
	 * @param extendEndTime
	 * @return
	 */
	boolean batchExtendEndTime(FreeCardBatchExtendEndTime extendEndTime);

	/**
	 * 根据免费车id和车场id查询授权信息
	 *
	 * @param cardId    免费车id
	 * @param parkLotId 车场id
	 * @return 授权信息
	 */
	FreeCardAuth selectByCardId(Long cardId, Long parkLotId);

	/**
	 * 根据车牌号和车场id查询授权信息
	 *
	 * @param plate     车牌号
	 * @param parkLotId 车场id
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @return 授权信息
	 */
	FreeCardAuth selectByPlate(String plate, Long parkLotId, Date startDate, Date endDate);

	/**
	 * 更新月卡为无效
	 *
	 * @param cardAuth 免费车授权对象
	 */
	void updateCardInvalid(FreeCardAuth cardAuth);

	/**
	 * 处理免费卡过期
	 */
	void freeCardExpire();

	/**
	 * 保存或更新免费车授权信息
	 *
	 * @param cardAuth 免费车授权对象
	 * @return 保存或更新结果
	 */
	boolean unifySaveOrUpdate(FreeCardAuth... cardAuth);

	/**
	 * 批量删除免费车授权信息
	 *
	 * @param ids       免费车id列表
	 * @param parkLotId 车场id
	 * @return 删除结果
	 */
	Boolean batchDelete(String ids, Long parkLotId);

	/**
	 * 逻辑删除授权信息
	 *
	 * @param authIds 授权id列表
	 */
	void customDeleteLogic(List<Long> authIds);

	/**
	 * 获取免费车授权详情
	 *
	 * @param id 授权id
	 * @return 免费车授权详情
	 */
	AppFreeCardVO detail(Long id);

	/**
	 * 审批通过后，启用免费车
	 *
	 * @param id 授权id
	 * @return 操作结果
	 */
	Boolean processPass(Long id);

}
