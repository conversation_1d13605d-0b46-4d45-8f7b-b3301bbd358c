package com.lecent.park.listener;

import com.lecent.park.event.IASyncDataForwardingEvent;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.service.IChannelOpengateLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

/**
 * 紧急开门监听
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableAsync
public class UrgentOpenGateListener {

	@Autowired
	private IChannelOpengateLogService  openGateLogService;


	/**
	 * 紧急开门监听
	 *
	 * @param event
	 */
	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.ChannelCommandConstant).URGENT_SNAP")
	public void urgentOpenGate(IASyncDataForwardingEvent event) {
		ParkChannelMessageEvent message = (ParkChannelMessageEvent) event;
		openGateLogService.triggerUrgentSnap(message);
	}
}
