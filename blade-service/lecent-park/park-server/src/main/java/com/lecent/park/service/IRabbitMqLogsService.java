package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.RabbitMqLogs;
import com.lecent.park.vo.RabbitMqLogsVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 接收rabbitmq数据日志 服务类
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
public interface IRabbitMqLogsService extends BaseService<RabbitMqLogs> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rabbitMqLogs
	 * @return
	 */
	IPage<RabbitMqLogsVO> selectRabbitMqLogsPage(IPage<RabbitMqLogsVO> page, RabbitMqLogsVO rabbitMqLogs);

	/**
	 * 保存rabbitmq日志
	 * @param tradeNo
	 */
	void saveReceiveRabbitMqLogs(String tradeNo);
}
