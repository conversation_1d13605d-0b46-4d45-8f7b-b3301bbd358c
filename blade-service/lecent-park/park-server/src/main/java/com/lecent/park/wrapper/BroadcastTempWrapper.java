package com.lecent.park.wrapper;

import com.lecent.park.entity.BroadcastTemp;
import com.lecent.park.vo.BroadcastTempVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 设置设置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
public class BroadcastTempWrapper extends BaseEntityWrapper<BroadcastTemp, BroadcastTempVO>  {

	public static BroadcastTempWrapper build() {
		return new BroadcastTempWrapper();
 	}

	@Override
	public BroadcastTempVO entityVO(BroadcastTemp broadcastTemp) {
		BroadcastTempVO deviceSettingVO = BeanUtil.copy(broadcastTemp, BroadcastTempVO.class);

		//User createUser = UserCache.getUser(deviceSetting.getCreateUser());
		//User updateUser = UserCache.getUser(deviceSetting.getUpdateUser());
		//deviceSettingVO.setCreateUserName(createUser.getName());
		//deviceSettingVO.setUpdateUserName(updateUser.getName());

		return deviceSettingVO;
	}


}
