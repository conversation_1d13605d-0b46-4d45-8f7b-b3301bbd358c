package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.constant.StatisticsTypeEnum;
import com.lecent.park.dto.CardOrderDTO;
import com.lecent.park.dto.ParklotIncomeDTO;
import com.lecent.park.entity.CardOrder;
import com.lecent.park.excel.CardOrderExcel;
import com.lecent.park.vo.*;
import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import org.springblade.common.payment.PayResult;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 套餐订单表 服务类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface ICardOrderService extends BaseService<CardOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cardOrderDTO
	 * @return
	 */
	IPage<CardOrderVO> selectCardOrderPage(IPage<CardOrderVO> page, CardOrderDTO cardOrderDTO);

	CardOrderTotalVO getCardOrderTotal(CardOrderDTO cardOrderDTO);

	List<CardOrderExcel> getExportData(Long cardId);

	void exportCardOrder(CardOrderDTO cardOrder, HttpServletResponse response);

	/**
	 * 根据订单编号查询
	 *
	 * @param tradeNo
	 * @return
	 */
	CardOrderVO getCardOrderByTradeNo(String tradeNo);

	/**
	 * 创建订单
	 *
	 * @param order
	 * @return
	 */
	CardOrderVO createOrder(CardOrderVO order);

	/**
	 * 获取订单
	 *
	 * @param tradeNo
	 * @return
	 */
	CardOrderVO getByTradeNo(String tradeNo);

	/**
	 * 获取未支付的订单
	 *
	 * @param tradeNo 流水号
	 * @return
	 */
	CardOrderVO getUnPaidByTradeNo(String tradeNo);

	/**
	 * 支付成功
	 *
	 * @param tradeNo
	 * @return
	 */
	Boolean paySuccess(String tradeNo);
	Boolean paySuccess(String tradeNo, PaymentSuccessPayload payload);

	/**
	 * 获取缴费记录
	 *
	 * @param plate
	 * @return
	 */
	List<CardOrderVO> getPayRecord(String plate);

	/**
	 * 获取缴费详细
	 *
	 * @param cardOrderVO
	 * @return
	 */
	CardOrderVO getPayOrderDetail(CardOrderVO cardOrderVO);

	/**
	 * 根据车牌查询缴费记录
	 *
	 * @param plate plate
	 * @return 缴费列表
	 */
	List<UserCardOrderVO> getCardOrderListByPlate(String plate);

	List<UserCardOrderVO> getCardOrderListByPlateParkId(String plate, Long parkId);

	/**
	 * ccb建行小程序获取分页
	 *
	 * @param page
	 * @param cardOrderDTO
	 * @return
	 */
	IPage<CardOrderVO> miniPage(IPage<CardOrderVO> page, CardOrderDTO cardOrderDTO);

	/**
	 * 付款成功后主动扫描未更新的订单
	 *
	 * @return
	 */
	List<String> getTradeNoList();

	/**
	 * 月卡收入
	 *
	 * @param parkLotIds
	 * @param statisticsTypeEnum
	 * @return
	 */
	BigDecimal statisticAmount(List<Long> parkLotIds, StatisticsTypeEnum statisticsTypeEnum);

	/**
	 * 月卡停车收入统计
	 *
	 * @param parkLotIds 停车场ID
	 * @param startDate  开始日期
	 * @param endDate    结束日期
	 * @param startHour  开始时间
	 * @param endHour    结束时间
	 * @return {@link List}<{@link HistogramStatisticsVO}>
	 */
	List<HistogramStatisticsVO> incomeHistogramStatistics(List<Long> parkLotIds, String startDate, String endDate,
														  Integer startHour, Integer endHour);

	List<GuardPayWayStatisticsVO> payWayRatio(Long parklotId);

	/**
	 * 更新月卡缴费后未更新的订单
	 */
	void updateCardEndDate();

	/**
	 * 公众号我的订单-月卡订单
	 *
	 * @param page
	 * @return
	 */
	IPage<CardOrderVO> userCardOrder(IPage<CardOrderVO> page);

	/**
	 * 开具发票停车记录
	 *
	 * @param invoiceCardOrderVO
	 * @return
	 */
	List<InvoiceCardOrderVO> recordList(InvoiceCardOrderVO invoiceCardOrderVO);

	List<CardOrder> getByIdsParklotId(String parkLotId, List<Long> cardOrders);

	/**
	 * 查询月卡支付结果
	 *
	 * @param tradeNo tradeNo
	 * @return
	 */
	PayResult queryMonthCardPayResult(String tradeNo);

	void sendCtCardOrder(CardOrder cardOrder);

	List<ParklotIncomeDataVO> getParklotIncome(ParklotIncomeDTO parklotIncomeDTO);

	Boolean removeOrder( CardOrderDTO order);

}
