package com.lecent.park.service.impl;

import com.lecent.park.en.coupon.CouponCategory;
import com.lecent.park.entity.ParkingDiscountRecord;
import com.lecent.park.vo.ParkingDiscountRecordVO;
import com.lecent.park.mapper.ParkingDiscountRecordMapper;
import com.lecent.park.service.IParkingDiscountRecordService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 停车优惠记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Service
public class ParkingDiscountRecordServiceImpl extends BaseServiceImpl<ParkingDiscountRecordMapper, ParkingDiscountRecord> implements IParkingDiscountRecordService {

	@Override
	public IPage<ParkingDiscountRecordVO> selectParkingDiscountRecordPage(IPage<ParkingDiscountRecordVO> page, ParkingDiscountRecordVO parkingDiscountRecord) {
		return page.setRecords(baseMapper.selectParkingDiscountRecordPage(page, parkingDiscountRecord));
	}

	@Override
	public Boolean finishDiscount(String tradeNo) {
		List<ParkingDiscountRecord> list = lambdaQuery()
				.eq(ParkingDiscountRecord::getTradeNo, tradeNo)
				.eq(ParkingDiscountRecord::getStatus, 0)
				.list();
		LocalDateTime now = LocalDateTime.now();
		list.forEach(record -> {
			record.setStatus(1);
			record.setDiscountTime(now);
		});
		return updateBatchById(list);
	}

	@Override
	public Integer countDiscountRecord(String plate, CouponCategory type) {
		return lambdaQuery().eq(ParkingDiscountRecord::getPlate, plate)
				.eq(Func.notNull(type), ParkingDiscountRecord::getDiscountType, type)
				.eq(ParkingDiscountRecord::getStatus, 1)
				.count();
	}
}
