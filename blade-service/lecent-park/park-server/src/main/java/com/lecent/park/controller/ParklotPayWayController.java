package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.core.tool.databind.annottion.DataBindSerialize;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotPayWay;
import com.lecent.park.service.IParklotPayWayService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.ParklotPayWayVO;
import com.lecent.park.vo.ParklotPayWaysVO;
import com.lecent.park.wrapper.PayWayWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 支付渠道配置表 控制器
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@RestController
@AllArgsConstructor
@RequestMapping({"/parklotPayWay", "/parklotpay"})
@Api(value = "车场支付绑定支付商户渠道配置表", tags = "车场支付绑定支付商户渠道接口")
@Slf4j
public class ParklotPayWayController extends BladeController {

	private IParklotPayWayService payWayService;

	private IParklotService parklotService;

	/**
	 * 详情
	 */
	@DataBindSerialize
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入payWay")
	public R<ParklotPayWayVO> detail(ParklotPayWay payWay) {
		ParklotPayWay detail = payWayService.getOne(Condition.getQueryWrapper(payWay));
		return R.data(PayWayWrapper.build().entityVO(detail));
	}

	/**
	 * 自定义分页 支付渠道配置表
	 */
	@DataBindSerialize
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入payWay")
	public R<IPage<ParklotPayWayVO>> page(ParklotPayWayVO payWay, Query query) {
		IPage<ParklotPayWayVO> pages = payWayService.selectPayWayPage(Condition.getPage(query), payWay);
		return R.data(pages);
	}

	/**
	 * 新增或修改 支付渠道配置表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入payWay")
	public R submit(@Valid @RequestBody ParklotPayWay payWay) {
		return R.status(payWayService.saveOrUpdate(payWay));
	}


	/**
	 * 新增或修改 支付渠道配置表
	 */
//	@PostMapping("/submits")
//	@ApiOperationSupport(order = 6)
//	@ApiOperation(value = "批量新增或修改", notes = "传入payWay")
//	public R submits(@Valid @RequestBody List<ParklotPayWay> payWays) {
//		return R.status(payWayService.customSaveOrUpdate(payWays));
//	}
	@PostMapping("/submits")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "批量新增或修改", notes = "传入payWay")
	public R submits(@Valid @RequestBody ParklotPayWaysVO parklotPayWaysVO) {

		List<ParklotPayWayVO> payWayVOList = parklotPayWaysVO.getPayWayVOList();
		Parklot parklot = parklotService.getById(parklotPayWaysVO.getParklotId());
		parklot.setBusinessName(parklotPayWaysVO.getBusinessName());
		parklot.setBusinessId(parklotPayWaysVO.getBusinessId());
		parklotService.saveOrUpdate(parklot);

		List<ParklotPayWay> payWayList = new ArrayList<>();
		try {
			for (ParklotPayWayVO parklotPayWayVO : payWayVOList) {
				ParklotPayWay payWay = new ParklotPayWay();
				BeanUtils.copyProperties(parklotPayWayVO, payWay);
				payWayList.add(payWay);
			}


		} catch (BeansException e) {
			log.error("BeanUtil property copy  failed :BeansException", e);
		} catch (Exception e) {
			log.error("BeanUtil property copy failed:Exception", e);
		}
		return R.status(payWayService.customSaveOrUpdate(payWayList));
	}

	/**
	 * 删除 支付渠道配置表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(payWayService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 获取当前租户下的支付渠道
	 */
	@DataBindSerialize
	@GetMapping("/listPays/{parklotId}")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "获取当前车场已绑定的支付商户", notes = "")
	public R<ParklotPayWaysVO> listPays(@PathVariable Long parklotId) {
		ParklotPayWaysVO parklotPayWaysVO = new ParklotPayWaysVO();
		List<ParklotPayWayVO> payWayVOList = payWayService.listPays(parklotId);
		parklotPayWaysVO.setPayWayVOList(payWayVOList);
		Parklot parklot = parklotService.getById(parklotId);
		parklotPayWaysVO.setBusinessId(parklot.getBusinessId());
		parklotPayWaysVO.setBusinessName(parklot.getBusinessName());
		parklotPayWaysVO.setParklotId(parklotId);
		return R.data(parklotPayWaysVO);
	}

}
