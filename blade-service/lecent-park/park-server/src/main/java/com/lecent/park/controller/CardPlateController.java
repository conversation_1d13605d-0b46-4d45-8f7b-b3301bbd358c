package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.CardPlateDTO;
import com.lecent.park.entity.CardPlate;
import com.lecent.park.service.ICardPlateService;
import com.lecent.park.vo.CardPlateVO;
import com.lecent.park.wrapper.CardPlateWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 套餐车牌表 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/cardplate")
@Api(value = "套餐车牌表", tags = "套餐车牌表接口")
public class CardPlateController extends BladeController {

	private ICardPlateService cardPlateService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cardPlate")
	public R<CardPlateVO> detail(CardPlate cardPlate) {
		CardPlate detail = cardPlateService.getOne(Condition.getQueryWrapper(cardPlate));
		return R.data(CardPlateWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 套餐车牌表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cardPlate")
	public R<IPage<CardPlateVO>> list(CardPlate cardPlate, Query query) {
		IPage<CardPlate> pages = cardPlateService.page(Condition.getPage(query), Condition.getQueryWrapper(cardPlate));
		return R.data(CardPlateWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 套餐车牌表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入cardPlate")
	public R<IPage<CardPlateVO>> page(CardPlateVO cardPlate, Query query) {
		IPage<CardPlateVO> pages = cardPlateService.selectCardPlatePage(Condition.getPage(query), cardPlate);
		return R.data(pages);
	}

	/**
	 * 新增 套餐车牌表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入cardPlate")
	public R save(@Valid @RequestBody CardPlate cardPlate) {
		return R.status(cardPlateService.save(cardPlate));
	}

	/**
	 * 修改 套餐车牌表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入cardPlate")
	public R update(@Valid @RequestBody CardPlate cardPlate) {
		return R.status(cardPlateService.updateById(cardPlate));
	}

	/**
	 * 新增或修改 套餐车牌表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入cardPlate")
	public R submit(@Valid @RequestBody CardPlate cardPlate) {
		return R.status(cardPlateService.saveOrUpdate(cardPlate));
	}


	/**
	 * 删除 套餐车牌表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cardPlateService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 获取车主全部车牌
	 */
	@GetMapping("/getAllCar")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "获取车主全部车牌", notes = "传入phone")
	public R<List<CardPlateDTO>> getAllCar(String phone) {
		return R.data(cardPlateService.getAllCar(phone));
	}

}
