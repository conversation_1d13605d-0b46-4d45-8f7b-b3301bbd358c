package com.lecent.park.scheduled;

import com.lecent.park.service.IParkMerchantParklotPlateService;
import com.lecent.park.service.IParkMerchantParklotService;
import com.lecent.park.service.IParkMerchantRuleOrderService;
import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 酒店授权按时长授权周期结算
 * <AUTHOR>
 */
@Component
@Slf4j
public class AuthPlateScheduled {

	@Autowired
	private IParkMerchantParklotPlateService authPlateService;
	@Autowired
	private IParkMerchantParklotService merchantParklotService;
	@Autowired
	private IParkMerchantRuleOrderService merchantOrderService;


	/**
	 * 每天00点00分00秒处理停车记录（*结算）
	 */
	@Scheduled(cron = "0 0 0 * * ? ")
	@RedisLock(value = "lecent:park::timedTask:lock:everyDayScheduled")
	public void everyDayScheduled() {
		log.info("开始执行车辆授权每天00点00分00秒处理停车记录（*结算）任务开始==============");
		authPlateService.everyDayCheckSettlement();
		log.info("开始执行车辆授权每天00点00分00秒处理停车记录（*结算）任务结束==============");

	}


	/**
	 * 扫描支付成功后未更新的订单(15分钟执行一次)
	 */
	@Scheduled(cron = "0 0/15 * * * ?")
	@RedisLock(value = "lecent:park::timedTask:lock:merchantPaySuccess")
	public void queryPaySuccessOrderScheduled() {
		log.info("开始执行扫描商户支付成功后未更新的订单==============");

		List<String> tradeNoList = merchantOrderService.getTradeNoList();
		if (Func.isEmpty(tradeNoList)){
			return;
		}
		for (String tradeNo:tradeNoList) {
			merchantParklotService.paySuccess(new PaymentSuccessPayload(tradeNo));
		}
	}


}
