package com.lecent.park.listener;

import com.lecent.park.service.ParkingPlaceStatusService;
import com.leliven.park.domain.order.parking.event.ParkingOrderDomainEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 车位状况变更监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParkingPlaceStatusListener {

	@Resource
	private ParkingPlaceStatusService parkingPlaceStatusService;

	/**
	 * 变更车位状况
	 *
	 * @param event 事件
	 */
	@EventListener
	public void changePlaceStatus(ParkingOrderDomainEvent event) {
		try {
			parkingPlaceStatusService.onParkingOrderEvent(event);
		} catch (Exception e) {
			log.warn("变更车位状况失败：", e);
		}
	}

}
