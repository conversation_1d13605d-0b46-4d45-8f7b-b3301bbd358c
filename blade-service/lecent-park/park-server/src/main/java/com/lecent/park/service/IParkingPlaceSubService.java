package com.lecent.park.service;

import com.lecent.park.entity.ParkingPlaceSub;
import com.lecent.park.vo.ParkingPlaceSubVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 车位预约 服务类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
public interface IParkingPlaceSubService extends BaseService<ParkingPlaceSub> {

	/**
	 * 自定义分页
	 *
	 * @param page            页
	 * @param parkingPlaceSub 车位预约
	 * @return {@link List }<{@link ParkingPlaceSubVO }>
	 */
	IPage<ParkingPlaceSubVO> selectParkingPlaceSubPage(IPage<ParkingPlaceSubVO> page, ParkingPlaceSubVO parkingPlaceSub);

}
