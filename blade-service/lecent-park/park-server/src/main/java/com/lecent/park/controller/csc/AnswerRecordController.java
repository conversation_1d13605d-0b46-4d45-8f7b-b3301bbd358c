package com.lecent.park.controller.csc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.AnswerRecord;
import com.lecent.park.service.IAnswerRecordService;
import com.lecent.park.vo.AnswerRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 客服接听记录  控制器
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/answer/record")
@Api(value = "客服接听记录 ", tags = "客服接听记录 接口")
public class AnswerRecordController extends BladeController {

	private IAnswerRecordService answerRecordService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入answerRecord")
	public R<AnswerRecord> detail(AnswerRecord answerRecord) {
		AnswerRecord detail = answerRecordService.getOne(Condition.getQueryWrapper(answerRecord));
		return R.data(detail);
	}

	/**
	 * 自定义分页 客服接听记录
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入answerRecord")
	public R<IPage<AnswerRecordVO>> page(AnswerRecordVO answerRecord, Query query) {
		IPage<AnswerRecordVO> pages = answerRecordService.selectAnswerRecordPage(Condition.getPage(query), answerRecord);
		return R.data(pages);
	}

	/**
	 * 新增 客服接听记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入answerRecord")
	public R save(@Valid @RequestBody AnswerRecord answerRecord) {
		return R.status(answerRecordService.customSave(answerRecord));
	}


}
