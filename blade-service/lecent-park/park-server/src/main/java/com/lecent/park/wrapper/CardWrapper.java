package com.lecent.park.wrapper;

import com.lecent.park.entity.Card;
import com.lecent.park.vo.CardVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 套餐卡信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class CardWrapper extends BaseEntityWrapper<Card, CardVO> {

	public static CardWrapper build() {
		return new CardWrapper();
	}

	@Override
	public CardVO entityVO(Card card) {
		CardVO cardVO = BeanUtil.copy(card, CardVO.class);

		//User createUser = UserCache.getUser(card.getCreateUser());
		//User updateUser = UserCache.getUser(card.getUpdateUser());
		//cardVO.setCreateUserName(createUser.getName());
		//cardVO.setUpdateUserName(updateUser.getName());

		return cardVO;
	}

}
