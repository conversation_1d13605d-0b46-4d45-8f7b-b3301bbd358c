package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.CardPlateDTO;
import com.lecent.park.entity.CardPlate;
import com.lecent.park.vo.CardPlateVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 套餐车牌表 服务类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface ICardPlateService extends BaseService<CardPlate> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cardPlate
	 * @return
	 */
	IPage<CardPlateVO> selectCardPlatePage(IPage<CardPlateVO> page, CardPlateVO cardPlate);

	/**
	 * 获取车主全部车牌
	 *
	 * @param phone
	 * @return
	 */
	List<CardPlateDTO> getAllCar(String phone);
}
