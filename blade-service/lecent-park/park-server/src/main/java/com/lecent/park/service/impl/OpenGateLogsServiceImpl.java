package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.entity.Channel;
import com.lecent.park.entity.OpenGateLogs;
import com.lecent.park.entity.Parklot;
import com.lecent.park.mapper.OpenGateLogsMapper;
import com.lecent.park.service.IChannelService;
import com.lecent.park.service.IOpenGateLogsService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.OpenGateLogsVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 开闸日志 服务实现类
 *
 * <AUTHOR>
 * @since 2021-02-09
 */
@Slf4j
@Service
public class OpenGateLogsServiceImpl extends BaseServiceImpl<OpenGateLogsMapper, OpenGateLogs> implements IOpenGateLogsService {


	@Autowired
	private IParklotService parkLotService;
	@Autowired
	private IChannelService channelService;

	@Override
	public IPage<OpenGateLogsVO> selectOpenGateLogsPage(IPage<OpenGateLogsVO> page, OpenGateLogsVO openGateLogs) {
		return page.setRecords(baseMapper.selectOpenGateLogsPage(page, openGateLogs));
	}

	@Override
	@Async
	@Transactional(rollbackFor = {Exception.class})
	public void saveLogs(ChannelTodoVO todo) {
		String passWay = todo.getStatus() == 1 ? "人工处理放行" : todo.getStatus() == 2 ? "自主缴费放行" : "无费用自动通行";
		Parklot parklot = ParkLotCaches.getParkLot(todo.getParklotId());
		if (Func.isEmpty(parklot)){
			return;
		}
		Channel channel = channelService.getById(todo.getChannelId());
		if (Func.isEmpty(channel)){
			return;
		}
		OpenGateLogs build = OpenGateLogs.builder()
			.parkLotName(parklot.getName())
			.channelName(channel.getName())
			.plate(todo.getPlate())
			.passWay(passWay)
			.todoId(todo.getId())
			.build();
		build.setTenantId(parklot.getTenantId());
		this.save(build);
	}


	/**
	 * 每周一04点00分00秒删除日志
	 */
	@SneakyThrows
	@Scheduled(cron = "0 30 0/1 * * ?")
	@RedisLock(value = "lecent:park::timedTask:lock:open:gate:logs:remove:batch", waitTime = 0L)
	@Transactional(rollbackFor = Exception.class)
	public void deleteLogs() {
		Thread.sleep(2000);
		log.debug("[批量删除开闸日志]任务开始==============");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		Date date = new Date();
		List<OpenGateLogs> list = lambdaQuery()
				.select(OpenGateLogs::getId)
				.lt(OpenGateLogs::getCreateTime, DateUtil.minusDays(date, 7))
				.last("limit 2000")
				.list();

		List<Long> ids = list.stream().map(BaseEntity::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			List<List<Long>> partition = Lists.partition(ids, 2000);
			partition.forEach(this::removeByIds);
		}

		stopWatch.stop();
		log.info("[批量删除开闸日志]任务结束, 条数: {}, 耗时:[{}s]==============", list.size(), stopWatch.getTotalTimeSeconds());
	}

}
