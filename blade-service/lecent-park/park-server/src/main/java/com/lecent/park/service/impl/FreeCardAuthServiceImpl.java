package com.lecent.park.service.impl;

import cn.hutool.core.date.DateUnit;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.app.vo.AppFreeCardVO;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.common.enums.card.CardStatusConstants;
import com.lecent.park.core.log.utils.AuditLogger;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.entity.CardLog;
import com.lecent.park.entity.FreeCard;
import com.lecent.park.entity.FreeCardAuth;
import com.lecent.park.mapper.FreeCardAuthMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.FreeCardAuthVO;
import com.lecent.park.vo.FreeCardBatchExtendEndTime;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.springblade.core.tool.constant.BladeConstant.DB_IS_DELETED;

/**
 * 公司角色表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class FreeCardAuthServiceImpl extends BaseServiceImpl<FreeCardAuthMapper, FreeCardAuth> implements IFreeCardAuthService {
	private final ICardLogService cardLogService;

	private final IMessageLogService messageService;

	private final IUserParklotService userParkLotService;

	private final IPlatePropertyService platePropertyService;

	private final IFreeCardService freeCardService;

	@Autowired
	private IUserClient userClient;

	public FreeCardAuthServiceImpl(ICardLogService cardLogService,
								   IMessageLogService messageService,
								   IUserParklotService userParkLotService,
								   IPlatePropertyService platePropertyService, @Lazy IFreeCardService freeCardService) {
		this.cardLogService = cardLogService;
		this.messageService = messageService;
		this.userParkLotService = userParkLotService;
		this.platePropertyService = platePropertyService;
		this.freeCardService = freeCardService;
	}

	@Override
	public IPage<FreeCardAuthVO> selectFreeCardAuthPage(IPage<FreeCardAuthVO> page, FreeCardAuthVO freeCardAuth) {
		return page.setRecords(baseMapper.selectFreeCardAuthPage(page, freeCardAuth));
	}

	/**
	 * 统一保存或更新
	 *
	 * @param saveBeans 更新实体
	 * @return true 成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean unifySaveOrUpdate(FreeCardAuth... saveBeans) {
		for (FreeCardAuth saveBean : saveBeans) {
			// 删除
			if (Integer.valueOf(DB_IS_DELETED).equals(saveBean.getIsDeleted())) {
				platePropertyService.removeByCardIds(PlatePropertyType.FREE_CARD, saveBean.getId());
				deleteLogic(Collections.singletonList(saveBean.getId()));
				continue;
			}
			super.saveOrUpdate(saveBean);
			FreeCardAuth oldCar = getById(saveBean.getId());


			// 删除无效授权
			if (EnableStatus.isInvalid(oldCar.getStatus()) && oldCar.getEndTime().compareTo(new Date())>0) {
				platePropertyService.removeByCardIds(PlatePropertyType.FREE_CARD, oldCar.getId());
				continue;
			}
			if (Func.isEmpty(saveBean.getPlate())) {
				FreeCard freeCard = freeCardService.getById(saveBean.getFreeCardId());
				LecentAssert.notNull(freeCard, "未找到免费车信息");

				saveBean.setPlate(freeCard.getPlate());
			}

			platePropertyService.update(oldCar.getTenantId(), oldCar.getParklotId(),
				saveBean.getPlate(),
				oldCar.getId(),
				PlatePropertyType.FREE_CARD,
				oldCar.getStartTime(),
				oldCar.getEndTime());
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void customDeleteLogic(List<Long> authIds) {
		deleteLogic(authIds);

		platePropertyService.removeByCardIds(PlatePropertyType.FREE_CARD, authIds);
	}

	@Override
	public AppFreeCardVO detail(Long id) {
		if (id == null) {
			return null;
		}
		return baseMapper.freeAuthById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean processPass(Long id) {
		FreeCardAuth freeCardAuth = this.getById(id);
		if (freeCardAuth == null) {
			return Boolean.FALSE;
		}
		this.lambdaUpdate()
			.set(FreeCardAuth::getStatus, CardStatusConstants.EFFECTIVE)
			.eq(FreeCardAuth::getId, id).update();

		freeCardService.update(Wrappers.<FreeCard>lambdaUpdate()
			.set(FreeCard::getStatus, CardStatusConstants.EFFECTIVE)
			.eq(FreeCard::getId, freeCardAuth.getFreeCardId()));

		AuditLogger.add(freeCardAuth.getFreeCardId().toString(), "freeCard", "新增免费车", "", "", "", "");
		return Boolean.TRUE;
	}

	@Override
	public List<FreeCardAuthVO> getFreeCardAuthVOList(Long freeCardId){
		List<FreeCardAuth> list=  getListByFreeCardId(freeCardId);
		Map<Long,String> map=new HashMap<>();
		List<FreeCardAuthVO> authVOS=new ArrayList<>();
		FreeCardAuthVO authVO=null;
		if(Func.isNotEmpty(list)){
			for(FreeCardAuth cardAuth: list){
				authVO= BeanUtil.copy(cardAuth,FreeCardAuthVO.class);
				if(cardAuth.getCreateUser()!=null){
					if(map.containsKey(cardAuth.getCreateUser())){
						authVO.setCreateUserName(map.get(cardAuth.getCreateUser()));
					}else{
						R<User> userR= userClient.userInfoById(cardAuth.getCreateUser());
						if(userR!=null &&userR.getData() !=null){
							authVO.setCreateUserName(userR.getData().getRealName());
							map.put(cardAuth.getCreateUser(),userR.getData().getRealName());
						}


					}
				}
				if(cardAuth.getUpdateUser()!=null){
					if(map.containsKey(cardAuth.getUpdateUser())){
						authVO.setUpdateUserName(map.get(cardAuth.getUpdateUser()));
					}else{
						R<User> userR= userClient.userInfoById(cardAuth.getUpdateUser());
						if(userR!=null &&userR.getData() !=null){
							authVO.setUpdateUserName(userR.getData().getRealName());
							map.put(cardAuth.getUpdateUser(),userR.getData().getRealName());
						}


					}
				}
				authVOS.add(authVO);
			}
		}
		return authVOS;
	}


	@Override
	public List<FreeCardAuth> getListByFreeCardId(Long freeCardId) {
		List<Long> parkLotIds = userParkLotService.getCurrentUserBindParkLotIds();
		if (Func.isEmpty(parkLotIds)) {
			return Collections.emptyList();
		}
		return list(Wrappers.<FreeCardAuth>query().lambda().eq(FreeCardAuth::getFreeCardId, freeCardId)
			.in(FreeCardAuth::getParklotId, parkLotIds)
			.orderByDesc(FreeCardAuth::getCreateTime));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchCancelAuth(List<Long> ids, String memo) {
		List<FreeCardAuth> cardAuths = listByIds(ids);

		for (FreeCardAuth cardAuth : cardAuths) {
			cardAuth.setStatus(EnableStatus.INVALID.getCode());
			if (Func.isNotBlank(memo)) {
				cardAuth.setMemo(memo);
			}
			unifySaveOrUpdate(cardAuth);
		}

		cardAuths.forEach(auth -> {
			AuditLogger.add(auth.getFreeCardId() + "",
				"freeCard",
				"免费车取消授权",
				auth.getParklotName() + ",有效",
				auth.getParklotName() + ",无效",
				null,
				memo);
		});
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean cancelAuth(Long id, String memo) {
		return batchCancelAuth(Collections.singletonList(id), memo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean recoverAuth(Long id, String memo) {
		FreeCardAuth cardAuth = getById(id);
		if (cardAuth == null) {
			return false;
		}
		AuditLogger.add(cardAuth.getFreeCardId().toString(),
			"freeCard",
			"免费车批量恢复授权",
			cardAuth.getParklotName() + ",无效",
			cardAuth.getParklotName() + ",有效",
			null,
			memo);

		cardAuth.setMemo(memo);
		cardAuth.setStatus(EnableStatus.ACTIVE.getCode());
		return unifySaveOrUpdate(cardAuth);
	}




	@Override
	public FreeCardAuth selectByCardId(Long cardId, Long parkLotId) {
		return baseMapper.selectByCardId(cardId, parkLotId);
	}

	@Override
	public FreeCardAuth selectByPlate(String plate, Long parkLotId, Date startDate, Date endDate) {
		return baseMapper.selectByPlate(plate, parkLotId, startDate, endDate);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean extendEndTime(FreeCardAuth cardAuth) {
		FreeCardAuth oldCardAuth = getById(cardAuth.getId());
		long isBefore = cn.hutool.core.date.DateUtil.between(oldCardAuth.getEndTime(), cardAuth.getEndTime(), DateUnit.SECOND, false);
		if (isBefore <= 0) {
			// 延长时间不可小于之前的授权结束时间
			LecentAssert.alertException("延长时间小于之前时间");
		}
		AuditLogger.add(cardAuth.getFreeCardId().toString(),
			"freeCard",
			"免费车延长授期权时间",
			cardAuth.getParklotName() + "," + Func.formatDate(oldCardAuth.getEndTime()),
			cardAuth.getParklotName() + "," + Func.formatDate(cardAuth.getEndTime()),
			null,
			cardAuth.getMemo());
		cardAuth.setStatus(EnableStatus.ACTIVE.getCode());
		return unifySaveOrUpdate(cardAuth);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchExtendEndTime(FreeCardBatchExtendEndTime extendEndTime) {
		// 查询修改授权截止时间的授权卡记录
		List<FreeCardAuth> freeCardAuths = this.baseMapper.selectByIds(extendEndTime.getCardAuthIds());
		LecentAssert.notEmpty(freeCardAuths, "为查询相关车辆授权信息");
		for (FreeCardAuth oldCardAuth : freeCardAuths) {
			// 授权记录
			AuditLogger.add(oldCardAuth.getFreeCardId().toString(),
				"freeCard",
				"免费车延长授期权时间",
				oldCardAuth.getParklotName() + "," + Func.formatDate(oldCardAuth.getEndTime()),
				oldCardAuth.getParklotName() + "," + Func.formatDate(extendEndTime.getEndTime()),
				null,
				oldCardAuth.getMemo());
			oldCardAuth.setStatus(EnableStatus.ACTIVE.getCode());
			// 保存修改记录
			oldCardAuth.setEndTime(extendEndTime.getEndTime());
			unifySaveOrUpdate(oldCardAuth);
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchRecoverAuth(List<Long> authIds) {
		LecentAssert.notEmpty(authIds, "授权id不能为空");

		List<FreeCardAuth> cardAuths = listByIds(authIds);
		LecentAssert.notEmpty(cardAuths, "未查询到授权信息");

		List<CardLog> cardLogs = new ArrayList<>();
		for (FreeCardAuth cardAuth : cardAuths) {
			cardAuth.setStatus(EnableStatus.ACTIVE.getCode());
			AuditLogger.add(cardAuth.getFreeCardId().toString(),
				"freeCard",
				"免费车批量恢复授权",
				cardAuth.getParklotName() + ",无效",
				cardAuth.getParklotName() + ",有效",
				null,
				cardAuth.getMemo());
		}
		cardLogService.saveBatch(cardLogs);

		return unifySaveOrUpdate(cardAuths.toArray(new FreeCardAuth[0]));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCardInvalid(FreeCardAuth cardAuth) {
		FreeCardAuth freeCardAuth = this.getById(cardAuth.getId());
		if (Func.isEmpty(freeCardAuth)) {
			return;
		}
		if (freeCardAuth.getEndTime().getTime() > cardAuth.getEndTime().getTime()) {
			return;
		}
		if (freeCardAuth.getEndTime().getTime() > System.currentTimeMillis()) {
			return;
		}
		// 设置为无效
		freeCardAuth.setStatus(EnableStatus.INVALID.getCode());
		unifySaveOrUpdate(freeCardAuth);
		messageService.freeCardOverdueMsgCall(cardAuth);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void freeCardExpire() {
		List<FreeCardAuth> list = list(Wrappers.<FreeCardAuth>lambdaQuery().select(FreeCardAuth::getId)
			.eq(FreeCardAuth::getStatus, EnableStatus.ACTIVE.getCode())
			.le(FreeCardAuth::getEndTime, new Date())
		);
		for (FreeCardAuth cardAuth : list) {
			cardAuth.setStatus(EnableStatus.INVALID.getCode());

			// 更新
			unifySaveOrUpdate(cardAuth);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchDelete(String ids, Long parkLotId) {
		List<Long> bindParkLotIds = userParkLotService.getCurrentUserBindParkLotIds(parkLotId);
		LecentAssert.notEmpty(bindParkLotIds, "您未获得车场权限，禁止删除！");

		List<FreeCardAuth> auths = list(Wrappers.<FreeCardAuth>lambdaQuery()
			.eq(FreeCardAuth::getParklotId, parkLotId)
			.in(FreeCardAuth::getFreeCardId, Func.toLongList(ids)));
		for (FreeCardAuth auth : auths) {
			auth.setIsDeleted(DB_IS_DELETED);
			unifySaveOrUpdate(auth);
		}
		return true;
	}
}
