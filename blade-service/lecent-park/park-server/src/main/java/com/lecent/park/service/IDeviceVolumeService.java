package com.lecent.park.service;

import com.lecent.park.entity.DeviceVolume;
import com.lecent.park.vo.DeviceVolumeVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
public interface IDeviceVolumeService extends BaseService<DeviceVolume> {
	/**
	 * 查询设备信息
	 */
	boolean saveList(DeviceVolumeVO deviceVolumeVo);


	/**
	 * 查询设备音量设置
	 *
	 * @param deviceId
	 * @return
	 */
	List<DeviceVolumeVO> selectDeviceList(String deviceId);

	boolean batchSave(List<DeviceVolume> deviceVolumes);

	/**
	 * 获取设备音量
	 * @param placeDeviceId
	 * @return
	 */
	int getVoiceLevel(Long placeDeviceId);
}
