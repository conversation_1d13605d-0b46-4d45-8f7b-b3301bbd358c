package com.lecent.park.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkMerchantParklotDTO;
import com.lecent.park.dto.ParkMerchantRenewDTO;
import com.lecent.park.entity.ParkMerchantParklot;
import com.lecent.park.entity.Parklot;
import com.lecent.park.vo.MerchantPayVo;
import com.lecent.park.vo.ParkMerchantParklotVO;
import com.lecent.payment.vo.UnifiedOrderResultVO;
import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import org.springblade.core.mp.base.BaseService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 酒店商户与车场关联表 服务类
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
public interface IParkMerchantParklotService extends BaseService<ParkMerchantParklot> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parkMerchantParklot
	 * @return
	 */
	IPage<ParkMerchantParklotVO> selectParkMerchantParklotPage(IPage<ParkMerchantParklotVO> page, ParkMerchantParklotVO parkMerchantParklot);


	/**
	 * @param parkMerchantParklot
	 * @return
	 * @doc 酒店商户与车场关联的新增与修改
	 */
	boolean submit(ParkMerchantParklotDTO parkMerchantParklot);


	/**
	 * 根据登录的商家查询该商家授权的车场
	 *
	 * @return 车场列表
	 */
	List<Parklot> getCurrentMerchantParkList();

	List<ParkMerchantParklot> getByMerchantId(Long merchantId);

	/**
	 * @param merchantId 商户id
	 * @param parklotId  车场id
	 * @return 车场授权车场信息
	 */
	ParkMerchantParklot getByMerchantIdParklotId(Long merchantId, Long parklotId);

	/**
	 * 根据用户id查询授权车场列表
	 *
	 * @param userId
	 * @return
	 */
	List<ParkMerchantParklotVO> getMerchantParkListByUserId(Long userId);

	/**
	 * 查询按自然周授权所有记录
	 * @return
	 */
	List<ParkMerchantParklot> getAllWeekAuth();

	/**
	 * 查询按自然月授权所有记录
	 * @return
	 */
	List<ParkMerchantParklot> getAllMonthAuth();

	/**
	 * 根据商户ID车场ID查询商户车场信息
	 * @param merchantId
	 * @param parkLotIds
	 * @return
	 */
	List<ParkMerchantParklotVO> getMerchantParkLotList(Long merchantId, List<Long> parkLotIds);

	/**
	 * 商户套餐集合
	 * @return
	 */
	List<ParkMerchantParklotVO> userMerchantRuleList();

	/**
	 * 充值金额计算
	 * @param ruleId
	 * @param months
	 * @return
	 */
	BigDecimal calculateCost(Long ruleId, Integer months);

	/**
	 * 去支付
	 * @param payVo
	 * @return
	 */
	UnifiedOrderResultVO toPay(MerchantPayVo payVo);

	/**
	 * 付款成功后回调
	 * @param payload
	 */
	void paySuccess(PaymentSuccessPayload payload);

	/**
	 * 支付结果查询
	 *
	 * @param tradeNo
	 * @return
	 */
	Boolean payResultQuery(String tradeNo);

	ParkMerchantParklotVO detail(Long id);

	/**
	 * 更新授权规则
	 *
	 * @param parkMerchantParklot
	 * @return
	 */
	Boolean updateAuthRule(ParkMerchantParklotDTO parkMerchantParklot);

	ParkMerchantParklot getLoginUserRuleByParklotId(Long parklotId);

	Boolean renew(ParkMerchantRenewDTO parkMerchantRenewDTO);


	Boolean delParkMerchantParklot(List<Long> ids);


	/**
	 * 上次结算时间
	 * @param merchantParkLot
	 * @return
	 */
	Date preSettlementTime(ParkMerchantParklot merchantParkLot);

	List<ParkMerchantParklot> allSettlementCycle();

	/**
	 * 更新
	 * @param parkMerchantParklot
	 * @return
	 */
	Boolean updateMerchantParkLot(ParkMerchantParklotDTO parkMerchantParklot);

	/**
	 * 结算开始时间
	 * @param settleFrequency
	 * @param preSettlementTime
	 * @param cycleNumber
	 * @return
	 */
	Date settlementStartTime(Integer settleFrequency, Date preSettlementTime, Integer cycleNumber);

	/**
	 * 生成商户二维码
	 */
	JSONObject generateQrCode(Long parklotId);

	/**
	 * 从缓存获取二维码信息
	 */
	JSONObject getQrCodeFromCache(String merchantId, String parklotId, String requestId);
}
