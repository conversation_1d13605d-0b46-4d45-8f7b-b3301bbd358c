package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotSpecialPlate;
import com.lecent.park.vo.ParklotSpecialPlateVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-04-21
 */
public class ParklotSpecialPlateWrapper extends BaseEntityWrapper<ParklotSpecialPlate, ParklotSpecialPlateVO> {

	public static ParklotSpecialPlateWrapper build() {
		return new ParklotSpecialPlateWrapper();
	}

	@Override
	public ParklotSpecialPlateVO entityVO(ParklotSpecialPlate parklotSpecialPlate) {
		ParklotSpecialPlateVO parklotSpecialPlateVO = BeanUtil.copy(parklotSpecialPlate, ParklotSpecialPlateVO.class);

		//User createUser = UserCache.getUser(parklotSpecialPlate.getCreateUser());
		//User updateUser = UserCache.getUser(parklotSpecialPlate.getUpdateUser());
		//parklotSpecialPlateVO.setCreateUserName(createUser.getName());
		//parklotSpecialPlateVO.setUpdateUserName(updateUser.getName());

		return parklotSpecialPlateVO;
	}

}
