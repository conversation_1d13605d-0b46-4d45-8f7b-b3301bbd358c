package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.device.entity.DeviceManage;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.ExitTypeConstant;
import com.lecent.park.device.service.IDeviceManageService;
import com.lecent.park.device.utils.GateActionUtils;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.entity.*;
import com.lecent.park.event.channeltodo.DutyLoginOutEvent;
import com.lecent.park.event.channeltodo.RefreshChannelEvent;
import com.lecent.park.mapper.UserChannelDutyMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 通道值班表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserChannelDutyServiceImpl extends BaseServiceImpl<UserChannelDutyMapper, UserChannelDuty> implements IUserChannelDutyService {

	/**
	 * 事件发布器
	 */
	private ApplicationEventPublisher eventPublisher;

	private IUserChannelDutyItemService userChannelDutyItemService;
	private IChannelAbnormalReasonService channelAbnormalReasonService;
	private IChannelService channelService;
	private IParklotService parklotService;
	private IChannelTodoService channelTodoService;

	@Override
	public IPage<UserChannelDutyVO> selectUserChannelDutyPage(IPage<UserChannelDutyVO> page, UserChannelDutyVO userChannelDuty) {
		return page.setRecords(baseMapper.selectUserChannelDutyPage(page, userChannelDuty));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveLoginChannel(String channelIds) {

		List<Long> channelList = Func.toLongList(channelIds);

		UserChannelDuty userChannelDuty = saveUserChannelDuty();

		List<Channel> channels = channelService.listByIds(channelList);
		Long dutyId = userChannelDuty.getId();
		//退出用户所有的值班通道
		exitUserDuty(dutyId);

		//保存用戶新选择的通道
		channels.forEach(c -> saveItem(c, dutyId));
		noticeAppGuard(channels, false);
		return true;
	}

	private void noticeAppGuard(List<Channel> channels, boolean isLogout) {
		Long userId = SecureUtil.getUserId();
		String channelIdList = channels.stream().map(channel -> channel.getId().toString()).collect(Collectors.joining());
		GateActionUtils.noticeAppGuardRefreshChannel(userId, channelIdList, isLogout);
	}

	@RedisLock(value = "userChannelDuty")
	private UserChannelDuty saveUserChannelDuty() {
		Long userId = SecureUtil.getUserId();
		UserChannelDuty userChannelDuty = getByUserId(userId);
		if (Func.isNull(userChannelDuty)) {
			userChannelDuty = new UserChannelDuty();
			userChannelDuty.setStartTime(LocalDateTime.now());
			userChannelDuty.setUserId(userId);
			this.save(userChannelDuty);
		}
		return userChannelDuty;
	}


	/**
	 * 1.判断每一个通道是否有其他人正在值班。
	 * 没有正在值班的，
	 * 无操作；
	 * 有正在值班的，
	 * 获取正在值班的用户值班的通道列表；跟自己的通道列表比较；自己全部包含，将正在值班的用户退出值班，更新值班日志，
	 * 部分包含，将正在值班的用户包含的通道退出值班
	 *
	 * @param channelIdList 通道id列表
	 */
	private void exitChannelIfOnDuty(List<Long> channelIdList) {

		List<UserChannelDutyItem> dutyChannelList = getDutyChannelList(channelIdList);

		if (Func.isEmpty(dutyChannelList)) {
			return;
		}

		dutyChannelList.forEach(item -> {
			Long userId = item.getUserId();
			List<Long> userChannelList = userChannelDutyItemService.getByUserId(userId);

			if (Func.isNotEmpty(userChannelList)) {
				List<Long> intersection = channelIdList.stream().filter(userChannelList::contains).collect(toList());
				if (intersection.containsAll(userChannelList)) {
					logoutUserDutyItem(userChannelList);
					logoutUserDuty(userId);
					logoutUser(userId);
				} else {
					logoutUserDutyItem(intersection);

					RefreshChannelEvent event = RefreshChannelEvent.builder().userId(userId).build();
					eventPublisher.publishEvent(event);
				}
			}
		});
	}

	/**
	 * 将正在值班用户强制退出
	 *
	 * @param userId
	 */
	private void logoutUser(Long userId) {
		//发布登录退出事件
		DutyLoginOutEvent event = DutyLoginOutEvent.builder().userId(userId).build();
		eventPublisher.publishEvent(event);
		log.info("==================================================================================");
		log.info(">>>>>>>>>>>>>>>>>>用户id:{}被强制退出登录>>>>>>>>>>>>>>>>>>>", userId);
	}


	/**
	 * 退出值班日志
	 *
	 * @param userId
	 */
	private void logoutUserDuty(Long userId) {
		this.lambdaUpdate()
			.set(UserChannelDuty::getStatus, Constants.ZERO)
			.set(UserChannelDuty::getEndTime, DateUtil.now())
			.set(UserChannelDuty::getUpdateTime, DateUtil.now())
			.eq(UserChannelDuty::getUserId, userId)
			.eq(UserChannelDuty::getStatus, Constants.ONE)
			.isNull(UserChannelDuty::getEndTime)
			.update();
	}

	/**
	 * 退出值班通道
	 *
	 * @param userChannelList
	 */
	private void logoutUserDutyItem(List<Long> userChannelList) {
		if (Func.isEmpty(userChannelList)) {
			return;
		}

		userChannelDutyItemService.lambdaUpdate()
			.set(UserChannelDutyItem::getStatus, Constants.ZERO)
			.set(UserChannelDutyItem::getType, ExitTypeConstant.SYSTEM_EXIT)
			.set(UserChannelDutyItem::getEndTime, DateUtil.now())
			.in(UserChannelDutyItem::getChannelId, userChannelList)
			.update();
	}

	private List<UserChannelDutyItem> getDutyChannelList(List<Long> channelIdList) {
		return userChannelDutyItemService.list(Wrappers.<UserChannelDutyItem>query().lambda()
			.in(UserChannelDutyItem::getChannelId, channelIdList)
			.eq(UserChannelDutyItem::getStatus, Constants.ONE));
	}

	private void saveItem(Channel c, Long dutyId) {
		Long userId = SecureUtil.getUserId();
		UserChannelDutyItem one = userChannelDutyItemService.getOne(Wrappers.<UserChannelDutyItem>lambdaQuery()
			.eq(UserChannelDutyItem::getParklotId, c.getParklotId())
			.eq(UserChannelDutyItem::getChannelId, c.getId())
			.eq(UserChannelDutyItem::getDutyId, dutyId)
			.eq(UserChannelDutyItem::getUserId, userId));
		if (Func.notNull(one)) {
			one.setStatus(1);
			one.setStartTime(LocalDateTime.now());
			userChannelDutyItemService.updateById(one);
		} else {
			UserChannelDutyItem userChannelDutyItem = new UserChannelDutyItem();
			userChannelDutyItem.setUserId(userId);
			userChannelDutyItem.setStartTime(LocalDateTime.now());
			userChannelDutyItem.setChannelId(c.getId());
			userChannelDutyItem.setParklotId(c.getParklotId());
			userChannelDutyItem.setDutyId(dutyId);
			userChannelDutyItem.setStatus(1);
			userChannelDutyItemService.save(userChannelDutyItem);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateLogoutChannel(Long userId) {
		this.baseMapper.updateLogoutChannel(userId);
		userChannelDutyItemService.updateLogoutChannel(userId);
		//发布登录退出事件
//		DutyLoginOutEvent event = DutyLoginOutEvent.builder().userId(userId).build();
//		eventPublisher.publishEvent(event);
		noticeAppGuard(Collections.EMPTY_LIST, true);
		return true;
	}

	@Override
	public boolean getCurUserDutyStatus(Long userId) {
		int num = this.count(Wrappers.<UserChannelDuty>query().lambda()
			.eq(UserChannelDuty::getUserId, userId)
			.eq(UserChannelDuty::getStatus, Constants.ONE));
		return num > 0;
	}

	@Override
	public IPage<UserChannelDutyVO> userChannelDuty(IPage<UserChannelDutyVO> page, long userId) {
		return page.setRecords(baseMapper.userChannelDuty(page, userId));
	}

	@Override
	public UserChannelDutyVO userDutying(long userId) {
		return baseMapper.userDutying(userId);
	}


	@Override
	public UserChannelInfoVO info(String channels) {

		List<Long> channelIds = Func.toLongList(channels);
		UserChannelDuty duty = this.getByUserId(SecureUtil.getUserId());
		if (Func.isEmpty(duty) && Func.isEmpty(channelIds)) {
			throw new ServiceException("请您先选择车场通道，在进行值班。");
		}
		if (Func.isNotEmpty(duty)) {
			channelIds = userChannelDutyItemService.getByUserId(SecureUtil.getUserId());
		}

		UserChannelInfoVO vo = new UserChannelInfoVO();
		fillUserInfo(vo);
		fillReasons(vo);
		vo.setDuting(Func.isNotEmpty(duty));
		vo.setDutyTime(Func.isEmpty(duty) ? null : duty.getStartTime());
		vo.setParklots(fillParklot(channelIds));
		return vo;
	}

	private void fillUserInfo(UserChannelInfoVO vo) {
		BladeUser user = SecureUtil.getUser();
		vo.setCurrentTime(LocalDateTime.now());
		vo.setRealName(user.getNickName());
		vo.setLoginName(user.getAccount());
		vo.setUserId(user.getUserId());
	}

	private final IDeviceManageService deviceManageService;

	private List<ParklotInfoVO> fillParklot(List<Long> channelIds) {
		List<ParklotInfoVO> parklotVOS = new ArrayList<>();
		ParklotInfoVO parklotInfoVO = new ParklotInfoVO();
		List<ChannelInfoVO> channelInfoVOList = new ArrayList<>();
		Map<Long, DeviceManage> collect;

		List<Channel> list = channelService.lambdaQuery().in(Channel::getId, channelIds).list();
		if (Func.isNotEmpty(list)) {
			//获取摄像头配置信息
			List<DeviceManage> cameraConfig = deviceManageService.getCameraConfig(list.get(0).getParklotId());
			if (cameraConfig != null) {
				collect = cameraConfig.stream().collect(Collectors.toMap(DeviceManage::getChannelId, v -> v));
			} else {
				collect = new HashMap<>(1);
			}

			for (Channel channel : list) {
				Parklot parklot = ParkLotCaches.getParkLot(channel.getParklotId());
				if (null == parklot) {
					continue;
				}

				parklotInfoVO.setParklotId(parklot.getId());
				parklotInfoVO.setName(parklot.getName());
				ChannelInfoVO channelInfoVO = new ChannelInfoVO();
				channelInfoVO.setChannelId(channel.getId());
				channelInfoVO.setChannelName(channel.getName());
				channelInfoVO.setType(channel.getType());
				channelInfoVO.setDeviceStatus(0);
				channelInfoVO.setParklotNo(parklot.getParklotNo());
				channelInfoVO.setChannelNo(channel.getChannelNo());
				channelInfoVO.setParklotId(channel.getParklotId());
				ChannelTodoVO todo = channelTodoService.latestInfoByChannelId(channel.getId());
				channelInfoVO.setHasTodo(Func.isNotEmpty(todo));
				channelInfoVO.setTodo(todo);
				channelInfoVO.setChannelPicture(channel.getChannelPicture());

				//填充摄像头视频协议
				DeviceManage deviceManage = collect.get(Long.valueOf(channel.getChannelNo()));

				if (Func.isNotEmpty(deviceManage)) {
					channelInfoVO.setCamera(deviceManage);
				} else {
					channelInfoVO.setCamera(new DeviceManage());
				}

				channelInfoVOList.add(channelInfoVO);
			}

		}
		parklotInfoVO.setChannels(channelInfoVOList);
		parklotVOS.add(parklotInfoVO);
		return parklotVOS;
	}

	private void fillReasons(UserChannelInfoVO vo) {
		List<ChannelAbnormalReason> list = channelAbnormalReasonService.selectList(SecureUtil.getUser().getTenantId());

		list.forEach(item -> {
			if (item.getType() == 1) {
				vo.getEnterReasons().add(item);
			}
			if (item.getType() == 2) {
				vo.getLeaveReasons().add(item);
			}
			if (item.getType() == 3) {
				vo.getFeeReasons().add(item);
			}
		});
	}

	@Override
	public UserChannelDuty getByUserId(Long userId) {
		List<UserChannelDuty> duties = list(Wrappers.<UserChannelDuty>lambdaQuery().eq(UserChannelDuty::getUserId, userId)
			.eq(UserChannelDuty::getStatus, EnableStatus.ACTIVE.getCode())
			.orderByDesc(UserChannelDuty::getCreateTime));
		if (duties.isEmpty()) {
			return null;
		}
		// 存在多个值班通道的时候，重置之前值班的数据
		if (duties.size() > 1) {
			for (UserChannelDuty duty : duties) {
				duty.setEndTime(LocalDateTime.now());
				duty.setStatus(EnableStatus.INVALID.getCode());
			}
			updateBatchById(duties);
		}
		return duties.get(0);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateDutyLogOnLine(String channelIds) {
		List<Long> channelList = Func.toLongList(channelIds);

		UserChannelDuty userChannelDuty = saveUserChannelDuty();

		List<Channel> channels = channelService.listByIds(channelList);
		Long dutyId = userChannelDuty.getId();
		//退出用户所有的值班通道
		exitUserDuty(dutyId);
		//保存用戶新选择的通道
		channels.forEach(c -> saveItem(c, dutyId));
		noticeAppGuard(channels, false);
		return true;
	}

	@Override
	public UserChannelDuty getCurrentUserDutyInfo() {
		return getOne(Wrappers.<UserChannelDuty>lambdaQuery()
			.eq(UserChannelDuty::getUserId, AuthUtil.getUserId())
			.eq(UserChannelDuty::getStatus, 1)
			.last("LIMIT 1"));
	}

	private void exitUserDuty(Long dutyId) {
		Long userId = SecureUtil.getUserId();
		userChannelDutyItemService.lambdaUpdate()
			.eq(UserChannelDutyItem::getDutyId, dutyId)
			.eq(UserChannelDutyItem::getUserId, userId)
			.set(UserChannelDutyItem::getStatus, 0)
			.update();
	}

}
