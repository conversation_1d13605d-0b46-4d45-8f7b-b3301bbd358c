package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.entity.ChangeDataLog;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.vo.ChangeDataLogVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 更改数据日志 服务类
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
public interface IChangeDataLogService extends BaseService<ChangeDataLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param changeDataLog
	 * @return
	 */
	IPage<ChangeDataLogVO> selectChangeDataLogPage(IPage<ChangeDataLogVO> page, ChangeDataLogVO changeDataLog);

	/**
	 * 岗亭修改车牌保存日志
	 * @param channelTodo
	 * @param oldPlate
	 * @return
	 */
	boolean modifyPlateInsertLog(ChannelTodo channelTodo, String oldPlate);

	/**
	 * 岗亭端匹配进场记录修改车牌保存日志
	 * @param parking
	 * @param channelTodo
	 * @param parkingOrderDTO
	 * @return
	 */
	boolean matchingEnterModifyPalteInsertLog(ParkingOrder parking, ChannelTodo channelTodo, ParkingOrderDTO parkingOrderDTO);
}
