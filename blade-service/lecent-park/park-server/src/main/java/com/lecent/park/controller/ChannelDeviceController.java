package com.lecent.park.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.ChannelDevice;
import com.lecent.park.service.IChannelDeviceService;
import com.lecent.park.vo.ChannelDeviceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *  控制器
 * <AUTHOR>
 * @since 2021-01-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/channeldevice")
@Api(value = "", tags = "接口")
public class ChannelDeviceController extends BladeController {

	private IChannelDeviceService channelDeviceService;

	/**
	 * 查询List
	 */
	@GetMapping("/listByChannelId")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询List", notes = "传入channelId")
	public R listByChannelId(Long channelId) {
		List<ChannelDevice> list = channelDeviceService.listByChannelId(channelId);
		return R.data(list);
	}



	/**
	 * 新增或修改
	 */
	@PostMapping("/saveOrUpdate")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入channelDevice")
	public R saveOrUpdate(@Valid @RequestBody ChannelDeviceVO channelDevice) {
		return R.status(channelDeviceService.saveOrUpdateChannelDevice(channelDevice));
	}


}
