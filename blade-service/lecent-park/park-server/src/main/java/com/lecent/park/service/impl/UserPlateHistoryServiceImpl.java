package com.lecent.park.service.impl;

import com.lecent.park.entity.UserPlateHistory;
import com.lecent.park.mapper.UserPlateHistoryMapper;
import com.lecent.park.service.IUserPlateHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.CUser;
import org.springblade.system.user.feign.ICUserClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 微信用户历史缴费车牌
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Slf4j
@Service
public class UserPlateHistoryServiceImpl extends BaseServiceImpl<UserPlateHistoryMapper, UserPlateHistory> implements IUserPlateHistoryService {

    @Resource
    private ICUserClient userClient;

    @Override
    public List<String> queryPlateListByUserId(Long userId) {
        List<UserPlateHistory> list = lambdaQuery().eq(UserPlateHistory::getUserId, userId).list();
        return list.stream().map(UserPlateHistory::getPlate).collect(Collectors.toList());
    }

    @Override
    public Boolean haveUserPlate(String plate, String userId) {
        return lambdaQuery().eq(UserPlateHistory::getPlate, plate).eq(UserPlateHistory::getUserId, userId).count() > 0;
    }

    @Async
    @Override
    public void saveUserPlateHistory(String tenantId, String openId, String plate) {
        try {
            log.info("保存微信用户缴费车牌 openId={} plate={}", openId, plate);
            if (Func.isBlank(openId) || Func.isBlank(plate)) {
                return;
            }
            R<CUser> r = userClient.getUserByTenantIdAndUnionId(tenantId, openId);
            if (r.isSuccess() && Func.isNotEmpty(r.getData())) {
                CUser user = r.getData();
                log.info("保存微信用户缴费车牌 user={}", Func.toJson(user));
                LecentAssert.notNull(user.getId(), "用户id为空");
                UserPlateHistory userPlateHistory = new UserPlateHistory();
                userPlateHistory.setPlate(plate);
                userPlateHistory.setUserId(user.getId());
                userPlateHistory.setTenantId(user.getTenantId());
                save(userPlateHistory);
            } else {
                log.error("保存微信用户缴费车牌 openId[{}]未查询到用户信息", openId);
            }
        } catch (Exception e) {
            log.error("saveUserPlateHistory", e);
        }
    }
}
