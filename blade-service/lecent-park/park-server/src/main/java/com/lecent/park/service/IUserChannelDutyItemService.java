package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.UserChannelDutyItem;
import com.lecent.park.vo.UserChannelDutyItemVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 通道值班表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
public interface IUserChannelDutyItemService extends BaseService<UserChannelDutyItem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userChannelDutyItem
	 * @return
	 */
	IPage<UserChannelDutyItemVO> selectUserChannelDutyItemPage(IPage<UserChannelDutyItemVO> page, UserChannelDutyItemVO userChannelDutyItem);

	void updateLogoutChannel(Long userId);

	List<Long> getByUserId(Long userId);

	/**
	 * 获取正在值班中的通道
	 * @param channelId 通道id
	 * @return
	 */
	UserChannelDutyItem getOnDutyItemByChannelId(Long channelId);

	/**
	 * 岗亭端根据当前登录用户获取值班得车场Id
	 * @param userId
	 * @return
	 */
	Long getCurrentUserParklotId(Long userId);


	/**
	 * 获取登录用户的值班通道列表
	 * @return UserChannelDutyItem
	 */
	List<UserChannelDutyItem> listByLoginUser();

}
