package com.lecent.park.wrapper;

import com.lecent.park.entity.UserChannelDutyItem;
import com.lecent.park.vo.UserChannelDutyItemVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 通道值班表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
public class UserChannelDutyItemWrapper extends BaseEntityWrapper<UserChannelDutyItem, UserChannelDutyItemVO>  {

	public static UserChannelDutyItemWrapper build() {
		return new UserChannelDutyItemWrapper();
 	}

	@Override
	public UserChannelDutyItemVO entityVO(UserChannelDutyItem userChannelDutyItem) {
		UserChannelDutyItemVO userChannelDutyItemVO = BeanUtil.copy(userChannelDutyItem, UserChannelDutyItemVO.class);

		//User createUser = UserCache.getUser(userChannelDutyItem.getCreateUser());
		//User updateUser = UserCache.getUser(userChannelDutyItem.getUpdateUser());
		//userChannelDutyItemVO.setCreateUserName(createUser.getName());
		//userChannelDutyItemVO.setUpdateUserName(updateUser.getName());

		return userChannelDutyItemVO;
	}

}
