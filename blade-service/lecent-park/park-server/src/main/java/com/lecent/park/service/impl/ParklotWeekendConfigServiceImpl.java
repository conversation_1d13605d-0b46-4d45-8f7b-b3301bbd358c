package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.ParklotWeekendConfig;
import com.lecent.park.mapper.ParklotWeekendConfigMapper;
import com.lecent.park.service.IParklotWeekendConfigService;
import com.lecent.park.vo.ParklotWeekendConfigVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
@Service
public class ParklotWeekendConfigServiceImpl extends BaseServiceImpl<ParklotWeekendConfigMapper, ParklotWeekendConfig> implements IParklotWeekendConfigService {

	@Override
	public IPage<ParklotWeekendConfigVO> selectParklotWeekendConfigPage(IPage<ParklotWeekendConfigVO> page, ParklotWeekendConfigVO parklotWeekendConfig) {
		return page.setRecords(baseMapper.selectParklotWeekendConfigPage(page, parklotWeekendConfig));
	}





	@Override
	public boolean isConfigWeekend(Long parkLotId) {
		List<ParklotWeekendConfig> list = this.list(Wrappers.<ParklotWeekendConfig>query().lambda().eq(ParklotWeekendConfig::getStatus, 1));
		if (ObjectUtil.isEmpty(list) || list.size()<1){
			return false;
		}
		ParklotWeekendConfig parklotWeekendConfig = list.get(0);
		if (1==parklotWeekendConfig.getIsSelected()){
			return true;
		}

		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveConfig(Integer isSettingWeekend) {

		this.remove(Wrappers.emptyWrapper());

		ParklotWeekendConfig weekendConfig = new ParklotWeekendConfig();
		weekendConfig.setIsSelected(isSettingWeekend);

		this.save(weekendConfig);

	}
}
