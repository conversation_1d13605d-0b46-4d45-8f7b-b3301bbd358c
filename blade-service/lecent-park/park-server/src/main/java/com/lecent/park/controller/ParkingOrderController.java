package com.lecent.park.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.biz.IParkingOrderBiz;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import com.lecent.park.entity.ParkingDetail;
import com.lecent.park.entity.ParkingDetailDTO;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.TimeNode;
import com.lecent.park.service.IParkingOrderService;
import com.lecent.park.vo.ParkingOrderVO;
import com.lecent.park.wrapper.ParkingOrderWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 停车订单 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/parkingOrder")
@Api(value = "停车订单接口", tags = "停车订单接口")
@Validated
@Slf4j
public class ParkingOrderController extends BladeController {

    private IParkingOrderBiz parkingOrderBiz;
    private IParkingOrderService parkingService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入parking")
    public R<ParkingOrderVO> detail(ParkingOrder parking) {
        ParkingOrderVO detail = this.parkingOrderBiz.detail(parking.getId());
        if (Func.isEmpty(detail)) {
            return R.data(null);
        }
        List<TimeNode> timeNodeList = parkingService.getParkingTimeNodeList(detail.getParkingTimeNode());
        timeNodeList = timeNodeList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
            () -> new TreeSet<>(Comparator.comparing(TimeNode::getStartDate))), ArrayList::new));
        timeNodeList = timeNodeList.stream().sorted(Comparator.comparing(TimeNode::getStartDate)).collect(Collectors.toList());
        detail.setParkingTimeNode(JSON.toJSONString(timeNodeList));
        return R.data(detail);
    }


    /**
     * 详情
     */
    @GetMapping("/getParkingList")
    @ApiOperation(value = "详情", notes = "传入parking")
    public R<List<ParkingOrderVO>> getParkingList(ParkingOrder parking) {
        ParkingOrderVO detail = this.parkingOrderBiz.detail(parking.getId());
        List<ParkingOrderVO> list = new ArrayList<>();
        list.add(detail);
        return R.data(list);
    }

    /**
     * 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入parking")
    public R<IPage<ParkingOrderVO>> list(ParkingOrder parking, Query query) {
        IPage<ParkingOrder> pages = parkingService.page(Condition.getPage(query), Condition.getQueryWrapper(parking));
        return R.data(ParkingOrderWrapper.build().pageVO(pages));
    }

    /**
     * 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入parking")
    public R<IPage<ParkingOrderVO>> page(ParkingOrderDTO parking, Query query) {
        Page<ParkingOrderVO> page = (Page) Condition.getPage(query);
        page.setSearchCount(false);
        IPage<ParkingOrderVO> pages = parkingService.newCustomPage(page, parking);
        return R.data(pages);
    }

    @GetMapping("/pageTotal")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入parking")
    public R pageTotal(ParkingOrderDTO parking) {
        return R.data(parkingService.pageTotal(parking));
    }

    /**
     * 岗亭自定义分页
     */
    @GetMapping("/gtPage")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "岗亭分页", notes = "传入parking")
    public R<IPage<ParkingOrderVO>> gtPage(ParkingOrderDTO parking, Query query) {
        IPage<ParkingOrderVO> pages = parkingService.customPage(parking, query);
        return R.data(pages);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入parking")
    public R save(@Valid @RequestBody ParkingOrder parking) {
        return R.status(parkingService.save(parking));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入parking")
    public R update(@Valid @RequestBody ParkingOrder parking) {
        return R.status(parkingService.updateById(parking));
    }

    /**
     * 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入parking")
    public R submit(@Valid @RequestBody ParkingOrder parking) {
        return R.status(parkingService.saveOrUpdate(parking));
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(parkingService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 进场记录
     */
    @GetMapping("/insidePage")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "进场记录", notes = "ParkingOrderDTO")
    public R<IPage<ParkingOrderVO>> insidePage(ParkingOrderDTO parking, Query query) {
        parking.setParkingStatus(ParkingStatusEnum.PARK_IN.getValue());
        IPage<ParkingOrderVO> pages = parkingService.insidePage(Condition.getPage(query), parking);
        return R.data(pages);
    }


    /**
     * 酒店授权车场停车记录
     */
    @GetMapping("/merchantAuthPlateList")
    @ApiOperation(value = "酒店授权车场停车记录", notes = "酒店授权车场停车记录")
    public R<List<ParkingOrderVO>> merchantAuthPlateList(
        @RequestParam("authPlateId") @NotNull(message = "authPlateId不能为空") Long authPlateId) {
        List<ParkingOrderVO> parkingOrderList = parkingService.merchantAuthPlateList(authPlateId);
        return R.data(parkingOrderList);
    }


    /**
     * 导入在场车辆
     */
    @PostMapping("/importParkingOrder")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "导入在场车辆", notes = "导入在场车辆")
    public R importParkingOrder(@RequestParam(value = "file", required = false) MultipartFile file,
                                @RequestParam(value = "parkLotId") @NotNull Long parkLotId) throws Exception {
        Boolean b = parkingService.importParkingOrder(file, parkLotId);
        return R.status(b);
    }

    /**
     * 在场车辆导入模板下载
     */
    @GetMapping("/downloadTemplate")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "在场车辆导入模板下载", notes = "response")
    public void downloadTemplate(HttpServletResponse response) {
        parkingService.downloadTemplate(response);
    }

    /**
     * 根据订单id 生成小程序二维码
     */
    @GetMapping("/getUnlimitedQRCodeByParkingOrderId")
    @ApiOperation(value = "根据订单id生成小程序二维码", notes = "response")
    public R getUnlimitedQRCodeByParkingOrderId(@RequestParam Long parkingOrderId) {
        String qrCodeByParkingOrderId = parkingService.getUnlimitedQRCodeByParkingOrderId(parkingOrderId);
        return R.data(qrCodeByParkingOrderId);
    }

    /**
     * 修改车牌（客服系统调用）
     */
    @GetMapping("/putPlate")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "修改车牌（客服系统调用）", notes = "传入parking")
    public R putPlate(ParkingOrder parking) {
        return R.status(parkingService.putPlate(parking));
    }

    /**
     * 获取对应的订单（客服系统调用）
     */
    @GetMapping("/getOrderByParkingId")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取对应的订单（客服系统调用）", notes = "parkingId")
    public ParkingOrderVO getOrderByPlate(@RequestParam("parkingId") Long parkingId) {
        return parkingService.getOrderByParkingId(parkingId);
    }

    /**
     * 根据车牌查询欠费金额（客服系统调用）
     */
    @TenantIgnore
    @GetMapping("/selectUnpaidAmountByPlate")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "根据车牌查询欠费金额（客服系统调用）", notes = "plate")
    public BigDecimal selectUnpaidAmountByPlate(@RequestParam("plate") String plate, @RequestParam("parklotId") Long parklotId) {
        return parkingService.selectUnpaidAmountByPlate(plate, parklotId);
    }

    /**
     * 查询停车记录
     */
    @GetMapping("/v2/page")
    @ApiOperation(value = "查询停车记录", notes = "传入parking")
    public R<IPage<ParkingDetail>> queryParkingOrderPage(ParkingOrderDTO parking, Query query) {
        IPage<ParkingDetail> page = Condition.getPage(query);
        return R.data(page.setRecords(parkingService.customQueryParkingOrder(page, parking)));
    }

    @GetMapping("/handle/plate/color/unusual/order")
    @ApiOperation(value = "处理车牌颜色异常订单")
    public R<Boolean> handlePlateColorUnusualOrder() {
        return R.status(parkingService.handlePlateColorUnusualOrder());
    }

    /**
     * 查询停车记录
     */
    @PostMapping("/updateParkingOrder")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "修改停车信息（客服系统调用）", notes = "传入parking")
    public R<Boolean> updateParkingOrder(@RequestBody ParkingOrderDTO parking) {
        return R.data(parkingService.updateParkingOrder(parking));
    }

    /**
     * 获取修改后的费用
     */
    @PostMapping("/getUpdateParkingOrderAmount")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "获取修改后的费用（客服系统调用）", notes = "传入parking")
    public R<ParkingDetailDTO> getUpdateParkingOrderAmount(@RequestBody ParkingOrderDTO parking) {
        return R.data(parkingService.getUpdateParkingOrderAmount(parking));
    }

    /**
     * 获取修改后的费用
     */
    @PostMapping("/delParkingOrder")
    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "删除停车记录（客服系统调用）", notes = "传入parking")
    public R<Boolean> delParkingOrder(@RequestBody ParkingOrderDTO parking) {
        return R.data(parkingService.delParkingOrder(parking));
    }

    /**
     * 清理停车记录（客服系统调用）
     * 不生成异常处理记录，释放车位
     */
    @PostMapping("/cleanParkingOrder")
    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "清理停车记录（客服系统调用）", notes = "传入parking")
    public R<Boolean> cleanParkingOrder(@RequestBody ParkingOrderDTO parking) {
        return R.data(parkingService.cleanParkingOrder(parking));
    }

    /**
     * 异常订单退款处理
     */
    @PostMapping("/refundAbnormalOrder")
    @ApiOperation(value = "异常订单退款处理", notes = "传入parking")
    public R<Boolean> refundAbnormalOrder(@RequestParam("parkingOrderId") Long parkingOrderId) {
        return R.data(parkingService.refundAbnormalOrder(parkingOrderId));
    }

    /**
     * 异常订单无需处理
     */
    @PostMapping("/ignoreAbnormalOrder")
    @ApiOperation(value = "异常订单无需处理", notes = "传入parking")
    public R<Boolean> ignoreAbnormalOrder(@RequestParam("parkingOrderId") Long parkingOrderId) {
        return R.data(parkingService.ignoreAbnormalOrder(parkingOrderId));
    }

    /**
     * 通过车牌和时间查询停车记录（交警专用）
     */
    @GetMapping("/queryByPlateAndTime")
    @ApiOperationSupport(order = 15)
    @ApiOperation(value = "通过车牌和时间查询停车记录（交警专用）", notes = "传入车牌、进场开始和结束时间、出场开始和结束时间")
    public R<IPage<ParkingOrderVO>> queryByPlateAndTime(ParkingOrderDTO parking, Query query) {
        return R.data(parkingService.queryByPlateAndTime(Condition.getPage(query), parking));
    }
}
