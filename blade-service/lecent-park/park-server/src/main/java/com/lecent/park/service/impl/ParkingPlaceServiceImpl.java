package com.lecent.park.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.device.entity.DeviceParam;
import com.lecent.device.enums.ProductParamK;
import com.lecent.device.feign.IDeviceClient;
import com.lecent.device.vo.DeviceVO;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.cache.ParklotPlaceCache;
import com.lecent.park.common.constant.ParkingStatus;
import com.lecent.park.common.enums.space.ParkingPlaceTypeEnum;
import com.lecent.park.controller.ebo.mini.resp.EboParkLotPlace;
import com.lecent.park.controller.ebo.mini.resp.PlaceDevice;
import com.lecent.park.domain.space.ParkingPlaceDomain;
import com.lecent.park.domain.space.ParkingPlaceDomainFactory;
import com.lecent.park.dto.space.ParkingPlaceDTO;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.en.parklot.ParkLotType;
import com.lecent.park.entity.*;
import com.lecent.park.service.*;
import com.lecent.park.vo.ParkingPlaceDeviceVO;
import com.lecent.park.vo.ParkingPlaceStatisticsVO;
import com.lecent.park.vo.ParkingPlaceVO;
import com.lecent.park.vo.RemainPlaceNumVO;
import com.lecent.park.vo.place.PlaceIteratorItemVO;
import com.lecent.park.vo.place.PlaceIteratorLastNextIdVO;
import com.lecent.park.vo.place.PlaceIteratorVO;
import com.lecent.park.wrapper.ParkingPlaceWrapper;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParkingPlaceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.common.utils.stream.StreamUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.system.cache.ParamCache;
import org.springblade.system.entity.Dict;
import org.springblade.system.feign.IDictClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车位信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Slf4j
@Service
public class ParkingPlaceServiceImpl extends BaseServiceImpl<ParkingPlaceMapper, ParkingPlace> implements IParkingPlaceService {

	@Resource
	private ICardCategoryService cardCategoryService;
	@Resource
	@Lazy
	private IParklotRegionService parklotRegionService;
	@Resource
	@Lazy
	private IFreeCardAuthService freeCardAuthService;
	@Resource
	@Lazy
	private ICardService cardService;
	@Resource
	@Lazy
	private IParkingOrderService parkingOrderService;
	@Resource
	@Lazy
	private IReserveParkService reserveParkService;
	@Resource
	private IDomainService domainService;
	@Resource
	private IBaseAppConfigService baseAppConfigService;
	@Resource
	private IParklotDeviceRetService parklotDeviceRetService;
	@Resource
	private IDeviceClient deviceClient;
	@Resource
	private ParkingPlaceStatusService parkingPlaceStatusService;
	@Resource
	private IBVehicleUnconsciousRelationService vehicleUnconsciousRelationService;
	@Resource
	private IDictClient dictClient;

	@Resource
	private IUserParklotService userParklotService;

	@Override
	public IPage<ParkingPlaceVO> selectParkingPlacePage(IPage<ParkingPlaceVO> page, ParkingPlaceDTO parkingPlaceDTO) {
		if (!AuthUtil.isAdministrator()) {
			parkingPlaceDTO.setUserId(AuthUtil.getUserId());
		}

		final List<ParkingPlaceVO> parkingPlaceList = baseMapper.selectParkingPlacePage(page, parkingPlaceDTO);

		if (!parkingPlaceList.isEmpty()) {
			final List<Long> parkPlaceIds = parkingPlaceList.stream()
				.map(BaseEntity::getId)
				.collect(Collectors.toList());
			final Map<Long, List<DeviceVO>> devices = this.parklotDeviceRetService.getDevicesMap(parkPlaceIds);
			parkingPlaceList.forEach(t -> t.setDeviceList(devices.get(t.getId())));
		}

		return page.setRecords(parkingPlaceList);
	}

	@Override
	public ParkingPlaceVO detailById(Long id) {
		LecentAssert.isTrue(Objects.nonNull(id), "参数id不能为空");
		return buildParkingPlaceVO(Optional.ofNullable(getById(id)));
	}

	@Override
	public ParkingPlaceVO detailByShortUrl(String shortUrl) {
		LecentAssert.notBlank(shortUrl, "参数[shortUrl]不能为空");
		return buildParkingPlaceVO(oneOptByShortUrl(shortUrl));
	}

	@Override
	public ParkingPlaceVO detailByPayCode(String payCode) {
		LecentAssert.isTrue(Func.isNoneBlank(payCode), "参数payCode不能为空");
		return buildParkingPlaceVO(oneOptByPayCode(payCode));
	}

	private ParkingPlaceVO buildParkingPlaceVO(Optional<ParkingPlace> parkingPlaceOpt) {
		return parkingPlaceOpt.map(t -> {
			Map<Long, List<DeviceVO>> devicesMap = this.parklotDeviceRetService.getDevicesMap(ListUtil.of(t.getId()));
			return ParkingPlaceWrapper.build().entityVO(t, ParkLotCaches.existParkLot(t.getParklotId()), devicesMap.get(t.getId()));
		}).orElse(null);
	}

	private Optional<ParkingPlace> oneOptByShortUrl(String shortUrl) {
		return this.lambdaQuery().eq(ParkingPlace::getShortUrl, shortUrl).oneOpt();
	}

	private Optional<ParkingPlace> oneOptByPayCode(String payCode) {
		return this.lambdaQuery().eq(ParkingPlace::getPayCode, payCode).oneOpt();
	}


	/**
	 * 获取车位状况统计信息
	 *
	 * @param parkingPlaceDTO 搜索条件
	 * @return HashMap<String, Integer>
	 */
	@Slave
	@Override
	public ParkingPlaceStatisticsVO getParkSpaceStateStatistics(ParkingPlaceDTO parkingPlaceDTO) {
		parkingPlaceDTO.setUserId(AuthUtil.getUserId());
		ParkingPlaceStatisticsVO placeStatisticsVO = new ParkingPlaceStatisticsVO();
		placeStatisticsVO.init();
		HashMap<String, Object> map = this.baseMapper.getParkSpaceStateStatistics(parkingPlaceDTO);
		placeStatisticsVO.setPlaceCount(Integer.parseInt(map.get("total").toString()));
		List<ParkingPlaceDeviceVO> itemList = new ArrayList<>();
		ParkingPlaceDeviceVO item = new ParkingPlaceDeviceVO();
		item.setName("占用车位数");
		item.setCount(Integer.parseInt(map.get("usedCount").toString()));
		itemList.add(item);
		item = new ParkingPlaceDeviceVO();
		item.setName("空闲车位数");
		item.setCount(Integer.parseInt(map.get("freeCount").toString()));
		itemList.add(item);
		item = new ParkingPlaceDeviceVO();
		item.setName("暂停车位数");
		item.setCount(Integer.parseInt(map.get("pauseCount").toString()));
		itemList.add(item);
		placeStatisticsVO.setPlaceList(itemList);

		//统计设备
		List<ParkingPlaceDeviceVO> list = this.baseMapper.getParkSpaceStateStatisticsByDevice(parkingPlaceDTO);
		int deviceCount = 0;
		int offlineDeviceCount = 0;
		List<ParkingPlaceDeviceVO> deviceList = new ArrayList<>();
		List<ParkingPlaceDeviceVO> offlineDeviceList = new ArrayList<>();
		if (Func.isNotEmpty(list)) {
			R<List<Dict>> dictR = dictClient.getList("device_type");
			for (ParkingPlaceDeviceVO deviceVO : list) {
				String deviceTypeName = "";
				if (Func.isNotEmpty(dictR) && Func.isNotEmpty(dictR.getData())) {
					for (Dict dict : dictR.getData()) {
						if (dict.getDictKey().equals(deviceVO.getDeviceType().toString())) {
							deviceTypeName = dict.getDictValue();
							break;
						}
					}
				}
				deviceCount = deviceCount + deviceVO.getDeviceCount();
				offlineDeviceCount = offlineDeviceCount + deviceVO.getOfflineDeviceCount();
				item = new ParkingPlaceDeviceVO();
				item.setDeviceType(deviceVO.getDeviceType());
				item.setCount(deviceVO.getDeviceCount());
				item.setName(deviceTypeName);
				deviceList.add(item);
				item = new ParkingPlaceDeviceVO();
				item.setDeviceType(deviceVO.getDeviceType());
				item.setCount(deviceVO.getOfflineDeviceCount());
				item.setName(deviceTypeName);
				offlineDeviceList.add(item);
			}
		}
		placeStatisticsVO.setDeviceCount(deviceCount);
		placeStatisticsVO.setDeviceList(deviceList);
		placeStatisticsVO.setOfflineDeviceCount(offlineDeviceCount);
		placeStatisticsVO.setOfflineDeviceList(offlineDeviceList);
		return placeStatisticsVO;
	}

	/**
	 * @param parklotId 车场id
	 * @return 车位数量
	 */
	@Override
	public int getListByParklotId(Long parklotId) {
		return count(Wrappers.<ParkingPlace>lambdaQuery().eq(ParkingPlace::getParklotId, parklotId));
	}

	@Override
	public int getListByParklotId(Long parklotId, Integer status) {
		return count(Wrappers.<ParkingPlace>lambdaQuery().eq(ParkingPlace::getParklotId, parklotId).eq(BaseEntity::getStatus, status));
	}

	@Override
	public List<ParkingPlace> listByParklotId(Long parklotId) {
		return this.lambdaQuery().eq(ParkingPlace::getParklotId, parklotId).list();
	}

	public List<ParkingPlace> listByParklotId(List<Long> parklotIds) {
		List<ParkingPlace> placeList = this.lambdaQuery().in(ParkingPlace::getParklotId, parklotIds).list();
		return placeList;
	}

	@Override
	public boolean save(ParkingPlaceDTO parkingPlace) {
		parkingPlace.setBatchSize(1);
		return batchSave(parkingPlace);
	}


	/**
	 * 批量创建车位
	 *
	 * @return boolean
	 */
	@Override
	@RedisLock(value = ParkCacheNames.LECENT_PARK_SPACE_EDIT_LOCK, param = "#parkingPlaceDTO.parklotId")
	public boolean batchSave(ParkingPlaceDTO parkingPlaceDTO) {
		final Long parklotId = parkingPlaceDTO.getParklotId();
		final Parklot parklot = ParkLotCaches.existParkLot(parklotId);
		final Integer batchSize = parkingPlaceDTO.getBatchSize();

		LecentAssert.notNull(batchSize, "批量创建车位数量不能为空");
		LecentAssert.isTrue(batchSize > 0, "批量创建车位数量必须大于0");
		LecentAssert.notNull(parkingPlaceDTO.getPlaceType(), "车位类型不能为空");

		ParklotRegion region = parklotRegionService.getById(parkingPlaceDTO.getRegionId());

		int curMaxPayCodeNum = getCurMaxPayCodeNum(parklotId);

		if (parklot.isRoadSide()) {
			checkMaxNumLimit(curMaxPayCodeNum + batchSize, parklot.getTempLotAmount());
			parkingPlaceDTO.setPayQrcodePath(existPayQrcodePath());

		} else {
			LecentAssert.notNull(region, "未查询到区域信息");
			//路外停车场才校验区域
			validateRegionLimit(parkingPlaceDTO, region);
		}

		final ArrayList<ParkingPlaceDomain> parkingPlaceDomains = new ArrayList<>();

		// 去除dto多余参数
		ParkingPlaceDTO parkingPlace = new ParkingPlaceDTO();
		parkingPlace.setBatchSize(parkingPlaceDTO.getBatchSize());
		parkingPlace.setParklotId(parkingPlaceDTO.getParklotId());
		parkingPlace.setIsAppointment(parkingPlaceDTO.getIsAppointment());
		parkingPlace.setPlaceType(parkingPlaceDTO.getPlaceType());
		parkingPlace.setPayQrcodePath(parkingPlaceDTO.getPayQrcodePath());

		for (int i = 1; i <= batchSize; i++) {
			int nextPayCodeNum = curMaxPayCodeNum + i;
			ParkingPlaceDomain domain = ParkingPlaceDomainFactory.get(parklot.isRoadSide());
			domain.create(parkingPlace, region, nextPayCodeNum);
			log.debug("ParkingPlaceDomain = {}", domain);
			// 校验车位编号重复
			checkPlaceCode(domain.getCheckPlaceCodeQueryWrapper());
			parkingPlaceDomains.add(domain);
		}

		List<ParkingPlace> parkingPlaces = parkingPlaceDomains.stream()
			.peek(t -> t.generateShortQrUrl(this))
			.map(ParkingPlaceDomain::toDO)
			.collect(Collectors.toList());
		boolean result = this.saveBatch(parkingPlaces);
		// 同步创建车位状态
		parkingPlaceStatusService.batchCreateByParkingPlace(parkingPlaces);
		return result;
	}

	/**
	 * 更新车位信息
	 *
	 * @param parkingPlace 车位信息DTO
	 * @return boolean
	 */
	@Override
	public boolean update(ParkingPlaceDTO parkingPlace) {
		LecentAssert.notNull(parkingPlace.getId(), "车位ID不能为空");
		// 编辑
		final Parklot parklot = ParkLotCaches.existParkLot(parkingPlace.getParklotId());
		//校验重复车位编号
		checkPlaceCode(parkingPlace, ParkLotType.isRoadIn(parklot.getParklotType()));
		return updateById(parkingPlace);
	}

	private void checkPlaceCode(ParkingPlace parkingPlace, boolean isRoadIn) {
		LambdaQueryWrapper<ParkingPlace> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper = queryWrapper.eq(ParkingPlace::getPlaceCode, parkingPlace.getPlaceCode())
			.eq(ParkingPlace::getParklotId, parkingPlace.getParklotId());
		// 为路外停车场时，增加区域条件
		queryWrapper.func(!isRoadIn, t -> t.eq(ParkingPlace::getRegionId, parkingPlace.getRegionId()));
		queryWrapper.func(Objects.nonNull(parkingPlace.getId()), t -> t.ne(ParkingPlace::getId, parkingPlace.getId()));

		checkPlaceCode(queryWrapper);
	}

	private void checkPlaceCode(LambdaQueryWrapper<ParkingPlace> queryWrapper) {
		int count = selfService().countTenantIgnore(queryWrapper);
		if (count > 0) {
			throw new ServiceException("操作失败，车位编号重复，请重新填写！");
		}
	}

	@Override
	@TenantIgnore
	public int countTenantIgnore(Wrapper<ParkingPlace> queryWrapper) {
		return this.count(queryWrapper);
	}

	@Override
	public List<ParkingPlace> dropListByRegionId(Long regionId) {
		return this.list(Wrappers.<ParkingPlace>query().lambda().eq(ParkingPlace::getRegionId, regionId));
	}

	@Override
	public List<ParkingPlace> getByRegionIds(List<Long> regionIdList) {
		ArrayList<Integer> types = new ArrayList<>();
		types.add(ParkingPlaceTypeEnum.TEMP.getValue());
		types.add(ParkingPlaceTypeEnum.RENT_VIP.getValue());
		types.add(ParkingPlaceTypeEnum.RENT_NO_BLOCK.getValue());
		LambdaQueryWrapper<ParkingPlace> wrapper = Wrappers.<ParkingPlace>lambdaQuery()
			.in(ParkingPlace::getRegionId, regionIdList)
			.in(ParkingPlace::getPlaceType, types);
		List<FreeCardAuth> freeCardAuths = freeCardAuthService
			.list(Wrappers.<FreeCardAuth>lambdaQuery()
				.isNotNull(FreeCardAuth::getPlaceIds)
				.ne(FreeCardAuth::getPlaceIds, ""));
		LambdaQueryWrapper<Card> cardLambdaQueryWrapper = Wrappers.<Card>lambdaQuery()
			.isNotNull(Card::getPlaceCodeId)
			.ne(Card::getPlaceCodeId, "");
		for (int i = 0; i < regionIdList.size(); i++) {
			if (i == regionIdList.size() - 1) {
				cardLambdaQueryWrapper.like(Card::getRegionId, regionIdList.get(i));
				break;
			}
			cardLambdaQueryWrapper.like(Card::getRegionId, regionIdList.get(i)).or();
		}
		List<Card> cardList = cardService.list(cardLambdaQueryWrapper);
		List<Long> placeIds = new ArrayList<>();
		if (cardList.size() > 0) {
			placeIds.addAll(cardList.stream().map(Card::getPlaceCodeId)
				.collect(Collectors.toList()));
		}
		if (freeCardAuths.size() > 0) {
			placeIds.addAll(Func.toLongList(freeCardAuths.stream().map(FreeCardAuth::getPlaceIds)
				.collect(Collectors.joining(","))));
		}
		if (placeIds.size() > 0) {
			wrapper.notIn(ParkingPlace::getId, placeIds);
		}
		return this.list(wrapper);
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	@RedisLock(value = ParkCacheNames.LECENT_RESERVE_PARK_PLACE_LOCK, param = "#parklotId+':'+#placeType")
	public List<ParkingPlace> reservePlace(Integer count, Integer interest, Long parklotId, Integer placeType) {
		List<ParkingPlace> ret = new ArrayList<>(count);
		List<ParkingPlace> parkingPlaces = new ArrayList<>();
		List<ParkingOrder> parkingOrders =
			parkingOrderService.list(Wrappers.<ParkingOrder>lambdaQuery().eq(ParkingOrder::getStatus,
				ParkingStatus.SPRKING_PRESENT));
		LambdaQueryWrapper<ParkingPlace> wrapper = Wrappers.<ParkingPlace>lambdaQuery()
			.eq(ParkingPlace::getParklotId, parklotId)
			.eq(ParkingPlace::getIsAppointment, 2);
		if (parkingOrders.size() > 0) {
			wrapper.notIn(ParkingPlace::getId, parkingOrders.stream()
				.map(ParkingOrder::getPlaceId).collect(Collectors.toList()));
		}
		//查询车位表中可以预约的车位列表
		if (ParkingPlaceTypeEnum.TEMP.getValue().equals(placeType)) {
			wrapper.eq(ParkingPlace::getPlaceType,
				ParkingPlaceTypeEnum.TEMP.getValue());
			parkingPlaces = list(wrapper);
		} else if (ParkingPlaceTypeEnum.RENT_VIP.getValue().equals(placeType)) {
			wrapper.eq(ParkingPlace::getPlaceType,
				ParkingPlaceTypeEnum.RENT_VIP.getValue());
			parkingPlaces = list(wrapper);
		}

		for (int i = 0; i < count; i++) {
			for (ParkingPlace parkingPlace : parkingPlaces) {
				boolean contain = ret.contains(parkingPlace);
				// 如果当前车位可用，直接结束本层循环
				if (!contain && isFreeOrResever(parkingPlace)) {
					ret.add(parkingPlace);
					break;
				}
			}
		}

		return ret;
	}

	private boolean isFreeOrResever(ParkingPlace place) {
		int freeCardAuths = freeCardAuthService.count(Wrappers.<FreeCardAuth>lambdaQuery()
			.eq(FreeCardAuth::getParklotId,
				place.getParklotId())
			.eq(FreeCardAuth::getStatus, 1)
			.like(FreeCardAuth::getPlaceIds,
				place.getId()));
		if (freeCardAuths == 0) {
			int reseverCount = reserveParkService.count(Wrappers.<ReservePark>lambdaQuery()
				.eq(ReservePark::getPlaceId, place.getId())
				.in(ReservePark::getStatus, Arrays.asList(0, 1
					, 2)));
			return reseverCount == 0;
		}

		return false;
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public boolean releasePlace(List<Long> placeIds) {
		if (!placeIds.isEmpty()) {
			placeIds.forEach(id -> {
				ParkingPlace parkingPlace = getById(id);
				LecentAssert.notNull(parkingPlace, "没有此车位");
				parkingPlace.setIsFree(ParkingPlaceIdleState.IDLE.getValue());
				updateById(parkingPlace);
			});
		}
		return true;
	}

	@Override
	public ParkingPlace getByPlaceCode(String placeCode) {
		return this.getOne(Wrappers.<ParkingPlace>lambdaQuery()
			.eq(ParkingPlace::getPlaceCode, placeCode));
	}

	@Override
	public List<ParkingPlace> getByRuleIdRegionIds(List<Long> regionIdList, Long ruleId) {
		CardCategory cardCategory = cardCategoryService.getById(ruleId);
		LecentAssert.notNull(cardCategory, "收费规则不存在！");
		LambdaQueryWrapper<ParkingPlace> wrapper = Wrappers.<ParkingPlace>lambdaQuery()
			.in(ParkingPlace::getRegionId, regionIdList);
		if (!ParkingPlaceTypeEnum.OTHER.getValue().equals(Integer.valueOf(cardCategory.getClassify()))) {
			if (ParkingPlaceTypeEnum.SHARE_CARD.getValue().equals(Integer.valueOf(cardCategory.getClassify()))) {
				wrapper.eq(ParkingPlace::getPlaceType, ParkingPlaceTypeEnum.TEMP.getValue());
			} else {
				wrapper.eq(ParkingPlace::getPlaceType, cardCategory.getClassify());
			}
		}
		List<FreeCardAuth> freeCardAuths = freeCardAuthService
			.list(Wrappers.<FreeCardAuth>lambdaQuery()
				.isNotNull(FreeCardAuth::getPlaceIds)
				.ne(FreeCardAuth::getPlaceIds, ""));
		LambdaQueryWrapper<Card> cardLambdaQueryWrapper = Wrappers.<Card>lambdaQuery()
			.isNotNull(Card::getPlaceCodeId)
			.ne(Card::getPlaceCodeId, "");
		for (int i = 0; i < regionIdList.size(); i++) {
			if (i == regionIdList.size() - 1) {
				cardLambdaQueryWrapper.like(Card::getRegionId, regionIdList.get(i));
				break;
			}
			cardLambdaQueryWrapper.like(Card::getRegionId, regionIdList.get(i)).or();
		}
		List<Card> cardList = cardService.list(cardLambdaQueryWrapper);
		List<Long> placeIds = new ArrayList<>();
		if (cardList.size() > 0) {
			placeIds.addAll(cardList.stream().map(Card::getPlaceCodeId)
				.collect(Collectors.toList()));
		}
		if (freeCardAuths.size() > 0) {
			placeIds.addAll(freeCardAuths.stream().map(FreeCardAuth::getPlaceIds)
				.map(Long::valueOf)
				.collect(Collectors.toList()));
		}
		if (placeIds.size() > 0) {
			wrapper.notIn(ParkingPlace::getId, placeIds);
		}
		return list(wrapper);
	}

	@Override
	public RemainPlaceNumVO getRemainPlaceNumByRegionId(Long regionId) {
		ParklotRegion region = parklotRegionService.getById(regionId);
		LecentAssert.notNull(region, "区域不存在！");

		Integer indNum = this.count(Wrappers.<ParkingPlace>lambdaQuery()
			.eq(ParkingPlace::getRegionId, regionId)
			.eq(ParkingPlace::getPlaceType, ParkingPlaceTypeEnum.RIGHT.getValue()));

		Integer letterNum = this.count(Wrappers.<ParkingPlace>lambdaQuery()
			.eq(ParkingPlace::getRegionId, regionId)
			.eq(ParkingPlace::getPlaceType,
				ParkingPlaceTypeEnum.MOTHER_CHILD_PLACE.getValue()));

		Integer tempNum = this.count(Wrappers.<ParkingPlace>lambdaQuery()
			.eq(ParkingPlace::getRegionId, regionId)
			.eq(ParkingPlace::getPlaceType, ParkingPlaceTypeEnum.TEMP.getValue()));

		Integer vipNum = this.count(Wrappers.<ParkingPlace>lambdaQuery()
			.eq(ParkingPlace::getRegionId, regionId)
			.eq(ParkingPlace::getPlaceType, ParkingPlaceTypeEnum.RENT_VIP.getValue()));

		Integer accNum = this.count(Wrappers.<ParkingPlace>lambdaQuery()
			.eq(ParkingPlace::getRegionId, regionId)
			.eq(ParkingPlace::getPlaceType,
				ParkingPlaceTypeEnum.RENT_NO_BLOCK.getValue()));

		return RemainPlaceNumVO.builder()
			.accNum(region.getAccessibilityLotAmount() - accNum)
			.indNum(region.getIndependentOwnershipAmount() - indNum)
			.letterNum(region.getLetterLotAmount() - letterNum)
			.tempNum(region.getTempLotAmount() - region.getVipLotAmount() - region.getAccessibilityLotAmount() - tempNum)
			.vipNum(region.getVipLotAmount() - vipNum)
			.build();
	}

	@Override
	public PlaceDevice isPlaceFree(Long parkLotPlaceId) {
		ParkingPlace parkingPlace = this.getById(parkLotPlaceId);
		PlaceDevice build = PlaceDevice.builder().isDown(Func.isNotEmpty(parkingPlace) && parkingPlace.getIsFree() == 1).build();

		ParklotDeviceRet spaceDevice = parklotDeviceRetService.getByPlaceIdProduct(parkLotPlaceId, DeviceType.PARKING_LOCK);
		if (spaceDevice != null) {
			R<DeviceVO> deviceR = deviceClient.getById(spaceDevice.getDeviceId());
			if (deviceR.isSuccess() && deviceR.getData() != null) {
				build.setDeviceName(deviceR.getData().getName());
				build.setSn(deviceR.getData().getSn());
				build.setMac(deviceR.getData().getMac());
				if (Func.isNotEmpty(deviceR.getData().getParams())) {
					for (DeviceParam param : deviceR.getData().getParams()) {
						if (ProductParamK.BLUETOOTH_NAME.getKey().equals(param.getK())) {
							build.setBluetoothName(param.getV());
						}
					}
				}
			}
		}

		return build;
	}

	@Override
	public int countReservedVipNumber(Long parkLotId) {
		return baseMapper.countReservedVipNumber(parkLotId);
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public boolean updateIsFree(Long id, int isFree) {
		LambdaUpdateWrapper<ParkingPlace> updateCondition =
			Wrappers.<ParkingPlace>update().lambda()
				.set(ParkingPlace::getIsFree, isFree)
				.eq(ParkingPlace::getId, id);

		return this.update(updateCondition);
	}

	@Override
	public String getPlaceIds(String regionId) {
		return baseMapper.getPlaceIds(regionId);
	}

	@Override
	public List<ParkingPlaceVO> exprPlace(ParkingPlaceDTO parkingPlace) {
		return baseMapper.selectParkingPlacePage(null, parkingPlace);
	}

	@Override
	public List<ParkingPlace> placeListByRegionId(Long regionId) {
		return list(Wrappers.<ParkingPlace>lambdaQuery().eq(ParkingPlace::getRegionId, regionId));
	}

	@Override
	public ParkingPlace checkById(Long placeId) {
		ParkingPlace place = this.getById(placeId);
		LecentAssert.notNull(place, "没有此车位");
		return place;
	}

	/**
	 * 根据设备序列号查询车位信息
	 *
	 * @param sn 设备序列号
	 * @return ParkingPlace
	 */
	@Nullable
	@Override
	public ParkingPlace getByDeviceSn(String sn) {
		return this.parklotDeviceRetService.getOneOptByDeviceSn(sn)
			.map(t -> this.getById(t.getParkPlaceId()))
			.orElse(null);
	}

	/**
	 * 根据设备ID查询车位信息
	 *
	 * @param sn 设备序列号
	 * @return ParkingPlace
	 */
	@Nullable
	@Override
	public ParkingPlace getByDeviceId(Long deviceId) {
		return this.parklotDeviceRetService.getOneOptByDeviceId(deviceId)
			.map(t -> this.getById(t.getParkPlaceId()))
			.orElse(null);
	}

	@Nullable
	@Override
	public ParkingPlace existByDeviceSn(String sn) {
		ParkingPlace parkingPlace = this.getByDeviceSn(sn);
		LecentAssert.notNull(parkingPlace, "序列号为[{}]的设备未查询到车位信息", sn);
		return parkingPlace;
	}


	@Override
	public String shortQrUrl(Long placeId, String url) {
		ParkingPlace parkingPlace = checkById(placeId);

		String shortUrl = parkingPlace.getShortUrl();
		if (Func.isNotBlank(shortUrl)) {
			return shortUrl;
		}

		shortUrl = getShortQrUrl(parkingPlace.getParklotId(), url);

		parkingPlace.setShortUrl(shortUrl);
		parkingPlace.setLongUrl(url);
		updateById(parkingPlace);
		return shortUrl;
	}

	@Override
	public String getShortQrUrl(Long parklotId, String url) {
		Parklot parklot = ParkLotCaches.existParkLot(parklotId);

		Long scanMimiId = parklot.getScanMimiId();
		LecentAssert.notNull(scanMimiId, "车场未配置小程序ID");

		BaseAppConfig config = baseAppConfigService.getConfigById(scanMimiId);
		LecentAssert.notNull(config, "未找到配置信息");

		// 小程序短链接
		return domainService.getMiniShortUrl(config.getAppId(), config.getAppSecret(), url);
	}

	@Override
	public List<EboParkLotPlace> eboParkLotPlaceList(Long parklotId) {
		return listByParklotId(parklotId).stream()
			.map(t ->
				EboParkLotPlace.builder()
					.placeCode(t.getPlaceCode())
					.placeStatus(t.getIsFree())
					.build())
			.collect(Collectors.toList());
	}

	@Slave
	@Override
	public int usedNumber(Long parklotId) {
		return this.lambdaQuery()
			.eq(ParkingPlace::getParklotId, parklotId)
			.eq(ParkingPlace::getIsFree, ParkingPlaceIdleState.OCCUPIED.getValue())
			.count();
	}

	@Override
	public int getDisableNum(Long parklotId) {
		return this.lambdaQuery()
			.eq(ParkingPlace::getParklotId, parklotId)
			.eq(ParkingPlace::getStatus, EnableStatus.INVALID)
			.count();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean enableSpace(Long id, String memo) {
		return this.updateStatusProcess(id, EnableStatus.ACTIVE.getCode(), memo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean disableSpace(Long id, String memo) {
		return this.updateStatusProcess(id, EnableStatus.INVALID.getCode(), memo);
	}

	/**
	 * 车位状态变更业务
	 *
	 * @param ids    车位ID
	 * @param status 车位状态
	 * @param memo   备注
	 * @return boolean
	 */
	@Override
	public boolean updateStatusProcess(List<Long> ids, Integer status, String memo) {

		for (Long id : ids) {
			updateStatusProcess(id, status, memo);
		}
		return true;
	}

	/**
	 * 车位状态变更业务
	 *
	 * @param id     车位ID
	 * @param status 车位状态
	 * @param memo   备注
	 * @return boolean
	 */
	@Override
	public boolean updateStatusProcess(@Valid @NotNull(message = "参数[id]不能为空") Long id,
									   @NotNull(message = "参数[status]不能为空") Integer status,
									   @NotBlank(message = "参数[memo]不能为空") String memo) {
		// 如果车位暂停使用则在场车辆出场
		if (status == 0) {
			ParkingPlaceStatusDO statusDO = parkingPlaceStatusService.getByPlaceId(id);
			if (statusDO != null && statusDO.getIdeState() == 0) {
				ParkingOrder parking = new ParkingOrder();
				parking.setPlaceId(statusDO.getPlaceId());
				parking.setPlate(statusDO.getPlate());
				parkingOrderService.outParking(parking);
			}
		}

		ParkingPlace place = new ParkingPlace();
		place.setId(id);
		place.setStatus(status);
		place.setMemo(memo);
		return updateById(place);
	}

	@Override
	public boolean updateSpaceLocation(ParkingPlaceDTO dto) {
		return updateSpaceLocation(dto.getId(), dto.getLng(), dto.getLat(), dto.getLocation());
	}

	@Override
	public boolean updateSpaceLocation(Long id, Double lng, Double lat, String location) {
		ParkingPlace parkingPlace = new ParkingPlace();
		parkingPlace.setId(id);
		parkingPlace.setLng(lng);
		parkingPlace.setLat(lat);
		parkingPlace.setLocation(location);
		return this.updateById(parkingPlace);
	}


	@Override
	public void updateCurStatus(Long id, ParkingPlaceIdleState freeStatus, String plate) {
		if (ParkingPlaceIdleState.IDLE.equals(freeStatus)) {
			plate = StringPool.EMPTY;
		}
		log.info("更新车位[id={}]状态为：{} 车牌：{}", id, freeStatus.getName(), plate);
		if (id != null) {
			this.lambdaUpdate().set(ParkingPlace::getIsFree, freeStatus.getValue())
				.set(ParkingPlace::getPlate, plate)
				.eq(ParkingPlace::getId, id)
				.update();
			ParklotPlaceCache.del(id);
		}
	}

	@Override
	public boolean updateById(ParkingPlace parkingPlace) {
		boolean success = super.updateById(parkingPlace);
		ParklotPlaceCache.del(parkingPlace.getId());
		return success;
	}

	/**
	 * 判断是否存在PayQrcodePath
	 * 存在则返回EboMini车位支付二维码路径
	 * 不存在抛出异常信息
	 *
	 * @return String
	 */
	private String existPayQrcodePath() {
		String payQrcodePath = ParamCache.getValue("ebo.mini.park.space.pay.qrcode.path");
		LecentAssert.isTrue(Func.isNotBlank(payQrcodePath), "EboMini车位支付二维码路径未配置在参数管理中");
		return payQrcodePath;
	}

	/**
	 * 校验车位数量限制
	 *
	 * @param maxNum              目前最大数量
	 * @param tempParkingLimitNum 临停车位限制数量
	 */
	private void checkMaxNumLimit(int maxNum, int tempParkingLimitNum) {
		LecentAssert.isFalse(maxNum > 999, "路内车场总车位数量不能超过999");
		LecentAssert.isFalse(maxNum > tempParkingLimitNum,
			"路内车场总车位数量不能超过车场配置的临停车位数[{}]", tempParkingLimitNum);
	}

	/**
	 * 获取当前最大的支付码
	 *
	 * @param parklotId 车场id
	 * @return int
	 */
	private int getCurMaxPayCodeNum(Long parklotId) {
		return Optional.ofNullable(this.baseMapper.getMaxPayCodeByParklot(parklotId))
			.map(t -> Integer.valueOf(t.trim().substring(t.length() - 3)))
			.orElse(0);
	}

	/**
	 * 校验区域数量限制
	 *
	 * @param parkingPlace 车位信息
	 */
	private void validateRegionLimit(ParkingPlace parkingPlace, ParklotRegion region) {
		final Long regionId = parkingPlace.getRegionId();
		final Integer placeType = parkingPlace.getPlaceType();

		//查询区域、车位类型查询当前车位数量
		int count = count(new QueryWrapper<ParkingPlace>().lambda()
			.eq(ParkingPlace::getRegionId, regionId)
			.eq(ParkingPlace::getPlaceType, placeType));

		//新增校验车位数量，编辑不用校验
		if (ParkingPlaceTypeEnum.RIGHT.getValue().equals(placeType)) {
			Integer ownershipAmount = region.getIndependentOwnershipAmount();
			LecentAssert.isTrue(ownershipAmount > count, "此区域没有多余的独立产权车位可配置");
		}

		if (ParkingPlaceTypeEnum.MOTHER_CHILD_PLACE.getValue().equals(placeType)) {
			Integer letterLotAmount = region.getLetterLotAmount();
			LecentAssert.isTrue(letterLotAmount > count, "此区域没有多余的子母车位可配置");
		}

		if (ParkingPlaceTypeEnum.TEMP.getValue().equals(placeType)) {
			int tempLotAmount =
				region.getTempLotAmount() - region.getVipLotAmount() - region.getAccessibilityLotAmount();
			LecentAssert.isTrue(tempLotAmount > count, "此区域没有多余的临停车位可配置");
		}

		if (ParkingPlaceTypeEnum.RENT_VIP.getValue().equals(placeType)) {
			Integer vipLotAmount = region.getVipLotAmount();
			LecentAssert.isTrue(vipLotAmount > count, "此区域没有多余的vip车位可配置");
		}

		if (ParkingPlaceTypeEnum.RENT_NO_BLOCK.getValue().equals(placeType)) {
			Integer accessibilityLotAmount = region.getAccessibilityLotAmount();
			LecentAssert.isTrue(accessibilityLotAmount > count, "此区域没有多余的无障碍车位可配置");
		}
	}

	private IParkingPlaceService selfService() {
		return SpringUtil.getBean(IParkingPlaceService.class);
	}

	@Override
	public IPage<ParkingPlaceVO> informationRegistrationPage(IPage<ParkingPlaceVO> page, ParkingPlaceDTO parkingPlaceDTO) {
		if (!SecureUtil.isAdministrator()) {
			parkingPlaceDTO.setUserId(SecureUtil.getUserId());
		}
		List<ParkingPlaceVO> parkingPlaceList = baseMapper.selectParkingPlacePage(page, parkingPlaceDTO);
		if (!parkingPlaceList.isEmpty()) {
			// 车位有车查询车主无感开通信息
			List<String> plates = parkingPlaceList.stream().map(ParkingPlaceVO::getPlate).filter(k -> Func.isNotBlank(k))
				.collect(Collectors.toList());
			if (!plates.isEmpty()) {
				Map<String, String> unconsciousRelationMap = vehicleUnconsciousRelationService.lambdaQuery()
					.in(BVehicleUnconsciousRelation::getPlate, plates)
					.list().stream().collect(Collectors.toMap(BVehicleUnconsciousRelation::getPlate, BVehicleUnconsciousRelation::getUnconsciousType, (a, b) -> b));
				parkingPlaceList = parkingPlaceList.stream()
					.peek(k -> k.setUnconsciousType(unconsciousRelationMap.get(k.getPlate()))).collect(Collectors.toList());
			}
		}
		return page.setRecords(parkingPlaceList);
	}

	@Override
	public PlaceIteratorVO placeIterator(Long placeId) {
		List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIds();
		// 根据placeId查询车位附近两个车位信息
		PlaceIteratorLastNextIdVO lastNextIdVO = baseMapper.getPlaceIteratorLastNextPlaceId(placeId, parkLotIds);
		List<Long> placeIds = new ArrayList<>(3);
		placeIds.add(placeId);
		if (null != lastNextIdVO.getLastPlaceId()) {
			placeIds.add(lastNextIdVO.getLastPlaceId());
		}
		if (null != lastNextIdVO.getNextPlaceId()) {
			placeIds.add(lastNextIdVO.getNextPlaceId());
		}
		List<ParkingPlace> parkingPlaces = listByIds(placeIds);
		LecentAssert.notEmpty(parkingPlaces, "车位信息不存在");
		Map<Long, ParkingPlace> placeMap = StreamUtils.toIdentityMap(parkingPlaces, ParkingPlace::getId);

		ParkingPlace currentPlace = placeMap.get(placeId);
		LecentAssert.notNull(currentPlace, "车位信息不存在");
		Parklot parkLot = ParkLotCaches.getParkLot(currentPlace.getParklotId());
		LecentAssert.notNull(parkLot, "车场信息不存在");

		return PlaceIteratorVO.builder()
			.currParklotId(parkLot.getId())
			.currParklotName(parkLot.getName())
			.lastPlace(buildPlaceIteratorItemVO(placeMap.get(lastNextIdVO.getLastPlaceId())))
			.currPlace(buildPlaceIteratorItemVO(currentPlace))
			.nextPlace(buildPlaceIteratorItemVO(placeMap.get(lastNextIdVO.getNextPlaceId())))
			.build();
	}

	/**
	 * 构建IteratorItemVO
	 *
	 * @param place 车位信息
	 * @return PlaceIteratorItemVO
	 */
	private PlaceIteratorItemVO buildPlaceIteratorItemVO(ParkingPlace place) {
		if (null == place) {
			return null;
		}
		return PlaceIteratorItemVO.builder().placeId(place.getId()).placeCode(place.getPlaceCode()).build();
	}
}
