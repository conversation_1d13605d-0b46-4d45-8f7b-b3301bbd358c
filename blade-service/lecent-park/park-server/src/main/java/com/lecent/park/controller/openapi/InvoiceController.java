package com.lecent.park.controller.openapi;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.CustomerInvoiceDTO;
import com.lecent.park.dto.invoice.InvoiceStatementDTO;
import com.lecent.park.en.invoice.InvoiceType;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotInvoiceRecord;
import com.lecent.park.service.ICardOrderService;
import com.lecent.park.service.IInvoiceService;
import com.lecent.park.service.IParklotInvoiceRecordService;
import com.lecent.park.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Description: 开发票接口
 * @author: cy
 * @date: 2021年10月25日 17:24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/park/invoice")
@Api(value = "车场发票开票", tags = "车场发票开票")
public class InvoiceController {

	private final ICardOrderService cardOrderService;
	private final IParklotInvoiceRecordService parklotInvoiceRecordService;
	private final IInvoiceService invoiceService;

	/**
	 * 开发票
	 */
	@PostMapping("/customer/invoice")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "开具发票（单个或者批量）", notes = "")
	public R invoice(@Valid @RequestBody CustomerInvoiceDTO customerInvoiceDTO) {
		return R.data(parklotInvoiceRecordService.invoice(customerInvoiceDTO));
	}

	@PostMapping("/customer/anew/invoice")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "重开具发票（单个或者批量）", notes = "")
	public R anewInvoice(@RequestBody CustomerInvoiceDTO customerInvoiceDTO) {
		return R.data(parklotInvoiceRecordService.anewInvoice(customerInvoiceDTO));
	}
	@PostMapping("/customer/fail/anew/invoice")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "失败重开", notes = "")
	public R  failAnewInvoice(@RequestBody CustomerInvoiceDTO customerInvoiceDTO){
		return R.data(parklotInvoiceRecordService.failAnewInvoice(customerInvoiceDTO));
	}

	/**
	 * 开具发票-车场筛选
	 */
	@GetMapping("/yard/screen")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车场筛选", notes = "传入phone")
	public R<List<Parklot>> yardScreen(String phone) {
		return R.data(invoiceService.yardScreen(phone));
	}

	/**
	 * 开具发票-订单类型
	 */
	@GetMapping("/type")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "订单类型")
	public R<List<Map>> enumToDict() {
		return R.data(InvoiceType.enumToDict());
	}

	/**
	 * 开具发票-记录
	 */
	@GetMapping("/record/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "开具发票停车记录", notes = "传入cardOrder")
	public R<List<InvoiceCardOrderVO>> record(InvoiceCardOrderVO invoiceCardOrderVO) {
		return R.data(cardOrderService.recordList(invoiceCardOrderVO));
	}

	/**
	 * 开票记录
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入parklotInvoiceRecord")
	public R<List<ParklotInvoiceRecordVO>> list(ParklotInvoiceRecordVO parkLotInvoiceRecord) {
		List<ParklotInvoiceRecordVO> list = parklotInvoiceRecordService.selectParklotInvoiceRecordPage(parkLotInvoiceRecord);
		return R.data(list);
	}

	/**
	 * 开票记录-发票详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "发票详情", notes = "传入parklotInvoiceRecord")
	public R<InvoiceRecordDetailVO> invoiceDetail(ParklotInvoiceRecord parklotInvoiceRecord) {
		InvoiceRecordDetailVO detail = parklotInvoiceRecordService.invoiceDetail(parklotInvoiceRecord);
		return R.data(detail);
	}

	/**
	 * 开票记录-发票订单详情
	 */
	@GetMapping("/record/detail")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "发票订单详情", notes = "传入recordId")
	public R<List<ParklotInvoiceRecordDetailVO>> recordDetail(ParklotInvoiceRecordDetailVO vo) {
		return R.data(parklotInvoiceRecordService.recordDetail(vo));
	}

	/**
	 * 开票报表统计
	 */
	@GetMapping("/record/statement")
	@ApiOperation(value = "开票报表统计", notes = "开票报表统计")
	public R<IPage<InvoiceStatementVO>> statement(Query query, InvoiceStatementDTO invoiceStatementDTO) {
		IPage<InvoiceStatementVO> page = Condition.getPage(query);
		List<InvoiceStatementVO> statement = parklotInvoiceRecordService.statement(page, invoiceStatementDTO);
		return R.data(page.setRecords(statement));
	}


}
