package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.BlackListDTO;
import com.lecent.park.dto.BlackListRemoveLogDTO;
import com.lecent.park.entity.BlackList;
import com.lecent.park.vo.BlackListVO;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;

/**
 * 车辆黑名单 服务类
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface IBlackListService extends BaseService<BlackList> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param blackListDto
	 * @return
	 */
	IPage<BlackListVO> selectBlackListPage(IPage<BlackListVO> page, BlackListDTO blackListDto);


	/**
	 * 是否是黑名单
	 *
	 * @param parkLotId 车场id
	 * @param plate     车牌
	 * @return true/false
	 */
	boolean isBlack(Long parkLotId, String plate);

	/**
	 * 新增黑名单
	 *
	 * @param blackListDto
	 * @return
	 */
	boolean customSubmit(BlackListDTO blackListDto);

	/**
	 * 自定义详情
	 *
	 * @param id
	 * @return
	 */
	BlackListVO detail(String id);

	/**
	 * 自定义删除
	 *
	 * @return
	 */
	boolean customRemove(BlackListRemoveLogDTO blackListRemoveLogDto);


	/**
	 * 恢复黑名单
	 */
	boolean rollback(BlackListRemoveLogDTO blackListRemoveLogDto);

	/**
	 * 黑名单导出
	 *
	 * @param blackListDto
	 * @param response
	 */
	void blackListExport(BlackListDTO blackListDto, HttpServletResponse response);

	/**
	 * 根据车牌查询黑名单记录
	 *
	 * @param plate
	 * @return
	 */
	BlackList getByPlate(String plate, Long parkLotId);

	/**
	 * 获取黑名单
	 *
	 * @param id id
	 * @return bean
	 */
	BlackList getBlackListById(Long id);
}
