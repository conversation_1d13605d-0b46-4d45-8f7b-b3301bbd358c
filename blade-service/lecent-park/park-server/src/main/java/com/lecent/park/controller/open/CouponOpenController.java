package com.lecent.park.controller.open;

import com.lecent.park.dto.third.ThirdCouponDTO;
import com.lecent.park.service.IThirdCouponService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

/**
 * 第三方 授权控制层
 * 正常情况下，所有第三方的请求都先进入公司的开放平台，在开放平台进行授权，认证，加解密之类的操作，请求认证通过后，再由开放平台调用不同的业务系统。
 * 现在由于开放平台还没有开发完成，所以开放平台的的认证，解密等操作暂时放在这里，等开放平台稳定后，再将此功能迁移至开放平台。
 *
 * <AUTHOR>
 * @date 2021年11月02日
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/open-api/coupon")
@Api(value = "第三方优惠卷接口")
@Slf4j
public class CouponOpenController {
	/**
	 * 第三方（星力集团）优惠卷服务
	 */
	private final IThirdCouponService thirdCouponService;

	/**
	 * 给用户发送优惠卷
	 */
	@PostMapping("/v1/send")
	public R sendCoupon(@RequestBody ThirdCouponDTO thirdCouponDTO) {
		return R.status(thirdCouponService.thirdSendCoupon(thirdCouponDTO));
	}

	/**
	 * 获取已使用但是未稽核的优惠劵列表
	 */
	@GetMapping("/v1/list/un-verification")
	public R unVerificationCoupons(String parklotNo) {
		return R.data(thirdCouponService.unVerificationCoupons(parklotNo));
	}

	/**
	 * 获取优惠劵核销信息
	 */
	@GetMapping("/v1/detail")
	public R verificationDetail(String couponNo) {
		log.info("verificationDetail-param---->[{}]", couponNo);
		return R.data(thirdCouponService.verificationDetail(couponNo));
	}
}
