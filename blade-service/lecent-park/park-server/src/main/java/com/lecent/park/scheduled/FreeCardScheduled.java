package com.lecent.park.scheduled;


import com.lecent.park.service.IFreeCardAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.lock.RedisLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 免费卡定时任务
 * <AUTHOR>
 */
@Component
@Slf4j
public class FreeCardScheduled {

	@Resource
	private IFreeCardAuthService authService;

	/**
	 * 每天凌晨扫描即将过期的免费卡
	 */
	@Scheduled(cron = "0 0 0 * * ? ")
	@RedisLock(value = "lecent:park::timedTask:lock:freeCardExpire")
	public void queryPaySuccessOrderScheduled() {
        try {
            authService.freeCardExpire();
        } catch (ServiceException e) {
			log.error("过期免费车定时任务异常", e);
		}
    }

}
