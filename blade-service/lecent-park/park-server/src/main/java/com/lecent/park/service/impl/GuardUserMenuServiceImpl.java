package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.GuardBtn;
import com.lecent.park.mapper.GuardUserMenuMapper;
import com.lecent.park.service.IGuardBtnService;
import com.lecent.park.service.IGuardUserMenuService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.dto.GuardUserMenuDTO;
import org.springblade.system.entity.GuardUserMenu;
import org.springblade.system.vo.GuardUserMenuVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 岗亭用户菜单关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Service
public class GuardUserMenuServiceImpl extends BaseServiceImpl<GuardUserMenuMapper, GuardUserMenu> implements IGuardUserMenuService {

	@Lazy
	@Resource
	private IGuardBtnService guardBtnService;

	@Override
	public IPage<GuardUserMenuVO> selectGuardUserMenuPage(IPage<GuardUserMenuVO> page, GuardUserMenuVO guardUserMenu) {
		return page.setRecords(baseMapper.selectGuardUserMenuPage(page, guardUserMenu));
	}

	@Override
	public boolean setMobileGuardUserMenu(GuardUserMenuDTO guardUserMenuDto) {
		Long userId = guardUserMenuDto.getUserId();
		List<String> menuIds = guardUserMenuDto.getMenuIds();
		remove(new LambdaQueryWrapper<GuardUserMenu>().eq(GuardUserMenu::getUserId, userId));
		List<GuardUserMenu> menuList = menuIds.stream().map(menuId -> {
			GuardUserMenu guardUserMenu = new GuardUserMenu();
			guardUserMenu.setUserId(userId);
			guardUserMenu.setMenuId(Long.valueOf(menuId));
			return guardUserMenu;
		}).collect(Collectors.toList());
		saveBatch(menuList);
		return true;
	}

	@Override
	public Boolean addGuardBtn(Long userId) {

		List<GuardUserMenu> list = this.list(Wrappers.<GuardUserMenu>lambdaQuery().eq(GuardUserMenu::getUserId, userId));
		if (Func.isNotEmpty(list)) {
			return false;
		}

		List<GuardBtn> btnList = guardBtnService.list();
		if (Func.isEmpty(btnList)) {
			return true;
		}

		List<GuardUserMenu> userBtnList = new ArrayList<>();
		for (GuardBtn btn : btnList) {
			GuardUserMenu userBtn = new GuardUserMenu();
			userBtn.setUserId(userId);
			userBtn.setMenuId(btn.getId());
			userBtnList.add(userBtn);
		}

		if (Func.isNotEmpty(userBtnList)) {
			return saveBatch(userBtnList);
		}
		return false;
	}



	@Override
	public String getExtendParam(Long userId, Long menuId) {
		List<GuardUserMenu> list = this.list(Wrappers.<GuardUserMenu>lambdaQuery().eq(GuardUserMenu::getUserId, userId));
		if (Func.isNotEmpty(list)){
			List<GuardUserMenu> collect = list.stream().filter(g -> g.getMenuId().equals(menuId)).collect(Collectors.toList());
			if (Func.isNotEmpty(collect)){
				return collect.get(0).getExtendParam();
			}
		}
		return null;
	}
}
