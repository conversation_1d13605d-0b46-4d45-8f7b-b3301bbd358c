package com.lecent.park.controller.wechat;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.UserAddr;
import com.lecent.park.service.IUserAddrService;
import com.lecent.park.vo.UserAddrVO;
import com.lecent.park.wrapper.UserAddrWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *  用戶地址 控制器
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wechat/user-addr")
@Api(value = "用戶地址", tags = "用戶地址")
public class WechatUserAddrController extends BladeController {

	private IUserAddrService userAddrService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入userAddr")
	public R<UserAddrVO> detail(UserAddr userAddr) {
		UserAddr detail = userAddrService.getOne(Condition.getQueryWrapper(userAddr));
		return R.data(UserAddrWrapper.build().entityVO(detail));
	}

	/**
	 * 查询用户的所有地址
	 */
	@GetMapping("/list-user-addr")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询用户的所有地址", notes = "查询用户的所有地址")
	public R<List<UserAddrVO>> listUserAddr() {
		return R.data(userAddrService.listUserAddr());
	}




	/**
	 * 新增或修改 发票抬头表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入userAddr")
	public R submit(@Valid @RequestBody UserAddr userAddr) {
		return R.status(userAddrService.customSubmit(userAddr));
	}


	/**
	 * 删除 发票抬头表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userAddrService.deleteLogic(Func.toLongList(ids)));
	}


}
