package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.UserParklotSettingDTO;
import com.lecent.park.entity.ChannelTree;
import com.lecent.park.entity.UserChannel;
import com.lecent.park.service.IUserChannelService;
import com.lecent.park.vo.UserChannelVO;
import com.lecent.park.wrapper.UserChannelWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 公司员工车场资源授权表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@RestController
@AllArgsConstructor
@Validated
@RequestMapping("/userchannel")
@Api(value = "公司员工车场资源授权表", tags = "公司员工车场资源授权表接口")
public class UserChannelController extends BladeController {

	private IUserChannelService userChannelService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入userChannel")
	public R<UserChannelVO> detail(UserChannel userChannel) {
		UserChannel detail = userChannelService.getOne(Condition.getQueryWrapper(userChannel));
		return R.data(UserChannelWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 公司员工车场资源授权表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入userChannel")
	public R<IPage<UserChannelVO>> list(UserChannel userChannel, Query query) {
		IPage<UserChannel> pages = userChannelService.page(Condition.getPage(query), Condition.getQueryWrapper(userChannel));
		return R.data(UserChannelWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 公司员工车场资源授权表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入userChannel")
	public R<IPage<UserChannelVO>> page(UserChannelVO userChannel, Query query) {
		IPage<UserChannelVO> pages = userChannelService.selectUserChannelPage(Condition.getPage(query), userChannel);
		return R.data(pages);
	}

	/**
	 * 新增 公司员工车场资源授权表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入userChannel")
	public R save(@Valid @RequestBody UserParklotSettingDTO userParklotSettingDTO) {
		return R.status(userChannelService.saveUserChannel(userParklotSettingDTO));
	}

	/**
	 * 修改 公司员工车场资源授权表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入userChannel")
	public R update(@Valid @RequestBody UserChannel userChannel) {
		return R.status(userChannelService.updateById(userChannel));
	}

	/**
	 * 新增或修改 公司员工车场资源授权表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入userChannel")
	public R submit(@Valid @RequestBody UserChannel userChannel) {
		return R.status(userChannelService.saveOrUpdate(userChannel));
	}


	/**
	 * 删除 公司员工车场资源授权表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userChannelService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 获取通道信息  包括车场拥有的通道和用户已绑定的通道信息
	 */
	@GetMapping("/getChannelTrees")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "获取通道树形列表", notes = "获取通道树形列表")
	public R<List<ChannelTree>> getChannelTrees(Long userId, String labels) {
		return R.data(userChannelService.getChannelTree(userId, labels));
	}


	/**
	 * 获取车场用户
	 */
	@GetMapping("/getParkLotUser")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "获取车场用户", notes = "获取车场用户")
	public R getParkLotUser() {
		return R.data(userChannelService.getParkLotUser());
	}


}
