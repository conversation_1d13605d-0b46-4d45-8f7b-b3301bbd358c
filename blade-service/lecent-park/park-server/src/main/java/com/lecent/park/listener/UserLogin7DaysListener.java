package com.lecent.park.listener;

import cn.hutool.core.date.DateUnit;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.core.mq.rabbitmq.MessageConstant;
import com.lecent.park.core.mq.rabbitmq.utils.ObjUtil;
import com.lecent.park.discount.coupon.service.ICouponActivityService;
import com.lecent.park.en.coupon.SendConditionEnum;
import com.lecent.park.en.coupon.UserActionEnum;
import com.lecent.park.entity.CouponActivity;
import com.lecent.park.discount.coupon.service.IUserCouponService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.CUser;
import org.springblade.system.user.entity.UserLoginLog;
import org.springblade.system.user.feign.ICUserLoginLogClient;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_EXCHANGE_NAME;


/**
 * 建行公众号用户登录监听
 *
 * <AUTHOR>
 */

@Component
@Slf4j
@Order
@AllArgsConstructor
public class UserLogin7DaysListener {

	private final ICouponActivityService couponActivityService;
	private final ICUserLoginLogClient userLoginLogClient;
	private final IUserCouponService userCouponService;

	/**
	 * 建行公众号用户登录满7天监听(自动发放优惠劵)
	 *
	 * @param message 消息
	 */
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = MessageConstant.LECENT_PARK_USER_LOGIN_QUEUE_DAYS, durable = "true")
		, exchange = @Exchange(value = LECENT_PARK_EXCHANGE_NAME
		, type = ExchangeTypes.TOPIC), key = MessageConstant.LECENT_PARK_USER_LOGIN_7_DAYS_KEY)})
	@RedisLock(value = ParkCacheNames.PARK_DAY_COUPON)
	public void userLoginMessageHandler(Message message) {

		CUser user = ObjUtil.toObjectMessageCharacter(message, CUser.class, "UTF-8");
		if (null == user) {
			return;
		}
		Long userId = user.getId();

		log.info("监听用户登录发劵，用户信息：[{}]", JsonUtil.toJson(user));

		R<UserLoginLog> logResult = userLoginLogClient.getLastLog(userId);
		if (!logResult.isSuccess() || Func.isEmpty(logResult.getData().getLoginTime())) {
			log.info("登录用户没有历史登录日志，不发劵");
			return;
		}

		//是否7天未登录
		if (DateUtil.now().getTime() - logResult.getData().getLoginTime().getTime() < DateUnit.WEEK.getMillis()) {
			log.info("登录用户不满足7天未登录条件，不发劵");
			return;
		}

		//获取正在进行的7天未登录送优惠劵的活动
		List<CouponActivity> onLineActivityList = couponActivityService.getOnLineActivityBySendCondition(SendConditionEnum.USER_ACTION.getValue(), UserActionEnum.NEVER_LOGIN_IN_SEVEN_DAY.getValue());

		log.info("正在进行的7天未登录的活动列表：[{}]", JsonUtil.toJson(onLineActivityList));

		//给用户发劵
		for (CouponActivity a : onLineActivityList) {

			//如果用户再活动中已经发放过，不再发放
			Integer count = userCouponService.getByUserIdActivityId(userId, a.getId());
			if (count > 0) {
				continue;
			}
			couponActivityService.sendCouponToUser(a, user, "用户7天未登录，登录后自动发放");
		}
	}
}

