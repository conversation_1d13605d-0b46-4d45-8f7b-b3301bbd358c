package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.CardCategoryDiscount;
import com.lecent.park.vo.CardCategoryDiscountVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 月卡套餐折扣信息 服务类
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
public interface ICardCategoryDiscountService extends BaseService<CardCategoryDiscount> {

	/**
	 * 自定义分页
	 *
	 * @param page                 page
	 * @param cardCategoryDiscount cardCategoryDiscount
	 * @return 分页列表
	 */
	IPage<CardCategoryDiscountVO> selectCardCategoryDiscountPage(IPage<CardCategoryDiscountVO> page, CardCategoryDiscountVO cardCategoryDiscount);

	/**
	 * 根据缴费月数获取套餐对应的折扣
	 *
	 * @param payMonthNum 续费月数
	 * @param categoryId  月卡套餐ID
	 * @return 折扣套餐
	 */
	CardCategoryDiscount discountByDuration(Integer payMonthNum, Long categoryId);

}
