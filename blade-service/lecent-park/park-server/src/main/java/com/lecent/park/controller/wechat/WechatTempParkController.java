package com.lecent.park.controller.wechat;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.ClientTodo;
import com.lecent.park.service.ICCBMiniAppTempParkService;
import com.lecent.park.vo.ChannelTodoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序临停缴费控制器
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("wechat/temp-park")
@Api(value = "小程序临停缴费控制器", tags = "小程序临停缴费控制器")
public class WechatTempParkController extends BladeController {


	private ICCBMiniAppTempParkService iccbMiniAppTempParkService;

	/**
	 * 车牌的临停详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "车牌的临停详情", notes = "传入车牌")
	public R<ChannelTodoVO> detail(String plate) {
		return R.data(iccbMiniAppTempParkService.detail(plate));
	}


	/**
	 * TODO 接口未使用
	 * 根据代办id获取临停缴费详情信息 （）
	 */
	@GetMapping("/detailByTodoId")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据代办id获取临停缴费详情信息", notes = "根据代办id获取临停缴费详情信息")
	public R<ChannelTodoVO> detailByTodoId(String todoId) {
		return R.data(iccbMiniAppTempParkService.detailByTodoId(todoId));
	}


	/**
	 * 临停缴费
	 */
	@GetMapping("/pay")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "临停缴费", notes = "传入代码信息")
	public R pay(ClientTodo clientTodo) {
		return iccbMiniAppTempParkService.pay(clientTodo);
	}


	/**
	 * 查询临停缴费是否成功
	 *
	 * @param outTradeNo
	 * @return
	 */
	@GetMapping("/order-status")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "order-status", notes = "查询临停缴费是否成功")
	public synchronized R isSuccessOrFail(String outTradeNo) {
		return iccbMiniAppTempParkService.payCallback(outTradeNo);
	}

}
