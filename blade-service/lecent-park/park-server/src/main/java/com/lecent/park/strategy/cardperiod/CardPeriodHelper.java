package com.lecent.park.strategy.cardperiod;

import cn.hutool.core.util.NumberUtil;
import com.lecent.park.common.utils.DateUtils;
import org.springblade.core.tool.utils.DateUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR>
 * 月卡时段计算辅助类
 */
public class CardPeriodHelper {

	/**
	 * 12个月
	 */
	private static final BigDecimal months = new BigDecimal(12);

	/**
	 * 365 天
	 */
	private static final BigDecimal dayOfYear = new BigDecimal(365);

	/**
	 * 计算续费天数
	 *
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @return 天数
	 */
	public static int getRenewalDays(Date startDate, Date endDate) {
		return (int) cn.hutool.core.date.DateUtil.betweenDay(startDate, endDate, true);
	}

	/**
	 * 计算续费月数
	 *
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @return 月数
	 */
	public static int getRenewalMonths(Date startDate, Date endDate) {
		return (int) cn.hutool.core.date.DateUtil.betweenMonth(startDate, endDate, true) + 1;
	}


	/**
	 * 月卡续费是否跳缴
	 *
	 * @param cardEndDate 月卡结束时间
	 * @return 是否跳缴
	 */
	public static boolean isSkipMonth(Date cardEndDate) {

		Date renewDate = DateUtil.now();

		if (renewDate.getTime() < cardEndDate.getTime()) {
			return false;
		}

		return cn.hutool.core.date.DateUtil.betweenMonth(cardEndDate, renewDate, true) > 1;
	}

	/**
	 * 月卡续费开始时间计算
	 *
	 * @param cardEndDate 月卡结束时间
	 * @return 续费开始时间
	 */
	public static Date calculateStartDate(Date cardEndDate) {

		if (isSkipMonth(cardEndDate)) {
			return DateUtils.getStartTimeOfDay(DateUtil.now());
		}

		return DateUtils.getStartTimeOfNextDay(cardEndDate);
	}

	/**
	 * 计算日单价
	 * @param unitPrice 月单价
	 * @return 日单价
	 */
	public static BigDecimal getPriceOfDay(BigDecimal unitPrice) {
		return unitPrice.multiply(months).divide(dayOfYear, BigDecimal.ROUND_HALF_UP);
	}

	/**
	 * 计算续费总金额
	 * @param unitPrice 单价
	 * @param num       数量
	 * @return 总金额
	 */
	public static BigDecimal calculateTotalAmount(BigDecimal unitPrice, long num) {
		return NumberUtil.mul(unitPrice,num).setScale(0, BigDecimal.ROUND_HALF_UP);
	}

	/**
	 * @param startDate 	续费开始时间
	 * @param monthNum  	续费月份
	 * @param isEarlyEnd	是否提前结束
	 * @return 续费结束时间
	 */
	public static Date calculateEndDate(Date startDate, Integer monthNum,boolean isEarlyEnd) {

		Date renewEndDate = DateUtil.plusMonths(startDate, monthNum);

		if(isEarlyEnd) {
			//8.8-9.7提前一天结束
			LocalDateTime startDateTime = DateUtil.fromDate(startDate);
			LocalDateTime endDateTime = DateUtil.fromDate(renewEndDate);
			if (startDateTime.getDayOfMonth() > endDateTime.getDayOfMonth()) {
				endDateTime = endDateTime.plusDays(1);
			}
			endDateTime = endDateTime.minusSeconds(1);
			return DateUtil.toDate(endDateTime);
		}

		return DateUtils.getEndTimeOfDay(renewEndDate);
	}
}
