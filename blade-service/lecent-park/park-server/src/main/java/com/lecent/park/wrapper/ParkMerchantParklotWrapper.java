package com.lecent.park.wrapper;

import com.lecent.park.entity.ParkMerchantParklot;
import com.lecent.park.vo.ParkMerchantParklotVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 酒店商户与车场关联表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
public class ParkMerchantParklotWrapper extends BaseEntityWrapper<ParkMerchantParklot, ParkMerchantParklotVO>  {

	public static ParkMerchantParklotWrapper build() {
		return new ParkMerchantParklotWrapper();
 	}

	@Override
	public ParkMerchantParklotVO entityVO(ParkMerchantParklot parkMerchantParklot) {
		ParkMerchantParklotVO parkMerchantParklotVO = BeanUtil.copy(parkMerchantParklot, ParkMerchantParklotVO.class);

		//User createUser = UserCache.getUser(parkMerchantParklot.getCreateUser());
		//User updateUser = UserCache.getUser(parkMerchantParklot.getUpdateUser());
		//parkMerchantParklotVO.setCreateUserName(createUser.getName());
		//parkMerchantParklotVO.setUpdateUserName(updateUser.getName());

		return parkMerchantParklotVO;
	}

}
