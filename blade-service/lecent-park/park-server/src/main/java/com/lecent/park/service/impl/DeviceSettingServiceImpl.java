package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.DeviceSetting;
import com.lecent.park.mapper.DeviceSettingMapper;
import com.lecent.park.service.IDeviceSettingService;
import com.lecent.park.vo.DeviceSettingVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 设备语音播报设置表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-28
 */
@Service
public class DeviceSettingServiceImpl extends BaseServiceImpl<DeviceSettingMapper, DeviceSetting> implements IDeviceSettingService {

	@Override
	public IPage<DeviceSettingVO> selectDeviceSettingPage(IPage<DeviceSettingVO> page, DeviceSettingVO deviceSetting) {
		return page.setRecords(baseMapper.selectDeviceSettingPage(page, deviceSetting));
	}

}
