package com.lecent.park.strategy.cardperiod;

import com.lecent.park.dto.CardDurationDTO;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.vo.CardDurationVO;

/**
 * 月卡时段计算
 */
public interface CardPeriodPeriodStrategy {

	/**
	 * 计算月卡有效期的共同算法
	 *
	 * @param cardDurationDTO cardDurationDTO
	 * @return CardDurationVO
	 */
	CardDurationVO getCardPeriod(CardDurationDTO cardDurationDTO, Card card, CardCategory cardCategory);
}
