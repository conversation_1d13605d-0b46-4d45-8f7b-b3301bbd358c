package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.SchedulingLogs;
import com.lecent.park.vo.SchedulingLogsVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 排班日志表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
public interface ISchedulingLogsService extends BaseService<SchedulingLogs> {

	/**
	 * 自定义分页
	 *
	 * @param page           页
	 * @param schedulingLogs 排班日志
	 * @return {@link IPage}<{@link SchedulingLogsVO}>
	 */
	IPage<SchedulingLogsVO> selectSchedulingLogsPage(IPage<SchedulingLogsVO> page, SchedulingLogsVO schedulingLogs);

	/**
	 * 按排班表id获取排班日志
	 *
	 * @param scheduleId 排班表id
	 * @return {@link List}<{@link SchedulingLogs}>
	 */
	List<SchedulingLogsVO> getByScheduleId(Long scheduleId);

	/**
	 * 通过排班表ids逻辑删除排班日志
	 *
	 * @param scheduleIds 排班表ids
	 * @return {@link Boolean}
	 */
	Boolean deleteLogsByScheduleIds(List<Long> scheduleIds);

	/**
	 * 获取值班人员
	 *
	 * @param schedulingLogs 排班日志
	 * @return {@link List}<{@link SchedulingLogsVO}>
	 */
	List<SchedulingLogsVO> getWatchkeeper(SchedulingLogsVO schedulingLogs);

	/**
	 * 通过排班表ids批量获取排班日志
	 *
	 * @param scheduleIds 排班表ids
	 * @return {@link List}<{@link SchedulingLogsVO}>
	 */
	List<SchedulingLogsVO> getByScheduleIds(List<Long> scheduleIds);
}
