package com.lecent.park.service;

import com.lecent.park.dto.third.ThirdCouponDTO;
import com.lecent.park.entity.Coupon;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.entity.UserCoupon;
import com.lecent.park.vo.third.ThirdCouponVO;
import com.lecent.park.vo.third.ThirdCouponVerificationVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 第三方（星力集团）优惠劵服务
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
public interface IThirdCouponService extends BaseService<UserCoupon> {


	/**
	 * 第三方发送优惠劵
	 *
	 * @param thirdCouponDTO 优惠劵参数
	 * @return boolean
	 */
	boolean thirdSendCoupon(ThirdCouponDTO thirdCouponDTO);

	/**
	 * 第三方优惠劵保存
	 *
	 * @param coupon         优惠劵
	 * @param thirdCouponDTO 第三方其他参数
	 * @param tenantId       租户id
	 * @return boolean
	 */
	boolean thirdSave(Coupon coupon, ThirdCouponDTO thirdCouponDTO, String tenantId);

	/**
	 * 优惠劵核销
	 *
	 * @param tempParkingOrder 临停订单
	 */
	void verificationCoupon(TempParkingOrder tempParkingOrder);

	/**
	 * 获取已使用但是未核销的优惠劵
	 *
	 * @param parklotNo 车场编号
	 * @return 未核销的优惠劵
	 */
	List<ThirdCouponVerificationVO> unVerificationCoupons(String parklotNo);

	/**
	 * 获取优惠劵核销结果
	 *
	 * @param couponNo 优惠劵编码
	 * @return 优惠劵信息
	 */
	ThirdCouponVO verificationDetail(String couponNo);

}

