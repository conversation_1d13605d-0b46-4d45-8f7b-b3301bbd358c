package com.lecent.park.wrapper;

import com.lecent.park.entity.BizOptLog;
import com.lecent.park.vo.BizOptLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 业务操作日志表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
public class BizOptLogWrapper extends BaseEntityWrapper<BizOptLog, BizOptLogVO>  {

	public static BizOptLogWrapper build() {
		return new BizOptLogWrapper();
 	}

	@Override
	public BizOptLogVO entityVO(BizOptLog bizOptLog) {
		BizOptLogVO bizOptLogVO = BeanUtil.copy(bizOptLog, BizOptLogVO.class);

		//User createUser = UserCache.getUser(bizOptLog.getCreateUser());
		//User updateUser = UserCache.getUser(bizOptLog.getUpdateUser());
		//bizOptLogVO.setCreateUserName(createUser.getName());
		//bizOptLogVO.setUpdateUserName(updateUser.getName());

		return bizOptLogVO;
	}

}
