package com.lecent.park.controller.open.community;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.BUserPlate;
import com.lecent.park.service.IBUserPlateService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;

/**
 * 提供给社区的绑定车牌功能（乌当紧急验收，开放接口不走openapi校验）
 *
 * <AUTHOR>
 * @date 2021年11月02日
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/open-community/plate")
@Api(value = "社区车牌绑定")
@Validated
@Slf4j
public class OpenPlateController {

	private final IBUserPlateService userPlateService;

	/**
	 * 用户根据openId查询车牌
	 */
	@GetMapping("/list")
	public R getListByOpenId(@NotEmpty(message = "手机号不能为空") String phone) {
		return R.data(userPlateService.getByPhone(Long.valueOf(phone)));
	}

	/**
	 * 绑定车牌
	 */
	@PostMapping("/submit")
	public R getListByOpenId(String openId,
							 @NotEmpty(message = "plate不能为空") String plate,
							 @NotEmpty(message = "手机号不能为空") String phone) {
		return R.status(userPlateService.bind(openId, plate, phone));
	}

	/**
	 * 删除车牌
	 */
	@PostMapping("/remove")
	public R remove(@NotEmpty(message = "openId不能为空") String openId,
					@NotEmpty(message = "plate不能为空") String plate) {
		return R.data(userPlateService.remove(Wrappers.<BUserPlate>lambdaQuery()
			.eq(BUserPlate::getOpenId, openId).eq(BUserPlate::getPlate, plate)));
	}
}
