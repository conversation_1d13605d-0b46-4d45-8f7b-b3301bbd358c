package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParkMerchantRuleOrder;
import com.lecent.park.vo.MerchantPayVo;
import com.lecent.park.vo.ParkMerchantOrderStatisticsVO;
import com.lecent.park.vo.ParkMerchantParklotVO;
import com.lecent.park.vo.ParkMerchantRuleOrderVO;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-04-19
 */
public interface IParkMerchantRuleOrderService extends BaseService<ParkMerchantRuleOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parkMerchantRuleOrder
	 * @return
	 */
	IPage<ParkMerchantRuleOrderVO> selectParkMerchantRuleOrderPage(IPage<ParkMerchantRuleOrderVO> page, ParkMerchantRuleOrderVO parkMerchantRuleOrder);

	boolean saveRuleOrder();

	/**
	 * 创建订单
	 * @param mp
	 * @param payVo
	 * @return
	 */
	ParkMerchantRuleOrder createOrder(ParkMerchantParklotVO mp, MerchantPayVo payVo);

	/**
	 * 根据交易流水号查询订单信息
	 * @param tradeNo
	 * @return
	 */
	ParkMerchantRuleOrder getByTradeNo(String tradeNo);

	/**
	 * 订单统计
	 * @param parkMerchantRuleOrder
	 * @return
	 */
	ParkMerchantOrderStatisticsVO orderStatistics(ParkMerchantRuleOrderVO parkMerchantRuleOrder);

	/**
	 * 查询支付成功未更新的订单
	 * @return
	 */
	List<String> getTradeNoList();

	/**
	 * @param parkMerchantRuleOrderVO
	 * @param response
	 */
    void exportDetailData(ParkMerchantRuleOrderVO parkMerchantRuleOrderVO, HttpServletResponse response);

	/**
	 * 生成支付成功订单
	 * @param mp 商户车场
	 * @param payVo 支付信息
	 */
	void createSuccessOrder(ParkMerchantParklotVO mp, MerchantPayVo payVo);

	/**
	 * 商户充值
	 * @param order
	 * @return
	 */
	Boolean merchantPay(ParkMerchantRuleOrder order);
}
