package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.TempParkingSegmentCfg;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.TempParkingSegmentCfgMapper;
import com.lecent.park.service.ITempParkingSegmentCfgService;
import com.lecent.park.vo.TempParkingSegmentCfgVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 临停分段配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-30
 */
@Service
public class TempParkingSegmentCfgServiceImpl extends BaseServiceImpl<TempParkingSegmentCfgMapper, TempParkingSegmentCfg> implements ITempParkingSegmentCfgService {

	@Override
	public IPage<TempParkingSegmentCfgVO> selectTempParkingSegmentCfgPage(IPage<TempParkingSegmentCfgVO> page, TempParkingSegmentCfgVO tempParkingSegmentCfg) {
		return page.setRecords(baseMapper.selectTempParkingSegmentCfgPage(page, tempParkingSegmentCfg));
	}

}
