package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.bizoptlog.AroundOpt;
import com.lecent.park.bizoptlog.AroundOptHeader;
import com.lecent.park.bizoptlog.OptTypeEnum;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.CacheConstant;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.entity.ParklotSpecialPlate;
import com.lecent.park.entity.TempParkingChargeRule;
import com.lecent.park.mapper.ParklotSpecialPlateMapper;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IParklotSpecialPlateService;
import com.lecent.park.service.ITempParkingChargeRuleService;
import com.lecent.park.service.IUserParklotService;
import com.lecent.park.common.utils.ComUtil;
import com.lecent.park.vo.ParklotSpecialPlateVO;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.CacheUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-21
 */
@Service
@AllArgsConstructor
public class ParklotSpecialPlateServiceImpl extends
	BaseServiceImpl<ParklotSpecialPlateMapper, ParklotSpecialPlate> implements IParklotSpecialPlateService {
	/**
	 * 特殊车辆缓存
	 */
	private static final String SPECIAL_PLATE_CACHE_NAME = "specialPlate:";

	private IParklotService parklotService;

	private ITempParkingChargeRuleService tempParkingChargeRuleService;

	private IUserParklotService userParklotService;

	@Override
	public IPage<ParklotSpecialPlateVO> selectParklotSpecialPlatePage(IPage<ParklotSpecialPlateVO> page, ParklotSpecialPlateVO queryBean) {
		List<Long> bindParkLotIds = userParklotService.getCurrentUserBindParkLotIds(queryBean.getParklotId());
		// 无关联车场直接返回空
		if (CollectionUtil.isEmpty(bindParkLotIds)) {
			return page.setRecords(Collections.emptyList());
		}
		queryBean.setParklotIdList(bindParkLotIds);
		return page.setRecords(baseMapper.selectParklotSpecialPlatePage(page, queryBean));
	}

	/**
	 * 统一保存或更新
	 *
	 * @param saveBean 更新实体
	 * @return true 成功
	 */
	private boolean unifySaveOrUpdate(ParklotSpecialPlate saveBean) {
		CacheUtils.delKey(CacheConstant.PARK_CACHE + SPECIAL_PLATE_CACHE_NAME + saveBean.getParklotId());
		return super.saveOrUpdate(saveBean);
	}

	@Override
	public Boolean customDeleteLogic(List<Long> ids) {
		if (Func.isEmpty(ids)) {
			return false;
		}
		for (Long id : ids) {
			ParklotSpecialPlate parklotSpecialPlate = this.getById(id);
			if (parklotSpecialPlate == null) {
				continue;
			}
			CacheUtils.delKey(CacheConstant.PARK_CACHE + SPECIAL_PLATE_CACHE_NAME + parklotSpecialPlate.getParklotId());
		}

		return deleteLogic(ids);
	}

	@Override
	@AroundOpt(serviceClass = IParklotSpecialPlateService.class, title = "特殊车辆管理", optType = OptTypeEnum.ADD,
		customMsg = "#parklotSpecialPlate.specialCarName", customMsgTemp = "新增特殊车辆,{}",
		headers = {
			@AroundOptHeader(headerClass = IParklotService.class, headerId = "parklotId", headerName = "name"),
			@AroundOptHeader(headerClass = IParklotSpecialPlateService.class, headerId = "id", headerName = "specialCarName"),
		}
	)
	public Boolean submit(ParklotSpecialPlate parklotSpecialPlate) {
		String matchPlate = parklotSpecialPlate.getMatchPlate();

		//校验车牌规则
		List<String> plateRuleList = Func.toStrList(matchPlate);
		if (plateRuleList.stream().distinct().count() < plateRuleList.size()) {
			throw new ServiceException("车牌规则重复，请检查后重试！");
		}

		for (String plateRule : plateRuleList) {
			int count = getCountByPlateRule(plateRule, parklotSpecialPlate.getId(), parklotSpecialPlate.getParklotId());
			if (count > 0) {
				throw new ServiceException(String.format("车牌规则：[%s]已存在，请检查后重试!", plateRule));
			}
		}

		if (Func.isEmpty(ParkLotCaches.getParkLot(parklotSpecialPlate.getParklotId()))) {
			throw new ServiceException(String.format("车场id：[%s]找不到对应的车场", parklotSpecialPlate.getParklotId()));
		}

		Long tempRuleId = parklotSpecialPlate.getTempRuleId();
		// 数据清洗
		if (Long.valueOf(-1L).equals(tempRuleId)) {
			parklotSpecialPlate.setTempRuleId(null);
			tempRuleId = null;
		}
		if (null != tempRuleId) {
			TempParkingChargeRule chargeRuleService = tempParkingChargeRuleService.getById(tempRuleId);
			LecentAssert.notNull(chargeRuleService, String.format("规则id：[%s]找不到对应的收费规则", parklotSpecialPlate.getTempRuleId()));
		}

		//校验车辆名称
		String specialCarName = parklotSpecialPlate.getSpecialCarName();
		int count = this.countBySpecialCarName(specialCarName, parklotSpecialPlate.getId(), parklotSpecialPlate.getParklotId());
		if (count > 0) {
			throw new ServiceException("该停车场内特殊车辆名称已经存在，请检查后重试！");
		}
		return unifySaveOrUpdate(parklotSpecialPlate);
	}

	@Override
	public int countBySpecialCarName(String specialCarName, Long id, Long parklotId) {
		return baseMapper.countBySpecialCarName(specialCarName, id, parklotId);
	}

	@Override
	public int getCountByPlateRule(String plateRule, Long id, Long parklotId) {
		return baseMapper.getCountByPlateRule(plateRule, id, parklotId);
	}

	/**
	 * 查询车场有效特殊车辆
	 *
	 * @param parkLotId 车场ID
	 * @return 特殊车辆
	 */
	private List<ParklotSpecialPlate> listActiveSpecialPlatesByParkLotId(Long parkLotId) {
		return list(Wrappers.<ParklotSpecialPlate>lambdaQuery()
			.eq(ParklotSpecialPlate::getParklotId, parkLotId)
			.eq(ParklotSpecialPlate::getStatus, EnableStatus.ACTIVE.getCode()));
	}

	@Override
	public List<ParklotSpecialPlate> selectByParkLotId(Long parkLotId) {
		return CacheUtils.getList(CacheConstant.PARK_CACHE + SPECIAL_PLATE_CACHE_NAME + parkLotId,
			ParklotSpecialPlate.class,
			() -> listActiveSpecialPlatesByParkLotId(parkLotId),
			CacheUtils.DEFAULT_1_MONTH);
	}

	@Override
	public ParklotSpecialPlate matchSpecialCard(Long parkLotId, String plate) {
		List<ParklotSpecialPlate> specialPlates = selectByParkLotId(parkLotId);
		if (Func.isEmpty(specialPlates)) {
			return null;
		}

		for (ParklotSpecialPlate specialPlate : specialPlates) {
			if (Func.isEmpty(specialPlate.getMatchPlate())) {
				continue;
			}
			String[] specialPlateRuleArray = Func.toStrArray(specialPlate.getMatchPlate());
			for (String plateRule : specialPlateRuleArray) {
				if (ComUtil.plateMatch(plate, plateRule)) {
					return specialPlate;
				}
			}
		}
		return null;
	}

}
