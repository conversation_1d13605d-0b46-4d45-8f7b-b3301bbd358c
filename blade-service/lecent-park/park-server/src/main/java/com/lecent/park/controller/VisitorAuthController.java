package com.lecent.park.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.VisitorAuthDTO;
import com.lecent.park.entity.VisitorAuth;
import com.lecent.park.service.IVisitorAuthService;
import com.lecent.park.third.ThirdHttpRes;
import com.lecent.park.third.ThirdHttpVisitorInfo;
import com.lecent.park.vo.VisitorAuthVO;
import com.lecent.park.wrapper.VisitorAuthWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 访客授权表 控制器
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/visitorauth")
@Api(value = "访客授权表", tags = "访客授权表接口")
@Slf4j
public class VisitorAuthController extends BladeController {

	private IVisitorAuthService visitorAuthService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入visitorAuth")
	public R<VisitorAuthVO> detail(VisitorAuth visitorAuth) {
		return R.data(visitorAuthService.getDetail(visitorAuth.getId()));
	}

	@GetMapping("/not-token/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "无token获取详情", notes = "传入visitorAuth")
	public R<VisitorAuthVO> detailNotToken(VisitorAuth visitorAuth) {
		return R.data(visitorAuthService.getDetail(visitorAuth.getId()));
	}

	/**
	 * 分页 访客授权表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入visitorAuth")
	public R<IPage<VisitorAuthVO>> list(VisitorAuth visitorAuth, Query query) {
		IPage<VisitorAuth> pages = visitorAuthService.page(Condition.getPage(query),
			Condition.getQueryWrapper(visitorAuth));
		return R.data(VisitorAuthWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 访客授权表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入visitorAuth")
	public R<IPage<VisitorAuthVO>> page(VisitorAuthDTO visitorAuthDto, Query query) {
		IPage<VisitorAuthVO> pages = visitorAuthService.selectVisitorAuthPage(Condition.getPage(query), visitorAuthDto);
		return R.data(pages);
	}

	/**
	 * 新增 访客授权表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入visitorAuth")
	public R save(@Valid @RequestBody VisitorAuth visitorAuth) {
		return R.data(visitorAuthService.addVisitorAuth(visitorAuth));
	}

	/**
	 * 修改 访客授权表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入visitorAuth")
	public R update(@Valid @RequestBody VisitorAuth visitorAuth) {
		return R.data(visitorAuthService.updateCustom(visitorAuth));
	}

	/**
	 * 新增或修改 访客授权表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入visitorAuth")
	public R submit(@Valid @RequestBody VisitorAuth visitorAuth) {
		return R.status(visitorAuthService.saveOrUpdate(visitorAuth));
	}

	/**
	 * 删除 访客授权表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(visitorAuthService.customDeleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 判断车牌今天是否已经入场
	 */
	@GetMapping("/isEnter")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "判断车牌今天是否已经入场", notes = "传入plate")
	public R<String> isEnter(String plate, Long parklotId) {
		return R.data(visitorAuthService.isEnter(plate, parklotId));
	}

	/**
	 * 设置车场用户授权限制
	 */
	@GetMapping("/setVisitorAuthLimit")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "设置车场用户授权限制", notes = "传入plate")
	public R setVisitorAuthLimit(Integer parklotlimit,
								 Integer userlimit,
								 Long parklotId) {
		return R.status(visitorAuthService.setVisitorAuthLimit(parklotlimit, userlimit, parklotId));
	}


	/**
	 * 多车授权时用户扫码添加车辆的免费权限
	 */
	@PostMapping("/addUserFreePermission")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "多车授权时用户扫码添加车辆的免费权限", notes = "传入plate,授权id")
	public R addUserFreePermission(@RequestBody VisitorAuth visitorAuth) {
		return R.status(visitorAuthService.addUserFreePermission(visitorAuth));
	}


	/**
	 * 建行调用接口
	 */
	@PostMapping("/visitorCar")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "建行调用接口", notes = "visitorAuth")
	public ThirdHttpRes visitorCar(@Valid ThirdHttpVisitorInfo thirdHttpVisitorInfo) {
		log.info("接收建行数据为：{}", JSON.toJSONString(thirdHttpVisitorInfo));
		return visitorAuthService.addThirdVisitorAuth(thirdHttpVisitorInfo);
	}


}
