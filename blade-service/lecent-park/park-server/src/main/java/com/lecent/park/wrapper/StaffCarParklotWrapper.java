package com.lecent.park.wrapper;

import com.lecent.park.entity.StaffCarParklot;
import com.lecent.park.vo.StaffCarParklotVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 员工车辆与车场关联信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
public class StaffCarParklotWrapper extends BaseEntityWrapper<StaffCarParklot, StaffCarParklotVO>  {

	public static StaffCarParklotWrapper build() {
		return new StaffCarParklotWrapper();
 	}

	@Override
	public StaffCarParklotVO entityVO(StaffCarParklot staffCarParklot) {
		StaffCarParklotVO staffCarParklotVO = Objects.requireNonNull(BeanUtil.copy(staffCarParklot, StaffCarParklotVO.class));

		//User createUser = UserCache.getUser(staffCarParklot.getCreateUser());
		//User updateUser = UserCache.getUser(staffCarParklot.getUpdateUser());
		//staffCarParklotVO.setCreateUserName(createUser.getName());
		//staffCarParklotVO.setUpdateUserName(updateUser.getName());

		return staffCarParklotVO;
	}

}
