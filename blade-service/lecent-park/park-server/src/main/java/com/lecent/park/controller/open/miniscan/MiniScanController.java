package com.lecent.park.controller.open.miniscan;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.ParklotLocationDTO;
import com.lecent.park.service.IParklotLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.map.tencent.response.LocationSearchResponse;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 停车系统微信小程序进出场、车牌授权扫码端,
 * 对外接口路径为：http://localhost:80/open-api/lecent-park/mini-scan/子路径
 *
 * <AUTHOR>
 * @since 2022-03-09
 */
@Slf4j
@RestController
@Validated
@AllArgsConstructor
@RequestMapping(OpenApiConstant.OPEN_API + "/mini-scan")
@Api(value = "小程序扫码端接口", tags = "小程序扫码端接口")
public class MiniScanController extends BladeController {

	private IParklotLocationService parkLotLocationService;

	/**
	 * 地图搜索接口
	 * 是否按搜索定位点距离查询标识
	 */
	@PostMapping("/v1/parkLot/search")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车场搜索", notes = "传入parkLot")
	public R<LocationSearchResponse> search(@RequestBody ParklotLocationDTO parklotLocationDTO) {
		return R.data(parkLotLocationService.search(parklotLocationDTO));
	}
}
