package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.service.IMyPointsService;
import com.lecent.park.vo.PointsChangeRecord;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/my/points")
public class MyPointsController {

	private final IMyPointsService myPointsService;

	@GetMapping("/page")
	public R<IPage<PointsChangeRecord>> page(PointsChangeRecord record, Query query) {
		return R.data(myPointsService.page(Condition.getPage(query), record));
	}

	@GetMapping("/total")
	public R<PointsChangeRecord> totalPoints(PointsChangeRecord record) {
		return R.data(myPointsService.totalPoints(record));
	}
}
