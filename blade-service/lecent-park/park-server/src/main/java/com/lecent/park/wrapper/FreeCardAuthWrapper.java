package com.lecent.park.wrapper;

import com.lecent.park.entity.FreeCardAuth;
import com.lecent.park.vo.FreeCardAuthVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 公司角色表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public class FreeCardAuthWrapper extends BaseEntityWrapper<FreeCardAuth, FreeCardAuthVO>  {

	public static FreeCardAuthWrapper build() {
		return new FreeCardAuthWrapper();
 	}

	@Override
	public FreeCardAuthVO entityVO(FreeCardAuth freeCardAuth) {
		FreeCardAuthVO freeCardAuthVO = BeanUtil.copy(freeCardAuth, FreeCardAuthVO.class);

		//User createUser = UserCache.getUser(freeCardAuth.getCreateUser());
		//User updateUser = UserCache.getUser(freeCardAuth.getUpdateUser());
		//freeCardAuthVO.setCreateUserName(createUser.getName());
		//freeCardAuthVO.setUpdateUserName(updateUser.getName());

		return freeCardAuthVO;
	}

}
