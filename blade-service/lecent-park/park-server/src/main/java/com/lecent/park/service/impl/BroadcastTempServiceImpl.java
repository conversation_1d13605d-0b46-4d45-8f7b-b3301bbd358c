package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.BroadcastTemp;
import com.lecent.park.mapper.BroadcastTempMapper;
import com.lecent.park.service.IBroadcastTempService;
import com.lecent.park.vo.BroadcastTempVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;


/**
 * 设置设置表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
@Service
public class BroadcastTempServiceImpl extends BaseServiceImpl<BroadcastTempMapper, BroadcastTemp> implements IBroadcastTempService {

	@Override
	public IPage<BroadcastTempVO> selectBroadcastTempPage(IPage<BroadcastTempVO> page, BroadcastTempVO broadcastTempVO) {
		return page.setRecords(baseMapper.selectBroadcastTempPage(page, broadcastTempVO));
	}


}
