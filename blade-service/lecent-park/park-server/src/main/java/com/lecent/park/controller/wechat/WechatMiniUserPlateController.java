package com.lecent.park.controller.wechat;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.BUserPlate;
import com.lecent.park.service.IBUserPlateService;
import com.lecent.park.vo.BUserPlateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户车辆控制器
 * <AUTHOR>
 * @since 2020-06-16
 */
@RestController
@RequestMapping("wechat/user-plate")
@Api(value = "用户车辆控制器", tags = "用户车辆控制器")
public class WechatMiniUserPlateController extends BladeController {

	@Autowired
	private IBUserPlateService userPlateService;

	/**
	 * 用户绑定车牌集合
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取用户绑定的车牌", notes = "")
	public R<List<BUserPlateVO>> list() {
		return R.data(userPlateService.listUserPlates());
	}


	/**
	 * 获取用户前三个我的车辆
	 */
	@GetMapping("/listThreeMinePlate")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "获取用户绑定的车牌", notes = "")
	public R<List<BUserPlateVO>> listThreeMinePlate() {
		return R.data(userPlateService.listThreeMinePlate());
	}


	/**
	 * 解綁我的车辆
	 */
	@PostMapping("/unbind")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "解綁我的车辆", notes = "解綁我的车辆")
	public R unbind(@ApiParam(value = "主键", required = true) @RequestParam String bindId) {
		return R.status(userPlateService.unbind(Long.valueOf(bindId)));
	}


	/**
	 * 修改我的车辆
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改我的车辆", notes = "传入bUserPlate")
	public R update(@Valid @RequestBody BUserPlate bUserPlate) {
		String phone = SecureUtil.getPhone();
		bUserPlate.setPhone(Long.valueOf(phone));
		return R.status(userPlateService.updateById(bUserPlate));
	}

	/**
	 * 绑定我的车辆
	 */
	@PostMapping("/bind")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "绑定我的车辆", notes = "传入bUserPlate")
	public R bind(@Valid @RequestBody BUserPlate bUserPlate) {
		return R.status(userPlateService.bind(bUserPlate));
	}




}
