package com.lecent.park.wrapper;

import com.lecent.park.entity.FreeCard;
import com.lecent.park.vo.FreeCardVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 公司角色表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public class FreeCardWrapper extends BaseEntityWrapper<FreeCard, FreeCardVO>  {

	public static FreeCardWrapper build() {
		return new FreeCardWrapper();
 	}

	@Override
	public FreeCardVO entityVO(FreeCard freeCard) {
		FreeCardVO freeCardVO = BeanUtil.copy(freeCard, FreeCardVO.class);

		//User createUser = UserCache.getUser(freeCard.getCreateUser());
		//User updateUser = UserCache.getUser(freeCard.getUpdateUser());
		//freeCardVO.setCreateUserName(createUser.getName());
		//freeCardVO.setUpdateUserName(updateUser.getName());

		return freeCardVO;
	}

}
