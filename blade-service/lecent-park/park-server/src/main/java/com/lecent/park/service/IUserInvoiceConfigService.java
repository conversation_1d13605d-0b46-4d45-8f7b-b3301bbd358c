package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.UserInvoiceConfig;
import com.lecent.park.vo.UserInvoiceConfigVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 开发票抬头配置 服务类
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface IUserInvoiceConfigService extends BaseService<UserInvoiceConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userInvoiceConfg
	 * @return
	 */
	IPage<UserInvoiceConfigVO> selectUserInvoiceConfigPage(IPage<UserInvoiceConfigVO> page, UserInvoiceConfigVO userInvoiceConfg);

	/**
	 * 新增 开发票抬头配置
	 *
	 * @param userInvoiceConfig
	 * @return
	 */
	Boolean saveConfig(UserInvoiceConfig userInvoiceConfig);

	/**
	 * 修改 开发票抬头配置
	 *
	 * @param userInvoiceConfig
	 * @return
	 */
	Boolean updateInvoiceConfig(UserInvoiceConfig userInvoiceConfig);
}
