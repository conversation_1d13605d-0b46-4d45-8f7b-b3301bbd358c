package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ReserveParkConfig;
import com.lecent.park.mapper.ReserveParkConfigMapper;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IReserveParkConfigService;
import com.lecent.park.vo.ReserveParkConfigVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 月卡变更日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Service
public class ReserveParkConfigServiceImpl extends BaseServiceImpl<ReserveParkConfigMapper, ReserveParkConfig> implements IReserveParkConfigService {

	@Autowired
	private IParklotService parklotService;
	@Override
	public IPage<ReserveParkConfigVO> selectReserveParkConfigPage(IPage<ReserveParkConfigVO> page, ReserveParkConfigVO reserveParkConfig) {
		return page.setRecords(baseMapper.selectReserveParkConfigPage(page, reserveParkConfig));
	}

	@Override
	public ReserveParkConfig getConfig(Long parkId, String tenantId) {
		return getOne(new QueryWrapper<ReserveParkConfig>().lambda().eq(ReserveParkConfig::getParkId, parkId).eq(ReserveParkConfig::getTenantId, tenantId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveConfig(ReserveParkConfig config) {
		if (Func.isEmpty(config.getParkId())) {
			throw new ServiceException("未传入停车场id");
		}
		Parklot parklot = ParkLotCaches.getParkLot(config.getParkId());
		if (Func.isEmpty(parklot)) {
			throw new ServiceException("未找到车场车场id对应的停车场");
		}
		ReserveParkConfig reserveParkConfig = getConfig(parklot.getId(), parklot.getTenantId());
		if (Func.isEmpty(reserveParkConfig)) {
			return save(config);
		} else {
			config.setId(reserveParkConfig.getId());
			return updateById(config);
		}
	}
}
