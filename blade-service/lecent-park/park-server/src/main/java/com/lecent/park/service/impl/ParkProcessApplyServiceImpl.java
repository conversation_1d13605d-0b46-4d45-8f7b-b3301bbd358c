package com.lecent.park.service.impl;

import cn.hutool.core.util.StrUtil;
import com.lecent.park.entity.*;
import com.lecent.park.service.ICardCategoryService;
import com.lecent.park.service.IParkProcessApplyService;
import com.lecent.park.service.IParklotAuxiliaryService;
import com.lecent.process.park.abnormality.audit.entity.ParkAbnormalityAudit;
import com.lecent.process.park.abnormality.audit.feign.IParkAbnormalityAuditClient;
import com.lecent.process.park.free.car.entity.ParkFreeCar;
import com.lecent.process.park.free.car.feign.IParkFreeCarClient;
import com.lecent.process.park.month.card.entity.ParkMonthCard;
import com.lecent.process.park.month.card.feign.IParkMonthCardClient;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ParkProcessApplyServiceImpl implements IParkProcessApplyService {

	/**
	 * 车场扩展信息服务
	 */
	private IParklotAuxiliaryService parklotAuxiliaryService;

	/**
	 * 稽核审核服务
	 */
	private IParkAbnormalityAuditClient parkAbnormalityAuditClient;

	/**
	 * 月卡审核服务
	 */
	private IParkMonthCardClient parkMonthCardClient;

	/**
	 * 免费车审核业务
	 */
	private IParkFreeCarClient parkFreeCarClient;

	/**
	 * 月卡规则服务
	 */
	private ICardCategoryService cardCategoryService;


	/**
	 * 发起稽核审批
	 * @param unpaidOrder    临停未付订单
	 * @param parklotId    车场id
	 * @param plate
	 */
	@Override
	public void applyAbnormalityAudit(TempParkingUnpaidOrder unpaidOrder, Long parklotId, String plate) {

		ParklotAuxiliary parklotAuxiliary = parklotAuxiliaryService.getByParklotId(parklotId);
		if (Func.isEmpty(parklotAuxiliary.getAbnormalityAuditTemplateId())) {
			throw new ServiceException("车场没有配置稽核审核模版！");
		}


		ParkAbnormalityAudit parkAbnormalityAudit = new ParkAbnormalityAudit();
		parkAbnormalityAudit.setTempParkingUnpaidOrderId(unpaidOrder.getId());
		parkAbnormalityAudit.setAuditStatus(unpaidOrder.getAuditStatus());
		parkAbnormalityAudit.setAuditRemark(unpaidOrder.getAuditRemake());
		parkAbnormalityAudit.setRealAmount(unpaidOrder.getRealAmount());
		parkAbnormalityAudit.setImageUrl(unpaidOrder.getImageUrl());
		parkAbnormalityAudit.setProcessTemplateId(parklotAuxiliary.getAbnormalityAuditTemplateId());
		parkAbnormalityAudit.setTitle(StrUtil.format("{}-异常放行稽核审批",plate));

		R<ParkAbnormalityAudit> result = parkAbnormalityAuditClient.startProcess(parkAbnormalityAudit);

		LecentAssert.isTrue(result.isSuccess(),result.getMsg());

	}

	/**
	 * 提交月卡申请
	 * @param card 月卡信息
	 */
	@Override
	public void applyMonthCard(Card card) {

		ParklotAuxiliary parklotAuxiliary = parklotAuxiliaryService.getByParklotId(Long.valueOf(card.getParklotIds()));
		if (Func.isEmpty(parklotAuxiliary.getMonthCardTemplateId())) {
			throw new ServiceException("车场没有配置月卡审核模版，提交申请失败！");
		}

		ParkMonthCard parkMonthCard = new ParkMonthCard();
		parkMonthCard.setCardId(card.getId());
		parkMonthCard.setCardRuleId(card.getCategoryId());
		parkMonthCard.setTitle(StrUtil.format("{}-月卡开卡审批",card.getPlates()));
		parkMonthCard.setProcessTemplateId(parklotAuxiliary.getMonthCardTemplateId());

		CardCategory cardCategory = cardCategoryService.getById(card.getCategoryId());
		if (Func.isNotEmpty(cardCategory)) {
			parkMonthCard.setDiscountCard(cardCategory.getDiscountCard());
		}

		R<ParkMonthCard> result = parkMonthCardClient.startProcess(parkMonthCard);

		LecentAssert.isTrue(result.isSuccess(),result.getMsg());

	}


	/**
	 * 发起免费车申请
	 * @param cardAuth cardAuth
	 * @param plate
	 */
	@Override
	public void applyFreeCar(FreeCardAuth cardAuth, String plate) {

		ParklotAuxiliary parklotAuxiliary = parklotAuxiliaryService.getByParklotId(cardAuth.getParklotId());
		if (Func.isEmpty(parklotAuxiliary.getFreeCarTemplateId())) {
			throw new ServiceException("车场没有配置免费车审核模版，提交申请失败！");
		}

		ParkFreeCar parkFreeCar = new ParkFreeCar();
		parkFreeCar.setFreeCardAuthId(cardAuth.getId());
		parkFreeCar.setProcessTemplateId(parklotAuxiliary.getFreeCarTemplateId());
		parkFreeCar.setTitle(StrUtil.format("{}-免费车审批",plate));

		R<ParkFreeCar> result = parkFreeCarClient.startProcess(parkFreeCar);
		LecentAssert.isTrue(result.isSuccess(),result.getMsg());

	}
}
