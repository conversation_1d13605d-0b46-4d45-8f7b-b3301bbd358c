package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.entity.StaffCar;
import com.lecent.park.entity.StaffCarParklot;
import com.lecent.park.mapper.StaffCarParklotMapper;
import com.lecent.park.service.IPlatePropertyService;
import com.lecent.park.service.IStaffCarParklotService;
import com.lecent.park.service.IStaffCarService;
import com.lecent.park.vo.StaffCarParklotVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * 员工车辆与车场关联信息 服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Service
public class StaffCarParklotServiceImpl extends
	BaseServiceImpl<StaffCarParklotMapper, StaffCarParklot> implements IStaffCarParklotService {

	private final IStaffCarService staffCarService;

	private final IPlatePropertyService platePropertyService;

	public StaffCarParklotServiceImpl(@Lazy IStaffCarService staffCarService,
									  IPlatePropertyService platePropertyService) {
		this.staffCarService = staffCarService;
		this.platePropertyService = platePropertyService;
	}

	/**
	 * 统一保存或更新
	 *
	 * @param saveBean 更新实体
	 */
	@Override
	public void unifySaveOrUpdate(StaffCarParklot saveBean) {
		super.saveOrUpdate(saveBean);

		StaffCarParklot oldBean = getById(saveBean.getId());

		StaffCar staffCar = staffCarService.getById(oldBean.getStaffId());
		LecentAssert.notNull(staffCar, "员工信息不能为空");

		// 更新汇总表
		platePropertyService.update(oldBean.getTenantId(),
			oldBean.getParklotId(),
			staffCar.getPlate(),
			oldBean.getId(),
			PlatePropertyType.STAFF_CARD);
	}

	@Override
	public StaffCarParklot getBeanById(Long id) {
		return getById(id);
	}

	@Override
	public IPage<StaffCarParklotVO> selectStaffCarParklotPage(IPage<StaffCarParklotVO> page, StaffCarParklotVO staffCarParklot) {
		return page.setRecords(baseMapper.selectStaffCarParklotPage(page, staffCarParklot));
	}

	@Override
	public StaffCarParklot selectByPlate(String plate, Long parkLotId) {
		return baseMapper.selectByPlate(plate, parkLotId);
	}

	@Override
	public StaffCarParklot getStaffCarById(Long id) {
		return getOne(Wrappers.<StaffCarParklot>lambdaQuery().eq(StaffCarParklot::getStaffId, id).last(" limit 1"));
	}

	@Override
	public boolean customDelete(Long relatedId) {
		deleteLogic(Collections.singletonList(relatedId));

		return platePropertyService.removeByCardIds(PlatePropertyType.STAFF_CARD, relatedId);
	}
}
