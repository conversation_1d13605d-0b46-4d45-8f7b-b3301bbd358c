package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParklotTimeDurationAddBatch;
import com.lecent.park.entity.ParklotTimeDuration;
import com.lecent.park.vo.ParklotTimeDurationDetailVO;
import com.lecent.park.vo.ParklotTimeDurationVO;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;
import java.util.List;

/**
 * 停车场停车时段配置 服务类
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
public interface IParklotTimeDurationService extends BaseService<ParklotTimeDuration> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotTimeDuration
	 * @return
	 */
	IPage<ParklotTimeDurationVO> selectParklotTimeDurationPage(IPage<ParklotTimeDurationVO> page, ParklotTimeDurationVO parklotTimeDuration);

	List<ParklotTimeDuration> getListByParkLotId(Long parkLotId, Integer type, Date currentDate);

	/**
	 * 批量新增停车时段
	 * @param parklotTimeDuration 新增参数
	 * @return
	 */
    Boolean saveBatchDuration(ParklotTimeDurationAddBatch parklotTimeDuration);

	ParklotTimeDurationDetailVO getTimeDurationByParklotId(Long parklotId);

	/**
	 * 当前是否禁停
	 * @param durationType
	 * @return
	 */
	boolean isNoParking(int durationType, String nowTime);
}
