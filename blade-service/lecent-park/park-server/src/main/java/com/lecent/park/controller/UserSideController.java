package com.lecent.park.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.UserSideDTO;
import com.lecent.park.service.ICardOrderService;
import com.lecent.park.service.ICardService;
import com.lecent.park.service.UserSideService;
import com.lecent.park.vo.*;
import com.lecent.payment.vo.UnifiedOrderResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 用户端月卡接口 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@AllArgsConstructor
@Validated
@RequestMapping("/user-side")
@Api(value = "用户端月卡接口", tags = "用户端月卡接口")
@Slf4j
public class UserSideController extends BladeController {

	private ICardService cardService;

	private ICardOrderService cardOrderService;

	private UserSideService userSideService;

	/**
	 * 根据车牌查询月卡
	 */
	@GetMapping("/card/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据车牌查询月卡", notes = "根据车牌查询月卡")
	public R<List<CardVO>> list(@NotBlank @RequestParam String plate) {
		return R.data(cardService.listUserCard(plate));
	}


	/**
	 * 月卡详情
	 */
	@GetMapping("/card/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "用户端月卡详情", notes = "用户端月卡详情")
	public R<UserSideCardVO> detail(@NotEmpty @RequestParam String id) {
		return R.data(cardService.userSideCardDetail(id));
	}

	/**
	 * 获取支付某个月的详细信息
	 */
	@GetMapping("/card/month-info")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取支付某个月的详细信息", notes = "获取支付某个月的详细信息")
	public R<PayMonth> getPayMonthInfo(@NotNull(message = "缴费月数不能为空") Integer monthNum, @NotNull(message = "月卡id不能为空") Long cardId) {
		return R.data(cardService.getPayMonthInfo(monthNum, cardId));
	}


	/**
	 * 续费
	 */
	@PostMapping("/card/renewalFee")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "续费", notes = "传入card")
	public R renewalFee(@Valid @RequestBody UserSideDTO userSideDto) {
		return R.data(userSideService.renewalFee(userSideDto));
	}

	@PostMapping("/card/recovery")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "追缴", notes = "传入card")
	public R<UnifiedOrderResultVO>  recovery( @RequestBody UserSideDTO userSideDto){
		return R.data(userSideService.recovery(userSideDto));
	}



	/**
	 * 根据车牌查询缴费记录
	 */
	@GetMapping("/card/getCardOrderListByPlate")
	@ApiOperation(value = "根据车牌查询缴费记录", notes = "根据车牌查询缴费记录")
	public R<List<UserCardOrderVO>> getCardOrderListByPlate(@NotBlank(message = "车牌号不能为空") @RequestParam String plate) {
		return R.data(cardOrderService.getCardOrderListByPlate(plate));
	}

	/**
	 * 支付回调
	 *
	 * @param tradeNo
	 * @return
	 */
	@GetMapping("/payCallback")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "paymentCallback", notes = "支付回调")
	public synchronized R payCallback(String tradeNo) {
		return R.data(userSideService.payCallback(tradeNo));
	}


	/**
	 * 支付回调
	 *
	 * @param todoId
	 * @return
	 */
	@GetMapping("/getPayStatus")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "getPayStatus", notes = "支付回调")
	public synchronized R getPayStatus(String todoId) {
		return R.data(userSideService.getPayStatus(todoId));
	}


	/**
	 * 用户端查詢订单状态
	 *
	 * @param outTradeNo
	 * @return
	 */
	@GetMapping("/card/order-status")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "用户端查詢订单状态", notes = "传outTradeNo")
	public R orderStatus(String outTradeNo) {
		return R.data(userSideService.orderStatus(outTradeNo));
	}


	/**
	 * 获取月卡最新的有效时段
	 */
	@GetMapping("/card/getNewestTimeDuration")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取月卡最新的有效时段", notes = "获取月卡最新的有效时段")
	public R<Map<String, String>> getNewestTimeDuration(@RequestParam @NotNull Long cardId,
														@RequestParam @NotNull Integer monthNum,
														@RequestParam Integer reChargeType) {
		return R.data(cardService.getNewestTimeDuration(cardId, monthNum, reChargeType));
	}

	@GetMapping("/card/findCardTempUnpaidOrder")
	@ApiOperation(value = "查询异常未缴", notes = "传入id")
	public R<List<CardTempUnpaidOrderVO>> findCardTempUnpaidOrder(@ApiParam(value = "月卡id") @RequestParam(required=false) Long cardId){
		List<CardTempUnpaidOrderVO> cardTempUnpaidOrderVOs = userSideService.findCardTempUnpaidOrder(cardId);
		return R.data(cardTempUnpaidOrderVOs);
	}
	@GetMapping("/card/findCardTempUnpaidOrderByItem")
	@ApiOperation(value = "查询异常未缴", notes = "传入id")
	public R<CardTempUnpaidOrderVO> findCardTempUnpaidOrderByItem(@ApiParam(value = "月卡id") @RequestParam Long cardId){
		CardTempUnpaidOrderVO cardTempUnpaidOrderVOs = userSideService.findCardTempUnpaidOrderByItem(cardId);
		return R.data(cardTempUnpaidOrderVOs);
	}


}
