package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.UserChannelDutyItem;
import com.lecent.park.mapper.UserChannelDutyItemMapper;
import com.lecent.park.service.IUserChannelDutyItemService;
import com.lecent.park.vo.UserChannelDutyItemVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 通道值班表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@Service
public class UserChannelDutyItemServiceImpl extends BaseServiceImpl<UserChannelDutyItemMapper, UserChannelDutyItem> implements IUserChannelDutyItemService {

	@Override
	public IPage<UserChannelDutyItemVO> selectUserChannelDutyItemPage(IPage<UserChannelDutyItemVO> page, UserChannelDutyItemVO userChannelDutyItem) {
		return page.setRecords(baseMapper.selectUserChannelDutyItemPage(page, userChannelDutyItem));
	}

	@Override
	public void updateLogoutChannel(Long userId) {
		baseMapper.updateLogoutChannel(userId);
	}

	@Override
	public List<Long> getByUserId(Long userId) {
		return this.list(Wrappers.<UserChannelDutyItem>query().lambda()
						.eq(UserChannelDutyItem::getUserId, userId)
						.eq(UserChannelDutyItem::getStatus, Constants.ONE))
						.stream()
						.map(UserChannelDutyItem::getChannelId)
						.collect(Collectors.toList());
	}

	@Override
	public UserChannelDutyItem getOnDutyItemByChannelId(Long channelId) {
		return this.getOne(Wrappers.<UserChannelDutyItem>lambdaQuery()
			.eq(UserChannelDutyItem::getStatus,Constants.ONE)
			.eq(UserChannelDutyItem::getChannelId,channelId));
	}

	@Override
	public Long getCurrentUserParklotId(Long userId) {
		List<UserChannelDutyItem> list = this.list(Wrappers.<UserChannelDutyItem>lambdaQuery()
			.eq(UserChannelDutyItem::getUserId, userId)
			.eq(UserChannelDutyItem::getStatus, Constants.ONE));

		if (Func.isEmpty(list)) {
			return -1L;
		}
		return list.get(0).getParklotId();
	}

    @Override
    public List<UserChannelDutyItem> listByLoginUser() {

		return this.list(Wrappers.<UserChannelDutyItem>query().lambda()
			.eq(UserChannelDutyItem::getUserId, SecureUtil.getUserId())
			.eq(UserChannelDutyItem::getStatus, 1));

    }

}
