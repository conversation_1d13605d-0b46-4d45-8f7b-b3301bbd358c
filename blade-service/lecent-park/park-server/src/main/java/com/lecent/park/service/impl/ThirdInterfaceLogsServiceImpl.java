package com.lecent.park.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.lecent.park.entity.ThirdInterfaceLogs;
import com.lecent.park.mapper.ThirdInterfaceLogsMapper;
import com.lecent.park.service.ThirdInterfaceLogsService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdInterfaceLogsServiceImpl extends BaseServiceImpl<ThirdInterfaceLogsMapper, ThirdInterfaceLogs> implements ThirdInterfaceLogsService {


	@Override
	@Transactional(rollbackFor = {Exception.class})
	@Async
	public void saveLogs(String name, Integer type, String url, Object requestParam, Object responseData, Date requestTime, Date responseTime) {
		String requestStr = "", responseStr = "";
		if (Func.notNull(requestParam)) {
			requestStr = JSON.toJSONString(requestParam);
			if (requestStr.length() > 8000) {
				requestStr = requestStr.substring(0, 8000);
			}
		}
		if (Func.notNull(responseData)) {
			responseStr = JSON.toJSONString(responseData);
			if (responseStr.length() > 8000) {
				responseStr = responseStr.substring(0, 8000);
			}
		}

		requestTime = requestTime != null ? requestTime : new Date();
		responseTime = responseTime != null ? responseTime : new Date();

		ThirdInterfaceLogs build = ThirdInterfaceLogs.builder()
			.interfaceName(name)
			.interfaceType(type)
			.interfaceUrl(url)
			.requestParam(requestStr)
			.responseData(responseStr)
			.requestTime(requestTime)
			.responseTime(responseTime)
			.build();

		this.save(build);
	}

	/**
	 * 每周一03点00分00秒删除日志
	 */
	@SneakyThrows
	@Scheduled(cron = "0 30 0/1 * * ?")
	@RedisLock(value = "lecent:park::timedTask:lock:third:interface:logs:remove:batch", waitTime = 0L)
	@Transactional(rollbackFor = Exception.class)
	public void deleteLogs(){
		Thread.sleep(2000);
		log.debug("[批量删除历史第三方接口日志]任务开始==============");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		Date date = new Date();
		List<ThirdInterfaceLogs> list = lambdaQuery()
				.select(ThirdInterfaceLogs::getId)
				.lt(ThirdInterfaceLogs::getCreateTime, DateUtil.minusDays(date, 7))
				.last("limit 2000")
				.list();

		List<Long> ids = list.stream().map(BaseEntity::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			List<List<Long>> partition = Lists.partition(ids, 2000);
			partition.forEach(this::removeByIds);
		}

		stopWatch.stop();
		log.info("[批量删除历史第三方接口日志]任务结束, 条数: {}, 耗时:[{}s]==============", list.size(), stopWatch.getTotalTimeSeconds());
	}

}
