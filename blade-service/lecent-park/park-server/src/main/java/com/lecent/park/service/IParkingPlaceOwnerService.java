package com.lecent.park.service;

import com.lecent.park.entity.ParkingPlaceOwner;
import com.lecent.park.vo.ParkingPlaceOwnerVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 我的车位 服务类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface IParkingPlaceOwnerService extends BaseService<ParkingPlaceOwner> {

	/**
	 * 自定义分页
	 *
	 * @param page              页
	 * @param parkingPlaceOwner 停车位所有者
	 * @return {@link IPage }<{@link ParkingPlaceOwnerVO }>
	 */
	IPage<ParkingPlaceOwnerVO> selectParkingPlaceOwnerPage(IPage<ParkingPlaceOwnerVO> page, ParkingPlaceOwnerVO parkingPlaceOwner);

	/**
	 * 提交
	 *
	 * @param parkingPlaceOwner 停车位所有者
	 * @return {@link Boolean }
	 */
	Boolean submit(ParkingPlaceOwnerVO parkingPlaceOwner);

	/**
	 * 明细
	 *
	 * @param id id
	 * @return {@link ParkingPlaceOwnerVO }
	 */
	ParkingPlaceOwnerVO detail(Long id);
}
