package com.lecent.park.service;

import com.lecent.park.dto.ViolationRecordDTO;
import com.lecent.park.entity.ViolationRecord;
import com.lecent.park.vo.ViolationRecordVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 停车违章记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-04-13
 */
public interface IViolationRecordService extends BaseService<ViolationRecord> {

    /**
     * 自定义分页
     *
     * @param page            页
     * @param violationRecord 违规记录
     * @return {@link IPage}<{@link ViolationRecordVO}>
     */
    IPage<ViolationRecordVO> selectViolationRecordPage(IPage<ViolationRecordVO> page, ViolationRecordVO violationRecord);

    /**
     * 违章记录详情
     *
     * @param id id
     * @return {@link ViolationRecordVO}
     */
    ViolationRecordVO detail(Long id);

    /**
     * 创建违规记录
     *
     * @param violationRecordDTO 违规记录
     */
    void createViolationRecord(ViolationRecordDTO violationRecordDTO);
}
