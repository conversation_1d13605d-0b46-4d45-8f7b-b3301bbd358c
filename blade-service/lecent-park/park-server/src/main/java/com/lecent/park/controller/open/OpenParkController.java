package com.lecent.park.controller.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ClientParkLot;
import com.lecent.park.dto.ParklotDTO;
import com.lecent.park.dto.ParklotIncomeDTO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.open.dto.OpenParklotDTO;
import com.lecent.park.open.vo.OpenParklotVO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.ParklotVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车场开放接口
 *
 * <AUTHOR>
 * @since 2022-03-09
 */
@Slf4j
@RestController
@Validated
@AllArgsConstructor
@RequestMapping(value = {
	OpenApiConstant.OPEN_API + "park",
	OpenApiConstant.OPEN_API + "parklot"
})
@Api(value = "车场开放接口", tags = "车场开放接口")
public class OpenParkController extends BladeController {

	private IParklotService parklotService;


	/**
	 * 自定义分页车场信息表
	 */
	@GetMapping("/v1/parklotList")
	@ApiOperation(value = "分页", notes = "传入parklot")
	public R<IPage<ClientParkLot>> getParkLotInfoPage(ParklotDTO parklot, Query query) {
		IPage<ClientParkLot> pages = parklotService.getParkLotInfoPage(Condition.getPage(query), parklot);
		return R.data(pages);
	}


	/**
	 * 车场信息集合
	 */
	@PostMapping("/v1/listParkLot")
	@ApiOperation(value = "List", notes = "传入parklot")
	public R<List<Map<String, Object>>> listParkLot() {
		List<ParklotVO> parkLots = parklotService.userParkLotDetailList();
		if (Func.isEmpty(parkLots)) {
			return R.data(null);
		}

		List<Map<String, Object>> resultList = parkLots.stream()
			.map(p ->
				new HashMap<String, Object>(8) {
				{
					put("parklotId", p.getId());
					put("name", p.getName());
					put("addr", p.getAddress());
					put("imgUrl", p.getParklotImgUrl());
					put("type",p.getParklotType());
					put("chargeRuleDesc", p.getChargeRuleDesc());
					put("totalParkingNumber", p.getTempLotAmount());
					put("remainParkingNumber", p.getParkingNum());
					put("lng", p.getLng());
					put("lat", p.getLat());
				}}
			).collect(Collectors.toList());

		return R.data(resultList);
	}


	/**
	 * 自定义分页车场信息表
	 */
	@GetMapping("/v1/income")
	@ApiOperation(value = "分页", notes = "传入parklot")
	public R income(ParklotIncomeDTO parklotIncomeDTO) {
		return parklotService.getParklotIncome(parklotIncomeDTO);
	}

}
