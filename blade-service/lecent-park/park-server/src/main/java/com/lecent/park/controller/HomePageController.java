package com.lecent.park.controller;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.service.IHomePageService;
import com.lecent.park.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 首页统计控制器
 *
 * <AUTHOR>
 * @date 2021-05-17 10:33
 */
@Slave
@RestController
@AllArgsConstructor
@RequestMapping("/homePageStatistics")
@Api(value = "首页统计", tags = "首页统计")
public class HomePageController {


	private final IHomePageService homePageService;

	/**
	 * 收入统计
	 */
	@GetMapping("/incomeStatistics")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "收入统计", notes = "收入统计")
	public R<IncomeStatisticsVO> incomeStatistics() {
		return R.data(homePageService.incomeStatistics());
	}

	@GetMapping("/income/today")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "今日收入统计", notes = "今日收入统计")
	public R<IncomeStatisticsVO> incomeOfToday() {
		return R.data(homePageService.incomeStatisticsOfToday());
	}


	/**
	 * 收入直方图统计
	 */
	@GetMapping("/incomeHistogramStatistics")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "收入直方图统计", notes = "收入直方图统计")
	public R<List<HistogramStatisticsVO>> incomeHistogramStatistics(HistogramStatisticsVO statisticsVO) {
		return R.data(homePageService.incomeHistogramStatistics(statisticsVO));
	}


	/**
	 * 月卡过期预警
	 */
	@GetMapping("/cardEarlyWarning")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "月卡过期预警", notes = "月卡过期预警")
	public R<List<CardVO>> cardEarlyWarning() {
		return R.data(homePageService.cardEarlyWarning());
	}


	/**
	 * 车流量统计
	 */
	@GetMapping("/carFlowRateStatistics")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "车流量统计", notes = "车流量统计")
	@ApiImplicitParams({@ApiImplicitParam(name = "statisticsType", value = "统计类型 1-日；2-周；3-月")})
	public R<List<FlowRateVO>> carFlowRateStatistics(@RequestParam("statisticsType") Integer statisticsType) {
		return R.data(homePageService.carFlowRateStatistics(statisticsType));
	}


	/**
	 * 缴费方式统计
	 */
	@GetMapping("/payWayStatistics")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "缴费方式统计", notes = "缴费方式统计")
	@ApiImplicitParams({@ApiImplicitParam(name = "parklotType", value = "统计类型 3-全部；2-路内；1-路外")})
	public R<List<PayWayStatisticsVO>> payWayStatistics(@RequestParam("parklotType") Integer parklotType) {
		return R.data(homePageService.payWayStatistics(parklotType));
	}


	/**
	 * 出入场记录
	 */
	@GetMapping("/enterAndExitRecord")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "出入场记录", notes = "出入场记录")
	public R<List<HomeEnterAndExitVO>> enterAndExitRecord() {
		return R.data(homePageService.enterAndExitRecord());
	}


}
