package com.lecent.park.wrapper;

import com.lecent.park.entity.MCoupon;
import com.lecent.park.vo.MCouponVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 优惠券明细表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
public class MCouponWrapper extends BaseEntityWrapper<MCoupon, MCouponVO>  {

	public static MCouponWrapper build() {
		return new MCouponWrapper();
 	}

	@Override
	public MCouponVO entityVO(MCoupon mCoupon) {
		MCouponVO mCouponVO = BeanUtil.copy(mCoupon, MCouponVO.class);

		//User createUser = UserCache.getUser(mCoupon.getCreateUser());
		//User updateUser = UserCache.getUser(mCoupon.getUpdateUser());
		//mCouponVO.setCreateUserName(createUser.getName());
		//mCouponVO.setUpdateUserName(updateUser.getName());

		return mCouponVO;
	}

}
