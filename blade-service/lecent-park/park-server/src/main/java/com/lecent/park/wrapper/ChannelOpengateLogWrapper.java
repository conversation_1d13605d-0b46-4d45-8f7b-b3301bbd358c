package com.lecent.park.wrapper;

import com.lecent.park.entity.ChannelOpengateLog;
import com.lecent.park.vo.ChannelOpengateLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 异常开闸日志记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public class ChannelOpengateLogWrapper extends BaseEntityWrapper<ChannelOpengateLog, ChannelOpengateLogVO>  {

	public static ChannelOpengateLogWrapper build() {
		return new ChannelOpengateLogWrapper();
 	}

	@Override
	public ChannelOpengateLogVO entityVO(ChannelOpengateLog channelOpengateLog) {
		ChannelOpengateLogVO channelOpengateLogVO = BeanUtil.copy(channelOpengateLog, ChannelOpengateLogVO.class);

		//User createUser = UserCache.getUser(channelOpengateLog.getCreateUser());
		//User updateUser = UserCache.getUser(channelOpengateLog.getUpdateUser());
		//channelOpengateLogVO.setCreateUserName(createUser.getName());
		//channelOpengateLogVO.setUpdateUserName(updateUser.getName());

		return channelOpengateLogVO;
	}

}
