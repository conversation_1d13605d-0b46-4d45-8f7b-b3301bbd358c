package com.lecent.park.controller.open;

import com.lecent.park.dto.Platform170NoticeDTO;
import com.lecent.park.discount.cashreduce.service.IPlatform170CouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 170平台立减金接口  控制器
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/open/coupon")
@Api(value = "170平台立减金开放接口 ", tags = "170平台立减金开放接口")
public class Platform170OpenCouponController extends BladeController {

	private final IPlatform170CouponService platform170CouponService;

	/**
	 * 核销立减金
	 */
	@PostMapping("/update/status")
	@ApiOperation(value = "核销立减金", notes = "核销立减金")
	public Platform170NoticeDTO updateToUsed(@RequestParam String organization_id,
											 @RequestParam String param,
											 @RequestParam String sign) {
		log.info("170平台核销立减金请求参数，organization_id：{},param:{},sign:{}", organization_id, param, sign);
		return platform170CouponService.updateToUsed(organization_id, param, sign);
	}

}
