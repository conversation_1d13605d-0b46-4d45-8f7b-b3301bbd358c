package com.lecent.park.controller;

import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.en.channeltodo.ChargeType;
import com.lecent.park.en.temporder.CreateWay;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 枚举字典控制器
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@AllArgsConstructor
@RequestMapping("/enum-dict")
@RestController
public class EnumController extends BladeController {

	private static final String KEY = "key";
	private static final String VALUE = "value";

	@GetMapping("/chargeType/dict")
	@ApiOperation(value = "收费类型字典", notes = "收费类型字典")
	public R chargeTypeDict() {
		List<Map<String, String>> result = new ArrayList<>();
		for (ChargeType value : ChargeType.values()) {
			Map<String, String> map = new HashMap<>();
			map.put(KEY, value.getName());
			map.put(VALUE, value.getMsg());
			result.add(map);
		}
		return R.data(result);
	}

	@GetMapping("/channelWay/dict")
	@ApiOperation(value = "触发方式字典", notes = "收费类型字典")
	public R triggerTypeDict() {
		List<Map<String, Object>> result = new ArrayList<>();
		for (ChannelWay value : ChannelWay.values()) {
			Map<String, Object> map = new HashMap<>();
			map.put(KEY, value.getValue());
			map.put(VALUE, value.getName());
			result.add(map);
		}
		return R.data(result);
	}

	@GetMapping("/payChannel/dict")
	@ApiOperation(value = "创建方式字典", notes = "创建方式字典")
	public R createWayDict() {
		List<Map<String, Object>> result = new ArrayList<>();
		for (CreateWay value : CreateWay.values()) {
			Map<String, Object> map = new HashMap<>();
			map.put(KEY, value.getValue());
			map.put(VALUE, value.getName());
			result.add(map);
		}
		return R.data(result);
	}


}
