package com.lecent.park.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.service.IGuardBtnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.vo.GuardUserMenuVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 岗亭端按钮表 控制器
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guard-btn")
@Api(value = "岗亭端按钮表", tags = "岗亭端按钮表接口")
public class GuardBtnController extends BladeController {

	private IGuardBtnService guardBtnService;


	/**
	 * 获取移动岗亭端用户配置的菜单列表
	 */
	@GetMapping("/mobile-guard-user-menu")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取移动岗亭端用户配置的菜单列表", notes = "获取移动岗亭端用户配置的菜单列表")
	public R<List<GuardUserMenuVO>> mobileGuardUserMenu(@RequestParam(required = false) String userId) {
		return R.data(guardBtnService.mobileGuardUserMenu(Func.notNull(userId) ? Long.valueOf(userId) : null));
	}


}
