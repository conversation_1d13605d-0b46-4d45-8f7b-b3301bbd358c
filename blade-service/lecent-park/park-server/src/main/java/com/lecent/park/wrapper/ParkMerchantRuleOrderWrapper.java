package com.lecent.park.wrapper;

import com.lecent.park.entity.ParkMerchantRuleOrder;
import com.lecent.park.vo.ParkMerchantRuleOrderVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-04-19
 */
public class ParkMerchantRuleOrderWrapper extends BaseEntityWrapper<ParkMerchantRuleOrder, ParkMerchantRuleOrderVO> {

	public static ParkMerchantRuleOrderWrapper build() {
		return new ParkMerchantRuleOrderWrapper();
	}

	@Override
	public ParkMerchantRuleOrderVO entityVO(ParkMerchantRuleOrder parkMerchantRuleOrder) {
		ParkMerchantRuleOrderVO parkMerchantRuleOrderVO = BeanUtil.copy(parkMerchantRuleOrder, ParkMerchantRuleOrderVO.class);

		//User createUser = UserCache.getUser(parkMerchantRuleOrder.getCreateUser());
		//User updateUser = UserCache.getUser(parkMerchantRuleOrder.getUpdateUser());
		//parkMerchantRuleOrderVO.setCreateUserName(createUser.getName());
		//parkMerchantRuleOrderVO.setUpdateUserName(updateUser.getName());

		return parkMerchantRuleOrderVO;
	}

}
