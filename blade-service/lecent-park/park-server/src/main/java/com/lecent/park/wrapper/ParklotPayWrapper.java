package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotPay;
import com.lecent.park.vo.ParklotPayVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场与商户对应关系表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
public class ParklotPayWrapper extends BaseEntityWrapper<ParklotPay, ParklotPayVO>  {

	public static ParklotPayWrapper build() {
		return new ParklotPayWrapper();
 	}

	@Override
	public ParklotPayVO entityVO(ParklotPay parklotPay) {
		ParklotPayVO parklotPayVO = BeanUtil.copy(parklotPay, ParklotPayVO.class);

		//User createUser = UserCache.getUser(parklotPay.getCreateUser());
		//User updateUser = UserCache.getUser(parklotPay.getUpdateUser());
		//parklotPayVO.setCreateUserName(createUser.getName());
		//parklotPayVO.setUpdateUserName(updateUser.getName());

		return parklotPayVO;
	}

}
