package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.lecent.park.entity.RabbitMqLogs;
import com.lecent.park.mapper.RabbitMqLogsMapper;
import com.lecent.park.service.IRabbitMqLogsService;
import com.lecent.park.vo.RabbitMqLogsVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 接收rabbitmq数据日志 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Slf4j
@Service
public class RabbitMqLogsServiceImpl extends BaseServiceImpl<RabbitMqLogsMapper, RabbitMqLogs> implements IRabbitMqLogsService {

	@Override
	public IPage<RabbitMqLogsVO> selectRabbitMqLogsPage(IPage<RabbitMqLogsVO> page, RabbitMqLogsVO rabbitMqLogs) {
		return page.setRecords(baseMapper.selectRabbitMqLogsPage(page, rabbitMqLogs));
	}

	@Override
	@Async
	@Transactional(rollbackFor = Exception.class)
	public void saveReceiveRabbitMqLogs(String tradeNo) {
		RabbitMqLogs build = RabbitMqLogs.builder().data(tradeNo).build();
		this.save(build);
	}



	/**
	 * 每周一02点00分00秒删除日志
	 */
	@SneakyThrows
	@Scheduled(cron = "0 30 0/1 * * ?")
	@RedisLock(value = "lecent:park::timedTask:lock:rabbit:mq:logs:remove:batch", waitTime = 0L)
	@Transactional(rollbackFor = Exception.class)
	public void deleteLogs() {
		Thread.sleep(2000);
		log.debug("[批量删除接收rabbitmq数据日志]任务开始==============");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		Date date = new Date();
		List<RabbitMqLogs> list = lambdaQuery()
				.select(RabbitMqLogs::getId)
				.lt(RabbitMqLogs::getCreateTime, DateUtil.minusDays(date, 7))
				.last("limit 20000")
				.list();

		List<Long> ids = list.stream().map(BaseEntity::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			List<List<Long>> partition = Lists.partition(ids, 2000);
			partition.forEach(this::removeByIds);
		}

		stopWatch.stop();
		log.info("[批量删除接收rabbitmq数据日志]任务结束, 条数: {}, 耗时:[{}s]==============", list.size(), stopWatch.getTotalTimeSeconds());
	}
}
