package com.lecent.park.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.lecent.park.entity.ReceiptPrintConfig;
import com.lecent.park.vo.ReceiptPrintConfigVO;
import java.util.Objects;

/**
 * 小票打印配置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
public class ReceiptPrintConfigWrapper extends BaseEntityWrapper<ReceiptPrintConfig, ReceiptPrintConfigVO>  {

	public static ReceiptPrintConfigWrapper build() {
		return new ReceiptPrintConfigWrapper();
 	}

	@Override
	public ReceiptPrintConfigVO entityVO(ReceiptPrintConfig receiptPrintConfig) {
		ReceiptPrintConfigVO receiptPrintConfigVO = Objects.requireNonNull(BeanUtil.copy(receiptPrintConfig, ReceiptPrintConfigVO.class));

		//User createUser = UserCache.getUser(receiptPrintConfig.getCreateUser());
		//User updateUser = UserCache.getUser(receiptPrintConfig.getUpdateUser());
		//receiptPrintConfigVO.setCreateUserName(createUser.getName());
		//receiptPrintConfigVO.setUpdateUserName(updateUser.getName());

		return receiptPrintConfigVO;
	}

}
