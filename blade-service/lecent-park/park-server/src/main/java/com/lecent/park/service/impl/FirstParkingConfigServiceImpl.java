package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.CacheConstant;
import com.lecent.park.entity.FirstParkingConfig;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.Parklot;
import com.lecent.park.mapper.FirstParkingConfigMapper;
import com.lecent.park.service.IFirstParkingConfigService;
import com.lecent.park.vo.FirstParkingConfigVO;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springblade.common.utils.CacheUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 首停配置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-01
 */
@Slf4j
@Service
@AllArgsConstructor
public class FirstParkingConfigServiceImpl extends BaseServiceImpl<FirstParkingConfigMapper, FirstParkingConfig> implements IFirstParkingConfigService {

    @Override
    public IPage<FirstParkingConfigVO> selectFirstParkingConfigPage(IPage<FirstParkingConfigVO> page, FirstParkingConfigVO firstParkingConfig) {
        List<FirstParkingConfigVO> records = baseMapper.selectFirstParkingConfigPage(page, firstParkingConfig);
        // 填充车场名称
        records.forEach(this::fillParklotNames);
        return page.setRecords(records);
    }

    @Override
    public FirstParkingConfigVO getDetailById(Long id) {
        FirstParkingConfig config = getById(id);
        if (config == null) {
            return null;
        }

        FirstParkingConfigVO vo = new FirstParkingConfigVO();
        Func.copy(config, vo);

        // 填充车场名称
        fillParklotNames(vo);

        return vo;
    }

    /**
     * 填充车场名称
     */
    private void fillParklotNames(FirstParkingConfigVO vo) {
        if (Func.isNotBlank(vo.getParklotIds())) {
            List<Long> parklotIdList = Func.toLongList(vo.getParklotIds());
            vo.setParklotNames(parklotIdList.stream()
                .map(ParkLotCaches::getParkLot)
                .map(Parklot::getName)
                .collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submit(FirstParkingConfig firstParkingConfig) {
        if (firstParkingConfig.getId() != null) {
            CacheUtils.delPtKey(CacheConstant.FIRST_PARKING_CACHE + ":" + firstParkingConfig.getId());
        }
        return saveOrUpdate(firstParkingConfig);
    }

    @Override
    public boolean isFirstParking(Long firstParkingConfigId, String plate, Date enterTime) {
        try {
            if (Func.isEmpty(firstParkingConfigId) || PlateValidator.isNoPlate(plate) || enterTime == null) {
                return true;
            }

            String cacheKey = generateFirstParkingCacheKey(firstParkingConfigId, plate, enterTime);
            Boolean isFirstParking = CacheUtils.get(cacheKey, Boolean.class);
            if (isFirstParking != null) {
                return isFirstParking;
            }

            // 查询历史停车记录
            ParkingOrder lastParkingOrder = baseMapper.selectFirstParkingOrderByEnterTime(firstParkingConfigId, plate, enterTime);

            if (lastParkingOrder != null) {
                CacheUtils.setEx(cacheKey, false, Duration.ofDays(1));
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("判断是否首停异常:", e);
            return true;
        }
    }

    /**
     * 生成首停缓存键
     */
    private static String generateFirstParkingCacheKey(Long firstParkingConfigId, String plate, Date enterTime) {
        String enterTimeStr = DateUtil.format(enterTime, "yyyyMMdd");
        return CacheConstant.FIRST_PARKING_CACHE + ":" + firstParkingConfigId + ":" + plate + ":" + enterTimeStr;
    }
}
