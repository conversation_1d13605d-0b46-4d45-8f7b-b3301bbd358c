package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotFloor;
import com.lecent.park.vo.ParklotFloorVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
public class ParklotFloorWrapper extends BaseEntityWrapper<ParklotFloor, ParklotFloorVO>  {

	public static ParklotFloorWrapper build() {
		return new ParklotFloorWrapper();
 	}

	@Override
	public ParklotFloorVO entityVO(ParklotFloor parklotFloor) {
		ParklotFloorVO parklotFloorVO = BeanUtil.copy(parklotFloor, ParklotFloorVO.class);

		//User createUser = UserCache.getUser(parklotFloor.getCreateUser());
		//User updateUser = UserCache.getUser(parklotFloor.getUpdateUser());
		//parklotFloorVO.setCreateUserName(createUser.getName());
		//parklotFloorVO.setUpdateUserName(updateUser.getName());

		return parklotFloorVO;
	}

}
