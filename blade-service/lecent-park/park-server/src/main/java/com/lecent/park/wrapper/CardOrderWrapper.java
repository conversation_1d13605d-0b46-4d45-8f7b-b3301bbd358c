package com.lecent.park.wrapper;

import com.lecent.park.entity.CardOrder;
import com.lecent.park.vo.CardOrderVO;
import org.springblade.common.utils.SequenceNoUtils;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 套餐订单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class CardOrderWrapper extends BaseEntityWrapper<CardOrder, CardOrderVO> {

	public static CardOrderWrapper build() {
		return new CardOrderWrapper();
	}

	@Override
	public CardOrderVO entityVO(CardOrder cardOrder) {
		CardOrderVO cardOrderVO = BeanUtil.copy(cardOrder, CardOrderVO.class);

		//User createUser = UserCache.getUser(cardOrder.getCreateUser());
		//User updateUser = UserCache.getUser(cardOrder.getUpdateUser());
		//cardOrderVO.setCreateUserName(createUser.getName());
		//cardOrderVO.setUpdateUserName(updateUser.getName());

		return cardOrderVO;
	}

	public CardOrderVO param2EntityVO(String cardId, Integer renewalFeeNum, Date startDate, Date newDateAddMonths, BigDecimal totalMount) {
		CardOrder order = CardOrderVO.builder()
			.cardId(Long.valueOf(cardId))
			.tradeNo(SequenceNoUtils.generateNo())
			.payMonthNum(renewalFeeNum)
			.startDate(startDate)
			.endDate(newDateAddMonths)
			.totalAmount(totalMount)
			.build();
		CardOrderVO cardOrderVO = CardOrderWrapper.build().entityVO(order);
		return cardOrderVO;
	}

}
