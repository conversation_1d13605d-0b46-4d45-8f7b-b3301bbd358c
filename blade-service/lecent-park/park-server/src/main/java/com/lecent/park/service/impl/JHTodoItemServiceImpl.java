package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.common.constant.NeedPayEnum;
import com.lecent.park.common.constant.UserParkingOrderEnum;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.service.*;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-07 15:38
 */
@Service
@Slf4j
public class JHTodoItemServiceImpl implements JHTodoItemService {

	@Autowired
	@Lazy
	private ClientService clientService;

	@Autowired
	private ITempParkingChargeRuleService tempParkingChargeRuleService;

	@Autowired
	private ExitService exitService;

	@Autowired
	private ICardService cardService;

	@Autowired
	private IParkingOrderService parkingOrderService;

	@Autowired
	private ITempParkingOrderService tempParkingOrderService;


	@Override
	public IPage<TodoItemVO> todoItem(Query query, ParkingOrderDTO parkingOrderDTO) {
		IPage<TodoItemVO> page = Condition.getPage(query);
		List<TodoItemVO> ret = new ArrayList<>();
		IPage<ParkingOrderVO> parkingOrderVOIPage = parkingOrderService.selectUserParking(query, parkingOrderDTO);
		List<ParkingOrderVO> records = parkingOrderVOIPage.getRecords();
		if (Func.isNotEmpty(records)) {
			records.forEach(parkingOrder -> {
				if (parkingOrder.getParkingStatus().equals(2)) {
					ChannelTodoVO channelTodoVO = clientService.parkingCharge(parkingOrder.getParklotId(), parkingOrder.getPlate(), "");
					Date now = DateUtil.now();
					BigDecimal cost = channelTodoVO.getReceiveAmount();
					TodoItemVO todoItemVO = new TodoItemVO(
						parkingOrder.getParklotId().toString()
						, channelTodoVO.getPlate(),
						channelTodoVO.getParklotName(),
						channelTodoVO.getEnterTime(),
						now,
						cost,
						cost.compareTo(BigDecimal.ZERO) > 0 ? NeedPayEnum.NEED.getValue() : NeedPayEnum.NOT_NEED.getValue(),
						DateUtils.getDuration(channelTodoVO.getEnterTime(), now),
						channelTodoVO.getTotalAmount(),
						channelTodoVO.getPaidAmount(),
						channelTodoVO.getDiscountAmount(),
						parkingOrder.getId(),
						getParkingOrderStatus(channelTodoVO)
					);
					ret.add(todoItemVO);
				} else {
					TodoItemVO todoItemVO = new TodoItemVO(
						parkingOrder.getParklotId().toString(),
						parkingOrder.getPlate(),
						parkingOrder.getParklotName(),
						parkingOrder.getEnterTime(),
						parkingOrder.getExitTime(),
						parkingOrder.getTotalAmount(),
						NeedPayEnum.NOT_NEED.getValue(),
						DateUtils.getDuration(parkingOrder.getEnterTime(), parkingOrder.getExitTime()),
						parkingOrder.getTotalAmount(),
						parkingOrder.getPaidAmount(),
						parkingOrder.getDiscountAmount(),
						parkingOrder.getId(),
						UserParkingOrderEnum.PAID.getValue()
					);
					ret.add(todoItemVO);
				}
			});
		}
		page.setTotal(parkingOrderVOIPage.getTotal());
		page.setPages(parkingOrderVOIPage.getPages());
		page.setCurrent(parkingOrderVOIPage.getCurrent());
		page.setRecords(ret);
		System.out.println("page;" + JsonUtil.toJson(page));
		return page;
	}

	private Integer getParkingOrderStatus(ChannelTodoVO channelTodoVO) {
		BigDecimal totalAmount = channelTodoVO.getTotalAmount();
		BigDecimal paidAmount = channelTodoVO.getPaidAmount();

		//待付款
		if (totalAmount.compareTo(BigDecimal.ZERO) > 0 && paidAmount.compareTo(BigDecimal.ZERO) == 0) {
			return UserParkingOrderEnum.NOT_PAY.getValue();
		}
		//待补缴
		if (paidAmount.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(paidAmount) > 0) {
			return UserParkingOrderEnum.ORDER_STYLE_BACK.getValue();
		}
		//已完成
		if (totalAmount.compareTo(paidAmount) == 0) {
			return UserParkingOrderEnum.PAID.getValue();
		}

		return UserParkingOrderEnum.PAID.getValue();
	}

	private BigDecimal getNewTotalAmount(BigDecimal totalAmount, ParkingOrderVO parkingOrder) {
		List<TempParkingOrder> tempParkingOrderList =
			tempParkingOrderService.getByParkingId(parkingOrder.getId());
		//没有缴费记录，待付款
		if (Func.isEmpty(tempParkingOrderList)) {
			return totalAmount;
		}

		//最近的一次缴费时间
		Date lastPayTime = tempParkingOrderList.get(0).getCreateTime();

		//是否超过免费离场时间
		boolean timeout = tempParkingChargeRuleService.isMoreThanPayLeaveTime(lastPayTime, DateUtil.now(), parkingOrder.getChargeRuleId());


		if (timeout) {
			ProjectCost projectCost = exitService.calculateCost(parkingOrder.getParklotId(), parkingOrder.getPlate(), parkingOrder.getEnterTime(), DateUtil.now());
			BigDecimal newTotalAmount = projectCost.getTotalAmount();
			if (newTotalAmount.compareTo(totalAmount) > 0) {
				return newTotalAmount.subtract(totalAmount);
			}
		}

		return totalAmount;
	}

	/**
	 * 判断车辆在场时，是带缴费还是补缴
	 *
	 * @param parkingOrder 停车记录
	 * @return 停车订单状态
	 */
	private Integer getParkingOrderStatus(ParkingOrderVO parkingOrder) {

		List<TempParkingOrder> tempParkingOrderList =
			tempParkingOrderService.getByParkingId(parkingOrder.getId());

		//没有缴费记录，待付款
		if (Func.isEmpty(tempParkingOrderList)) {
			return UserParkingOrderEnum.NOT_PAY.getValue();
		}

		//最近的一次缴费时间
		Date lastPayTime = tempParkingOrderList.get(0).getCreateTime();

		//是否超过免费离场时间
		boolean timeout = tempParkingChargeRuleService
			.isMoreThanPayLeaveTime(lastPayTime, DateUtil.now(), parkingOrder.getChargeRuleId());

		if (timeout) {
			return UserParkingOrderEnum.ORDER_STYLE_BACK.getValue();
		} else {
			return UserParkingOrderEnum.PAID.getValue();
		}

	}


	@Override
	public CCBParkingOrderDetailVO getOrderDetail(Long parkingId) {

		CCBParkingOrderDetailVO result = new CCBParkingOrderDetailVO();
		List<ParkingOrderDetailVO> parkingOrderDetailVOList = tempParkingOrderService.getOrderListByParkingId(parkingId);

		if (Func.isEmpty(parkingOrderDetailVOList)) {
			return parkingOrderService.getParkOrderDetail(parkingId);
		}

		//获取第一次缴费记录，其他的为补缴
		ParkingOrderDetailVO firstTempParkingOrder = parkingOrderDetailVOList.get(0);
		//获取最后一次缴费记录
		ParkingOrderDetailVO lastTempParkingOrder = parkingOrderDetailVOList.get(parkingOrderDetailVOList.size() - 1);

		result.setPlate(firstTempParkingOrder.getPlate());
		result.setParklotName(firstTempParkingOrder.getParklotName());
		result.setEnterTime(firstTempParkingOrder.getEnterTime());
		result.setExitTime(firstTempParkingOrder.getExitTime());
		result.setTotalAmount(firstTempParkingOrder.getTotalAmount());
		result.setDiscountAmount(firstTempParkingOrder.getDiscountAmount());
		result.setPayTime(lastTempParkingOrder.getPayTime());

		//超时总时段
		if (parkingOrderDetailVOList.size() > 1) {
			result.setTimeoutDuration(DateUtils.getDuration(parkingOrderDetailVOList.get(1).getPayTime(), lastTempParkingOrder.getPayTime()));
		}

		BigDecimal allAmount = parkingOrderDetailVOList.stream().map(ParkingOrderDetailVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

		//总金额
		result.setAllAmount(allAmount);

		//补缴金额
		result.setTimeoutAmount(allAmount.subtract(firstTempParkingOrder.getTotalAmount()));


		Date endDate = Func.isEmpty(result.getExitTime()) ? DateUtil.now() : result.getExitTime();

		//停车时长
		result.setDuration(DateUtils.getDuration(result.getEnterTime(), endDate));

		result.setPlateType(cardService.isTimeoutPlate(firstTempParkingOrder.getPlate(), firstTempParkingOrder.getParklotId()));

		//缴费详情
		//List<String> chargeDataList =  parkingOrderDetailVOList.stream().map(ParkingOrderDetailVO::getChargeData).collect(Collectors.toList());

		result.setChargeData(lastTempParkingOrder.getChargeData());

		return result;
	}
}
