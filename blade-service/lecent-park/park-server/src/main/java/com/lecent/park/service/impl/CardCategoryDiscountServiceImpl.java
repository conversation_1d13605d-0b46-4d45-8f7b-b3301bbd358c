package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.CardCategoryDiscount;
import com.lecent.park.mapper.CardCategoryDiscountMapper;
import com.lecent.park.service.ICardCategoryDiscountService;
import com.lecent.park.vo.CardCategoryDiscountVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 月卡套餐折扣信息 服务实现类
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Service
public class CardCategoryDiscountServiceImpl extends BaseServiceImpl<CardCategoryDiscountMapper, CardCategoryDiscount> implements ICardCategoryDiscountService {


	/**
	 * 自定义分页
	 *
	 * @param page                 page
	 * @param cardCategoryDiscount cardCategoryDiscount
	 * @return 分页列表
	 */
	@Override
	public IPage<CardCategoryDiscountVO> selectCardCategoryDiscountPage(IPage<CardCategoryDiscountVO> page, CardCategoryDiscountVO cardCategoryDiscount) {
		return page.setRecords(baseMapper.selectCardCategoryDiscountPage(page, cardCategoryDiscount));
	}


	/**
	 * 根据缴费月数获取套餐对应的折扣
	 *
	 * @param payMonthNum 续费月数
	 * @param categoryId  月卡套餐ID
	 * @return 折扣套餐
	 */
	@Override
	public CardCategoryDiscount discountByDuration(Integer payMonthNum, Long categoryId) {
		// 获取套餐折扣规则
		return this.getOne(Wrappers.<CardCategoryDiscount>lambdaQuery()
			.eq(CardCategoryDiscount::getCategoryId, categoryId)
			.le(CardCategoryDiscount::getDuration, payMonthNum)
			.orderByDesc(CardCategoryDiscount::getDuration)
			.last("limit 1"));
	}

}
