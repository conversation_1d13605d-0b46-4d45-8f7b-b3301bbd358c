package com.lecent.park.listener;

import com.lecent.park.core.mq.rabbitmq.MessageConstant;
import com.lecent.park.core.mq.rabbitmq.utils.ObjUtil;
import com.lecent.park.dto.ETCResultDTO;
import com.lecent.park.service.ClientService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_EXCHANGE_NAME;


/**
 * etc 支付回调监听
 *
 * <AUTHOR>
 */

@Component
@Slf4j
@Order
public class ETCPaymentMessageListener {

	/**
	 * etc 支付成功代码标识
	 */
	private static final int OK = 1000;
	@Autowired
	private ClientService clientService;

	/**
	 * etc支付回调监听处理
	 *
	 * @param message
	 */
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = MessageConstant.LECENT_PARK_UP_ETC_QUEUE, durable = "true")
		, exchange = @Exchange(value = LECENT_PARK_EXCHANGE_NAME
		, type = ExchangeTypes.TOPIC), key = MessageConstant.LECENT_PARKING_UP_ETC)})
	public void parkChannelMessageHandler(Message message) {

		try {
			ETCResultDTO etcResult = ObjUtil.toObjectMessage(message, ETCResultDTO.class, "utf-8");
			log.info("etc支付的交换机：{}，路由：{}，返回的信息：{}", message.getMessageProperties().getReceivedExchange(), message.getMessageProperties().getReceivedRoutingKey(), JsonUtil.toJson(etcResult));
			if (Func.isNotEmpty(etcResult) && OK == etcResult.getRespCode()) {
				clientService.resolveETCPay(etcResult.getTodoId());
			}
			if (Func.isNotEmpty(etcResult) && OK != etcResult.getRespCode()) {
				log.info("todoId：{}调用etc扣费失败，etc返回失败原因：{}", etcResult.getTodoId(), etcResult.getMsg());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

