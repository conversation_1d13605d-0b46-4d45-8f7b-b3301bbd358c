package com.lecent.park.controller;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.biz.IParklotQuickBuildBiz;
import com.lecent.park.controller.cloudsentry.res.ResParkLot;
import com.lecent.park.dto.*;
import com.lecent.park.dto.parklot.ParkLotBaseConfigDTO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.*;
import com.lecent.park.wrapper.ParklotWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.lecent.park.cache.ParkCacheNames.LECENT_PARK_CACHE;

/**
 * 车场信息表 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@Validated
@AllArgsConstructor
@RequestMapping("/park/parklot")
@Api(value = "车场信息表", tags = "车场信息表接口")
public class ParklotController extends BladeController {

	private IParklotService parklotService;
	private IParklotQuickBuildBiz parklotQuickBuildBiz;

	/**
	 * 根据ID查询（不限制租户）
	 */
	@Slave
	@TenantIgnore
	@GetMapping("/getByIdExcludeTenantId")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ID查询（不限制租户）", notes = "根据ID查询（不限制租户）")
	public R<Parklot> getByIdExcludeTenantId(Long id) {
		return R.data(parklotService.getById(id));
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入parklot")
	public R<ParklotVO> detail(Parklot parklot) {
		ParklotVO detail = parklotService.detail(Condition.getQueryWrapper(parklot));
		return R.data(ParklotWrapper.build().entityVO(detail));
	}

	/**
	 * 路内车场详情
	 */
	@GetMapping("/roadSideDetail")
	@ApiOperation(value = "路内车场详情", notes = "路内车场详情")
	public R<ParklotRoadSideVO> roadSideDetail(Long parklotId) {
		ParklotRoadSideVO detail = parklotService.roadSideDetail(parklotId);
		return R.data(detail);
	}

	/**
	 * 分页 车场信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入parklot")
	public R<IPage<ParklotVO>> list(Parklot parklot, Query query) {
		IPage<Parklot> pages = parklotService.page(Condition.getPage(query), Condition.getQueryWrapper(parklot));
		return R.data(ParklotWrapper.build().pageVO(pages));
	}

	/**
	 * 分页 车场信息表
	 */
	@GetMapping("/likeParentList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入parklot")
	public R<List<Parklot>> likeList(Parklot parklot) {
		QueryWrapper<Parklot> queryWrapper = Condition.getQueryWrapper(parklot);
		List<Parklot> list = parklotService.list(queryWrapper);
		if (Func.isNotEmpty(list) && list.size() > 0) {
			list.forEach(p -> p.setParentId(p.getId()));
		}
		return R.data(list);
	}

	/**
	 * 车场下拉框select列表
	 */
	@GetMapping("/dropDownList")
	@ApiOperation(value = "车场下拉框select列表", notes = "车场下拉框select列表")
	public R<List<Parklot>> dropDownList() {
		List<Parklot> list = parklotService.dropDownList();
		return R.data(list);
	}


	/**
	 * 查询车场信息
	 */
	@GetMapping("/queryParkLots")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取车场信息列表", notes = "传入parklot")
	public R<IPage<ResParkLot>> queryParkLots(Query query, String name, String labels, Integer parklotType) {
		IPage<Parklot> pages = parklotService.pageByName(Condition.getPage(query), name, labels, parklotType);

		IPage<ResParkLot> resPages = Condition.getPage(query);
		resPages.setTotal(pages.getTotal());
		if (Func.isEmpty(pages.getRecords())) {
			return R.data(resPages.setRecords(Collections.emptyList()));
		}

		List<ResParkLot> resParkLots = pages.getRecords().stream().map(parklot -> ResParkLot.builder().id(parklot.getId()).code(parklot.getParklotNo()).name(parklot.getName()).build()).collect(Collectors.toList());
		return R.data(resPages.setRecords(resParkLots));
	}

	/**
	 * 车场下拉框select列表   新增的都可用
	 */
	@GetMapping("/enableList")
	@ApiOperation(value = "车场下拉框select列表，新增的都可用", notes = "车场下拉框select列表，新增的都可用")
	public R<List<Parklot>> enableList() {
		List<Parklot> list = parklotService.enableList();
		return R.data(list);
	}


	/**
	 * 所有路外停车场列表
	 */
	@GetMapping("/roadsideParklotList")
	@ApiOperation(value = "路外停车场列表", notes = "路外停车场列表")
	public R<List<Parklot>> roadsideParklotList() {
		List<Parklot> list = parklotService.roadsideParklotList();
		return R.data(list);
	}

	/**
	 * 给某个用户分配的路外停车场列表
	 */
	@GetMapping("/userParklotOutList")
	@ApiOperation(value = "给某个用户分配的路外停车场列表", notes = "给某个用户分配的路外停车场列表")
	public R<List<Parklot>> userParklotOutList() {
		List<Parklot> list = parklotService.userParklotOutList(SecureUtil.getUserId());
		return R.data(list);
	}

	/**
	 * 路内停车场列表
	 */
	@GetMapping("/ParklotInList")
	@ApiOperation(value = "路内停车场列表", notes = "路内停车场列表")
	public R<List<Parklot>> ParklotInList() {
		List<Parklot> list = parklotService.ParklotInList();
		return R.data(list);
	}

	/**
	 * 用户停车场List
	 */
	@GetMapping("/userParkLotList")
	@ApiOperation(value = "用户停车场List", notes = "用户停车场List")
	public R userParkLotList() {
		List<Parklot> list = parklotService.userParkLotList();
		return R.data(list);
	}

	/**
	 * 用户停车场List
	 */
	@GetMapping("/findUserParklotIdList")
	@ApiOperation(value = "用户停车场List", notes = "用户停车场List")
	public R<List<ParklotVO>> findUserParklotIdList(ParklotDTO parklot) {
		List<ParklotVO> list = parklotService.findUserParklotIdList(parklot);
		return R.data(list);
	}

	/**
	 * 自定义分页 车场信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入parklot")
	public R<IPage<ParklotVO>> page(ParklotDTO parklot, Query query) {
		IPage<ParklotVO> pages = parklotService.selectParklotPage(Condition.getPage(query), parklot);
		return R.data(pages);
	}

	/**
	 * 物业端车场分页
	 */
	@GetMapping("/pageByPropertyMgr")
	@ApiOperation(value = "物业端车场分页", notes = "物业端车场分页")
	public R<IPage<ParklotVO>> pageByPropertyMgr(ParklotDTO parklot, Query query) {
		if (!SecureUtil.isAdministrator()) {
			parklot.setUserId(SecureUtil.getUserId());
		}

		IPage<ParklotVO> pages = parklotService.pageByPropertyMgr(Condition.getPage(query), parklot);
		return R.data(pages);
	}

	/**
	 * 导出追缴订单
	 */
	@PostMapping("/export")
	@ApiOperation(value = "导出追缴订单", notes = "传入recoveredOrder")
	public void exportParklot(HttpServletResponse response, ParklotDTO parklot) {
		if (!SecureUtil.isAdministrator()) {
			parklot.setUserId(SecureUtil.getUserId());
		}
		parklotService.exportParklot(response, parklot);
	}


	/**
	 * 新增 车场信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入parklot")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, key = "'parkLotList'")
	public R save(@Valid @RequestBody Parklot parklot) {
		return R.status(parklotService.save(parklot));
	}

	/**
	 * 新增 车场信息表
	 */
	@PostMapping("/saveParklot")
	@ApiOperation(value = "新增", notes = "传入parklot")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, key = "'parkLotList'")
	public R saveParklot(@Valid @RequestBody ParklotAddDTO parklot) {
		return R.status(parklotService.saveParklot(parklot));
	}

	/**
	 * 修改 车场信息表
	 */
	@PostMapping("/updateParklot")
	@ApiOperation(value = "修改", notes = "传入parklot")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, key = "'parkLotList'")
	public R updateParklot(@RequestBody ParklotEditDTO parklot) {
		return R.status(parklotService.updateParklot(parklot));
	}

	/**
	 * 保存更新
	 */
	@PostMapping("/saveUpdate")
	public R saveUpdate(@RequestBody ParklotEditDTO parklot) {
		if (parklot.getParentId() == null) {
			parklot.setParentId(0L);
		}
		return R.status(parklotService.saveOrUpdate(parklot));
	}

	/**
	 * 修改 车场信息表(运维端)
	 */
	@PostMapping("/updateParklotByOpt")
	@ApiOperation(value = "修改", notes = "传入parklot")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, key = "'parkLotList'")
	public R updateParklotByOpt(@Valid @RequestBody Parklot parklot) {
		return R.status(parklotService.updateParklotByOpt(parklot));
	}

	/**
	 * 配置收费规则
	 */
	@PostMapping("/updateChargeRule")
	@ApiOperation(value = "配置收费规则", notes = "配置收费规则")
	public R updateChargeRule(@Valid @RequestBody ParklotRuleDTO parklot) {
		return R.status(parklotService.updateChargeRule(parklot.getParklotId(), parklot.getTempParkingChargeRuleId(), parklot.getCardRuleIds()));
	}

	/**
	 * 配置收费规则，根据车型
	 */
	@PostMapping("/updateChargeRuleType")
	@ApiOperation(value = "配置收费规则，根据车型", notes = "配置收费规则，根据车型")
	public R updateChargeRuleType(@Valid @RequestBody ParklotRuleTypeDTO parklotRuleTypeDTO) {
		return R.status(parklotService.updateChargeRuleType(parklotRuleTypeDTO));
	}

	/**
	 * 修改 车场信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入parklot")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, key = "'parkLotList'")
	public R update(@Valid @RequestBody Parklot parklot) {
		return R.status(parklotService.updateById(parklot));
	}

	/**
	 * 新增或修改 车场信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入parklot")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, key = "'parkLotList'")
	public R submit(@Valid @RequestBody Parklot parklot) {
		return R.status(parklotService.saveOrUpdate(parklot));
	}

	/**
	 * 删除 车场信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, key = "'parkLotList'")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(parklotService.deleteLogic(Func.toLongList(ids)));
	}

	@GetMapping("/getCardRulesDropListById")
	@ApiOperation(value = "根据id获取月卡规则列表", notes = "根据id获取月卡规则列表")
	public R<List<CardRuleVO>> getCardRulesDropListById(@NotNull(message = "id 不能为空") Long id) {
		return R.data(parklotService.getCardRulesDropListById(id));
	}

	@GetMapping("/getParkPlaceTypeList")
	@ApiOperation(value = "获取停车场的车位类型列表", notes = "获取停车场的车位类型列表")
	public R<List<Map<String, String>>> getParkPlaceTypeList(Long parklotId) {
		return R.data(parklotService.getParkPlaceTypeList(parklotId));
	}

	/**
	 * 获取该车场剩余车位（楼层可配置的车位）
	 */
	@GetMapping("/getRemainAmountById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取该车场剩余车位（楼层可配置的车位）", notes = "获取该车场剩余车位（楼层可配置的车位）")
	public R<Integer> getRemainAmountById(Long id) {
		Integer remainAmount = parklotService.getRemainAmountByParklotId(id);
		return R.data(remainAmount);
	}

	/**
	 * 获取该车场剩余车位每一种类型都返回
	 */
	@GetMapping("/getAllRemainAmountById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取该车场剩余车位（楼层可配置的车位）", notes = "获取该车场剩余车位（楼层可配置的车位）")
	public R<RemainAmountVO> getAllRemainAmountById(Long id) {
		RemainAmountVO remainAmountVO = parklotService.getAllRemainAmountById(id);
		return R.data(remainAmountVO);
	}

	/**
	 * 获取该车场可开卡数量
	 */
	@GetMapping("/getRemainCardNumById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取该车场可开卡数量", notes = "获取该车场可开卡数量")
	public R<Integer> getRemainCardNumById(Long parklotId, Long typeId) {
		Integer remainAmount = parklotService.getRemainCardNumById(parklotId, typeId);
		return R.data(remainAmount);
	}

	/**
	 * 获取用户配置的车场
	 */
	@GetMapping("/getUserParklot")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取用户配置的车场", notes = "获取用户配置的车场")
	public R<UserParklotSettingVO> getUserParklot(Long userId) {
		return R.data(parklotService.getUserParklot(userId));
	}

	/**
	 * 获取用户所属公司下都有得父车场信息
	 */
	@GetMapping("/getFatherParklot")
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "获取用户所属公司下都有得父车场信息", notes = "获取用户所属公司下都有得父车场信息")
	public R<List<Parklot>> getFatherParklot(String tenantId) {
		return R.data(parklotService.getFatherParklot(tenantId));
	}


	/**
	 * 小程序获取车场信息
	 */
	@GetMapping("/getAppletsParklotList")
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "小程序获取车场信息", notes = "传入当前位置得经纬度")
	public R<List<ParklotVO>> getAppletsParklotList(@RequestParam("lat") double lat, @RequestParam("lng") double lng) {
		return R.data(parklotService.getAppletsParklotList(lat, lng));
	}

	/**
	 * 获取车场及其通道
	 */
	@GetMapping("/parklot-channel")
	@ApiOperation(value = "获取车场及其通道", notes = "获取车场及其通道")
	public R<List<ParklotVO>> getParklotChannel(Parklot parklot) {
		return R.data(parklotService.getParklotChannel(parklot));
	}


	/**
	 * 获取车场通道信息
	 */
	@GetMapping("/parklot-channel-info")
	@ApiOperation(value = "获取车场通道信息", notes = "获取车场通道信息")
	public R<IPage<ParklotChannelVO>> parklotChannelInfo(ParklotChannelVO parklotChannelVO, Query query) {
		return R.data(parklotService.parklotChannelInfoPage(Condition.getPage(query), parklotChannelVO));
	}


	/**
	 * 轻松建车场通道
	 */
	@PostMapping("/easyBuildParkLot")
	@ApiOperationSupport(order = 25)
	@ApiOperation(value = "轻松建车场通道", notes = "传入parklot")
	public R easyBuildParkLot(@Validated(EasyBuildParkLot.saveValid.class) @RequestBody EasyBuildParkLot easyBuildParkLot) {
		return R.data(this.parklotQuickBuildBiz.easyBuildParkLot(easyBuildParkLot));
	}

	/**
	 * 批量创建车场
	 */
	@PostMapping("/batchAddParklot")
	@ApiOperationSupport(order = 25)
	@ApiOperation(value = "批量新增车场", notes = "传入parklotList")
	public R batchAddParklot(@RequestBody List<EasyBuildParkLot> batchAddParklot) {
		return R.data(this.parklotQuickBuildBiz.batchAddParklot(batchAddParklot));
	}

	/**
	 * 获取车场通道信息
	 */
	@GetMapping("/getParkLotChannelInfo")
	@ApiOperationSupport(order = 25)
	@ApiOperation(value = "获取车场通道信息", notes = "传入parkLotId")
	public R getParkLotChannelInfo(String parkLotIds) {
		return R.data(this.parklotQuickBuildBiz.getParkLotChannelInfo(parkLotIds));
	}

	/**
	 * 修改运营配置
	 */
	@PostMapping("/update-operating-config")
	@ApiOperationSupport(order = 26)
	@ApiOperation(value = "修改运营配置", notes = "传入newParklot")
	public R<Boolean> updateOperatingConfig(@RequestBody Parklot newParklot) {
		Parklot parklot = parklotService.getParkLotById(newParklot.getId());
		LecentAssert.notNull(parklot, "车场信息不存在");

		parklot.setIsScanSub(newParklot.getIsScanSub());
		parklot.setScanSubStartTime(newParklot.getScanSubStartTime());
		parklot.setScanSubEndTime(newParklot.getScanSubEndTime());
		parklot.setIsScanSubLimitTime(newParklot.getIsScanSubLimitTime());
		return R.status(parklotService.unifySaveOrUpdate(parklot));
	}


	/**
	 * 车场信息表(客服调用)
	 */
	@GetMapping("/getParkingLotList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "车场信息表(客服调用)", notes = "传入name")
	public R<List<Parklot>> getParkingLotList(@RequestParam(required = false) String name) {
		return R.data(this.parklotService.getParkingLotList(name));
	}

	/**
	 * 查询车场列表(客服调用)
	 */
	@GetMapping("/queryParkLotList")
	@ApiOperation(value = "查询车场列表(客服调用)")
	public R<List<ParklotVO>> queryParkLotList(CscCommonQueryParam queryParam) {
		return R.data(parklotService.queryParkLotList(queryParam));
	}

	/**
	 * 修改基础配置
	 */
	@PostMapping("/update-base-config")
	@ApiOperationSupport(order = 26)
	@ApiOperation(value = "修改基础配置", notes = "传入 configDTO")
	public R updateParkLotBaseConfig(@RequestBody ParkLotBaseConfigDTO configDTO) {
		return R.status(parklotService.updateParkLotBaseConfig(configDTO));
	}

	/**
	 * 查询车场列表(客服调用)
	 */
	@GetMapping("/getTenantList")
	@ApiOperation(value = "查询车场列表(客服调用)")
	public R<List<Map<String, Object>>> getTenantList(@RequestParam(value = "name", required = false) String name) {
		return R.data(parklotService.getTenantList(name));
	}

	/**
	 * 根据停车场ID查询授权的用户（客服系统）
	 */
	@GetMapping("/getUserIdByParkLotId")
	@ApiOperation(value = "根据停车场ID查询授权的用户(客服系统)", notes = "用户ID")
	public List<String> getUserIdByParkLotId(@RequestParam(value = "parkLotId") String parkLotId) {
		return parklotService.getUserIdByParkLotId(parkLotId);
	}

	/**
	 * 根据停车场ID查询授权的用户（客服系统）
	 */
	@GetMapping("/getMonthCardConfig")
	@ApiOperation(value = "根据车场id查询月开开通配置状态", notes = "车场id")
	public R getMonthCardConfig(@RequestParam(value = "parkLotIds") String parkLotIds) {
		if (StringUtils.isBlank(parkLotIds)) {
			return R.fail("请求参数车场id[parkLotIds]不能为空");
		}
		return R.data(parklotService.getMonthCardConfig(parkLotIds));
	}

	@GetMapping("/getParkingPlace")
	@ApiOperation(value = "根据Id查询停车位(去租户)", notes = "车场id")
	public R getParkingPlace(@RequestParam(value = "parkLotId" ,required = false) String parkLotId) {
		return R.data(parklotService.getParkingPlace(parkLotId));
	}

	/**
	 * 修改月卡配置
	 */
	@PostMapping("/updateMonthCardConfig")
	@ApiOperationSupport(order = 26)
	@ApiOperation(value = "修改月卡配置", notes = "传入 parklotDTO")
	public R updateMonthCardConfig(@RequestBody ParklotDTO parklotDTO) {
		Parklot parklot = this.parklotService.getById(parklotDTO.getId());
		if (null == parklot) {
			throw new ServiceException("未查询到车场信息！");
		}
		parklot.setIsOwnerSelect(parklotDTO.getIsOwnerSelect());
		parklot.setCommunityId(parklotDTO.getCommunityId());
		return R.status(parklotService.saveOrUpdate(parklot));
	}

	@PostMapping("/updatePushConfig")
	@ApiOperation(value = "修改推送配置", notes = "传入 Parklot")
	public R updatePushConfig(@RequestBody Parklot parklot) {
		return R.status(parklotService.updatePushConfig(parklot));
	}
	@GetMapping("/getListExcludeTenantId")
	public R<List<ParklotVO>> findParklotListExcludeTenantId(ParklotVO parklotVO){
		return R.data(parklotService.findParklotListExcludeTenantId(parklotVO));

	}
}
