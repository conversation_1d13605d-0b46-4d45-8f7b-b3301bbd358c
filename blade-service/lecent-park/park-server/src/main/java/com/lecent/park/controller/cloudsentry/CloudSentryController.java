package com.lecent.park.controller.cloudsentry;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.controller.cloudsentry.res.ResChannel;
import com.lecent.park.controller.cloudsentry.res.ResParkLot;
import com.lecent.park.entity.Channel;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IChannelService;
import com.lecent.park.service.IParklotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车场信息表 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@Validated
@AllArgsConstructor
@RequestMapping("/cloud-sentry/")
@Api(value = "车场信息表", tags = "车场信息表接口")
public class CloudSentryController extends BladeController {

	private IParklotService parklotService;

	private IChannelService channelService;

	/**
	 * 详情
	 */
	@GetMapping("/parklots")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取车场信息列表", notes = "传入parklot")
	public R<IPage<ResParkLot>> detail(Query query, @RequestParam("name") String name) {
		IPage<Parklot> pages = parklotService.pageByName(Condition.getPage(query), name, "", null);

		IPage<ResParkLot> resPages = Condition.getPage(query);
		resPages.setTotal(pages.getTotal());
		if (Func.isEmpty(pages.getRecords())) {
			return R.data(resPages.setRecords(Collections.emptyList()));
		}

		List<Parklot> parklots = pages.getRecords();
		// get lotIds
		List<Long> parklotIds = parklots.stream().map(Parklot::getId).collect(Collectors.toList());

		// get channels
		List<Channel> channels = channelService.listByIds(parklotIds);

		// group by channelId
		Map<Long, List<Channel>> channelMap = channels.stream().collect(Collectors.groupingBy(Channel::getParklotId));

		List<ResParkLot> resParkLots = parklots.stream()
			.map(parklot -> {
				List<Channel> channels1 = channelMap.get(parklot.getId());

				List<ResChannel> resChannels = channels1.stream().map(channel -> ResChannel.builder()
					.id(channel.getId())
					.code(String.valueOf(channel.getChannelNo()))
					.name(channel.getName())
					.build()).collect(Collectors.toList());

				return ResParkLot.builder()
					.id(parklot.getId())
					.code(parklot.getParklotNo())
					.name(parklot.getName())
					.channels(resChannels)
					.build();

			}).collect(Collectors.toList());
		return R.data(resPages.setRecords(resParkLots));
	}

}
