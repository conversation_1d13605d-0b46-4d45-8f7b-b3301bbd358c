package com.lecent.park.listener;

import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.service.IReserveParkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ReserveOrderExpirationListener extends KeyExpirationEventMessageListener {
	@Autowired
	private IReserveParkService reserveParkService;

	public ReserveOrderExpirationListener(RedisMessageListenerContainer listenerContainer) {
		super(listenerContainer);
	}

	@Override
	public void onMessage(Message message, byte[] pattern) {
		final String expiredKey = message.toString();
		String[] value = expiredKey.split("::");
		// 如果过期的key是预约订单的key
		if (value.length == 2) {
			if (value[0].equals(ParkCacheNames.LECENT_PARK_RESERVE_ORDER)) {
				reserveParkService.expiringReserve(value[1]);
			} else if (value[0].equals(ParkCacheNames.LECENT_PARK_RESERVE_PAY_ORDER)) {
				reserveParkService.expiringWxPayOrder(value[1]);
			}
		}
	}
}
