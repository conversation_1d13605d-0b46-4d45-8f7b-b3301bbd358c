package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.FirstParkingConfig;
import com.lecent.park.vo.FirstParkingConfigVO;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;

/**
 * 首停配置 服务类
 *
 * <AUTHOR>
 * @since 2024-10-01
 */
public interface IFirstParkingConfigService extends BaseService<FirstParkingConfig> {

    /**
     * 自定义分页
     *
     * @param page               页
     * @param firstParkingConfig 首停配置
     * @return 分页结果
     */
    IPage<FirstParkingConfigVO> selectFirstParkingConfigPage(IPage<FirstParkingConfigVO> page, FirstParkingConfigVO firstParkingConfig);

    /**
     * 根据ID获取详情
     *
     * @param id 配置ID
     * @return 首停配置详情
     */
    FirstParkingConfigVO getDetailById(Long id);

    /**
     * 提交首停配置
     *
     * @param firstParkingConfig 首停配置
     * @return 是否成功
     */
    boolean submit(FirstParkingConfig firstParkingConfig);

    /**
     * 判断是否是首次停车
     *
     * @param firstParkingConfigId 首停配置ID
     * @param plate                车牌号
     * @param enterTime            进场时间
     * @return 是否是首次停车
     */
    boolean isFirstParking(Long firstParkingConfigId, String plate, Date enterTime);
}
