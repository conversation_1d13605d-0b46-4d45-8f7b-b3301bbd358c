package com.lecent.park.wrapper;

import com.lecent.park.entity.EmployeeCar;
import com.lecent.park.vo.EmployeeCarVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 员工车辆包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
public class EmployeeCarWrapper extends BaseEntityWrapper<EmployeeCar, EmployeeCarVO>  {

	public static EmployeeCarWrapper build() {
		return new EmployeeCarWrapper();
 	}

	@Override
	public EmployeeCarVO entityVO(EmployeeCar employeeCar) {
		EmployeeCarVO employeeCarVO = Objects.requireNonNull(BeanUtil.copy(employeeCar, EmployeeCarVO.class));

		//User createUser = UserCache.getUser(employeeCar.getCreateUser());
		//User updateUser = UserCache.getUser(employeeCar.getUpdateUser());
		//employeeCarVO.setCreateUserName(createUser.getName());
		//employeeCarVO.setUpdateUserName(updateUser.getName());

		return employeeCarVO;
	}

}
