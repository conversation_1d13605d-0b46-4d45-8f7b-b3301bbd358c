package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParkingRecoveryConfig;
import com.lecent.park.vo.ParkingRecoveryConfigVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 停车追缴配置 服务类
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
public interface IParkingRecoveryConfigService extends BaseService<ParkingRecoveryConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page                  页
	 * @param parkingRecoveryConfig 停车追缴配置
	 * @return {@link IPage}<{@link ParkingRecoveryConfigVO}>
	 */
	IPage<ParkingRecoveryConfigVO> selectParkingRecoveryConfigPage(IPage<ParkingRecoveryConfigVO> page, ParkingRecoveryConfigVO parkingRecoveryConfig);

	/**
	 * 查询停车追缴配置
	 *
	 * @param parklotId    停车场id
	 * @return {@link ParkingRecoveryConfig}
	 */
	ParkingRecoveryConfig getByParklotId(Long parklotId);
}
