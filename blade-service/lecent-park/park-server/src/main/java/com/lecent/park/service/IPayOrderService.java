package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.PayOrder;
import com.lecent.park.vo.PayOrderVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 预约车位表 服务类
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
public interface IPayOrderService extends BaseService<PayOrder> {

	/**
	 * 自定义分页
	 *
	 * @param pagez
	 * @param reservePayOrder
	 * @return
	 */
	IPage<PayOrderVO> selectReservePayOrderPage(IPage<PayOrderVO> page, PayOrderVO reservePayOrder);

	PayOrder getOneByPayOrderId(String payOrderId);
}
