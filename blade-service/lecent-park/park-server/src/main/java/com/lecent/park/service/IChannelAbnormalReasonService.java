package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ChannelAbnormalReason;
import com.lecent.park.vo.ChannelAbnormalReasonVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 通道异常原因信息表 服务类
 *
 * <AUTHOR>
 * @since 2020-05-30
 */
public interface IChannelAbnormalReasonService extends BaseService<ChannelAbnormalReason> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param channelAbnormalReason
	 * @return
	 */
	IPage<ChannelAbnormalReasonVO> selectChannelAbnormalReasonPage(IPage<ChannelAbnormalReasonVO> page,
																   ChannelAbnormalReasonVO channelAbnormalReason);

	boolean submit(ChannelAbnormalReason channelAbnormalReason);

	List<ChannelAbnormalReason> selectList(String tenantId);

	List<ChannelAbnormalReason> listOutReason(ChannelAbnormalReason channelAbnormalReason);

	List<ChannelAbnormalReason> listReason();
}
