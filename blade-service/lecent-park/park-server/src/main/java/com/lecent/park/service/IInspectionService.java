package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.Inspection;
import com.lecent.park.vo.InspectionVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * 巡检记录表 服务类
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
public interface IInspectionService extends BaseService<Inspection> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspection
	 * @return
	 */
	IPage<InspectionVO> selectInspectionPage(IPage<InspectionVO> page, InspectionVO inspection);

	 List<InspectionVO> inspectionChannelList(Long channelId);

	 R report(List<InspectionVO> inspections);

	List<InspectionVO> inspectionUserList();
}
