package com.lecent.park.wrapper;

import com.lecent.park.entity.CorpseCleanLog;
import com.lecent.park.vo.CorpseCleanLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 设置设置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
public class CorpseCleanLogWrapper extends BaseEntityWrapper<CorpseCleanLog, CorpseCleanLogVO>  {

	public static CorpseCleanLogWrapper build() {
		return new CorpseCleanLogWrapper();
 	}

	@Override
	public CorpseCleanLogVO entityVO(CorpseCleanLog corpseCleanLog) {
		CorpseCleanLogVO corpseCleanLogVO = BeanUtil.copy(corpseCleanLog, CorpseCleanLogVO.class);
		return corpseCleanLogVO;
	}


}
