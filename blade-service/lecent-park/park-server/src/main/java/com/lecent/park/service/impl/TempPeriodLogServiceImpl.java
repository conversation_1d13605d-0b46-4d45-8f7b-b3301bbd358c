package com.lecent.park.service.impl;

import com.lecent.park.entity.TempPeriodLog;
import com.lecent.park.mapper.TempPeriodLogMapper;
import com.lecent.park.service.ITempPeriodLogService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 修改车牌临停记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class TempPeriodLogServiceImpl extends BaseServiceImpl<TempPeriodLogMapper, TempPeriodLog> implements ITempPeriodLogService {

	@Override
	public List<TempPeriodLog> selectTempPeriodLogList(String plate) {
		return baseMapper.selectTempPeriodLogList(plate);
	}
}
