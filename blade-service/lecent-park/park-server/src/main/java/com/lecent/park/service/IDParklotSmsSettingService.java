package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParklotSmsSetting;
import com.lecent.park.event.parking.NoticeTempOrderUnPayEvent;
import com.lecent.park.vo.ParklotSmsSettingVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 车场移动短信配置表 服务类
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
public interface IDParklotSmsSettingService extends BaseService<ParklotSmsSetting> {

	/**
	 * 自定义分页
	 *
	 * @param page               分页对象
	 * @param dParklotSmsSetting 车场短信催缴配置对象
	 * @return 分页结果对象
	 */
	IPage<ParklotSmsSettingVO> selectDParklotSmsSettingPage(IPage<ParklotSmsSettingVO> page, ParklotSmsSettingVO dParklotSmsSetting);

	/**
	 * 添加车场短信催缴配置
	 *
	 * @param parklotSmsSettingVO 车场短信催缴配置对象
	 * @return 添加是否成功
	 */
	boolean add(ParklotSmsSettingVO parklotSmsSettingVO);

	/**
	 * 新增或修改车场短信催缴配置
	 *
	 * @param parklotSmsSettingVO 车场短信催缴配置对象
	 * @return 新增或修改是否成功
	 */
	boolean addCall(ParklotSmsSettingVO parklotSmsSettingVO);

	/**
	 * 获取车场短信催缴配置详情
	 *
	 * @param parklotId 车场ID
	 * @return 车场短信催缴配置详情对象
	 */
	ParklotSmsSettingVO getDateDetail(Long parklotId);

	/**
	 * 根据车场ID获取车场短信催缴配置列表
	 *
	 * @param parklotId 车场ID
	 * @return 车场短信催缴配置列表
	 */
	List<ParklotSmsSetting> getByParklotId(Long parklotId);

	/**
	 * 判断是否发送短信
	 *
	 * @param event 未支付通知事件对象
	 */
	void isSendSms(NoticeTempOrderUnPayEvent event);
}
