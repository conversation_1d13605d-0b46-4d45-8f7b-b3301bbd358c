package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.InvoiceConfig;
import com.lecent.park.vo.InvoiceConfigVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 车场-开票相关配置表 服务类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface IInvoiceConfigService extends BaseService<InvoiceConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param invoiceConfig
	 * @return
	 */
	IPage<InvoiceConfigVO> selectInvoiceConfigPage(IPage<InvoiceConfigVO> page, InvoiceConfigVO invoiceConfig);

	/**
	 * 根据车场信息查询车场配置
	 *
	 * @param parkLotId
	 * @return
	 */
	InvoiceConfig queryConfigByParkLotId(String parkLotId);
}
