package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParklotSpecialPlate;
import com.lecent.park.vo.ParklotSpecialPlateVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-04-21
 */
public interface IParklotSpecialPlateService extends BaseService<ParklotSpecialPlate> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotSpecialPlate
	 * @return
	 */
	IPage<ParklotSpecialPlateVO> selectParklotSpecialPlatePage(IPage<ParklotSpecialPlateVO> page, ParklotSpecialPlateVO parklotSpecialPlate);

	/**
	 * @param parklotSpecialPlate 特殊车辆参数
	 * @return 成功/失败
	 */
	Boolean submit(ParklotSpecialPlate parklotSpecialPlate);

	/**
	 * @param specialCarName 特殊车牌名称
	 * @param id             ID
	 * @param parklotId      车场id
	 * @return 条数
	 */
	int countBySpecialCarName(String specialCarName, Long id, Long parklotId);

	/**
	 * @param plateRule 车牌规则
	 * @param id        id
	 * @return 条数
	 */
	int getCountByPlateRule(String plateRule, Long id, Long parklotId);

	/**
	 * 根据车场ID查询特殊匹配车牌
	 *
	 * @param parkLotId
	 * @return
	 */
	List<ParklotSpecialPlate> selectByParkLotId(Long parkLotId);

	/**
	 * 查询是否是特殊车辆
	 *
	 * @param parkLotId 车场ID
	 * @param plate     车牌
	 * @return bean
	 */
	ParklotSpecialPlate matchSpecialCard(Long parkLotId, String plate);

	/**
	 * 删除
	 *
	 * @param ids ids
	 * @return tf
	 */
	Boolean customDeleteLogic(List<Long> ids);
}
