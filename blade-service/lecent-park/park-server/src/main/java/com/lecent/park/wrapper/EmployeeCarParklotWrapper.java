package com.lecent.park.wrapper;

import com.lecent.park.entity.EmployeeCarParklot;
import com.lecent.park.vo.EmployeeCarParklotVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 员工车辆关联车场表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
public class EmployeeCarParklotWrapper extends BaseEntityWrapper<EmployeeCarParklot, EmployeeCarParklotVO>  {

	public static EmployeeCarParklotWrapper build() {
		return new EmployeeCarParklotWrapper();
 	}

	@Override
	public EmployeeCarParklotVO entityVO(EmployeeCarParklot employeeCarParklot) {
		EmployeeCarParklotVO employeeCarParklotVO = Objects.requireNonNull(BeanUtil.copy(employeeCarParklot, EmployeeCarParklotVO.class));

		//User createUser = UserCache.getUser(employeeCarParklot.getCreateUser());
		//User updateUser = UserCache.getUser(employeeCarParklot.getUpdateUser());
		//employeeCarParklotVO.setCreateUserName(createUser.getName());
		//employeeCarParklotVO.setUpdateUserName(updateUser.getName());

		return employeeCarParklotVO;
	}

}
