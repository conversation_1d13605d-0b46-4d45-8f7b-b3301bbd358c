package com.lecent.park.bizoptlog;


import org.springblade.core.mp.base.BaseService;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AroundOptHeader {
	/**
	 * 变更头部信息服务类
	 *
	 * @return
	 */
	Class<? extends BaseService> headerClass() default BaseService.class;

	/**
	 * 头部id
	 *
	 * @return
	 */
	String headerId() default "";

	/**
	 * 头部名称
	 *
	 * @return
	 */
	String headerName() default "";


	/**
	 * 排序
	 *
	 * @return
	 */
	int order() default Integer.MIN_VALUE;

}
