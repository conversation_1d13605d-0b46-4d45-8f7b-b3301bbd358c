package com.lecent.park.strategy.cardperiod.handler;

import com.lecent.park.en.card.CardPeriodTypeEnum;
import com.lecent.park.strategy.cardperiod.CardPeriodPeriodStrategy;
import com.lecent.park.strategy.cardperiod.impl.CardPeriodDay2DayEarlyEndStrategy;
import com.lecent.park.strategy.cardperiod.impl.CardPeriodDay2DayStrategy;
import com.lecent.park.strategy.cardperiod.impl.CardPeriodNaturalDaysStrategy;
import com.lecent.park.strategy.cardperiod.impl.CardPeriodNaturalMonthStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * 月卡时段处理handler
 */
@AllArgsConstructor
@Component
public class CardPeriodHandler {

	private static final Map<Integer, CardPeriodPeriodStrategy> map = new ConcurrentHashMap<>();


	private final CardPeriodNaturalMonthStrategy cardNaturalMonthStrategy;
	private final CardPeriodDay2DayStrategy cardDay2DayStrategy;
	private final CardPeriodDay2DayEarlyEndStrategy cardDay2DayEarlyEndStrategy;
	private final CardPeriodNaturalDaysStrategy cardManyDaysStrategy;

	@PostConstruct
	public void init() {
		map.put(CardPeriodTypeEnum.DAY_2_DAY.getRenewWay(), cardDay2DayStrategy);
		map.put(CardPeriodTypeEnum.NATURAL_MONTH.getRenewWay(), cardNaturalMonthStrategy);
		map.put(CardPeriodTypeEnum.DAY_2_DAY_EARLY_END.getRenewWay(), cardDay2DayEarlyEndStrategy);
		map.put(CardPeriodTypeEnum.MANY_DAYS.getRenewWay(), cardManyDaysStrategy);
	}

	public CardPeriodPeriodStrategy getHandler(Integer renewType) {
		return map.get(renewType);
	}
}
