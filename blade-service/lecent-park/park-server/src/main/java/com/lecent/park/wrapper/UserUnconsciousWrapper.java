package com.lecent.park.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.lecent.park.entity.BVehicleUnconsciousRelation;
import com.lecent.park.vo.BVehicleUnconsciousRelationVO;
import java.util.Objects;

/**
 * 用户无感支付信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public class UserUnconsciousWrapper extends BaseEntityWrapper<BVehicleUnconsciousRelation, BVehicleUnconsciousRelationVO>  {

	public static UserUnconsciousWrapper build() {
		return new UserUnconsciousWrapper();
 	}

	@Override
	public BVehicleUnconsciousRelationVO entityVO(BVehicleUnconsciousRelation bVehicleUNconsciousRelation) {
		BVehicleUnconsciousRelationVO userUnconsciousVO = Objects.requireNonNull(BeanUtil.copy(bVehicleUNconsciousRelation, BVehicleUnconsciousRelationVO.class));

		//User createUser = UserCache.getUser(userUnconscious.getCreateUser());
		//User updateUser = UserCache.getUser(userUnconscious.getUpdateUser());
		//userUnconsciousVO.setCreateUserName(createUser.getName());
		//userUnconsciousVO.setUpdateUserName(updateUser.getName());

		return userUnconsciousVO;
	}

}
