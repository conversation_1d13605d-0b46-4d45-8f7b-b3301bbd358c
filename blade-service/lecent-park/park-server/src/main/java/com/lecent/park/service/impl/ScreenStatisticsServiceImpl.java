package com.lecent.park.service.impl;

/**
 * @Description： 盘州大屏展示
 * <AUTHOR>
 * @Date: 2020/6/11 11:34
 */

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.ParkingStatus;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ScreenParklot;
import com.lecent.park.service.*;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class ScreenStatisticsServiceImpl implements IScreenStatisticsService {

	private IParkingOrderService parkingOrderService;
	private IIllegalOrderService iIllegalOrderService;
	private IParklotService parklotService;
	private ICalculateParkRemainNumberService calculateParkRemainNumberService;
	private IUserParklotService userParklotService;


	/**
	 * 近7天流量分析
	 *
	 * @param parklotId 车场id
	 * @return List<ParkingOrderStatisticsVO>
	 */
	@Override
	public ParkLastWeekVO lastWeekParkingOrder(String parklotId) {
		return parkingOrderService.lastWeekParkingOrder(parklotId);
	}

	/**
	 * 剩余车位最少top5
	 *
	 * @return
	 */
	@Override
	public List<PlaceRemainNumVO> remainPlaceTopFive() {
		List<PlaceRemainNumVO> result = new ArrayList<>();

		List<Long> parkLotIds = userParklotService.getParkLotIds(SecureUtil.getUserId());
		List<Parklot> parklotList = parklotService.listByIds(parkLotIds);

		parklotList.forEach(p -> {
			PlaceRemainNumVO vo = new PlaceRemainNumVO();
			vo.setParklotName(p.getName());
			vo.setTempNum(p.getTempLotAmount());

			int inParkNum = parkingOrderService.count(Wrappers.<ParkingOrder>lambdaQuery()
				.eq(ParkingOrder::getParkingStatus, ParkingStatus.SPRKING_PRESENT)
				.eq(ParkingOrder::getParklotId, p.getId()));

			vo.setRemainNum(p.getTempLotAmount() - inParkNum);
			result.add(vo);
		});

		result.sort(Comparator.comparingInt(PlaceRemainNumVO::getRemainNum));
		if (result.size() <= 5) {
			return result;
		}
		return result.subList(0, 5);
	}

	/**
	 * 停车时段分析
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	@Override
	public List<StopDurationVO> stopDuration(String parklotId) {
		//获取车场不同时段的出入场记录数量
		List<StopDurationVO> list = parkingOrderService.stopDuration(parklotId);
		int duration1 = 0;
		int duration2 = 0;
		int duration3 = 0;
		int duration4 = 0;
		int duration5 = 0;
		int duration6 = 0;
		//获取每个时间的的总停车记录数
		for (StopDurationVO stopDurationVO : list) {
			int hour = Integer.parseInt(stopDurationVO.getName());
			if (hour >= 0 && hour < 4) {
				duration1 += stopDurationVO.getValue();
			}
			if (hour >= 4 && hour < 8) {
				duration2 += stopDurationVO.getValue();
			}
			if (hour >= 8 && hour < 12) {
				duration3 += stopDurationVO.getValue();
			}
			if (hour >= 12 && hour < 16) {
				duration4 += stopDurationVO.getValue();
			}
			if (hour >= 16 && hour < 20) {
				duration5 += stopDurationVO.getValue();
			}
			if (hour >= 20 && hour < 24) {
				duration6 += stopDurationVO.getValue();
			}
		}

		List<StopDurationVO> result = new ArrayList<>();

		StopDurationVO vo1 = new StopDurationVO();
		vo1.setName("00:00-04:00");
		vo1.setValue(duration1);
		result.add(vo1);

		StopDurationVO vo2 = new StopDurationVO();
		vo2.setName("04:00-08:00");
		vo2.setValue(duration2);
		result.add(vo2);

		StopDurationVO vo3 = new StopDurationVO();
		vo3.setName("08:00-12:00");
		vo3.setValue(duration3);
		result.add(vo3);

		StopDurationVO vo4 = new StopDurationVO();
		vo4.setName("12:00-16:00");
		vo4.setValue(duration4);
		result.add(vo4);

		StopDurationVO vo5 = new StopDurationVO();
		vo5.setName("16:00-20:00");
		vo5.setValue(duration5);
		result.add(vo5);

		StopDurationVO vo6 = new StopDurationVO();
		vo6.setName("20:00-24:00");
		vo6.setValue(duration6);
		result.add(vo6);

		return result;
	}

	/**
	 * 收入环比
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	@Override
	public FinancialRate financialRate(String parklotId) {
		//当月总收入
		BigDecimal currentMonthPaidAmount = parkingOrderService.getCurrentMonthPaidAmount(parklotId);
		//上个月总收入
		BigDecimal lastMonthPaidAmount = parkingOrderService.getLastMonthPaidAmount(parklotId);
		//今年总收入
		BigDecimal currentYearPaidAmount = parkingOrderService.getCurrentYearPaidAmount(parklotId);
		//去年总收入
		BigDecimal lastYearPaidAmount = parkingOrderService.getLastYearPaidAmount(parklotId);
		//当月异常金额
		BigDecimal currentMonthOweAmount = parkingOrderService.getCurrentMonthOweAmount(parklotId);
		//上个月异常金额
		BigDecimal lastMonthOweAmount = parkingOrderService.getLastMonthOweAmount(parklotId);
		//今年异常金额
		BigDecimal currentYearOweAmount = parkingOrderService.getCurrentYearOweAmount(parklotId);
		//去年异常金额
		BigDecimal lastYearOweAmount = parkingOrderService.getLastYearOweAmount(parklotId);


		FinancialRate financialRate = new FinancialRate();
		if (lastMonthPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
			financialRate.setIncomeMon("---");
		} else {
			String incomeMon = currentMonthPaidAmount.subtract(lastMonthPaidAmount).divide(lastMonthPaidAmount, 2).toString();
			//上个月收入环比
			financialRate.setIncomeMon(incomeMon);
		}

		if (lastYearPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
			financialRate.setIncomeYoy("----");
		} else {
			String incomeYoy = currentYearPaidAmount.subtract(lastYearPaidAmount).divide(lastYearPaidAmount, 2).toString();
			//去年收入环比
			financialRate.setIncomeYoy(incomeYoy);
		}

		if (lastMonthOweAmount.compareTo(BigDecimal.ZERO) == 0) {
			financialRate.setOweMon("----");
		} else {
			String oweMon = currentMonthOweAmount.subtract(lastMonthOweAmount).divide(lastMonthOweAmount, 2).toString();
			//上个月异常金额环比
			financialRate.setOweMon(oweMon);
		}
		if (lastYearOweAmount.compareTo(BigDecimal.ZERO) == 0) {
			financialRate.setOweYoy("----");
		} else {
			String oweYoy = currentYearOweAmount.subtract(lastYearOweAmount).divide(lastYearOweAmount, 2).toString();
			//去年收入环比
			financialRate.setOweYoy(oweYoy);
		}
		return financialRate;
	}


	/**
	 * 泊位停车饱和度
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	@Override
	public ParkStopRateVO parkStopRate(String parklotId) {
		ParkStopRateVO result = new ParkStopRateVO();
		List<String> hours = new ArrayList<>();
		hours.add(DateUtils.getBeforeDateHourStr(7, DateUtils.getCloseHour()));
		hours.add(DateUtils.getBeforeDateHourStr(6, DateUtils.getCloseHour()));
		hours.add(DateUtils.getBeforeDateHourStr(5, DateUtils.getCloseHour()));
		hours.add(DateUtils.getBeforeDateHourStr(4, DateUtils.getCloseHour()));
		hours.add(DateUtils.getBeforeDateHourStr(3, DateUtils.getCloseHour()));
		hours.add(DateUtils.getBeforeDateHourStr(2, DateUtils.getCloseHour()));
		hours.add(DateUtils.getBeforeDateHourStr(1, DateUtils.getCloseHour()));
		hours.add(DateUtils.getBeforeDateHourStr(0, DateUtils.getCloseHour()));
		result.setXAxisData(hours);
		List<Double> numList = new ArrayList<>();
		int tempNum = 0;
		List<Parklot> list;
		List<Long> parkLotIds = new ArrayList<>();
		if (Func.isNotBlank(parklotId)) {
			Parklot parklot = ParkLotCaches.getParkLot(parklotId);
			LecentAssert.notNull(parklot, "不存在此车场");
			result.setParkName(parklot.getName());
			list = Collections.singletonList(parklot);
			parkLotIds.add(Long.valueOf(parklotId));
		} else {
			parkLotIds = userParklotService.getParkLotIds(SecureUtil.getUserId());
			list = parklotService.listByIds(parkLotIds);
			result.setParkName("所有");
		}
		if (Func.isNotEmpty(list)) {
			tempNum = list.stream().mapToInt(Parklot::getTempLotAmount).sum();
		}
		if (tempNum == 0) {
			for (int i = 0; i < 8; i++) {
				numList.add((double) i);
			}
			result.setSeriesData(numList);
			return result;
		}

		int numInPark7 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getBeforeDateHour(7, DateUtils.getCloseHour()));
		int numInPark6 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getBeforeDateHour(6, DateUtils.getCloseHour()));
		int numInPark5 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getBeforeDateHour(5, DateUtils.getCloseHour()));
		int numInPark4 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getBeforeDateHour(4, DateUtils.getCloseHour()));
		int numInPark3 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getBeforeDateHour(3, DateUtils.getCloseHour()));
		int numInPark2 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getBeforeDateHour(2, DateUtils.getCloseHour()));
		int numInPark1 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getBeforeDateHour(1, DateUtils.getCloseHour()));
		int numInPark0 = parkingOrderService.getNumInPark(parkLotIds, DateUtils.getCloseHour());

		numList.add(BigDecimal.valueOf(numInPark7)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		numList.add(BigDecimal.valueOf(numInPark6)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		numList.add(BigDecimal.valueOf(numInPark5)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		numList.add(BigDecimal.valueOf(numInPark4)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		numList.add(BigDecimal.valueOf(numInPark3)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		numList.add(BigDecimal.valueOf(numInPark2)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		numList.add(BigDecimal.valueOf(numInPark1)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		numList.add(BigDecimal.valueOf(numInPark0)
			.multiply(BigDecimal.valueOf(100))
			.divide(BigDecimal.valueOf(tempNum), 2, BigDecimal.ROUND_HALF_UP).doubleValue());

		result.setSeriesData(numList);
		return result;
	}


	/**
	 * 泊位利用率分析
	 *
	 * @param parklotId 车场id 车场id
	 * @return ParkLastWeekRateVO
	 */
	@Override
	public ParkLastWeekRateVO parkUseRate(String parklotId) {
		return parkingOrderService.parkUseRate(parklotId);
	}


	/**
	 * 财务分析
	 * @param parklotId 车场id
	 * @return 财务分析
	 */
	@Override
	public List<List<String>> financialAnalysis(String parklotId) {
		return parkingOrderService.financialAnalysis(parklotId);
	}

	@Override
	public List<ScreenParklot> screenParklots() {

		//获取登录用户的车场列表
		List<Long> parkLotIds = userParklotService.getParkLotIds(SecureUtil.getUserId());
		if (Func.isEmpty(parkLotIds)) {
			return Collections.emptyList();
		}

		List<Parklot> parklotList = parklotService.listByIds(parkLotIds);
		if (Func.isEmpty(parklotList)) {
			return Collections.emptyList();
		}

		List<ScreenParklot> screenParklots = new ArrayList<>();

		parklotList.forEach(parklot -> {
			ScreenParklot screenParklot = ScreenParklot.builder()
				.lng(parklot.getLng())
				.lat(parklot.getLat())
				.parklotId(String.valueOf(parklot.getId()))
				.label(parklot.getName())
				.build();
			screenParklots.add(screenParklot);
		});

		return screenParklots;
	}

	/**
	 * 大屏首页总统计数据
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	@Override
	public TotalStatisticsVO totalStatistics(String parklotId) {

		List<Long> parklotIds = new ArrayList<>();
		if (Func.isBlank(parklotId)) {
			parklotIds = userParklotService.getParkLotIds(SecureUtil.getUserId());
		} else {
			parklotIds.add(Long.valueOf(parklotId));
		}

		//今日临停收费总和
		BigDecimal parkingMoney = parkingOrderService.getTodayIncome(parklotIds);

		//今日异常少收金额
		BigDecimal noPayMoney = parkingOrderService.getTodayOwnFee(parklotIds);

		//在场车位数
		Integer parkSum = parkingOrderService.getTodayParkingSum(parklotIds);

		//剩余车位数
		int sum = parklotService.listByIds(parklotIds).stream().mapToInt(Parklot::getTempLotAmount).sum();
		Integer parkRemainNumber = sum - parkSum;

		//当日过车数量
		Integer passNum = parkingOrderService.getTodayPassNum(parklotIds);

		return TotalStatisticsVO.builder()
			.parkinMoney(parkingMoney)
			.nopayMoney(noPayMoney)
			.leftPlace(parkRemainNumber)
			.parksum(parkSum)
			.passNum(passNum)
			.build();
	}
}
