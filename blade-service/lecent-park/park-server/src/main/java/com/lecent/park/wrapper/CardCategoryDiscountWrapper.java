package com.lecent.park.wrapper;

import com.lecent.park.entity.CardCategoryDiscount;
import com.lecent.park.vo.CardCategoryDiscountVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 月卡套餐折扣信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
public class CardCategoryDiscountWrapper extends BaseEntityWrapper<CardCategoryDiscount, CardCategoryDiscountVO>  {

	public static CardCategoryDiscountWrapper build() {
		return new CardCategoryDiscountWrapper();
 	}

	@Override
	public CardCategoryDiscountVO entityVO(CardCategoryDiscount cardCategoryDiscount) {
		CardCategoryDiscountVO cardCategoryDiscountVO = BeanUtil.copy(cardCategoryDiscount, CardCategoryDiscountVO.class);

		//User createUser = UserCache.getUser(cardCategoryDiscount.getCreateUser());
		//User updateUser = UserCache.getUser(cardCategoryDiscount.getUpdateUser());
		//cardCategoryDiscountVO.setCreateUserName(createUser.getName());
		//cardCategoryDiscountVO.setUpdateUserName(updateUser.getName());

		return cardCategoryDiscountVO;
	}

}
