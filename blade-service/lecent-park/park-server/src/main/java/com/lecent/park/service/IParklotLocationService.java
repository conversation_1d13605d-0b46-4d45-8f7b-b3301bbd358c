package com.lecent.park.service;

import com.lecent.park.dto.ParklotLocationDTO;
import org.springblade.core.map.tencent.response.LocationSearchResponse;

/**
 * 车场位置服务业务接口
 *
 * <AUTHOR>
 */
public interface IParklotLocationService {

	/**
	 * 搜索当前定位点方法
	 * 说明:
	 * 大致逻辑分为两种
	 * 1.当前定位点搜索
	 * 2.输入的地点搜索
	 *
	 * 当是当前定位点搜索时,from坐标点为当前定位点
	 * 当是输入的地点搜索时,第一次搜索时是to(搜索点)坐标点,搜索的是to坐标点周围的位置,返回位置后,
	 * 还需要拿返回的位置结果集与from(当前定位点)坐标点计算距离最后结果
	 *
	 *根据以上逻辑,还需要拼接公司的停车定位,大概逻辑如下
	 * 拿出所有库里的停车场坐标
	 * 如果是当前定位搜索,使用from来多对多查询距离,过滤5公里以内的停车场
	 * 如果是搜索定位,使用to来与公司停车定位多对多查询,过滤5公里以内的停车场,之后还需要使用from与过滤之后的停车场定位多对多计算距离然后
	 * 返回
	 *
	 *
	 * @param parklotLocationDTO
	 * @return
	 */
	LocationSearchResponse search(ParklotLocationDTO parklotLocationDTO);

}
