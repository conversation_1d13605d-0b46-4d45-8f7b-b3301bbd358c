package com.lecent.park.service;

import com.lecent.park.dto.BabyScanDTO;
import org.springblade.common.payment.PayResult;

/**
 * 微信扫码服务类
 *
 * <AUTHOR>
 */
public interface ParkingBabyService {

	/**
	 * 停车宝宝被扫支付（用户出示付款码，商家用扫码枪扫描）
	 *
	 * @param babyScanDto 主扫请求参数
	 * @return
	 */
	boolean scanPayOrder(BabyScanDTO babyScanDto);

	/**
	 * 支付结果查询
	 *
	 * @param tradeNo 交易流水号
	 * @return 支付结果
	 */
	PayResult queryPayResult(String tradeNo);

	/**
	 * 被扫下单
	 *
	 * @param babyScanDto 被扫参数
	 * @return 下单结果
	 */
	PayResult createOrder(BabyScanDTO babyScanDto);
}
