package com.lecent.park.service;

import com.lecent.park.entity.ChannelDevice;
import com.lecent.park.vo.ChannelDeviceVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2021-01-04
 */
public interface IChannelDeviceService extends BaseService<ChannelDevice> {

	/**
	 * 新增或修改
	 * @param channelDevice
	 * @return
	 */
	Boolean saveOrUpdateChannelDevice(ChannelDeviceVO channelDevice);

	/**
	 * 查询List
	 * @param channelId
	 * @return
	 */
	List<ChannelDevice> listByChannelId(Long channelId);

	/**
	 * 更据通道ID设备类型查询
	 * @param channelId
	 * @param deviceType
	 * @return
	 */
	ChannelDevice getByChannelId(Long channelId, Integer deviceType);

	/**
	 * 更据设备序列号查询
	 * @param deviceSn
	 * @return
	 */
	ChannelDevice getByDeviceSn(String deviceSn);

	/**
	 * 保存通道设备
	 * @param build
	 * @return
	 */
	ChannelDevice saveChannelDevice(ChannelDevice build);
}
