package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.ParklotFloor;
import com.lecent.park.service.IParklotFloorService;
import com.lecent.park.vo.ParklotFloorVO;
import com.lecent.park.vo.UsedAmountVO;
import com.lecent.park.wrapper.ParklotFloorWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@RestController
@AllArgsConstructor
@Validated
@RequestMapping("/parklotfloor")
@Api(value = "", tags = "接口")
public class ParklotFloorController extends BladeController {

	private IParklotFloorService parklotFloorService;

	/**
	 * 根据楼层获取该楼层剩余总车位
	 *
	 * @return
	 */
	@GetMapping("/getRemainAmountByFloorId")
	@ApiOperation(value = "根据楼层获取该楼层剩余总车位", notes = "根据楼层获取该楼层剩余总车位")
	public R<ParklotFloor> getRemainAmountByFloorId(@NotNull(message = "楼层id不能为空") Long floorId) {
		return R.data(parklotFloorService.getRemainAmountByFloorId(floorId));
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入parklotFloor")
	public R<ParklotFloorVO> detail(ParklotFloor parklotFloor) {
		ParklotFloor detail = parklotFloorService.getOne(Condition.getQueryWrapper(parklotFloor));
		return R.data(ParklotFloorWrapper.build().entityVO(detail));
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入parklotFloor")
	public R<IPage<ParklotFloorVO>> list(ParklotFloor parklotFloor, Query query) {
		IPage<ParklotFloor> pages = parklotFloorService.page(Condition.getPage(query),
															 Condition.getQueryWrapper(parklotFloor));
		return R.data(ParklotFloorWrapper.build().pageVO(pages));
	}

	/**
	 * 根据车场获取楼层列表
	 */
	@GetMapping("/getListByParklotId")
	@ApiOperation(value = "根据车场获取楼层列表", notes = "根据车场获取楼层列表")
	public R<List<ParklotFloor>> getListByParklotId(@NotNull(message = "车场id不能为空") Long parklotId) {
		List<ParklotFloor> list = parklotFloorService.getListByParklotId(parklotId);
		return R.data(list);
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入parklotFloor")
	public R<IPage<ParklotFloorVO>> page(ParklotFloorVO parklotFloor, Query query) {
		if (!SecureUtil.isAdministrator()) {
			parklotFloor.setUserId(SecureUtil.getUserId());
		}
		IPage<ParklotFloorVO> pages = parklotFloorService.selectParklotFloorPage(Condition.getPage(query),
																				 parklotFloor);
		return R.data(pages);
	}

	@GetMapping("/getByParklotId")
	@ApiOperation(value = "分页", notes = "传入parklotFloor")
	public R<List<ParklotFloor>> getByParklotId(Long parklotId) {
		List<ParklotFloor> list = parklotFloorService.getByParklotId(parklotId);
		return R.data(list);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入parklotFloor")
	public R save(@Valid @RequestBody ParklotFloor parklotFloor) {
		return R.status(parklotFloorService.save(parklotFloor));
	}

	/**
	 * 新增
	 */
	@PostMapping("/saveBatch")
	@ApiOperation(value = "批量新增", notes = "批量新增")
	public R saveBatch(@Valid @RequestBody List<ParklotFloor> parklotFloor) {
		return R.status(parklotFloorService.doSaveBatch(parklotFloor));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入parklotFloor")
	public R update(@Valid @RequestBody ParklotFloor parklotFloor) {

		return R.status(parklotFloorService.updateById(parklotFloor));
	}

	/**
	 * 修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入parklotFloor")
	public R submit(@Valid @RequestBody ParklotFloor parklotFloor) {
		return R.status(parklotFloorService.submit(parklotFloor));
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(parklotFloorService.removeFloor(Func.toLongList(ids)));
	}

	/**
	 * 获取该车场已经使用的车位
	 */
	@GetMapping("/getUsedAmountById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取该车场剩余车位（楼层可配置的车位）", notes = "获取该车场剩余车位（楼层可配置的车位）")
	public R<UsedAmountVO> getUsedAmountById(Long parklotId) {
		UsedAmountVO usedAmountVO = parklotFloorService.getUsedAmountById(parklotId);
		return R.data(usedAmountVO);
	}

}
