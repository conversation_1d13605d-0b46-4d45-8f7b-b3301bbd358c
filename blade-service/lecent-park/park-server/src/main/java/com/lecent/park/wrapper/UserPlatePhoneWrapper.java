package com.lecent.park.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.lecent.park.entity.BVehiclePhoneRelation;
import com.lecent.park.vo.vehiclePhoneRelation.BVehiclePhoneRelationVO;
import java.util.Objects;

/**
 * 车牌手机号采集表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public class UserPlatePhoneWrapper extends BaseEntityWrapper<BVehiclePhoneRelation, BVehiclePhoneRelationVO>  {

	public static UserPlatePhoneWrapper build() {
		return new UserPlatePhoneWrapper();
 	}

	@Override
	public BVehiclePhoneRelationVO entityVO(BVehiclePhoneRelation BVehiclePhoneRelation) {
		BVehiclePhoneRelationVO userPlatePhoneVO = Objects.requireNonNull(BeanUtil.copy(BVehiclePhoneRelation, BVehiclePhoneRelationVO.class));

		//User createUser = UserCache.getUser(userPlatePhone.getCreateUser());
		//User updateUser = UserCache.getUser(userPlatePhone.getUpdateUser());
		//userPlatePhoneVO.setCreateUserName(createUser.getName());
		//userPlatePhoneVO.setUpdateUserName(updateUser.getName());

		return userPlatePhoneVO;
	}

}
