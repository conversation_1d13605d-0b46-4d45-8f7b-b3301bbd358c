package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.MessageLogDTO;
import com.lecent.park.entity.*;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.MessageLogVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 用户端消息日志表 服务类
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface IMessageLogService extends BaseService<MessageLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param messageLogDto
	 * @return
	 */
	IPage<MessageLogVO> selectMessageLogPage(IPage<MessageLogVO> page, MessageLogDTO messageLogDto);

	/**
	 * 设置为已读
	 * @param id
	 * @return
	 */
	boolean setRead(String id);

	/**
	 * 车辆进出场消息提醒
	 * @param channelTodoVO
	 */
	void channelCarMessageCall(ChannelTodoVO channelTodoVO);

	/**
	 * 停车缴费消息提醒
	 * @param order
	 */
	void tempPayMessageCall(TempParkingOrder order);

	/**
	 * 月卡缴费消息提醒
	 * @param card
	 * @param cardOrder
	 */
	void cardPayMessageCall(Card card, CardOrder cardOrder);

	/**
	 * 商户授权过期消息提醒
	 * @param authPlate
	 */
	void merchantAuthOverdueMsgCall(ParkMerchantParklotPlate authPlate);

	/**
	 * 月卡过期消息提醒
	 * @param newCard
	 */
	void cardOverdueMsgCall(Card newCard);

	/**
	 * 免费车过期消息提醒
	 * @param cardAuth
	 */
	void freeCardOverdueMsgCall(FreeCardAuth cardAuth);
}
