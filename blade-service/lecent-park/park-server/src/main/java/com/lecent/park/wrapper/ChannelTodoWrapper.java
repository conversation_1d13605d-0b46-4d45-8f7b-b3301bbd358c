package com.lecent.park.wrapper;

import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.vo.ChannelTodoVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场通道包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public class ChannelTodoWrapper extends BaseEntityWrapper<ChannelTodo, ChannelTodoVO> {

	public static ChannelTodoWrapper build() {
		return new ChannelTodoWrapper();
	}

	@Override
	public ChannelTodoVO entityVO(ChannelTodo channelTodo) {
		ChannelTodoVO channelTodoVO = BeanUtil.copy(channelTodo, ChannelTodoVO.class);

		//User createUser = UserCache.getUser(channelTodo.getCreateUser());
		//User updateUser = UserCache.getUser(channelTodo.getUpdateUser());
		//channelTodoVO.setCreateUserName(createUser.getName());
		//channelTodoVO.setUpdateUserName(updateUser.getName());

		return channelTodoVO;
	}

}
