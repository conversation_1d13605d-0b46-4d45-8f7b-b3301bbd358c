package com.lecent.park.controller.wechat;

import com.lecent.park.service.impl.ThirdCRHJService;
import com.lecent.park.service.impl.ThirdDeviceService;
import com.lecent.park.third.ThirdHttpRes;
import com.lecent.park.third.ThirdRes;
import com.lecent.park.third.ThirdResRemoteBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 第三方设备请求接口
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/thirdParkDevice")
@Api(tags = "第三方设备请求接口")
@Slf4j
public class ThirdDeviceController extends BladeController {
	@Autowired
	private ThirdDeviceService thirdDeviceService;

	@Autowired
	private ThirdCRHJService thirdCRHJService;


	@PostMapping("/thirdCameraTrigger")
	@ApiOperation(value = "thirdCameraTrigger", notes = "第三方摄像头触发")
	public ThirdRes thirdCameraTrigger(@RequestBody Map<String, Object> map) {
		return thirdDeviceService.thirdCameraTrigger(map);
	}


	@GetMapping("/open-door")
	@ApiOperation(value = "open-door", notes = "open-door")
	public ThirdRes<ThirdResRemoteBody> openCcbDoor(String channelId) {
		return thirdDeviceService.openCcbDoor(channelId, 1);
	}



	@GetMapping("/test-push-record")
	public ThirdHttpRes testPushRecord() {
		return thirdCRHJService.testPushRecord();
	}



	@GetMapping("/test-push-car")
	public ThirdHttpRes testPushCar() {
		return thirdCRHJService.testPushCar();
	}

}
