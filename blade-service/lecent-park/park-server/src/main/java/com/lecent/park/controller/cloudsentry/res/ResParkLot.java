package com.lecent.park.controller.cloudsentry.res;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 车场
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
@Builder
public class ResParkLot implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 车场ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "车场ID")
	private Long id;

	/**
	 * 车场编号
	 */
	@ApiModelProperty(value = "车场编号")
	private String code;

	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String name;

	/**
	 * 关联通道
	 */
	@ApiModelProperty(value = "关联通道")
	private List<ResChannel> channels;

}
