package com.lecent.park.listener;

import com.lecent.park.event.IASyncDataForwardingEvent;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.service.IChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

/**
 * 设备状态改变监听
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableAsync
public class DeviceStatusChangeListener {
	/**
	 * 停车场通道服务
	 */
	@Autowired
	private IChannelService channelService;

	@Async
	@EventListener(condition = "#event.commands==T(com.lecent.park.device.constant.ChannelCommandConstant).DEV_STATUS")
	public void deviceStatusChange(IASyncDataForwardingEvent event) {
		ParkChannelMessageEvent message = (ParkChannelMessageEvent) event;
		channelService.deviceStatusChange(message);

	}
}
