package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.BlackListDTO;
import com.lecent.park.dto.BlackListRemoveLogDTO;
import com.lecent.park.en.blacklist.PermitEnterExitEnum;
import com.lecent.park.entity.BlackList;
import com.lecent.park.excel.BlacklistExcel;
import com.lecent.park.service.IBlackListService;
import com.lecent.park.utils.excel.ExcelUtils;
import com.lecent.park.utils.excel.F;
import com.lecent.park.utils.excel.handle.ImportHandler;
import com.lecent.park.vo.BlackListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 车辆黑名单 控制器
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@RestController
@RequestMapping("/blacklist")
@Api(value = "车辆黑名单", tags = "车辆黑名单接口")
public class BlackListController extends BladeController {

	@Autowired
	private IBlackListService blackListService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入blackList")
	public R<BlackListVO> detail(String id) {
		return R.data(blackListService.detail(id));
	}


	/**
	 * 自定义分页 车辆黑名单
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入blackList")
	public R<IPage<BlackListVO>> page(BlackListDTO blackListDto, Query query) {
		IPage<BlackListVO> pages = blackListService.selectBlackListPage(Condition.getPage(query), blackListDto);
		return R.data(pages);
	}

	/**
	 * 新增 车辆黑名单
	 */
	@PostMapping("/customSubmit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入blackList")
	public R<Boolean> save(@Valid @RequestBody BlackListDTO blackListDto) {
		return R.status(blackListService.customSubmit(blackListDto));
	}

	/**
	 * 删除 车辆黑名单
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入id")
	public R<Boolean> remove(@Valid @RequestBody BlackListRemoveLogDTO blackListRemoveLogDto) {
		return R.status(blackListService.customRemove(blackListRemoveLogDto));
	}

	/**
	 * 恢复车辆黑名单
	 */
	@PostMapping("/rollback")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "恢复车辆黑名单", notes = "传入id")
	public R<Boolean> rollback(@Valid @RequestBody BlackListRemoveLogDTO blackListRemoveLogDto) {
		return R.status(blackListService.rollback(blackListRemoveLogDto));
	}

	/**
	 * 黑名单导出
	 */
	@PostMapping("/black-list-export")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "黑名单导出", notes = "传入cardDTO")
	public void blackListExport(BlackListDTO blackListDto, HttpServletResponse response) {
		blackListService.blackListExport(blackListDto, response);
	}


	/**
	 * 黑名单导入模板下载
	 */
	@GetMapping("/blacklistTemplateDownload")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "黑名单导入模板下载")
	public void blacklistTemplateDownload(HttpServletResponse response, @RequestParam(name = "allowOut", required = false) Integer allowOut) {
		BlacklistExcel build = BlacklistExcel.builder()
			.plate("贵XXXX")
			.remark("长期逃费车辆")
			.build();
		List<BlacklistExcel> dataList = new ArrayList<>();
		dataList.add(build);

		List<String> exportFields = new ArrayList<String>(2) {{
			add("plate");
			add("remark");
		}};

		String name = "黑名单导入模板";
		// 黄名单
		if (PermitEnterExitEnum.YELLOW_LIST.getCode().equals(allowOut)) {
			name = "黄名单导入模板";
		}
		ExcelUtils.exportExcel(BlacklistExcel.class, dataList, "", name, response, exportFields);
	}


	/**
	 * 黑名单导入
	 */
	@Transactional(rollbackFor = Exception.class)
	@PostMapping("/blacklistImport")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "黑名单导入")
	public void blacklistImport(@RequestParam(value = "file", required = false) MultipartFile file,
								@RequestParam(value = "parklotId") @NotNull Long parklotId,
								@RequestParam(value = "allowOut", required = false, defaultValue = "0") Integer allowOut) throws Exception {
		LecentAssert.notNull(parklotId, "请选择车场！");

		PermitEnterExitEnum permitEnum = PermitEnterExitEnum.of(allowOut);
		ExcelUtils.importExcel(file, BlacklistExcel.class, 1, new ImportHandler<BlacklistExcel>() {
			@Override
			public Object columnCallBack(int rowNum, int columnNum, Object cellValue) {
				LecentAssert.isFalse(F.isEmpty(cellValue), String.format("第%s行第%s列数据不能为空", rowNum, columnNum));
				return cellValue;
			}

			@Override
			public void saveCallBack(List<BlacklistExcel> cacheList) {
				List<BlackListDTO> list = Func.copy(cacheList, BlackListDTO.class);
				list.forEach(v -> {
					BlackList blackList = blackListService.getByPlate(v.getPlate(), parklotId);
					if (blackList != null) {
						v.setId(blackList.getId());
					}
					v.setParklotId(parklotId);
					v.setAllowOut(permitEnum.getCode());
					blackListService.customSubmit(v);
				});
			}
		});

	}


}
