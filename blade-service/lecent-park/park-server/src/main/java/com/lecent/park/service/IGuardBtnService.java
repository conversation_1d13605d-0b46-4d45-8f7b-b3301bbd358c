package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.GuardBtn;
import com.lecent.park.vo.GuardBtnVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.system.vo.GuardUserMenuVO;

import java.util.List;

/**
 * 岗亭端按钮表 服务类
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
public interface IGuardBtnService extends BaseService<GuardBtn> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param guardBtn
	 * @return
	 */
	IPage<GuardBtnVO> selectGuardBtnPage(IPage<GuardBtnVO> page, GuardBtnVO guardBtn);

	/**
	 * 获取移动岗亭端用户配置的菜单列表
	 * @param userId
	 * @return
	 */
	List<GuardUserMenuVO> mobileGuardUserMenu(Long userId);
}
