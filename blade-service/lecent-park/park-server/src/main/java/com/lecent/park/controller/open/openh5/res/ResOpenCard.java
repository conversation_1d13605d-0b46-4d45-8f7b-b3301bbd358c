package com.lecent.park.controller.open.openh5.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * 卡套餐
 *
 * <AUTHOR> zxr
 * @date : 2022/8/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResOpenCard {
	/**
	 * 车场名称
	 */
	private String parklotName;

	/**
	 * 车场地址
	 */
	private String addr;
	/**
	 * 开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String startTime;

	/**
	 * 卡套餐价格
	 */
	private List<ResCardCategory> cardCategories;
}
