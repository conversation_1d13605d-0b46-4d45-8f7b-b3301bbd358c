package com.lecent.park.service;

import com.lecent.park.dto.WechatUserInfoDTO;
import com.lecent.park.entity.WechatUserInfo;
import org.springblade.core.mp.base.BaseService;

/**
 * 微信用户基础信息 服务类
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
public interface IWechatUserInfoService extends BaseService<WechatUserInfo> {

    WechatUserInfo getOneByOpenId(String openId);

	boolean bindPhone(WechatUserInfoDTO wechatUserInfo);

	/**
	 * 获取短信验证码
	 * @param phone
	 * @return
	 */
	boolean sendValidCode(String phone);
}
