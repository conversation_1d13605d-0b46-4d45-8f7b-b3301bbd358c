package com.lecent.park.controller.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.service.JHTodoItemService;
import com.lecent.park.service.ICardOrderService;
import com.lecent.park.vo.CCBParkingOrderDetailVO;
import com.lecent.park.vo.CardOrderVO;
import com.lecent.park.vo.TodoItemVO;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微信订单记录
 *
 * <AUTHOR>
 * @date 2021-04-07 15:36
 */
@RestController
@RequestMapping("/wechat/order")
public class WechatTodoItemController {


	@Autowired
	private JHTodoItemService JHTodoItemService;
	@Autowired
	private ICardOrderService cardOrderService;


	/**
	 * 临停代缴项目
	 */
	@GetMapping("/todo-item")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "临停代缴项目", notes = "")
	public R<IPage<TodoItemVO>> todoItem(Query query, ParkingOrderDTO parkingOrderDTO) {
		return R.data(JHTodoItemService.todoItem(query, parkingOrderDTO));
	}

	/**
	 * 公众号我的订单-月卡订单
	 */
	@GetMapping("/card")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "公众号我的订单-月卡订单")
	public R<IPage<CardOrderVO>> userCardOrder(Query query) {
		IPage<CardOrderVO> pages = cardOrderService.userCardOrder(Condition.getPage(query));
		return R.data(pages);
	}

	/**
	 * 详情
	 */
	@GetMapping("/order-detail")
	@ApiOperation(value = "详情", notes = "")
	public R<CCBParkingOrderDetailVO> todoItem(Long id) {
		return R.data(JHTodoItemService.getOrderDetail(id));
	}


}
