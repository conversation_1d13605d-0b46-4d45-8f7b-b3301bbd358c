package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.common.constant.*;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.common.exception.NotFundReserveParkException;
import com.lecent.park.dto.ClientTodo;
import com.lecent.park.dto.ReserveParkDTO;
import com.lecent.park.en.reservepark.ReserveParkType;
import com.lecent.park.entity.*;
import com.lecent.park.event.channeltodo.ReloadParkPlaceEvent;
import com.lecent.park.event.channeltodo.ReturnParkPlaceEvent;
import com.lecent.park.mapper.ReserveParkMapper;
import com.lecent.park.service.*;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.ReserveParkVO;
import com.lecent.park.vo.ReserveParkingOrderVO;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import com.lecent.pay.core.enums.PayScene;
import com.lecent.payment.dto.RefundOrderDTO;
import com.lecent.payment.dto.UnifiedOrderDTO;
import com.lecent.payment.enums.PayStatus;
import com.lecent.payment.feign.IPaymentClient;
import com.lecent.payment.vo.OrderQueryResultVO;
import com.lecent.payment.vo.RefundOrderResultVO;
import com.lecent.payment.vo.UnifiedOrderResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.common.utils.SequenceNoUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 预约车位表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Slf4j
@Service
public class ReserveParkServiceImpl extends
	BaseServiceImpl<ReserveParkMapper, ReservePark> implements IReserveParkService {

	@Autowired
	private BladeRedis bladeRedis;

	@Autowired
	private ICardService cardService;

	@Lazy
	@Autowired
	private ClientService clientService;

	@Autowired
	private ICalculateParkRemainNumberService calculateParkRemainNumberService;

	@Autowired
	private IParklotPayWayService payWayService;

	@Autowired
	private IReserveParkConfigService reserveParkConfigService;

	@Autowired
	private ApplicationEventPublisher publisher;
	@Lazy
	@Resource
	private IPaymentClient paymentClient;

	@Autowired
	private IRefundRecordService refundRecordService;

	@Autowired
	private ISysClient sysClient;

	@Autowired
	private IPayOrderService reservePayOrderService;

	@Autowired
	private IParkingPlaceService parkingPlaceService;

	@Autowired
	private IBUserPlateService ibUserPlateService;

	@Autowired
	private ITempParkingChargeRuleService chargeRuleService;

	@Autowired
	private IFreeCardAuthService freeCardAuthService;

	@Autowired
	private IPlatePropertyService platePropertyService;

	@Override
	public IPage<ReserveParkVO> selectReserveParkPage(IPage<ReserveParkVO> page, ReserveParkDTO reservePark) {
		if (StringUtils.isBlank(reservePark.getOpenId())) {
			throw new ServiceException("未传入openId");
		}
		List<ReserveParkVO> result = baseMapper.selectReserveParkPage(page, reservePark);
		result.forEach(this::setRefundInfo);
		return page.setRecords(result);
	}

	/**
	 * 统一保存或更新
	 *
	 * @param saveBean 更新实体
	 * @return true 成功
	 */
	private boolean unifySaveOrUpdate(ReservePark saveBean) {
		super.saveOrUpdate(saveBean);

		// 预约成功的
		if (ReserveParkType.SUCCESS.getCode().equals(saveBean.getStatus())) {
			return platePropertyService.update(saveBean.getTenantId(), saveBean.getParkId(),
				Func.isNotBlank(saveBean.getPlate()) ? saveBean.getPlate() : saveBean.getOpenId(),
				saveBean.getId(),
				PlatePropertyType.RESERVE_CARD,
				saveBean.getReserveTime(),
				saveBean.getEndTime());
		} else {
			return platePropertyService.removeByCardIds(PlatePropertyType.RESERVE_CARD, saveBean.getId());
		}
	}

	@Override
	public void setRefundInfo(ReserveParkVO reserveParkVO) {
		RefundRecord refundRecord = refundRecordService.getByOutId(reserveParkVO.getOrderId());
		if (Func.isNotEmpty(refundRecord)) {
			reserveParkVO.setRefundMoney(refundRecord.getMoney());
			reserveParkVO.setRefundStatus(refundRecord.getStatus());
			// 0在退款订单里面是进行中
			if (refundRecord.getStatus().equals(ReserveRefundStatus.NO)) {
				reserveParkVO.setRefundStatus(ReserveRefundStatus.ING);
			}
		}
	}

	@Override
	public IPage<ReserveParkingOrderVO> selectParkPage(IPage<ReserveParkingOrderVO> page, ReservePark reservePark) {
		return page.setRecords(baseMapper.selectParkPage(page, reservePark.getOpenId(), reservePark.getCarStatus()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ReserveParkVO reserveList(ReserveParkDTO reservePark) {
		if (StringUtils.isBlank(reservePark.getOpenId())) {
			throw new ServiceException("未传入openId");
		}
		if (StringUtils.isBlank(reservePark.getPlates())) {
			throw new ServiceException("未传入预约车牌");
		}
		long endTimeCurrent = reservePark.getEndTime().getTime();
		long nowTimeCurrent = System.currentTimeMillis();
		if ((endTimeCurrent - nowTimeCurrent) < 30 * 60 * 1000) {
			throw new ServiceException("离场时间请至少选择半小时后");
		}
		// 分布式锁，防止重复请求
		String redisCache = reservePark.getOpenId() + reservePark.getPlates();
		String oldRedisCache = bladeRedis.get(redisCache);
		if (StringUtils.isNotBlank(oldRedisCache)) {
			throw new ServiceException("请勿重复操作");
		}
		bladeRedis.setEx(redisCache, redisCache, 5L);
		String[] plates = reservePark.getPlates().split(StringPool.COMMA);
		if (plates.length < 1) {
			throw new ServiceException("未传入预约车牌");
		}
		Parklot parklot = ParkLotCaches.getParkLot(reservePark.getParkId());
		if (Func.isEmpty(parklot)) {
			throw new ServiceException("找不到id为" + reservePark.getParkId() + "的车场");
		}

		ReserveParkConfig config = reserveParkConfigService.getConfig(reservePark.getParkId(), parklot.getTenantId());
		if (Func.isEmpty(config)) {
			throw new ServiceException("系统未配置预约规则");
		}

		int userReservedNum = baseMapper.getReserveCountByOpenId(reservePark.getParkId(), reservePark.getOpenId());
		if (Func.isEmpty(config.getDayReserveNum()) || userReservedNum >= config.getDayReserveNum()) {
			throw new ServiceException("您在该车场的单日预约次数已达到系统最大限制，今日内不可再预约");
		}

		List<String> errorPlates = new ArrayList<>();

		// 车牌校验，判断车牌是否是月卡或业主车
		for (String plate : plates) {
			if (cardService.hasCardOfPlate(reservePark.getPlate(), reservePark.getParkId())) {
				errorPlates.add(plate);
			}
		}
		if (!errorPlates.isEmpty()) {
			throw new ServiceException("您要预约车牌" + Arrays.toString(errorPlates.toArray()) + "是该停车场的月卡车牌，无需预约");
		}

		// 免费车校验
		for (String plate : plates) {
			FreeCardAuth freeCardAuth = freeCardAuthService.selectByPlate(reservePark.getPlate(), reservePark.getParkId(), new Date(), new Date());
			if (freeCardAuth != null && freeCardAuth.getId() != null) {
				errorPlates.add(plate);
			}
		}
		if (!errorPlates.isEmpty()) {
			throw new ServiceException("您要预约车牌" + Arrays.toString(errorPlates.toArray()) + "是该停车场的免费车，无需预约");
		}

		// 判断车牌是否已存在预约
		for (String plate : plates) {
			if (judgePlateIsReserved(reservePark.getPlate(), reservePark.getParkId())) {
				errorPlates.add(plate);
			}
		}
		if (!errorPlates.isEmpty()) {
			throw new ServiceException("您要预约车牌" + Arrays.toString(errorPlates.toArray()) + "已经有正在进行的预约，请勿重复预约");
		}

		// 锁定车位
		if (!calculateParkRemainNumberService.parkPlaceBook(parklot, plates.length)) {
			throw new ServiceException("剩余车位数不足");
		}

		PayOrder reservePayOrder = new PayOrder();
		reservePayOrder.setPayOrderId(SequenceNoUtils.generateNo());

		BigDecimal unitPrice = BigDecimal.ZERO;
		ProjectCost cost = clientService.commonCharge(parklot.getId(), new Date(), reservePark.getEndTime());
		if (Func.isNotEmpty(cost) && Func.isNotEmpty(cost.getReceiveAmount())) {
			unitPrice = cost.getReceiveAmount();
		}
		reservePayOrder.setTenantId(parklot.getTenantId());
		reservePayOrder.setMoney(unitPrice.multiply(BigDecimal.valueOf(plates.length)));
//		reservePayOrder.setMoney(BigDecimal.valueOf(0.01));
		reservePayOrder.setStatus(ReservePayStatus.PAID);
		if (!reservePayOrderService.save(reservePayOrder)) {
			releaseParkingPlace(reservePark.getParkId(), plates.length);
			throw new ServiceException("生成预约支付订单失败,请重试");
		}

		reservePark.setWxPayOrderId(reservePayOrder.getPayOrderId());
		// 自动分配车位并且保存车位预约订单
		List<String> platePlaces = autoAllotPlace(reservePark, config, parklot, plates, unitPrice);

		// 填充返回值
		ReserveParkVO result = BeanUtil.copy(reservePark, ReserveParkVO.class);
		assert result != null;

		result.setPlatePlaces(platePlaces);
		// 判断当前时间是否为临停时段
		if (parklot.getLimitAutoTime()) {
			String beginTime = DateUtils.digitalToClock(parklot.getLimitAutoBeginTime());
			String endTime = DateUtils.digitalToClock(parklot.getLimitAutoEndTime());
			result.setForbidTime(beginTime + "-" + endTime);
		}
		result.setIsPay(config.getIsPay());
		result.setReserveMoney(reservePayOrder.getMoney());
		result.setOverdueTime(config.getOverdueTime());
		result.setLat(parklot.getLat());
		result.setLng(parklot.getLng());
		result.setParkName(parklot.getName());
		result.setReservePayMoneyStr(reservePayOrder.getMoney().toEngineeringString());

		// 移除锁返回结果
		bladeRedis.del(redisCache);
		return result;
	}

	private List<String> autoAllotPlace(ReserveParkDTO reservePark, ReserveParkConfig config, Parklot parklot, String[] plates, BigDecimal unitPrice) {
		// 获取分配的车位
		List<ParkingPlace> places = parkingPlaceService.reservePlace(plates.length, reservePark.getPreference(), parklot.getId(), reservePark.getParkPlaceType());
		// 拆分预约订单
		if (plates.length > places.size()) {
			releaseParkingPlace(reservePark.getParkId(), plates.length);
			throw new ServiceException("没有足够的车位可以预约");
		}
		// 返回的车牌车位对应信息
		List<String> platePlaces = new ArrayList<>();
		for (int i = 0; i < plates.length; i++) {
			ReservePark tempResult = reservePark(reservePark, config, parklot, plates[i], places.get(i), unitPrice);
			if (Func.isEmpty(tempResult)) {
				releaseParkingPlace(reservePark.getParkId(), 1);
				throw new ServiceException("系统错误，预约失败");
			}
			if (StringUtils.isNotBlank(tempResult.getPlate()) && StringUtils.isNotBlank(tempResult.getPlaceCode())) {
				platePlaces.add(StringPool.PIPE + tempResult.getPlate() + StringPool.PIPE + StringPool.COLON + StringPool.PIPE + tempResult.getPlaceCode() + StringPool.PIPE);
			}
		}
		return platePlaces;
	}

	private ReservePark reservePark(ReserveParkDTO resource, ReserveParkConfig config, Parklot parklot, String plate, ParkingPlace place, BigDecimal unitPrice) {

		ReservePark reservePark = BeanUtil.copy(resource, ReservePark.class);
		assert reservePark != null;
		reservePark.setPlate(plate);
		log.info("开始预约，预约车牌号为：{}", reservePark.getPlate());

		// 生成订单号
		reservePark.setOrderId(SequenceNoUtils.generateNo());

		// 缓存校验
		String cache = bladeRedis.get(getRedisKey(reservePark.getOrderId()));
		if (StringUtils.isNotBlank(cache)) {
			log.error("已存在未过期的订单缓存{}，请确认数据", reservePark.getOrderId());
			return null;
		}
		try {
			reservePark.setStatus(ReserveStatus.NOR_PAY_SERVE_MONEY);
			long expiringTime = config.getOverdueTime() * 60L;
			if (config.getIsPay().equals(ReserveIsPayStatus.NEED_PAY)) {
				expiringTime = config.getNoPayOverdueTime() * 60L;
				reservePark.setReserveMoney(unitPrice);
			} else {
				reservePark.setStatus(ReserveStatus.SUCCESS);
			}
			// 添加过期缓存到redis
			bladeRedis.setEx(getRedisKey(reservePark.getOrderId()), reservePark.getOrderId(), expiringTime);
			reservePark.setReserveTime(new Date());
			reservePark.setIsPay(config.getIsPay());
			reservePark.setTenantId(parklot.getTenantId());
			reservePark.setPlaceCode(place.getPlaceCode());
			reservePark.setPlaceId(place.getId());

			if (!unifySaveOrUpdate(reservePark)) {
				throw new ServiceException("预约失败，保存订单时出现错误");
			}
			return reservePark;
		} catch (Exception e) {
			e.printStackTrace();
			releaseParkingPlace(reservePark.getParkId(), 1);
			List<Long> places = new ArrayList<>();
			places.add(place.getId());
			parkingPlaceService.releasePlace(places);
			throw new ServiceException("预约失败，保存订单时出现错误");
		}
	}

	private String getRedisKey(String orderId) {
		return ParkCacheNames.LECENT_PARK_RESERVE_ORDER + "::" + orderId;
	}

	/**
	 * 判断车牌是否已被预约
	 *
	 * @param plate
	 * @return
	 */
	private boolean judgePlateIsReserved(String plate, Long parkId) {
		ReservePark judge = getOne(new QueryWrapper<ReservePark>().lambda()
			.eq(ReservePark::getPlate, plate)
			.eq(ReservePark::getParkId, parkId)
			.and(
				wrapper -> wrapper.eq(ReservePark::getStatus, ReserveStatus.SUCCESS).or()
					.eq(ReservePark::getStatus, ReserveStatus.NOR_PAY_SERVE_MONEY)
			));
		return Func.isNotEmpty(judge);
	}

	@Override
	public ReservePark getValidPlate(String plate, Long parkId) {
		return getOne(new QueryWrapper<ReservePark>().lambda()
			.eq(ReservePark::getPlate, plate)
			.eq(ReservePark::getParkId, parkId)
			.eq(ReservePark::getStatus, ReserveStatus.SUCCESS));
	}

	@Override
	public ReserveParkVO getVoById(Long id) {
		if (Func.isEmpty(id)) {
			throw new ServiceException("未传入预约订单id");
		}
		ReservePark reservePark = getById(id);
		Parklot parklot = ParkLotCaches.getParkLot(reservePark.getParkId());
		PayOrder reservePayOrder = reservePayOrderService.getOneByPayOrderId(reservePark.getWxPayOrderId());
		LecentAssert.notNull(reservePayOrder, "未找到微信支付单号对应的订单");

		ReserveParkVO result = BeanUtil.copy(reservePark, ReserveParkVO.class);
		assert result != null;
		result.setPayStatus(reservePayOrder.getPayStatus());
		result.setPayTime(reservePayOrder.getPayTime());
		result.setLng(parklot.getLng());
		result.setLat(parklot.getLat());
		result.setParkName(parklot.getName());
		result.setAddress(parklot.getAddress());
		ReserveParkConfig config = reserveParkConfigService.getConfig(reservePark.getParkId(), reservePark.getTenantId());
		if (ReserveStatus.NOR_PAY_SERVE_MONEY.equals(reservePark.getStatus())) {
			Date endData = new Date(reservePark.getReserveTime().getTime() + config.getNoPayOverdueTime() * 60 * 1000L);
			result.setOverdueTime(config.getNoPayOverdueTime());
			result.setEndDate(endData);
		} else if (ReserveStatus.SUCCESS.equals(reservePark.getStatus())) {
			Date endData = new Date(DateUtil.toDate(reservePayOrder.getPayTime()).getTime() + config.getOverdueTime() * 60 * 1000L);
			result.setEndDate(endData);
			result.setOverdueTime(config.getOverdueTime());
		}
		// 设置退款信息
		setRefundInfo(result);
		return result;
	}

	@Override
	public ReservePark getOneByOrderId(String orderId) {
		return getByOrderId(orderId);
	}

	/**
	 * 订单到期时设置过期
	 *
	 * @param orderId
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void expiringReserve(String orderId) {
		log.info("开始执行订单号为{}的过期操作", orderId);
		ReservePark reservePark = getOne(new QueryWrapper<ReservePark>().lambda().eq(ReservePark::getOrderId, orderId));
		if (Func.isEmpty(reservePark)) {
			log.error("未找到订单号对应的订单");
			return;
		}

		PayOrder reservePayOrder = reservePayOrderService.getOneByPayOrderId(reservePark.getWxPayOrderId());
		LecentAssert.notNull(reservePayOrder, "未找到微信支付单号对应的订单");

		// 如果过期时车辆仍然未入场，那么订单失效
		Integer carStatus = reservePark.getCarStatus();
		if (carStatus.equals(ReserveCarStatus.NOT_ENTER)) {
			log.info("执行预约订单过期处理，被执行的订单号为{}", orderId);
			ReservePark update = new ReservePark();
			update.setId(reservePark.getId());
			if (reservePark.getIsPay().equals(ReserveIsPayStatus.NO_PAY)) {
				releaseParkingPlace(reservePark.getParkId(), 1);
				List<Long> places = new ArrayList<>();
				places.add(reservePark.getPlaceId());
				parkingPlaceService.releasePlace(places);
				update.setCarStatus(ReserveCarStatus.OVERDUE_NOT_ENTER);
			} else {
				releaseParkingPlace(reservePark.getParkId(), 1);
				List<Long> places = new ArrayList<>();
				places.add(reservePark.getPlaceId());
				parkingPlaceService.releasePlace(places);
				update.setStatus(ReserveStatus.OVERDUE);
				// 如果是需要支付预约金并且已经支付过了的
				if (reservePayOrder.getPayStatus().equals(ReservePayStatus.PAID)) {
					update.setCarStatus(ReserveCarStatus.OVERDUE_NOT_ENTER);
					// 后期可能做配置项退款
//					reserveFund(reservePark, reservePark.getReserveMoney());
				} else {
					update.setCarStatus(ReserveCarStatus.RESERVE_OVERDUE_NOT_ENTER);
				}
			}
			unifySaveOrUpdate(update);
		} else if (carStatus.equals(ReserveCarStatus.IN_PARK) || carStatus.equals(ReserveCarStatus.OUTED)) {
			// 如果该过期是预约成功并且车辆已入过场的情况下过期，则将状态改为已完成
			ReservePark update = new ReservePark();
			update.setId(reservePark.getId());
			update.setStatus(ReserveStatus.OVER);
			unifySaveOrUpdate(update);
			// 订单完成释放车位
			List<Long> places = new ArrayList<>();
			places.add(reservePark.getPlaceId());
			parkingPlaceService.releasePlace(places);
		}
	}

	/**
	 * 支付订单到期时设置过期
	 *
	 * @param wxPayOrderId
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void expiringWxPayOrder(String wxPayOrderId) {
		PayOrder payOrder = reservePayOrderService.getOneByPayOrderId(wxPayOrderId);
		if (Func.isEmpty(payOrder)) {
			log.error("未找到支付订单号订单号对应的支付订单");
			return;
		}

		List<ReservePark> reserveParks = getListByWxPayOrderId(wxPayOrderId);
		// 修改订单为不可取消状态
		for (ReservePark reservePark : reserveParks) {
			// 设置状态为不可取消预约
			reservePark.setIsCancel(0);
			unifySaveOrUpdate(reservePark);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean changeOrderStatus(Long id) {
		ReservePark reservePark = getById(id);
		if (null == reservePark) {
			throw new NotFundReserveParkException();
		}

		ReservePark update = new ReservePark();
		update.setId(reservePark.getId());
		log.info("车牌为{}的预约车开始入场，开始修改预约订单号为{}的订单状态", reservePark.getPlate(), reservePark.getOrderId());
		update.setCarStatus(ReserveCarStatus.IN_PARK);
		update.setIsCancel(0);

		releaseParkingPlace(reservePark.getParkId(), 1);
		return unifySaveOrUpdate(update);
	}

	private ReservePark getByOrderId(String orderId) {
		return getOne(new QueryWrapper<ReservePark>().lambda().eq(ReservePark::getOrderId, orderId));
	}

	@Override
	public List<ReservePark> getListByWxPayOrderId(String wxPayOrderId) {
		return list(new QueryWrapper<ReservePark>().lambda().eq(ReservePark::getWxPayOrderId, wxPayOrderId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ReserveParkVO reserveMoneyPayAfter(String wxPayOrderId) {
		log.info("-----------------------------------------------------------");
		log.info("开始执行订单支付后的操作");
		log.info("-----------------------------------------------------------");

		PayOrder reservePayOrder = reservePayOrderService.getOneByPayOrderId(wxPayOrderId);
		LecentAssert.notNull(reservePayOrder, "未找到微信支付单号对应的订单");

		List<ReservePark> reserveParks = getListByWxPayOrderId(wxPayOrderId);
		if (Func.isEmpty(reserveParks)) {
			log.error("未找到支付订单号为{}对应的预约订单，请确认数据", wxPayOrderId);
			throw new ServiceException("未找到支付订单号为" + wxPayOrderId + "对应的预约订单，请确认数据");
		}

		Parklot parklot = ParkLotCaches.getParkLot(reserveParks.get(0).getParkId());
		ReserveParkConfig config = reserveParkConfigService.getConfig(parklot.getId(), parklot.getTenantId());
		if (Func.isEmpty(parklot)) {
			throw new ServiceException("找不到id为" + reserveParks.get(0).getParkId() + "的车场");
		}
		// 初始化返回数据
		ReserveParkVO result = new ReserveParkVO();
		result.setParkName(parklot.getName());
		result.setLat(parklot.getLat());
		result.setLng(parklot.getLng());
		result.setPayStatus(ReservePayStatus.NOT_PAY);
		List<String> platePlaces = new ArrayList<>();
		// 获取车位与车牌对应关系
		for (ReservePark reservePark : reserveParks) {
			platePlaces.add(StringPool.PIPE + reservePark.getPlate() + StringPool.PIPE + StringPool.COLON + StringPool.PIPE + reservePark.getPlaceCode() + StringPool.PIPE);
		}
		result.setPlatePlaces(platePlaces);

		// 如果订单已支付
		if (reservePayOrder.getPayStatus().equals(ReservePayStatus.PAID)) {
			log.info("该订单已支付，直接返回");
			result.setPayStatus(ReservePayStatus.PAID);
			result.setPayTime(reservePayOrder.getPayTime());
			result.setReserveMoney(reservePayOrder.getPaidMoney());
			return result;
		}

		R<OrderQueryResultVO> payOrderStatus = paymentClient.queryUnifiedOrder(wxPayOrderId);
		if (Func.isEmpty(payOrderStatus) || Func.isEmpty(payOrderStatus.getData())) {
			log.error("-----------------------------------------------------------");
			log.error("查询订单状态失败,订单id为{}", wxPayOrderId);
			log.error("-----------------------------------------------------------");
			throw new ServiceException("查询订单状态失败");
		}

		// 状态校验，如果支付失败，直接返回
		if (PayStatus.SUCCESS.value() != payOrderStatus.getData().getStatus()) {
			log.info("查询支付失败");
			return result;
		}

		log.info("-----------------------------------------------------------");
		log.info("订单号为{}的订单支付成功,修改数据", wxPayOrderId);
		log.info("-----------------------------------------------------------");
		reservePayOrder.setPayStatus(ReservePayStatus.PAID);
		reservePayOrder.setPayTime(LocalDateTime.now());
		reservePayOrder.setPaidMoney(reservePayOrder.getMoney());
		reservePayOrderService.updateById(reservePayOrder);
		// 添加过期缓存到redis
		bladeRedis.setEx(ParkCacheNames.LECENT_PARK_RESERVE_PAY_ORDER + "::" + wxPayOrderId, wxPayOrderId, config.getOverdueTime() * 60L);

		// 获取车位与车牌对应关系
		for (ReservePark reservePark : reserveParks) {
			if (PayStatus.SUCCESS.value() == payOrderStatus.getData().getStatus()) {
				// 支付成功修改订单状态并且移除redis
				log.info("支付成功修改订单状态并且移除redis");
				if (!payAfterUpdateOrder(reservePark)) {
					throw new ServiceException("修改预约订单状态失败，失败订单号为：" + reservePark.getOrderId());
				}
			}
		}
		// 支付成功并且修改记录，返回修改后的数据
		result.setPayStatus(reservePayOrder.getPayStatus());
		result.setPayTime(LocalDateTime.now());
		result.setReserveMoney(reservePayOrder.getPaidMoney());
		return result;
	}

	/**
	 * 付款成功后修改订单状态
	 *
	 * @param reservePark
	 * @return
	 */
	private boolean payAfterUpdateOrder(ReservePark reservePark) {
		// 初始化需修改的预约订单数据
		ReservePark update = new ReservePark();
		update.setId(reservePark.getId());
		update.setReserveTime(new Date());
		update.setStatus(ReserveStatus.SUCCESS);

		// 支付成功后移除旧的定时缓存
		String redisOldCache = bladeRedis.get(getRedisKey(reservePark.getOrderId()));
		log.info("该订单的旧redis缓存为：{}", redisOldCache);
		if (StringUtils.isNotBlank(redisOldCache) && !bladeRedis.del(getRedisKey(reservePark.getOrderId()))) {
			log.error("移除redis缓存失败，失败的key为{}", getByOrderId(reservePark.getOrderId()));
			throw new ServiceException("移除redis缓存失败，失败的key为" + getByOrderId(reservePark.getOrderId()));
		} else {
			if (ReserveStatus.OVERDUE.equals(reservePark.getStatus())) {
				log.error("支付时间超过订单失效时间，执行自动退款");
				reserveFund(reservePark, reservePark.getReserveMoney());
				update.setStatus(ReserveStatus.OVERDUE);
			}
		}

		// 再次确认redis缓存是否被移除
		String tempCache = bladeRedis.get(getRedisKey(reservePark.getOrderId()));
		log.info("目前订单的redis缓存为：{}", tempCache);
		if (StringUtils.isNotBlank(tempCache)) {
			bladeRedis.del(getRedisKey(reservePark.getOrderId()));
		}

		String redisCache = bladeRedis.get(getRedisKey(reservePark.getOrderId()));
		if (StringUtils.isBlank(redisCache) && Func.isNotEmpty(reservePark.getReserveTime()) && Func.isNotEmpty(reservePark.getEndTime())) {
			long startCurrentTime = reservePark.getReserveTime().getTime();
			long endCurrentTime = reservePark.getEndTime().getTime();
			long expiringTime = (endCurrentTime - startCurrentTime) / 1000;
			// 添加过期缓存，到期自动执行
			log.info("重置订单号为{}订单的过期时间", reservePark.getOrderId());
			bladeRedis.setEx(getRedisKey(reservePark.getOrderId()), reservePark.getOrderId(), expiringTime);
		}
		try {
			ClientTodo clientTodo = new ClientTodo();
			clientTodo.setOpenId(reservePark.getOpenId());
			clientTodo.setPlate(reservePark.getPlate());
			ibUserPlateService.addWeChatPlate(clientTodo);
		} catch (Exception e) {
			log.error("", e);
		}
		return unifySaveOrUpdate(update);
	}

	/**
	 * 对预约订单进行补缴，由于产品设计这里是订单拆分，所以补缴时再生成一个支付订单
	 *
	 * @param resource
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String repairPayReserveMoney(ReserveParkDTO resource) {
		LecentAssert.notNull(resource.getId(), "未传入预约订单id");
		ReservePark reservePark = this.getById(resource.getId());
		LecentAssert.notNull(reservePark, "未找到该预约订单,数据错误，请联系系统管理员");
		Parklot parklot = ParkLotCaches.getParkLot(reservePark.getParkId());
		LecentAssert.notNull(parklot, "未找到预约订单中的停车场id");

		PayOrder reservePayOrder = new PayOrder();
		reservePayOrder.setMoney(reservePark.getReserveMoney());
		reservePayOrder.setTenantId(reservePark.getTenantId());
		reservePayOrder.setPayOrderId(SequenceNoUtils.generateNo());
		reservePayOrder.setPayStatus(ReservePayStatus.NOT_PAY);

		UnifiedOrderResultVO unifiedOrderResult = getWxPayOrder(parklot, reservePayOrder,
			resource.getReturnUrl() + "?wxPayOrderId=" + reservePayOrder.getPayOrderId(), parklot.getName());
		reservePayOrder.setReturnUrl(unifiedOrderResult.getPayData());
		reservePayOrderService.save(reservePayOrder);

		ReservePark update = new ReservePark();
		update.setId(resource.getId());
		update.setWxPayOrderId(reservePayOrder.getPayOrderId());

		unifySaveOrUpdate(update);

		return unifiedOrderResult.getPayData();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String reservePayBefore(ReserveParkDTO reservePark) {
		if (StringUtils.isBlank(reservePark.getWxPayOrderId())) {
			throw new ServiceException("未传入支付单id");
		}
		PayOrder reservePayOrder = reservePayOrderService.getOneByPayOrderId(reservePark.getWxPayOrderId());
		LecentAssert.notNull(reservePayOrder, "未找到微信支付单号对应的订单");

		if (reservePayOrder.getStatus().equals(ReservePayStatus.NOT_PAY)) {
			throw new ServiceException("该支付订单已过期");
		}

		if (StringUtils.isNotBlank(reservePayOrder.getReturnUrl())) {
			return reservePayOrder.getReturnUrl();
		}
		String parkLotName = getParkLotNameByWxPayOrder(reservePark.getWxPayOrderId());
		UnifiedOrderResultVO unifiedOrderResult = getWxPayOrder(null, reservePayOrder, reservePark.getReturnUrl(), parkLotName);
		// 将return设置到记录中
		PayOrder update = new PayOrder();
		update.setId(reservePayOrder.getId());
		update.setReturnUrl(unifiedOrderResult.getPayData());
		reservePayOrderService.updateById(update);
		return unifiedOrderResult.getPayData();
	}

	private UnifiedOrderResultVO getWxPayOrder(Parklot parklot, PayOrder reservePayOrder, String returnUrl, String parkLotName) {
		if (!(reservePayOrder.getMoney().compareTo(BigDecimal.ZERO) > 0)) {
			throw new ServiceException("预约离场时间过近，请选择至少半小时后");
		}
		ParklotPayWay parklotPayWay = payWayService.existPayWayByParkLotId(parklot.getId());

		UnifiedOrderDTO unifiedOrder = new UnifiedOrderDTO();
		unifiedOrder.setTenantId(reservePayOrder.getTenantId());
		unifiedOrder.setAttach("车位预约预约金");
		unifiedOrder.setBody(parkLotName + "：车位预约预约金");

		unifiedOrder.setOutTradeNo(reservePayOrder.getPayOrderId());
		unifiedOrder.setTotalFee(reservePayOrder.getMoney().toEngineeringString());
		unifiedOrder.setTradeType(PayScene.PAY_JSAPI.name());
		UnifiedOrderResultVO unifiedOrderResult;
		unifiedOrder.setReturnUrl(returnUrl);
		log.info("生成订单号为{}的预约金预支付订单", reservePayOrder.getPayOrderId());
		try {
			R<UnifiedOrderResultVO> result = paymentClient.payUnifiedOrder(unifiedOrder);
			if (Func.isEmpty(result)) {
				throw new ServiceException("生成预约付款订单失败");
			}
			unifiedOrderResult = result.getData();
		} catch (Exception e) {
			e.printStackTrace();
			log.error("调用生成微信支付订单失败");
			throw new ServiceException("调用生成微信支付订单失败");
		}
		if (Func.isEmpty(unifiedOrderResult) || StringUtils.isBlank(unifiedOrderResult.getPayData())) {
			throw new ServiceException("支付订单生成失败");
		}
		return unifiedOrderResult;
	}

	private String getParkLotNameByWxPayOrder(String wxPayOrderId) {
		List<ReservePark> reserveParks = list(new QueryWrapper<ReservePark>().lambda().eq(ReservePark::getWxPayOrderId, wxPayOrderId));
		if (reserveParks.isEmpty()) {
			throw new ServiceException("未找到支付订单号对应的预约订单");
		}
		Parklot parklot = ParkLotCaches.getParkLot(reserveParks.get(0).getParkId());
		LecentAssert.notNull(parklot, "未找到车场id对应的车场");
		return parklot.getName();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void orderPayAfter(String orderId) {
		ReservePark reservePark = getOneByOrderId(orderId);
		if (Func.isEmpty(reservePark)) {
			log.error("未找到订单id为{}对应的预约订单，请确认数据", orderId);
			return;
		}
		ReservePark update = new ReservePark();
		update.setId(reservePark.getId());
		update.setStatus(ReserveStatus.OVER);
		unifySaveOrUpdate(update);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean outPartAfter(ParkingOrder parkingOrder) {
		log.info("预约车辆开始出场，车牌为：{}", parkingOrder.getPlate());
		ReservePark reservePark = getOneByOrderId(parkingOrder.getReserveOrderId());
		if (Func.isEmpty(reservePark)) {
			log.error("未找到订单id为{}对应的预约订单，请确认数据", parkingOrder.getReserveOrderId());
			return false;
		}

		PayOrder reservePayOrder = reservePayOrderService.getOneByPayOrderId(reservePark.getWxPayOrderId());
		LecentAssert.notNull(reservePayOrder, "未找到微信支付单号对应的订单");
		ReservePark update = new ReservePark();
		update.setId(reservePark.getId());

		// 如果停车金额小于预约金并且系统配置退还预约金
//		if (ReservePayStatus.PAID.equals(reservePayOrder.getPayStatus()) && parkingOrder.getActualReceiveAmount().compareTo(reservePark.getReserveMoney()) < 0) {
//			ReserveParkConfig config = reserveParkConfigService.getConfig(reservePark.getParkId(), reservePark.getTenantId());
//			if (ReserveIsPayStatus.NEED_PAY.equals(config.getEarnestType())) {
//				BigDecimal refundMoney = reservePark.getReserveMoney().subtract(parkingOrder.getActualReceiveAmount());
//				reserveFund(reservePark, refundMoney);
//			}
//		}

		update.setCarStatus(ReserveCarStatus.OUTED);
		return unifySaveOrUpdate(update);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean cancelReserve(Long id) {
		ReservePark reservePark = getById(id);
		if (Func.isEmpty(reservePark)) {
			throw new ServiceException("找不到该id对应的订单");
		}
		if (!ReserveStatus.NOR_PAY_SERVE_MONEY.equals(reservePark.getStatus()) && !ReserveStatus.SUCCESS.equals(reservePark.getStatus())) {
			throw new ServiceException("该订单已过期失效，不需要取消预约");
		}
		ReserveParkConfig config = reserveParkConfigService.getConfig(reservePark.getParkId(), reservePark.getTenantId());
		long startTime = reservePark.getReserveTime().getTime();
		long nowTime = System.currentTimeMillis();
		if ((config.getOverdueTime() * 60 * 1000L + startTime) < nowTime) {
			throw new ServiceException("已超过系统规定的预约取消时间，当前不可取消预约");
		}
		PayOrder reservePayOrder = reservePayOrderService.getOneByPayOrderId(reservePark.getWxPayOrderId());
		LecentAssert.notNull(reservePayOrder, "未找到微信支付单号对应的订单");
		log.info("执行取消预约操作");
		ReservePark update = new ReservePark();
		update.setId(id);
		update.setStatus(ReserveStatus.CANCEL);
		if (ReservePayStatus.PAID.equals(reservePayOrder.getPayStatus()) && Func.isNotEmpty(reservePark.getReserveMoney())) {
			// 退款
			reserveFund(reservePark, reservePark.getReserveMoney());
		}
		// 移除缓存
		String redisOldCache = bladeRedis.get(getRedisKey(reservePark.getOrderId()));
		if (StringUtils.isNotBlank(redisOldCache) && !bladeRedis.del(getRedisKey(reservePark.getOrderId()))) {
			log.error("移除redis缓存失败，失败的key为{}", getByOrderId(reservePark.getOrderId()));
			throw new ServiceException("移除redis缓存失败，失败的key为" + getByOrderId(reservePark.getOrderId()));
		}
		// 取消预约移除占用车位
		releaseParkingPlace(reservePark.getParkId(), 1);
		List<Long> places = new ArrayList<>();
		places.add(reservePark.getPlaceId());
		parkingPlaceService.releasePlace(places);
		return unifySaveOrUpdate(update);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean refundCallback(String refundOrderId, int status) {
		if (StringUtils.isBlank(refundOrderId)) {
			throw new ServiceException("未传入退款订单id");
		}
		LecentAssert.notNull(status, "未传入退款订单状态");
		RefundRecord refundRecord = refundRecordService.getOneByOrderId(refundOrderId);
		if (Func.isEmpty(refundRecord)) {
			throw new ServiceException("未找到退款订单号对应的订单");
		}
		RefundRecord update = new RefundRecord();
		update.setId(refundRecord.getId());
		if (PayStatus.SUCCESS.value() == status) {
			update.setStatus(ReserveRefundStatus.SUCCESS);
		} else if (PayStatus.PROCESSING.value() == status) {
			return true;
		} else {
			update.setStatus(ReserveRefundStatus.FAIL);
		}

		return refundRecordService.updateById(update);
	}

	/**
	 * 退款
	 *
	 * @param reservePark
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public void reserveFund(ReservePark reservePark, BigDecimal refundMoney) {
		String fundOrderId = SequenceNoUtils.generateNo();

		RefundOrderDTO refundOrderDTO = new RefundOrderDTO();
		refundOrderDTO.setOutTradeNo(reservePark.getWxPayOrderId());

		refundOrderDTO.setOutRefundNo(fundOrderId);
		refundOrderDTO.setRefundFee(refundMoney.toEngineeringString());
		refundOrderDTO.setRefundDesc("取消车位预约，退款");
		refundOrderDTO.setTenantId(reservePark.getTenantId());
		R<String> r = sysClient.getParamValue("sys_gateway_host");
		if (Func.isEmpty(r) || StringUtils.isBlank(r.getData())) {
			throw new ServiceException("获取系统网关地址失败");
		}
		refundOrderDTO.setReturnUrl(r.getData() + "/lecent-park/reservepark/refundCallback");

		// refundOrderDTO.setTenantId(reservePark.getTenantId());

		// 插入退款记录
		RefundRecord refundRecord = new RefundRecord();
		refundRecord.setMoney(refundMoney);
		refundRecord.setOutsideId(reservePark.getOrderId());
		refundRecord.setRefundOrderId(fundOrderId);
		refundRecord.setReason(RefundEnum.RESERVE_REFUND.reason());
		refundRecord.setStatus(ReserveRefundStatus.ING);
		refundRecord.setTenantId(reservePark.getTenantId());

		R<RefundOrderResultVO> result = paymentClient.refundOrder(refundOrderDTO);
		if (Func.isEmpty(result) || Func.isEmpty(result.getData())) {
			refundRecord.setStatus(ReserveRefundStatus.FAIL);
		}

		refundRecordService.save(refundRecord);
	}

	@Override
	public boolean refundReserveOrder(String orderId) {
		ReservePark reservePark = getByOrderId(orderId);
		if (Func.isEmpty(reservePark)) {
			throw new ServiceException("未找到订单号对应的预约订单");
		}
		RefundRecord refundRecord = refundRecordService.getByOutId(reservePark.getOrderId());
		if (Func.isNotEmpty(refundRecord) && ReserveRefundStatus.SUCCESS.equals(refundRecord.getStatus())) {
			reserveFund(reservePark, refundRecord.getMoney());
		} else {
			reserveFund(reservePark, reservePark.getReserveMoney());
		}
		return true;
	}

	/**
	 * 释放车位
	 *
	 * @param parkId
	 * @param num
	 */
	public void releaseParkingPlace(Long parkId, Integer num) {
		//编辑车位数量等 重新计算
		ReloadParkPlaceEvent reloadParkPlaceEvent = new ReloadParkPlaceEvent();
		publisher.publishEvent(reloadParkPlaceEvent);

		//归还车位
		ReturnParkPlaceEvent returnParkPlaceEvent = new ReturnParkPlaceEvent();
		returnParkPlaceEvent.setParkLotId(parkId);
		returnParkPlaceEvent.setParkPlaceNumber(num);

		publisher.publishEvent(returnParkPlaceEvent);

	}

	@Override
	public TempParkingChargeRuleVO getParkChargeRuler(Long parkId) {
		return chargeRuleService.selectStandardOneByParkLotId(parkId);
	}

	@Override
	public ReservePark getReserveParkById(Long id) {
		return getById(id);
	}
}
