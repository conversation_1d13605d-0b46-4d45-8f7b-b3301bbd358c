package com.lecent.park.wrapper;

import com.lecent.park.entity.ParkMerchant;
import com.lecent.park.vo.ParkMerchantVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 酒店商户表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
public class ParkMerchantWrapper extends BaseEntityWrapper<ParkMerchant, ParkMerchantVO>  {

	public static ParkMerchantWrapper build() {
		return new ParkMerchantWrapper();
 	}

	@Override
	public ParkMerchantVO entityVO(ParkMerchant parkMerchant) {
		ParkMerchantVO parkMerchantVO = BeanUtil.copy(parkMerchant, ParkMerchantVO.class);

		//User createUser = UserCache.getUser(parkMerchant.getCreateUser());
		//User updateUser = UserCache.getUser(parkMerchant.getUpdateUser());
		//parkMerchantVO.setCreateUserName(createUser.getName());
		//parkMerchantVO.setUpdateUserName(updateUser.getName());

		return parkMerchantVO;
	}

}
