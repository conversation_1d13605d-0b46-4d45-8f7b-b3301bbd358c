package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.dto.WechatUserInfoDTO;
import com.lecent.park.entity.WechatUserInfo;
import com.lecent.park.mapper.WechatUserInfoMapper;
import com.lecent.park.service.IWechatUserInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.resource.constant.SmsConstant;
import org.springblade.resource.feign.ISmsClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;

/**
 * 微信用户基础信息 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Service
public class WechatUserInfoServiceImpl extends BaseServiceImpl<WechatUserInfoMapper, WechatUserInfo> implements IWechatUserInfoService {

	@Autowired
	private ISmsClient smsClient;
	@Autowired
	private BladeRedis bladeRedis;

	@Override
	public WechatUserInfo getOneByOpenId(String openId) {
		return getOne(new QueryWrapper<WechatUserInfo>().lambda().eq(WechatUserInfo::getOpenId, openId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean bindPhone(WechatUserInfoDTO wechatUserInfo) {
		if (StringUtils.isBlank(wechatUserInfo.getPhone())) {
			throw new ServiceException("未传入用户电话号码");
		}
		if (StringUtils.isBlank(wechatUserInfo.getOpenId())) {
			throw new ServiceException("未传入openId");
		}
		String validId = bladeRedis.get(ParkCacheNames.LECENT_PARK_WX_NOTE_PHONE + "::" + wechatUserInfo.getPhone());
		if (StringUtils.isBlank(validId)) {
			throw new ServiceException("已过期，请重新获取短信验证码");
		}
		R r = smsClient.validateMessage(SmsConstant.SEND_VALIDATE, wechatUserInfo.getPhone(), validId, wechatUserInfo.getValidCode());
		if (r.getCode() != 200) {
			throw new ServiceException("验证码输入错误，请重试");
		}
		WechatUserInfo search = getOneByOpenId(wechatUserInfo.getOpenId());
		if (search != null) {
			search.setPhone(wechatUserInfo.getPhone());
			return updateById(search);
		}
		WechatUserInfo save = BeanUtil.copy(wechatUserInfo, WechatUserInfo.class);
		assert save != null;
		return save(save);
	}


	@Override
	public boolean sendValidCode(String phone) {
		R r = smsClient.sendValidate("huaweicloud-val-1", phone);
		// 添加过期缓存到redis
		if (r.getCode() != 200) {
			throw new ServiceException("发送短信验证码失败");
		}
		LinkedHashMap map = (LinkedHashMap) r.getData();
		if (map.isEmpty()) {
			throw new ServiceException("发送短信验证码失败");
		}
		bladeRedis.setEx(ParkCacheNames.LECENT_PARK_WX_NOTE_PHONE + "::" + phone, map.get("id"), 30 * 60L);
		return true;
	}

}
