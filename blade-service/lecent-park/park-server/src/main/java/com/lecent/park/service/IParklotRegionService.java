package com.lecent.park.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParklotRegion;
import com.lecent.park.vo.ParklotRegionPlaceVO;
import com.lecent.park.vo.ParklotRegionVO;
import com.lecent.park.vo.RemainPlaceNumVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 车场区域信息表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public interface IParklotRegionService extends BaseService<ParklotRegion> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotRegion
	 * @return
	 */
	IPage<ParklotRegionVO> selectParklotRegionPage(IPage<ParklotRegionVO> page, ParklotRegionVO parklotRegion);

	/**
	 * 根据车场id获取区域列表
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	List<ParklotRegion> getByParklotId(Long parklotId);

	/**
	 * 根据楼层id获取区域名称列表
	 *
	 * @param id 楼层id
	 * @return
	 */
	List<String> getByFloorId(Long id);

	boolean submit(ParklotRegion parklotRegion);

	List<ParklotRegion> dropListByFloorId(Long floorId);

	boolean removeRegion(List<Long> toLongList);

	RemainPlaceNumVO getRemainPlaceNum(Long floorId);

	ParklotRegionVO detail(QueryWrapper<ParklotRegion> queryWrapper);

	List<ParklotRegion> dropListByParklotId(Long parklotId);

	List<ParklotRegionPlaceVO> getRegionPlaceByFloorId(Long floorId);

	List<ParklotRegionPlaceVO> getRegionPlaceByParklotId(Long parklotId);

	List<ParklotRegion> getRegionFloorId(Long floorId);

	/**
	 * 获取区域信息
	 *
	 * @param name
	 * @return
	 */
	List<ParklotRegion> getParkingLotRegionList(String name);
}
