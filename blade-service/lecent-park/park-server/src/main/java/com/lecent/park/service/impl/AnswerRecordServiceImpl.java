package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.entity.AnswerRecord;
import com.lecent.park.entity.Channel;
import com.lecent.park.entity.Parklot;
import com.lecent.park.mapper.AnswerRecordMapper;
import com.lecent.park.service.IAnswerRecordService;
import com.lecent.park.service.IChannelService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.AnswerRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.DateUtils;
import org.springblade.common.utils.OrderGenerateUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客服接听记录  服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Slf4j
@Service
public class AnswerRecordServiceImpl extends
	BaseServiceImpl<AnswerRecordMapper, AnswerRecord> implements IAnswerRecordService {

	/**
	 * sessionID 前缀标识
	 */
	private static final int PREFIX_NUMBER = 8;

	/**
	 * 车场ID位数
	 */
	private static final int PARKING_ID_NUMBER = 5;

	/**
	 * 时长
	 */
	private static final String PATTERN = "dd天hh小时mm分钟ss秒";

	private final IParklotService parklotService;

	private final IChannelService channelService;

	public AnswerRecordServiceImpl(IParklotService parklotService, IChannelService channelService) {
		this.parklotService = parklotService;
		this.channelService = channelService;
	}

	@Override
	public IPage<AnswerRecordVO> selectAnswerRecordPage(IPage<AnswerRecordVO> page, AnswerRecordVO answerRecord) {
		List<AnswerRecordVO> records = baseMapper.selectAnswerRecordPage(page, answerRecord);

		for (AnswerRecordVO vo : records) {
			Long durationMs = vo.getDurationMs();
			if (null != durationMs) {
				String timeStr = DateUtils.betweenSecondToTime(durationMs / 1000, PATTERN);
				vo.setDuration(timeStr);
			}
		}
		return page.setRecords(records);
	}

	@Override
	public boolean customSave(AnswerRecord answerRecord) {
		// sessionId=1（固定值）+00001（车场ID）+01（车场通道编号）+00000000=000010100000000
		String uid = answerRecord.getSessionId();

		if (null == uid || uid.length() < PREFIX_NUMBER) {
			log.error("uid错误uid={}", uid);
			return true;
		}

		uid = uid.substring(1, PREFIX_NUMBER);
		String parkingId = uid.substring(0, PARKING_ID_NUMBER);
		String channelNo = uid.substring(PARKING_ID_NUMBER);
		Parklot parklot = ParkLotCaches.getParkLot(Long.valueOf(parkingId));
		if (null == parklot) {
			log.error("车场信息找不到parkingId={}", parkingId);
			return true;
		}

		Channel channel = channelService.getChannelByNo(Long.valueOf(parkingId), Integer.valueOf(channelNo));
		if (null == channel) {
			log.error("车场通道信息找不到parkingId={}, channelNo={}", parkingId, channelNo);
			return true;
		}

		String sessionId = OrderGenerateUtils.generateNo("KF");
		AnswerRecord record = AnswerRecord.builder()
			.sessionId(sessionId)
			.parklotId(Long.valueOf(parkingId))
			.parklotName(parklot.getName())
			.channelNo(Integer.valueOf(channelNo))
			.channelName(channel.getName())
			.channelType(Integer.valueOf(1).equals(channel.getType()) ? "入口" : "出口")
			.durationMs(answerRecord.getDurationMs())
			.createUserName(AuthUtil.getUserName())
			.build();

		return save(record);
	}

}
