package com.lecent.park.listener;

import com.lecent.park.event.parking.NoticeTempOrderUnPayEvent;
import com.lecent.park.service.IDParklotSmsSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 车场(短信)通知相关
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ParkNoticeListener {

	@Autowired
	private IDParklotSmsSettingService  parklotSmsSettingService;
	@Async
	@EventListener
	public void unPayNoticeListener(NoticeTempOrderUnPayEvent event) {
		log.debug("监听到通知缴费" + "相关信息:msg={}", event);
		//TODO调用短信通知业务接口
		parklotSmsSettingService.isSendSms(event);
	}
}
