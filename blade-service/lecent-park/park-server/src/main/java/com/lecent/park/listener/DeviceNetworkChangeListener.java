package com.lecent.park.listener;

import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.device.entity.Device;
import com.lecent.park.core.notify.base.BaseMsgReceiver;
import com.lecent.park.service.IParklotDeviceRetService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.LecentAppConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 设备网络状态变化监听
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceNetworkChangeListener extends BaseMsgReceiver<List<Device>> {

	@Resource
	private IParklotDeviceRetService parklotDeviceRetService;

	protected DeviceNetworkChangeListener() {
		super(LecentAppConstant.APPLICATION_DEVICE_NAME, DeviceMQConstant.NETWORK_CHANGE_EVENT_CODE);
	}

	@Override
	public void accept(List<Device> devices) {
		this.parklotDeviceRetService.updateNetworkStatus(devices);
	}



}
