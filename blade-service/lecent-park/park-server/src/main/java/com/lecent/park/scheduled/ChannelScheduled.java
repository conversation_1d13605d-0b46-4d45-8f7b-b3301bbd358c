package com.lecent.park.scheduled;

import com.lecent.park.service.ClientService;
import com.lecent.park.service.ITempParkingOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 扫描通道付款成功不抬杆
 * <AUTHOR>
 */
@Component
@Slf4j
public class ChannelScheduled {
	@Autowired
	private ClientService clientService;
	@Autowired
	private ITempParkingOrderService temOrderService;


	/**
	 * 扫描支付成功后未更新的订单(8秒执行一次)
	 */
//	@Scheduled(cron = "0 0/1 * * * ? ")
//	@RedisLock(value = "lecent:park::timedTask:lock:tempPaySuccess")
//	public void queryPaySuccessOrderScheduled() {
//		List<String> tradeNoList = temOrderService.getTradeNoList();
//		if (Func.isEmpty(tradeNoList)){
//			return;
//		}
//		for (String tradeNo:tradeNoList) {
//			log.info("查询到有支付成功后未更新的订单：tradeNoList={}", JSON.toJSONString(tradeNoList));
//			try {
//				clientService.mqPaySuccessCallback(tradeNo);
//			}catch (Exception e){
//				log.error("queryPaySuccessOrderScheduled tradeNo="+tradeNo,e);
//			}
//		}
//	}

}
