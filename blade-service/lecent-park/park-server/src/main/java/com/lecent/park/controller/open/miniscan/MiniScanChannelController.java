package com.lecent.park.controller.open.miniscan;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.common.exception.NoEnterException;
import com.lecent.park.common.exception.NoTodoException;
import com.lecent.park.constant.ChannelConstant;
import com.lecent.park.dto.MiniScanChannelTodoDTO;
import com.lecent.park.dto.ResParkingOrderInfo;
import com.lecent.park.en.channeltodo.TypeEnum;
import com.lecent.park.service.ClientService;
import com.lecent.park.service.ITempParkingOrderService;
import com.lecent.park.vo.MiniScanPayDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.common.payment.PayResult;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 通道业务接口
 * 停车系统微信小程序进出场、车牌授权扫码端,
 * 对外接口路径为：http://localhost:80/open-api/lecent-park/mini-scan/子路径
 *
 * <AUTHOR>
 * @since 2022-03-09
 */
@Slf4j
@RestController
@Validated
@AllArgsConstructor
@RequestMapping(value = {OpenApiConstant.OPEN_API + "/mini-scan","/test-open-api/mini-scan"})
@Api(value = "车场通道业务服务接口", tags = "车场通道业务服务接口")
public class MiniScanChannelController extends BladeController {
	/**
	 * 无待办返回code
	 */
	private static final int NO_TODO = 2001;

	/**
	 * 无进场记录返回code
	 */
	private static final int NO_ENTER_RECORD = 2002;

	/**
	 * 进、出场成功
	 */
	private static final int ENTER_EXIT_SUCCESS = 2000;

	private ITempParkingOrderService tempParkingOrderService;

	private ClientService clientService;

	/**
	 * 查询待办车辆订单信息，及进场绑定无牌车（返回用户手机号、解密用户信息）
	 */
	@GetMapping("/v1/channel/clientTrigger")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询待办车辆订单信息，及进场绑定无牌车（返回用户手机号、解密用户信息）")
	public R<ResParkingOrderInfo> miniScanClientTrigger(@Valid MiniScanChannelTodoDTO channelTodoDTO) {

		ResParkingOrderInfo resInfo;
		try {
			resInfo = clientService.miniScanClientTrigger(channelTodoDTO);

			// 正常出入
			if (resInfo.getTodoStatus() != ChannelConstant.TODO_STATUS_CREATE && resInfo.getTodoStatus() != ChannelConstant.TODO_STATUS_DISABLE_ENTER) {
				return R.data(ENTER_EXIT_SUCCESS, resInfo, "成功");
			}
			// 等待支付
			else if (TypeEnum.LEAVE_STOP_PAYING.getName().equals(resInfo.getTodoType())) {
				return R.data(resInfo);
			}
			// 无进场记录
			else if (TypeEnum.LEAVE_STOP_NO_ENTER_RECORD.getName().equals(resInfo.getTodoType())) {
				throw new NoEnterException();
			} else {
				throw new ServiceException(TypeEnum.getMsg(resInfo.getTodoType()));
			}
		} catch (NoTodoException ntEx) {

			return R.fail(NO_TODO, "当前无待办");
		} catch (NoEnterException nex) {

			return R.fail(NO_ENTER_RECORD, "无进场记录");
		}
	}

	/**
	 * 停车缴费发起支付
	 */
	@PostMapping("/v1/channel/parking-order/pay")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "停车缴费发起支付", notes = "停车缴费发起支付")
	public R<PayResult> payment(@RequestBody @Valid MiniScanPayDTO payDTO) {
		return R.data(clientService.miniScanPay(payDTO));
	}


	/**
	 * 查询停车缴费付款是否成功
	 */
	@GetMapping("/v1/channel/parking-order/pay-result")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "parkingOrderPayResult", notes = "查询停车缴费付款是否成功")
	public R<PayResult> parkingOrderPayResult(String outTradeNo) {
		return R.data(tempParkingOrderService.queryParkingOrderPayResult(outTradeNo));
	}



}
