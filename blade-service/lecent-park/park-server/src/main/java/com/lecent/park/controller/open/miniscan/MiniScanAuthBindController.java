package com.lecent.park.controller.open.miniscan;

import com.lecent.park.dto.ClientTodo;
import com.lecent.park.dto.ReqVisitorAuth;
import com.lecent.park.service.ICardService;
import com.lecent.park.service.IParkMerchantParklotPlateService;
import com.lecent.park.service.IVisitorAuthService;
import com.lecent.park.vo.ParkMerchantNoPlateAuthDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 授权绑定车辆信息
 *
 * <AUTHOR>
 * @date 2022/03/11 12:10
 */
@RestController
@Validated
@AllArgsConstructor
@RequestMapping(OpenApiConstant.OPEN_API + "mini-scan")
@Api(value = "小程序无牌车绑定", tags = "小程序无牌车绑定")
public class MiniScanAuthBindController {

	private final IParkMerchantParklotPlateService parkMerchantParkLotPlateService;

	private final ICardService cardService;

	private final IVisitorAuthService visitorAuthService;

	/**
	 * 无牌车商户端绑定
	 */
	@PostMapping("/v1/merchant-auth/no-plate-bind")
	@ApiOperation(value = "无牌车商户端绑定", notes = "无牌车商户端绑定")
	public R noPlateAuth(@Valid @RequestBody ParkMerchantNoPlateAuthDTO authPlate) {
		return R.data(parkMerchantParkLotPlateService.noPlateAuth(authPlate.getId(), authPlate.getUnionId()));
	}

	/**
	 * 无牌车月卡车绑定
	 */
	@PostMapping("/v1/month-card/no-plate-bind")
	@ApiOperation(value = "cardBindingOpenId", notes = "无牌车月卡车绑定")
	public R<Boolean> monthCardBind(@RequestBody ClientTodo clientTodo) {
		LecentAssert.notNull(clientTodo.getCardId(), "月卡id不允许空！");
		LecentAssert.notBlank(clientTodo.getUnionId(), "微信uid不允许空！");

		// 不变动原有业务直接给openId负值
		clientTodo.setOpenId(clientTodo.getUnionId());
		return R.data(cardService.cardBindingOpenId(clientTodo));
	}

	/**
	 * 访客授权绑定
	 */
	@PostMapping("/v1/visitor-auth/bind")
	@ApiOperation(value = "cardBindingOpenId", notes = "访客授权绑定")
	public R<Boolean> visitorAuthBind(@Validated(ReqVisitorAuth.AddReqVisitorAuth.class) @RequestBody ReqVisitorAuth reqVisitorAuth) {
		return R.data(visitorAuthService.miniVisitorAuthBind(reqVisitorAuth));
	}
}
