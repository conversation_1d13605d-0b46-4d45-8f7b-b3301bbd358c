package com.lecent.park.controller.wechat;

import com.lecent.park.en.coupon.CouponStatusEnum;
import com.lecent.park.discount.coupon.service.IUserCouponService;
import com.lecent.park.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠劵服务
 * <AUTHOR>
 * @since 2021-04-09
 */
@RestController
@Validated
@AllArgsConstructor
@RequestMapping("/wechat/coupon")
@Api(value = "优惠劵服务", tags = "优惠劵服务")
public class WechatCouponController extends BladeController {

	private IUserCouponService userCouponService;

	/**
	 * 我的待使用优惠劵
	 */
	@GetMapping("/un-use-coupon")
	@ApiOperation(value = "我的待使用优惠劵", notes = "我的待使用优惠劵")
	public R<List<CCBCouponVO>> getUnUsedCoupon() {
		return R.data(userCouponService.getListByStatus(CouponStatusEnum.UN_USED.getValue()));
	}

	/**
	 * 我的已使用优惠劵
	 */
	@GetMapping("/used-coupon")
	@ApiOperation(value = "我的已使用优惠劵", notes = "我的已使用优惠劵")
	public R<List<CCBCouponVO>> getUsedCoupon() {
		return R.data(userCouponService.getListByStatus(CouponStatusEnum.USED.getValue()));
	}

	/**
	 * 我的已过期优惠劵
	 */
	@GetMapping("/overdue-coupon")
	@ApiOperation(value = "我的已过期优惠劵", notes = "我的已过期优惠劵")
	public R<List<CCBCouponVO>> getOverDueCoupon() {
		return R.data(userCouponService.getListByStatus(CouponStatusEnum.OVERDUE.getValue()));
	}

	/**
	 * 我的可赠送优惠劵列表
	 */
	@GetMapping("/available-give-list")
	@ApiOperation(value = "我的可赠送优惠劵列表", notes = "我的可赠送优惠劵列表")
	public R<List<CCBCouponVO>> availableGiveList() {
		return R.data(userCouponService.availableGiveList());
	}

	/**
	 * 我的优惠劵详情
	 */
	@GetMapping("/detail/{userCouponId}")
	@ApiOperation(value = "我的优惠劵详情", notes = "我的优惠劵详情")
	public R<CCBCouponDetailVO> couponDetail(@PathVariable Long userCouponId) {
		return R.data(userCouponService.couponDetail(userCouponId));
	}

	/**
	 * 我赠送给某个亲朋好友的赠送记录
	 *
	 * @param giveOtherUserId 好友用户id
	 * @return 优惠劵列表
	 */
	@GetMapping("/list/{giveOtherUserId}")
	@ApiOperation(value = "亲朋好友赠送记录", notes = "亲朋好友赠送记录")
	public R<List<CCBCouponGivenVO>> listByGiveOtherUserId(@PathVariable Long giveOtherUserId) {
		return R.data(userCouponService.listByGiveOtherUserId(giveOtherUserId));
	}


	/**
	 * 给好友赠送优惠劵
	 *
	 * @param userCouponId 我的用户优惠劵id
	 * @param phone        好友手机号
	 * @return 操作结果
	 */
	@PostMapping("/give-other-coupon")
	@ApiOperation(value = "给好友赠送优惠劵", notes = "给好友赠送优惠劵")
	public R<Boolean> giveOtherCoupon(@RequestParam @NotNull(message = "用户优惠劵ID不能为空") Long userCouponId,
									  @RequestParam @NotEmpty(message = "好友手机号不能为空") String phone) {
		return R.status(userCouponService.giveOtherCoupon(userCouponId, phone));
	}


	/**
	 * 用户在某个车场出场缴费时，最大的优惠金额
	 *
	 * @param totalAmount 应付金额
	 * @param parklotId   车场id
	 * @return 最大金额
	 */
	@GetMapping("/max-discount-amount")
	@ApiOperation(value = "用户在某个车场出场缴费时，最大的优惠金额", notes = "获取最大的优惠金额")
	public R<CCBCouponAmountVO> getMaxDiscountAmount(String totalAmount, String parklotId) {
		return R.data(userCouponService.getMaxDiscountAmount(totalAmount, parklotId, StringPool.EMPTY));
	}

	/**
	 * 用户支付时，可用的优惠劵列表
	 *
	 * @param totalAmount 应付金额
	 * @param parklotId   车场id
	 * @return 优惠劵列表
	 */
	@GetMapping("/pay-available-coupon")
	@ApiOperation(value = "用户支付时，可用的优惠劵列表", notes = "用户支付时，可用的优惠劵列表")
	public R<CCBPayCouponVO> payAvailableCoupon(@RequestParam @NotNull(message = "金额不能为空") BigDecimal totalAmount,
												@RequestParam @NotNull(message = "车场不能为空") Long parklotId,
												@RequestParam @NotEmpty(message = "车牌号不能为空") String plate) {
		return R.data(userCouponService.payAvailableCouponList(StringPool.EMPTY, totalAmount, parklotId, StringPool.EMPTY, StringPool.EMPTY, plate));
	}

	/**
	 * 用户支付时，不可用的优惠劵列表
	 *
	 * @param totalAmount 应付金额
	 * @param parklotId   车场id
	 * @return 优惠劵列表
	 */
	@GetMapping("/pay-un-available-coupon")
	@ApiOperation(value = "用户支付时，不可用的优惠劵列表", notes = "用户支付时，不可用的优惠劵列表")
	public R<List<CCBCouponVO>> payUnAvailableCoupon(@RequestParam @NotEmpty(message = "金额不能为空") String totalAmount,
													 @RequestParam @NotEmpty(message = "车场不能为空") String parklotId,
													 @RequestParam @NotEmpty(message = "车牌号不能为空") String plate) {
		return R.data(userCouponService.payUnAvailableCouponList(totalAmount, parklotId, plate));
	}

	/**
	 * 用户注册完成，获取注册赠送的优惠劵
	 *
	 * @return 优惠劵列表
	 */
	@GetMapping("/list-by-register")
	@ApiOperation(value = "用户注册完成，获取注册赠送的优惠劵", notes = "用户注册完成，获取注册赠送的优惠劵")
	public R<List<CCBCouponVO>> getListByRegister() {
		return R.data(userCouponService.getListByRegister());
	}

	/**
	 * 用户登录完成，获取7天未登录赠送的优惠劵
	 *
	 * @return 优惠劵列表
	 */
	@GetMapping("/list-by-login")
	@ApiOperation(value = "用户登录完成，获取登录赠送的优惠劵", notes = "用户登录完成，获取登录赠送的优惠劵")
	public R<List<CCBCouponVO>> getListByLogin() {
		return R.data(userCouponService.getListByLogin());
	}

	/**
	 * 用户首次绑定车牌完成，获取首次绑定车牌赠送的优惠劵
	 *
	 * @return 优惠劵列表
	 */
	@GetMapping("/list-by-bind")
	@ApiOperation(value = "用户首次绑定车牌完成，获取首次绑定车牌赠送的优惠劵", notes = "用户首次绑定车牌完成，获取首次绑定车牌赠送的优惠劵")
	public R<List<CCBCouponVO>> getListByBind() {
		return R.data(userCouponService.getListByBind());
	}

	/**
	 * 用户跳转到临停缴费页面，获取赠送的优惠劵
	 *
	 * @return 优惠劵列表
	 */
	@GetMapping("/list-by-temp-pay")
	@ApiOperation(value = "用户跳转到临停缴费页面，获取赠送的优惠劵", notes = "用户跳转到临停缴费页面，获取赠送的优惠劵")
	public R<List<CCBTempPayCoupon>> getListByTempPay(String openId) {
		return R.data(userCouponService.getListByTempPay(openId));
	}

	/**
	 * 用户跳转到支付成功页面，查看是否有需要赠送的优惠劵标识
	 *
	 * @return 优惠劵列表
	 */
	@GetMapping("/coupon-flag-by-success-pay")
	@ApiOperation(value = "用户跳转到支付成功页面，查看是否有需要赠送的优惠劵标识", notes = "用户跳转到支付成功页面，查看是否有需要赠送的优惠劵标识")
	public R<CCBCouponFlagVO> getCouponFlagBySuccessPay(String openId) {
		return R.data(userCouponService.getCouponFlagBySuccessPay(openId));
	}

	@GetMapping("/not-received-coupon-by-success-pay")
	@ApiOperation(value = "用户跳转到支付成功页面，查看待领取的优惠劵", notes = "用户跳转到支付成功页面，查看待领取的优惠劵")
	public R<List<CCBTempPayCoupon>> getNotReceivedCoupon() {
		return R.data(userCouponService.getNotReceivedCoupon());
	}

	@GetMapping("/not-received-coupon-by-wait-pay")
	@ApiOperation(value = "用户跳转到代缴费页面，查看待领取的优惠劵", notes = "用户跳转到支付成功页面，查看待领取的优惠劵")
	public R<List<CCBTempPayCoupon>> getNotReceivedCouponByWaitPay() {
		return R.data(userCouponService.getNotReceivedCouponByWaitPay());
	}

	/**
	 * 用户跳转到支付成功页面，获取赠送的优惠劵
	 *
	 * @return 优惠劵列表
	 */
	@GetMapping("/list-by-success-pay")
	@ApiOperation(value = "用户跳转到支付成功页面，获取赠送的优惠劵", notes = "用户跳转到支付成功页面，获取赠送的优惠劵")
	public R<List<CCBTempPayCoupon>> getListBySuccessPay(String openId) {
		return R.data(userCouponService.getListBySuccessPay(openId));
	}
}
