package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.FreeCardTypeDTO;
import com.lecent.park.entity.FreeCardType;
import com.lecent.park.vo.FreeCardTypeVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 免费车类型管理接口类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface IFreeCardTypeService extends BaseService<FreeCardType> {

	/**
	 * 自定义分页
	 *
	 * @param page         分页对象
	 * @param freeCardType 查询条件
	 * @return 分页结果
	 */
	IPage<FreeCardTypeVO> selectFreeCardTypePage(IPage<FreeCardTypeVO> page, FreeCardTypeVO freeCardType);

	/**
	 * 保存免费卡类型
	 *
	 * @param freeCardTypeDTO 免费卡类型DTO
	 * @return 保存成功返回true，否则返回false
	 */
	boolean saveTypes(FreeCardTypeDTO freeCardTypeDTO);

	/**
	 * 根据名称获取免费卡类型
	 *
	 * @param name 免费卡类型名称
	 * @return 免费卡类型对象
	 */
	FreeCardType getByName(String name);

	/**
	 * 获取所有免费卡类型
	 *
	 * @return 免费卡类型列表
	 */
	List<FreeCardType> listAll();

}
