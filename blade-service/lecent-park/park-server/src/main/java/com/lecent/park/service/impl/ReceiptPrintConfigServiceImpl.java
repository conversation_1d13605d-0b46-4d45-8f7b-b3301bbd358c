package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ReceiptPrintDTO;
import com.lecent.park.entity.*;
import com.lecent.park.mapper.ReceiptPrintConfigMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.PdaReceiptPrintConfigVO;
import com.lecent.park.vo.ReceiptPrintConfigVO;
import org.apache.commons.lang.StringUtils;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.CacheUtils;
import org.springblade.common.utils.SequenceNoUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.Tenant;
import org.springblade.system.feign.ISysClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;

/**
 * 小票打印配置 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Service
public class ReceiptPrintConfigServiceImpl
	extends BaseServiceImpl<ReceiptPrintConfigMapper, ReceiptPrintConfig> implements IReceiptPrintConfigService {

	@Resource
	private ISysClient sysClient;
	@Resource
	private IParklotService parklotService;
	@Resource
	private IParkingPlaceService parkingPlaceService;
	@Resource
	private IReceiptPrintLogService receiptPrintLogService;
	@Resource
	private IParkingOrderService parkingOrderService;
	@Resource
	private IChannelTodoService channelTodoService;

	@Override
	public IPage<ReceiptPrintConfigVO> selectReceiptPrintConfigPage(IPage<ReceiptPrintConfigVO> page,
																	ReceiptPrintConfigVO receiptPrintConfig) {
		return page.setRecords(baseMapper.selectReceiptPrintConfigPage(page, receiptPrintConfig));
	}

	@Override
	public PdaReceiptPrintConfigVO getPrintContentByParkingId(Long parkingId) {
		String newPrintSn = SequenceNoUtils.generateNo();
		String newContent = getPrintContent(parkingId, newPrintSn);
		// 兼容 pda 旧版本保留
		String printSn = SequenceNoUtils.generateNo();
		String printContent = getPrintContent(parkingId, printSn, true);
		return PdaReceiptPrintConfigVO.builder()
			.content(printContent)
			.newContent(newContent)
			.parkingId(parkingId)
			.printSn(newPrintSn)
			.build();
	}

	@Override
	public void receiptPrint(ReceiptPrintDTO printDTO) {
		String content = getPrintContent(printDTO.getParkingId(), printDTO.getPrintSn());
		// 创建打印记录
		ReceiptPrintLog receiptPrintLog = new ReceiptPrintLog();
		receiptPrintLog.setCreateUser(AuthUtil.getUserId());
		receiptPrintLog.setSn(printDTO.getPrintSn());
		receiptPrintLog.setCreateTime(new Date());
		receiptPrintLog.setParkingId(printDTO.getParkingId());
		receiptPrintLog.setContent(content);
		receiptPrintLogService.save(receiptPrintLog);
	}

	/**
	 * 获取小票内容
	 * @param parkingId 停车记录id
	 * @param printSn 打印sn
	 * @return 小票内容
	 */
	private String getPrintContent(Long parkingId, String printSn) {
		return getPrintContent(parkingId, printSn, false);
	}

	/**
	 * 获取小票内容
	 * @apiNote  兼容pda旧版本拓展方法
	 * @param parkingId 停车记录id
	 * @param printSn 打印sn
	 * @return 小票内容
	 */
	private String getPrintContent(Long parkingId, String printSn, boolean isBase64QrCode) {
		String cacheName = CacheNames.CACHE_NAME + "printContent:" + printSn;
		return CacheUtils.get(
			cacheName,
			String.class,
			() -> {
				// 停车记录
				ParkingOrder parkingOrder = parkingOrderService.getById(parkingId);
				// 停车代办
				String parkingType = "临停车";
				if (Func.isNotEmpty(parkingOrder.getExitTodoId())) {
					ChannelTodo channelTodo = channelTodoService.getById(parkingOrder.getExitTodoId());
					parkingType = Func.isNotBlank(channelTodo.getTypeDes()) ? channelTodo.getTypeDes() : parkingType;
				}
				// 车场信息
				Parklot parklot = parklotService.getById(parkingOrder.getParklotId());
				// 车位信息
				ParkingPlace parkingPlace = parkingPlaceService.getById(parkingOrder.getPlaceId());
				// 小票打印配置信息
				ReceiptPrintConfig receiptPrintConfig = queryPrintConfigByConfigId(parklot.getReceiptPrintConfigId());
				// 租户信息
				String servicePhone = StringUtils.EMPTY;
				R<Tenant> t = sysClient.getTenant(parklot.getTenantId());
				if (t.isSuccess() && StringUtils.isNotBlank(t.getData().getServicePhone())) {
					servicePhone = t.getData().getServicePhone();
				}
				// 欠费金额
				BigDecimal unpaidAmount = Optional.ofNullable(parkingOrder.getPlate())
						.map(item -> parkingOrderService.selectUnpaidAmountByPlate(parkingOrder.getPlate(), parkingOrder.getParklotId()))
						.orElse(BigDecimal.ZERO);
				// 构建票据信息
				Map<String, String> fieldsMap = new HashMap<>(16);
				fieldsMap.put("parklotName", parklot.getName());
				fieldsMap.put("plate", parkingOrder.getPlate());
				fieldsMap.put("parkingType", parkingType);
				fieldsMap.put("parkingSection", parklot.getName());
				fieldsMap.put("placeCode", parkingPlace.getPlaceCode());
				fieldsMap.put("enterTime", DateUtil.formatDateTime(parkingOrder.getEnterTime()));
				fieldsMap.put("chargeUserRealName", AuthUtil.getNickName());
				fieldsMap.put("servicePhone", servicePhone);
				fieldsMap.put("printSn", printSn);
				fieldsMap.put("unpaidAmount", unpaidAmount + "元");
				// 支付二维码
				if (isBase64QrCode) {
					String base64QrCode = parkingOrderService.getUnlimitedQRCodeByParkingOrderId(parkingId);
					fieldsMap.put("QRCode", "<img id='qrcode' style='width: 100%;' src='data:image/png;base64," + base64QrCode + "'></img>");
				} else {
					String qrCodeUrl = parkingOrderService.getPayQrCodeByParkingOrderId(parkingId).getUrl();
					fieldsMap.put("QRCode", "<img id='qrcode' style='width: 100%;' src='" + qrCodeUrl + "'></img>");
				}
				return Func.format(receiptPrintConfig.getContent(), fieldsMap);
			}
			, Duration.ofHours(1));
	}

	@Override
	public ReceiptPrintConfig queryPrintConfigByConfigId(Long configId) {
		ReceiptPrintConfig receiptPrintConfig = Optional.ofNullable(configId)
			.map(t -> getById(configId))
			.orElse(queryTenantDefaultConfig());
		if (Func.isEmpty(receiptPrintConfig)) {
			throw new ServiceException("未配置小票打印模版");
		}
		return receiptPrintConfig;
	}

	private ReceiptPrintConfig queryTenantDefaultConfig() {
		List<ReceiptPrintConfig> configs = this.lambdaQuery()
			.eq(ReceiptPrintConfig::getIsDefault, Boolean.TRUE)
			.list();
		if (Func.isNotEmpty(configs)) {
			return configs.get(0);
		}
		return  null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean customSaveOrUpdate(ReceiptPrintConfig receiptPrintConfig) {
		Boolean result = saveOrUpdate(receiptPrintConfig);
		if (Func.isNotEmpty(receiptPrintConfig.getIsDefault()) && Boolean.TRUE.equals(receiptPrintConfig.getIsDefault())) {
			// 当前被设置为默认配置，其他设备置为非默认
			LambdaUpdateWrapper<ReceiptPrintConfig> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.ne(ReceiptPrintConfig::getId, receiptPrintConfig.getId());
			updateWrapper.set(ReceiptPrintConfig::getIsDefault, Boolean.FALSE);
			update(updateWrapper);
		}
		return result;
	}

}
