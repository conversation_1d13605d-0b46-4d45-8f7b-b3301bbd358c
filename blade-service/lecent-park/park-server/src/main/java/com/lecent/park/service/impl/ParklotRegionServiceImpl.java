package com.lecent.park.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.en.parklot.ParkLotType;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotFloor;
import com.lecent.park.entity.ParklotRegion;
import com.lecent.park.mapper.ParklotRegionMapper;
import com.lecent.park.service.IParkingPlaceService;
import com.lecent.park.service.IParklotFloorService;
import com.lecent.park.service.IParklotRegionService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.ParklotRegionPlaceVO;
import com.lecent.park.vo.ParklotRegionVO;
import com.lecent.park.vo.RemainPlaceNumVO;
import com.lecent.park.wrapper.ParklotRegionWrapper;
import jodd.util.MathUtil;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车场区域信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Service
@AllArgsConstructor
public class ParklotRegionServiceImpl extends BaseServiceImpl<ParklotRegionMapper, ParklotRegion> implements IParklotRegionService {

	@Lazy
	private IParklotFloorService floorService;
	@Lazy
	private IParklotService parklotService;

	private IParkingPlaceService parkingPlaceService;

	@Override
	public IPage<ParklotRegionVO> selectParklotRegionPage(IPage<ParklotRegionVO> page, ParklotRegionVO parklotRegion) {
		return page.setRecords(baseMapper.selectParklotRegionPage(page, parklotRegion));
	}

	@Override
	public List<ParklotRegion> getByParklotId(Long parklotId) {
		if (Func.isEmpty(parklotId)) {
			return this.list();
		}
		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		LecentAssert.notNull(parklot, "不存在此车场");
		Integer parklotType = parklot.getParklotType();
		LambdaQueryWrapper<ParklotRegion> queryWrapper = Wrappers.<ParklotRegion>query()
			.lambda()
			.eq(ParklotRegion::getParklotId, parklotId);
		if (ParkLotType.isRoadOut(parklotType)) {
			List<ParklotFloor> floors =
				floorService.list(Wrappers.<ParklotFloor>lambdaQuery().eq(ParklotFloor::getParklotId
					, parklotId));
			List<Long> floorIds = floors.stream().map(ParklotFloor::getId).collect(Collectors.toList());
			if (floorIds.size() > 0) {
				queryWrapper.in(ParklotRegion::getFloorId, floorIds);
			}
		}
		return this.list(queryWrapper);
	}

	@Override
	public List<String> getByFloorId(Long id) {
		return this.list(Wrappers.<ParklotRegion>query()
			.lambda()
			.eq(ParklotRegion::getFloorId, id))
			.stream()
			.map(ParklotRegion::getRegionName)
			.collect(Collectors.toList());
	}

	@Override
	public boolean submit(ParklotRegion parklotRegion) {
		Parklot parklot = ParkLotCaches.getParkLot(parklotRegion.getParklotId());
		LecentAssert.notNull(parklot, StrUtil.format("id为：[{}]的停车场不存在！", parklotRegion.getParklotId()));
		if (ParkLotType.isRoadOut(parklot.getParklotType())) {
			validateAmount(parklotRegion);
		}
		if (ParkLotType.ROAD_IN.getValue() == parklot.getParklotType()) {
			ParklotFloor floor = this.selectFloor(parklot);
			parklotRegion.setFloorId(floor.getId());
			validateParklotInAmount(parklotRegion);
		}
		if (Func.isEmpty(parklotRegion.getId())) {
			parklotRegion.setRegionNo(generateRegionNo(parklotRegion.getParklotId()));
		}

		return saveOrUpdate(parklotRegion);
	}

	private ParklotFloor selectFloor(Parklot parklot) {
		List<ParklotFloor> floors = floorService.getByParklotId(parklot.getId());
		if (Func.isNotEmpty(floors) && floors.size() > 0) {
			return floors.get(0);
		}
		return floorService.addInnerRoad(parklot);
	}


	private String generateRegionNo(Long parklotId) {
		String regionNo;
		do {
			regionNo = String.valueOf(MathUtil.randomInt(1000, 9999));
		} while (validateNo(parklotId, regionNo));
		return regionNo;
	}

	/**
	 * 校验路内停车场车位
	 *
	 * @param parklotRegion 区域
	 */
	private void validateParklotInAmount(ParklotRegion parklotRegion) {
		this.validateTemp(parklotRegion);
		this.checkRegionName(parklotRegion);
		Long id = parklotRegion.getId();
		Long parklotId = parklotRegion.getParklotId();

		List<ParklotRegion> list = this.list(
			Func.isNotEmpty(id) ?
				Wrappers.<ParklotRegion>lambdaQuery().ne(ParklotRegion::getId, id).eq(ParklotRegion::getParklotId, parklotId) :
				Wrappers.<ParklotRegion>lambdaQuery().eq(ParklotRegion::getParklotId, parklotId)
		);

		int sum = list.stream().mapToInt(ParklotRegion::getTempLotAmount).sum();
		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		LecentAssert.notNull(parklot, StrUtil.format("id为：[{}]的停车场不存在！", parklotRegion.getParklotId()));
		if (parklotRegion.getTempLotAmount() + sum > parklot.getTempLotAmount()) {
			throw new ServiceException("添加的临停车位数超过了车场的临停车位数，请重新输入");
		}


	}

	private boolean validateNo(Long parklotId, String regionNo) {
		int count = count(new QueryWrapper<ParklotRegion>().lambda().eq(ParklotRegion::getParklotId, parklotId)
			.eq(ParklotRegion::getRegionNo, regionNo));
		return count > 0;
	}

	@Override
	public List<ParklotRegion> dropListByFloorId(Long floorId) {
		return this.list(Wrappers.<ParklotRegion>query().lambda().eq(ParklotRegion::getFloorId, floorId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeRegion(List<Long> idList) {
		for (Long id : idList) {
			int count = this.baseMapper.countPlaceByRegionId(id);
			if (count > 0) {
				throw new ServiceException("区域下存在车位，删除失败！");
			}
		}
		return this.deleteLogic(idList);
	}

	@Override
	public RemainPlaceNumVO getRemainPlaceNum(Long floorId) {
		return null;
	}

	@Override
	public ParklotRegionVO detail(QueryWrapper<ParklotRegion> queryWrapper) {
		ParklotRegion region = this.getOne(queryWrapper);
		Long parklotId = region.getParklotId();
		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		LecentAssert.notNull(parklot, "停车场不存在！");
		ParklotRegionVO parklotRegionVO = ParklotRegionWrapper.build().entityVO(region);
		parklotRegionVO.setParklotType(parklot.getParklotType());
		return parklotRegionVO;
	}

	@Override
	public List<ParklotRegion> dropListByParklotId(Long parklotId) {
		return this.list(Wrappers.<ParklotRegion>query().lambda()
			.eq(ParklotRegion::getParklotId, parklotId));
	}

	@Override
	public List<ParklotRegionPlaceVO> getRegionPlaceByFloorId(Long floorId) {
		List<ParklotRegionPlaceVO> result = new ArrayList<>();
		List<ParklotRegion> regionList = this.list(Wrappers.<ParklotRegion>query().lambda()
			.eq(ParklotRegion::getFloorId, floorId));
		if (Func.isEmpty(regionList)) {
			return result;
		}
		regionList.forEach(r -> {
			ParklotRegionPlaceVO vo = BeanUtil.copy(r, ParklotRegionPlaceVO.class);
			List<ParkingPlace> parkingPlaceList = parkingPlaceService.dropListByRegionId(r.getId());
			vo.setChildren(parkingPlaceList);
			result.add(vo);
		});
		return result;
	}

	@Override
	public List<ParklotRegionPlaceVO> getRegionPlaceByParklotId(Long parklotId) {
		List<ParklotRegionPlaceVO> result = new ArrayList<>();
		List<ParklotRegion> regionList = this.list(Wrappers.<ParklotRegion>query().lambda()
			.eq(ParklotRegion::getParklotId, parklotId));
		if (Func.isEmpty(regionList)) {
			return result;
		}
		regionList.forEach(r -> {
			ParklotRegionPlaceVO vo = BeanUtil.copy(r, ParklotRegionPlaceVO.class);
			List<ParkingPlace> parkingPlaceList = parkingPlaceService.dropListByRegionId(r.getId());
			vo.setChildren(parkingPlaceList);
			result.add(vo);
		});
		return result;
	}


	@Override
	public List<ParklotRegion> getRegionFloorId(Long floorId) {
		return list(Wrappers.<ParklotRegion>lambdaQuery().eq(ParklotRegion::getFloorId, floorId));
	}

	@Override
	public List<ParklotRegion> getParkingLotRegionList(String name) {
		return this.baseMapper.getParkingLotRegionList(name);
	}

	private void validateAmount(ParklotRegion parklotRegion) {
		initAmount(parklotRegion);

		checkRegionName(parklotRegion);

		if (Func.isNotEmpty(parklotRegion.getId())) {
			this.validateIndependent(parklotRegion);
			this.validateTemp(parklotRegion);
			this.validateLetter(parklotRegion);
			this.validateAcc(parklotRegion);
			this.validateVip(parklotRegion);
		}

		ParklotFloor parklotFloor = floorService.getById(parklotRegion.getFloorId());
		LecentAssert.notNull(parklotFloor, "楼层不存在或者被删除！");
		Integer tempLotAmount = Func.isEmpty(parklotRegion.getTempLotAmount()) ? 0 : parklotRegion.getTempLotAmount();
		Integer vipLotAmount = Func.isEmpty(parklotRegion.getVipLotAmount()) ? 0 : parklotRegion.getVipLotAmount();
		Integer accessibilityLotAmount = Func.isEmpty(parklotRegion.getAccessibilityLotAmount()) ? 0 :
			parklotRegion.getAccessibilityLotAmount();

		if (vipLotAmount + accessibilityLotAmount > tempLotAmount) {
			throw new ServiceException("vip车位和无障碍车位不能大于临停车位,请重新操作！");
		}

		List<ParklotRegion> list = getList(parklotRegion);

		Integer usedInd = list.stream().mapToInt(ParklotRegion::getIndependentOwnershipAmount).sum();
		if (parklotRegion.getIndependentOwnershipAmount() + usedInd > parklotFloor.getIndependentOwnershipAmount()) {
			throw new ServiceException("剩余独立产权车位不足，请修改后重试！");
		}

		Integer usedTemp = list.stream().mapToInt(ParklotRegion::getTempLotAmount).sum();
		if (parklotRegion.getTempLotAmount() + usedTemp > parklotFloor.getTempLotAmount()) {
			throw new ServiceException("剩余临停车位不足，请修改后重试！");
		}

		Integer usedLetter = list.stream().mapToInt(ParklotRegion::getLetterLotAmount).sum();
		if (parklotRegion.getLetterLotAmount() + usedLetter > parklotFloor.getLetterLotAmount()) {
			throw new ServiceException("剩余子母车位不足，请修改后重试！");
		}

		Integer usedVip = list.stream().mapToInt(ParklotRegion::getVipLotAmount).sum();
		if (parklotRegion.getVipLotAmount() + usedVip > parklotFloor.getVipLotAmount()) {
			throw new ServiceException("剩余VIP车位不足，请修改后重试！");
		}

		Integer usedAcc = list.stream().mapToInt(ParklotRegion::getAccessibilityLotAmount).sum();
		if (parklotRegion.getAccessibilityLotAmount() + usedAcc > parklotFloor.getAccessibilityLotAmount()) {
			throw new ServiceException("剩余无障碍车位不足，请修改后重试！");
		}
	}

	private void initAmount(ParklotRegion parklotRegion) {

		if (null == parklotRegion.getVipLotAmount()) {
			parklotRegion.setVipLotAmount(0);
		}
		if (null == parklotRegion.getAccessibilityLotAmount()) {
			parklotRegion.setAccessibilityLotAmount(0);
		}
		if (null == parklotRegion.getIndependentOwnershipAmount()) {
			parklotRegion.setIndependentOwnershipAmount(0);
		}
		if (null == parklotRegion.getTempLotAmount()) {
			parklotRegion.setTempLotAmount(0);
		}
	}

	private void validateVip(ParklotRegion parklotRegion) {
		Integer count = this.baseMapper.getAlreadyVip(parklotRegion.getId());
		if (parklotRegion.getVipLotAmount() < count) {
			throw new ServiceException("修改的VIP车位数量不能小于该区域已经添加的VIP车位数量，请重新修改或者到车位管理中先删除该区域下对应的车位");
		}
	}

	private void validateAcc(ParklotRegion parklotRegion) {
		Integer count = this.baseMapper.getAlreadyAcc(parklotRegion.getId());
		if (parklotRegion.getAccessibilityLotAmount() < count) {
			throw new ServiceException("修改的无障碍车位数量不能小于该区域已经添加的无障碍车位数量，请重新修改或者到车位管理中先删除该区域下对应的车位");
		}
	}

	private void validateLetter(ParklotRegion parklotRegion) {
		Integer count = this.baseMapper.getAlreadyLetter(parklotRegion.getId());
		if (parklotRegion.getLetterLotAmount() < count) {
			throw new ServiceException("修改的子母车位数量不能小于该区域已经添加的子母车位数量，请重新修改或者到车位管理中先删除该区域下对应的车位");
		}

	}

	private void validateTemp(ParklotRegion parklotRegion) {
		Integer count = this.baseMapper.getAlreadyTemp(parklotRegion.getId());
		if (parklotRegion.getTempLotAmount() < count) {
			throw new ServiceException("修改的临停车位数量不能小于该区域已经添加的临停车位数量，请重新修改或者到车位管理中先删除该区域下对应的车位");
		}
	}

	private void validateIndependent(ParklotRegion parklotRegion) {
		Integer count = this.baseMapper.getAlreadyInd(parklotRegion.getId());
		if (parklotRegion.getIndependentOwnershipAmount() < count) {
			throw new ServiceException("修改的独立产权车位数量不能小于该区域已经添加的独立产权车位数量，请重新修改或者到车位管理中先删除该区域下对应的车位");
		}
	}

	private List<ParklotRegion> getList(ParklotRegion parklotRegion) {

		LambdaQueryWrapper<ParklotRegion> query = new LambdaQueryWrapper<>();

		query = Func.isEmpty(parklotRegion.getId()) ?
			query.eq(ParklotRegion::getFloorId, parklotRegion.getFloorId()) :
			query.eq(ParklotRegion::getFloorId, parklotRegion.getFloorId()).ne(ParklotRegion::getId,
				parklotRegion.getId());

		return this.list(query);

	}

	private void checkRegionName(ParklotRegion parklotRegion) {
		int count = 0;
		Long id = parklotRegion.getId();
		if (Func.isEmpty(id)) {
			count = this.count(Wrappers.<ParklotRegion>lambdaQuery()
				.eq(ParklotRegion::getParklotId, parklotRegion.getParklotId())
				.eq(ParklotRegion::getRegionName, parklotRegion.getRegionName()));
		} else {
			count = this.count(Wrappers.<ParklotRegion>lambdaQuery()
				.eq(ParklotRegion::getParklotId, parklotRegion.getParklotId())
				.ne(ParklotRegion::getId, parklotRegion.getId())
				.eq(ParklotRegion::getRegionName, parklotRegion.getRegionName()));
		}

		LecentAssert.isTrue(count < 1, "该车场下此区域已存在，请重新修改");
	}

}
