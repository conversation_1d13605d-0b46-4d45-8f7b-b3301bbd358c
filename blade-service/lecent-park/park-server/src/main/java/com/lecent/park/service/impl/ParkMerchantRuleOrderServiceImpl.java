package com.lecent.park.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.constant.PayStatusEnum;
import com.lecent.park.entity.ParkMerchant;
import com.lecent.park.entity.ParkMerchantParklot;
import com.lecent.park.entity.ParkMerchantRuleOrder;
import com.lecent.park.excel.ParkMerchantRuleOrderExcel;
import com.lecent.park.mapper.ParkMerchantRuleOrderMapper;
import com.lecent.park.service.IParkMerchantParklotService;
import com.lecent.park.service.IParkMerchantRuleOrderService;
import com.lecent.park.service.IParkMerchantService;
import com.lecent.park.vo.MerchantPayVo;
import com.lecent.park.vo.ParkMerchantOrderStatisticsVO;
import com.lecent.park.vo.ParkMerchantParklotVO;
import com.lecent.park.vo.ParkMerchantRuleOrderVO;
import com.lecent.pay.core.enums.PayWay;
import org.springblade.common.utils.DateUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.common.utils.SequenceNoUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-19
 */
@Service
public class ParkMerchantRuleOrderServiceImpl extends BaseServiceImpl<ParkMerchantRuleOrderMapper, ParkMerchantRuleOrder> implements IParkMerchantRuleOrderService {

	@Autowired
	private IParkMerchantService merchantService;

	@Autowired
	@Lazy
	private IParkMerchantParklotService parkMerchantParklotService;


	@Override
	public IPage<ParkMerchantRuleOrderVO> selectParkMerchantRuleOrderPage(IPage<ParkMerchantRuleOrderVO> page, ParkMerchantRuleOrderVO parkMerchantRuleOrder) {

		ParkMerchant parkMerchant = merchantService.getById(SecureUtil.getBusinessId());
		if (parkMerchant != null) {
			parkMerchantRuleOrder.setMerchantId(parkMerchant.getId());
		}

		List<ParkMerchantRuleOrderVO> orderList = baseMapper.selectParkMerchantRuleOrderPage(page, parkMerchantRuleOrder);
		if (Func.isEmpty(orderList)) {
			return page.setRecords(null);
		}
		orderList.forEach(o -> {
			o.setPayTypeDes(PayWay.getNameByKey(o.getPayType()));
		});
		return page.setRecords(orderList);
	}

	@Override
	public boolean saveRuleOrder() {
		return false;
	}

	@Override
	public ParkMerchantRuleOrder createOrder(ParkMerchantParklotVO mp, MerchantPayVo payVo) {
		ParkMerchantRuleOrder order = new ParkMerchantRuleOrder();
		order.setMerchantId(mp.getParkMerchantId());
		order.setMerchantParklotId(mp.getId());
		order.setMerchantRuleId(mp.getMerchantRuleId());
		order.setPayTotalAmount(payVo.getTotalAmount());
		order.setPayType(PayWay.CCB_POLYMERIZATION.getKey());
		order.setStatus(1);
		order.setTradeNo(SequenceNoUtils.generateNo());
		order.setParklotId(mp.getParklotId());
		order.setTenantId(mp.getTenantId());

		if (mp.getAuthEndTime() == null || initStartDate(mp.getAuthEndTime())) {
			order.setStartDate(DateUtils.getMonthBegin(new Date()));
			Date monthEnd = DateUtils.getMonthEnd(new Date());
			Date addMonth = DateUtil.plusMonths(monthEnd, payVo.getMonths() - 1);
			order.setEndDate(addMonth);

		} else {
			order.setStartDate(DateUtil.plusSeconds(mp.getAuthEndTime(), 1));
			Date monthEnd = DateUtils.getMonthEnd(mp.getAuthEndTime());
			long day = DateUtils.getDay(mp.getAuthEndTime(), monthEnd);

			if (day > 1) {
				Date addMonth = DateUtil.plusMonths(mp.getAuthEndTime(), payVo.getMonths());
				order.setEndDate(addMonth);
			} else {
				Date addMonth = DateUtil.plusMonths(mp.getAuthEndTime(), payVo.getMonths());
				order.setEndDate(DateUtils.getMonthEnd(addMonth));
			}
		}
		return order;
	}


	private boolean initStartDate(Date authEndTime) {
		int daysOfMonth = DateUtils.getDaysOfMonth(DateUtil.plusMonths(new Date(), -1));
		Date date = DateUtil.plusDays(authEndTime, daysOfMonth);
		if (date.getTime() < System.currentTimeMillis()) {
			return true;
		}
		return false;
	}


	@Override
	public ParkMerchantRuleOrder getByTradeNo(String tradeNo) {
		List<ParkMerchantRuleOrder> list = this.list(Wrappers.<ParkMerchantRuleOrder>lambdaQuery()
			.eq(ParkMerchantRuleOrder::getTradeNo, tradeNo)
			.eq(ParkMerchantRuleOrder::getPayStatus, PayStatusEnum.UN_PAY.getValue()).orderByDesc(ParkMerchantRuleOrder::getCreateTime));
		if (Func.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public ParkMerchantOrderStatisticsVO orderStatistics(ParkMerchantRuleOrderVO parkMerchantRuleOrder) {
		return baseMapper.orderStatistics(parkMerchantRuleOrder);
	}

	@Override
	public List<String> getTradeNoList() {
		return baseMapper.getTradeNoList();
	}

	@Override
	public void exportDetailData(ParkMerchantRuleOrderVO parkMerchantRuleOrderVO, HttpServletResponse response) {

		ParkMerchant parkMerchant = merchantService.getByUserId(SecureUtil.getUserId());
		if (parkMerchant != null) {
			parkMerchantRuleOrderVO.setMerchantId(parkMerchant.getId());
		}

		List<ParkMerchantRuleOrderExcel> orderList = baseMapper.selectParkMerchantRuleOrderExport(parkMerchantRuleOrderVO);

		orderList.forEach(o -> {
			o.setPayTypeDes(PayWay.getNameByKey(o.getPayType()));
		});

		export(response, orderList);

	}

	@Override
	public void createSuccessOrder(ParkMerchantParklotVO mp, MerchantPayVo payVo) {

		//生成支付成功的订单
		ParkMerchantRuleOrder order = this.createOrder(mp, payVo);
		order.setPayStatus(PayStatusEnum.PAY_SUCCESS.getValue());
		this.save(order);

		//更新授权结束时间
		ParkMerchantParklot merchantParkLot = parkMerchantParklotService.getById(order.getMerchantParklotId());
		if (merchantParkLot == null) {
			return;
		}

		if (merchantParkLot.getAuthStartTime() == null) {
			merchantParkLot.setAuthStartTime(order.getStartDate());
		}
		merchantParkLot.setAuthEndTime(order.getEndDate());
		parkMerchantParklotService.updateById(merchantParkLot);
	}

	private void export(HttpServletResponse response, List<ParkMerchantRuleOrderExcel> orderList) {

		try {
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/x-download");
			String filedisplay = "商户充值订单.xls";
			filedisplay = URLEncoder.encode(filedisplay, "UTF-8");
			response.addHeader("Content-Disposition", "attachment;filename=" + filedisplay);
			OutputStream out = response.getOutputStream();
			ExcelWriter excelWriter = EasyExcel.write(out).build();
			//这里 需要指定写用哪个class去写
			WriteSheet writeSheet = EasyExcel.writerSheet(0, "商户充值订单").head(ParkMerchantRuleOrderExcel.class).build();
			excelWriter.write(orderList, writeSheet);
			//千万别忘记finish 会帮忙关闭流
			excelWriter.finish();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean merchantPay(ParkMerchantRuleOrder order) {
		LecentAssert.notNull(order.getMerchantParklotId(),"商户授权车场Id不能为空");
		LecentAssert.notNull(order.getPayTotalAmount(),"充值金额为空");
		if (order.getPayTotalAmount().compareTo(BigDecimal.ZERO)<=0){
			return true;
		}

		ParkMerchantParklot merchantParkLot = parkMerchantParklotService.getById(order.getMerchantParklotId());
		if (merchantParkLot==null){
			return false;
		}
		order.setMerchantId(merchantParkLot.getParkMerchantId());
		order.setParklotId(merchantParkLot.getParklotId());
		order.setMerchantRuleId(merchantParkLot.getMerchantRuleId());
		order.setPayType(1);
		order.setPayStatus(1);
		order.setTradeNo(SequenceNoUtils.generateNo());
		order.setPaidAmount(order.getPayTotalAmount());
		order.setStartDate(DateUtil.minusYears(new Date(),100));
		order.setEndDate(DateUtil.plusYears(new Date(),100));

		super.save(order);
		if (!order.isFirst()) {
			BigDecimal add = merchantParkLot.getTotalPrice().add(order.getPayTotalAmount());
			merchantParkLot.setTotalPrice(add);
		}
		return parkMerchantParklotService.updateById(merchantParkLot);
	}
}


