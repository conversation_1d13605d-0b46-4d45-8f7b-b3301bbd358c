package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.dto.UserParklotSettingDTO;
import com.lecent.park.entity.*;
import com.lecent.park.mapper.UserChannelMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.UserChannelVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.dto.GuardUserMenuDTO;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公司员工车场资源授权表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Service
public class UserChannelServiceImpl extends BaseServiceImpl<UserChannelMapper, UserChannel> implements IUserChannelService {

	@Lazy
	@Autowired
	private IParklotService parklotService;

	@Autowired
	private IChannelService iChannelService;

	@Lazy
	@Autowired
	private IUserParklotService userParklotService;

	@Autowired
	private IGuardUserMenuService guardUserMenuService;

	@Autowired
	private ISysClient sysClient;

	@Override
	public IPage<UserChannelVO> selectUserChannelPage(IPage<UserChannelVO> page, UserChannelVO userChannel) {
		return page.setRecords(baseMapper.selectUserChannelPage(page, userChannel));
	}

	@Override
	public List<ChannelTree> getChannelTree(Long userId, String labels) {
		List<Parklot> parklotList = SecureUtil.isAdmin() ? parklotService.list() : parklotService.userParkLotList();

		// 获取标签绑定车场
		if (Func.isNotBlank(labels)) {
			R<List<Long>> r = sysClient.getRelationByLabel(labels);
			if (r.isSuccess() && Func.isNotEmpty(r.getData())) {
				List<Long> ids = r.getData();
				parklotList = parklotList.stream().filter(parklot -> ids.contains(parklot.getId())).collect(Collectors.toList());
			} else {
				// 通过标签未查询到绑定车场id则将车场置空
				parklotList = new ArrayList<>();
			}
		}

		// 获取车场和通道授权
		final HashSet<Long> parklotPermission = new HashSet<>();
		final HashSet<Long> channelPermission = new HashSet<>();
		if (userId != null) {
			List<UserParklot> userParklotList = this.userParklotService.getByUserId(userId);
			parklotPermission.addAll(userParklotList.stream().map(UserParklot::getParklotId).collect(Collectors.toSet()));
			List<UserChannel> userChannelList = this.getUserChannel(userId);
			channelPermission.addAll(userChannelList.stream().map(UserChannel::getChannelId).collect(Collectors.toSet()));
		}

		List<ChannelTree> parklotChannelList = new ArrayList<>();
		parklotList.forEach(parklotVO -> {
			final ChannelTree parkDTO = ChannelTree.builder()
				.id(parklotVO.getId())
				.parentId(0L)
				.label(parklotVO.getName())
				.nodeType("PARK")
				.build();
			parkDTO.setChecked(parklotPermission.contains(parklotVO.getId()));
			parklotChannelList.add(parkDTO);
			final List<Channel> channelList = iChannelService.getListByParklotId(parklotVO.getId());
			channelList.forEach(channel -> {
				final ChannelTree channelDTO = ChannelTree.builder()
					.id(channel.getId())
					.parentId(parklotVO.getId())
					.label(parklotVO.getName() + "-" + channel.getName())
					.nodeType("CHANNEL")
					.build();
				channelDTO.setChecked(channelPermission.contains(channel.getId()));
				parklotChannelList.add(channelDTO);
			});
		});
		return ForestNodeMerger.merge(parklotChannelList);
	}


	@Override
	@Transactional(rollbackFor = {Exception.class})
	public boolean saveUserChannel(UserParklotSettingDTO userParklotSettingDTO) {
		List<Long> userIds = userParklotSettingDTO.getUserIds();
		if (Func.isEmpty(userIds)) {
			if (userParklotSettingDTO.getUserId() != null) {
				userIds = Collections.singletonList(userParklotSettingDTO.getUserId());
			} else {
				throw new ServiceException("用户id为空");
			}
		}
		List<UserChannel> userChannels = userParklotSettingDTO.getUserChannelList();
		List<String> checkParkotList = userParklotSettingDTO.getCheckParkotList();
		for (Long userId : userIds) {
			// 删除当前绑定车场
			userParklotService.customDeleteByUserId(userId);
			List<UserParklot> userParklotList = checkParkotList.stream()
				.distinct()
				.map(parklotId -> new UserParklot(userId, Long.valueOf(parklotId)))
				.collect(Collectors.toList());
			// 更新
			userParklotService.customSaveBatch(userParklotList);
			remove(new QueryWrapper<UserChannel>().lambda().eq(UserChannel::getUserId, userId));
			userChannels.forEach(userChannel -> {
				userChannel.setId(null);
				userChannel.setUserId(userId);
			});
			saveBatch(userChannels);
			if (Func.isNotEmpty(userParklotSettingDTO.getMenuIdList())) {
				GuardUserMenuDTO guardUserMenuDto = new GuardUserMenuDTO();
				guardUserMenuDto.setUserId(userId);
				guardUserMenuDto.setMenuIds(userParklotSettingDTO.getMenuIdList());
				guardUserMenuService.setMobileGuardUserMenu(guardUserMenuDto);
			}
		}
		return true;
	}

	@Override
	public List<Map> getParkLotUser() {
		List<Long> parkLotIds = userParklotService.getParkLotIds(AuthUtil.getUserId());
		if (Func.isEmpty(parkLotIds)) {
			return null;
		}

		return baseMapper.getParkLotUser(parkLotIds);
	}

	@Override
	public List<UserChannel> getUserChannel(Long userId) {
		return super.list(Wrappers.<UserChannel>lambdaQuery().eq(UserChannel::getUserId, userId));
	}
}
