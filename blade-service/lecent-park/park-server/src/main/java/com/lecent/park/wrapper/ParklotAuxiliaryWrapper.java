package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotAuxiliary;
import com.lecent.park.vo.ParklotAuxiliaryVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场信息扩展表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
public class ParklotAuxiliaryWrapper extends BaseEntityWrapper<ParklotAuxiliary, ParklotAuxiliaryVO>  {

	public static ParklotAuxiliaryWrapper build() {
		return new ParklotAuxiliaryWrapper();
 	}

	@Override
	public ParklotAuxiliaryVO entityVO(ParklotAuxiliary parklotAuxiliary) {
		ParklotAuxiliaryVO parklotAuxiliaryVO = BeanUtil.copy(parklotAuxiliary, ParklotAuxiliaryVO.class);

		//User createUser = UserCache.getUser(parklotAuxiliary.getCreateUser());
		//User updateUser = UserCache.getUser(parklotAuxiliary.getUpdateUser());
		//parklotAuxiliaryVO.setCreateUserName(createUser.getName());
		//parklotAuxiliaryVO.setUpdateUserName(updateUser.getName());

		return parklotAuxiliaryVO;
	}

}
