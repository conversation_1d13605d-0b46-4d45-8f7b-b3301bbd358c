package com.lecent.park.service;

import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.entity.PlateProperty;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;
import java.util.List;

/**
 * 智慧停车-车场车辆属性表 服务类
 *
 * <AUTHOR>
 * @since 2021-11-08
 */
public interface IPlatePropertyService extends BaseService<PlateProperty> {
	/**
	 * 添加属性
	 *
	 * @param tenantId  tenantId
	 * @param parkLotId 车场
	 * @param plates    车牌
	 * @param cardId    关联card id
	 * @param type      属性类型
	 * @return t
	 */
	boolean update(String tenantId, Long parkLotId, String plates, Long cardId, PlatePropertyType type);

	/**
	 * 添加属性
	 *
	 * @param tenantId  tenantId
	 * @param parkLotId 车场
	 * @param plates    车牌
	 * @param cardId    关联card id
	 * @param type      type    属性类型
	 * @param startTime 有效期开始时间
	 * @param endTime   有效期结束时间
	 * @return t
	 */
	boolean update(String tenantId, Long parkLotId, String plates, Long cardId, PlatePropertyType type, Date startTime, Date endTime);

	/**
	 * 添加属性
	 *
	 * @param tenantId  tenantId
	 * @param parkLotId 车场
	 * @param plates    车牌
	 * @param cardId    关联card id
	 * @param type      type    属性类型
	 * @param startTime 有效期开始时间
	 * @param endTime   有效期结束时间
	 * @param status    状态
	 * @return t
	 */
	boolean update(String tenantId, Long parkLotId, String plates, Long cardId, PlatePropertyType type, Date startTime, Date endTime, Integer status);

	/**
	 * 添加属性
	 *
	 * @param tenantId   tenantId
	 * @param parkLotIds 车场
	 * @param plates     车牌
	 * @param cardId     关联card id
	 * @param type       type    属性类型
	 * @param startTime  有效期开始时间
	 * @param endTime    有效期结束时间
	 * @param status     状态
	 * @return t
	 */
	boolean update(String tenantId, String parkLotIds, String plates, Long cardId, PlatePropertyType type, Date startTime, Date endTime, Integer status);

	/**
	 * 查询车辆属性
	 *
	 * @param parkLotId 车场ID
	 * @param plate     车牌
	 * @param enterDate 车辆进场时间
	 * @param exitDate  车辆出场时间
	 * @return 车辆属性列表
	 */
	List<PlateProperty> queryPlateProperties(Long parkLotId, String plate, Date enterDate, Date exitDate);

	/**
	 * 根据cardIds删除车辆属性
	 *
	 * @param type    类型
	 * @param cardIds card ids
	 * @return t
	 */
	boolean removeByCardIds(PlatePropertyType type, Long... cardIds);

	/**
	 * 根据cardIds删除车辆属性
	 *
	 * @param type    类型
	 * @param cardIds card ids
	 * @return t
	 */
	boolean removeByCardIds(PlatePropertyType type, List<Long> cardIds);
}
