package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ReqVisitorAuth;
import com.lecent.park.dto.VisitorAuthDTO;
import com.lecent.park.entity.VisitorAuth;
import com.lecent.park.third.ThirdHttpRes;
import com.lecent.park.third.ThirdHttpVisitorInfo;
import com.lecent.park.vo.VisitorAuthVO;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;
import java.util.List;

/**
 * 访客授权表 服务类
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
public interface IVisitorAuthService extends BaseService<VisitorAuth> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param visitorAuth
	 * @return
	 */
	IPage<VisitorAuthVO> selectVisitorAuthPage(IPage<VisitorAuthVO> page, VisitorAuthDTO visitorAuthDto);

	String isEnter(String plate, Long parklotId);

	VisitorAuth addVisitorAuth(VisitorAuth visitorAuth);

	boolean setVisitorAuthLimit(Integer parklotlimit, Integer userlimit, Long parklotId);

	VisitorAuthVO getDetail(Long id);

	boolean addUserFreePermission(VisitorAuth visitorAuth);


	/**
	 * 访客授权
	 * @param parkLotId
	 * @param plate
	 * @param openId
	 * @param enterTime
	 * @return
	 */
	VisitorAuth selectOneByPlate(Long parkLotId, String plate, String openId, Date enterTime);

	VisitorAuth updateCustom(VisitorAuth visitorAuth);

	ThirdHttpRes addThirdVisitorAuth(ThirdHttpVisitorInfo thirdHttpVisitorInfo);

	boolean customDeleteLogic(List<Long> ids);

	/**
	 * 获取过期的访客授权记录
	 *
	 * @return List<VisitorAuth>
	 */
	List<VisitorAuth> getExpireList();

	/**
	 * 访客授权绑定
	 *
	 * @param reqVisitorAuth reqInfo
	 * @return t
	 */
	boolean miniVisitorAuthBind(ReqVisitorAuth reqVisitorAuth);

	/**
	 * 获取访客授权实体
	 *
	 * @param id id
	 * @return bean
	 */
	VisitorAuth getVisitorAuthById(Long id);
}
