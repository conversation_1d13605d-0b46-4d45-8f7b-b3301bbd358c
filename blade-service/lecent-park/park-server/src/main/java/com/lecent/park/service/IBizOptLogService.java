package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.BizOptLog;
import com.lecent.park.vo.BizOptLogVO;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.secure.BladeUser;

import javax.servlet.http.HttpServletResponse;

/**
 * 业务操作日志表 服务类
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
public interface IBizOptLogService extends BaseService<BizOptLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bizOptLog
	 * @return
	 */
	IPage<BizOptLogVO> selectBizOptLogPage(IPage<BizOptLogVO> page, BizOptLogVO bizOptLog);


	/**
	 * 保存操作日志  带注解的自动打入操作日志
	 *
	 * @param beforeEntity
	 * @param afterEntity
	 */
	void saveBizLog(String title, BaseEntity beforeEntity, BaseEntity afterEntity, BladeUser user,
					Integer type, Integer optTerminal, String header);


	void saveLogForFinalParam(BladeUser user, String title, String content,
							  Integer type, Integer optTerminal);

	/**
	 * 自定义保存操作日志
	 */
	void saveBizLog(BladeUser user, String title,
					String content, Integer type, Integer optTerminal);


	/**
	 * 是否可以打印业务操作日志
	 *
	 * @return
	 */
	boolean enableSaveLog();

	void export(BizOptLogVO bizOptLog, HttpServletResponse response);
}
