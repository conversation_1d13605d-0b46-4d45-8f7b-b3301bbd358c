package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.bizoptlog.OptTerminalEnum;
import com.lecent.park.bizoptlog.OptTypeEnum;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.dto.SupplementCardDTO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.SupplementCard;
import com.lecent.park.entity.TempParkingChargeRule;
import com.lecent.park.mapper.SupplementCardMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.SupplementCardVO;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 附属卡(亲情卡)表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@Service
@AllArgsConstructor
@Slf4j
public class SupplementCardServiceImpl extends
	BaseServiceImpl<SupplementCardMapper, SupplementCard> implements ISupplementCardService {
	private ITempParkingChargeRuleService chargeRuleService;

	private IBizOptLogService bizOptLogService;
	private IOwnerDetailInfoService ownerDetailInfoService;
	private IParklotService parklotService;

	private IPlatePropertyService platePropertyService;

	private IUserParklotService userParklotService;

	@Override
	public IPage<SupplementCardVO> selectSupplementCardPage(IPage<SupplementCardVO> page, SupplementCardVO supplementCard) {
		List<Long> bindParkLotIds = userParklotService.getCurrentUserBindParkLotIds();
		if (bindParkLotIds.isEmpty()) {
			return page.setRecords(Collections.emptyList());
		}

		supplementCard.setParkLotIds(bindParkLotIds);
		return selectSupplementCarList(page, supplementCard);
	}

	@Override
	public IPage<SupplementCardVO> selectSupplementCarList(IPage page, SupplementCardVO supplementCardVO) {

		List<Long> bindParkLotIds = userParklotService.getCurrentUserBindParkLotIds();
		if (bindParkLotIds.isEmpty()) {
			return null;
		}
		supplementCardVO.setParkLotIds(bindParkLotIds);

		return baseMapper.selectSupplementCarList(page, supplementCardVO);
	}

	/**
	 * 统一保存或更新
	 *
	 * @param saveBean 更新实体
	 */
	private void unifySaveOrUpdate(SupplementCard saveBean) {
		super.saveOrUpdate(saveBean);

		if (Func.isBlank(saveBean.getTenantId())) {
			SupplementCard newBean = getById(saveBean.getId());

			saveBean.setTenantId(newBean.getTenantId());
		}
		platePropertyService.update(saveBean.getTenantId(),
			saveBean.getParklotId(),
			saveBean.getPlate(),
			saveBean.getId(),
			PlatePropertyType.SUPPLEMENT_CARD);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean customDeleteLogic(List<Long> ids) {
		platePropertyService.removeByCardIds(PlatePropertyType.SUPPLEMENT_CARD, ids);
		return deleteLogic(ids);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveOrUpdateSupplementCar(SupplementCardDTO supplementCardDTO) {
		Long id = supplementCardDTO.getId();
		Long ownerDetailId = supplementCardDTO.getOwnerDetailId();
		Long parkingChargeRuleId = supplementCardDTO.getParkingChargeRuleId();

		//校验剩余可开卡数量
		validateRemainCardNum(ownerDetailId, parkingChargeRuleId, supplementCardDTO.getCarInfoList().size());
		//校验车牌是否重复
		validatePlate(supplementCardDTO);
		List<SupplementCard> supplementCards = getSupplementCardLIst(supplementCardDTO);

		if (Func.isNull(id) && bizOptLogService.enableSaveLog()) {
			Parklot parklot = ParkLotCaches.getParkLot(supplementCardDTO.getParklotId());
			List<SupplementCard> carInfoList = supplementCardDTO.getCarInfoList();
			String plates = carInfoList.stream().map(SupplementCard::getPlate).collect(Collectors.joining());

			bizOptLogService.saveBizLog(AuthUtil.getUser(), "亲情卡信息",
				"新增" + parklot.getName() + "亲情卡，" + plates, OptTypeEnum.ADD.getValue(), OptTerminalEnum.WEB.getValue());
		}

		for (SupplementCard card : supplementCards) {

			unifySaveOrUpdate(card);
		}
		return true;
	}

	private void validatePlate(SupplementCardDTO supplementCardDTO) {
		//获取保存车牌信息的对象
		List<SupplementCard> copy = BeanUtil.copy(supplementCardDTO.getCarInfoList(), SupplementCard.class);
		List<String> plates = new LinkedList<>();
		for (SupplementCard supplementCard : copy) {
			plates.add(supplementCard.getPlate());
		}
		List<String> collect = plates.stream().distinct().collect(Collectors.toList());
		for (String s : collect) {
			plates.remove(s);
		}
		if (plates.size() > 0) {
			LecentAssert.alertException("[" + copy.get(0).getPlate() + "]车牌重复填写");
		}
		List<SupplementCard> supplementCards = baseMapper.selectList(Wrappers.<SupplementCard>lambdaQuery()
			.eq(SupplementCard::getParklotId, supplementCardDTO.getParklotId())
			.in(SupplementCard::getPlate, collect));
		if (supplementCards.size() > 0) {
			LecentAssert.alertException("[" + supplementCards.get(0).getPlate() + "]车牌已经存在");
		}
	}

	private List<SupplementCard> getSupplementCardLIst(SupplementCardDTO supplementCardDTO) {
		List<SupplementCard> supplementCards = new LinkedList<>();
		//一个亲情卡业主可以一次添加多个车辆信息
		supplementCardDTO.getCarInfoList().forEach(item -> {
			//一个车辆信息对应一条亲情卡数据
			SupplementCard supplementCard = Objects.requireNonNull(BeanUtil.copy(supplementCardDTO, SupplementCard.class));
			supplementCard.setPlate(item.getPlate());
			supplementCard.setPhone(item.getPhone());
			supplementCards.add(supplementCard);
		});
		return supplementCards;
	}

	@Override
	public Integer selectUsableCarNum(SupplementCard supplementCard) {
		//查询业主正常使用的亲情卡
		List<SupplementCard> supplementCards = selectUsableCarInfo(supplementCard.getOwnerDetailId(), supplementCard.getParkingChargeRuleId());
		TempParkingChargeRuleVO tempParkingChargeRuleVO = chargeRuleService.selectOneById(supplementCard.getParkingChargeRuleId());
		Integer total = tempParkingChargeRuleVO.getCarNum();
		Integer using = supplementCards.size();
		log.debug("授权总数量{},已使用数量{}", total, using);
		return total - using;
	}


	public List<SupplementCard> selectUsableCarInfo(Long ownerDetailId, Long parkingChargeRuleId) {
		return this.list(Wrappers.<SupplementCard>lambdaQuery()
			.eq(SupplementCard::getOwnerDetailId, ownerDetailId)
			.eq(SupplementCard::getParkingChargeRuleId, parkingChargeRuleId)
			.eq(SupplementCard::getStatus, 1));
	}


	@Override
	public SupplementCard selectByPlate(String plate, Long parklotId) {
		return this.getOne(Wrappers.<SupplementCard>lambdaQuery()
			.eq(SupplementCard::getPlate, plate)
			.eq(SupplementCard::getParklotId, parklotId)
			.eq(SupplementCard::getStatus, 1)
			.orderByDesc(SupplementCard::getCreateTime)
			.last("limit 1")
		);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateCard(SupplementCard supplementCard) {
		SupplementCard card = this.getById(supplementCard.getId());
		if (Func.isEmpty(card)) {
			throw new ServiceException("亲情卡不存在！");
		}
		supplementCard.setParklotId(card.getParklotId());
		supplementCard.setPlate(card.getPlate());
		unifySaveOrUpdate(supplementCard);

		if (bizOptLogService.enableSaveLog()) {
			SupplementCard oldCard = getById(supplementCard.getId());
			Long parklotId = oldCard.getParklotId();
			Parklot parklot = ParkLotCaches.getParkLot(parklotId);

			String optName = supplementCard.getStatus().equals(1) ? "启用" : "禁用";
			Integer type = supplementCard.getStatus().equals(1) ? OptTypeEnum.ENABLE.getValue() : OptTypeEnum.DISABLE.getValue();
			bizOptLogService.saveBizLog(SecureUtil.getUser(), "亲情卡信息",
				optName + parklot.getName() + "亲情卡，" + oldCard.getPlate(), type, OptTerminalEnum.WEB.getValue());
		}
		return true;
	}

	/**
	 * @param ownerId 业主id
	 * @param ruleId  收费规则id
	 * @param CarNum  开卡数量
	 */
	private void validateRemainCardNum(Long ownerId, Long ruleId, int CarNum) {

		//只要卡没有注销，就占用开卡数量
		List<SupplementCard> list = this.list(Wrappers.<SupplementCard>lambdaQuery()
			.eq(SupplementCard::getOwnerDetailId, ownerId)
			.eq(SupplementCard::getParkingChargeRuleId, ruleId));

		TempParkingChargeRule rule = chargeRuleService.getById(ruleId);
		if (Func.isEmpty(rule) || Func.isEmpty(rule.getCarNum()) || rule.getCarNum() <= 0) {
			return;
		}
		if (list.size() + CarNum > rule.getCarNum()) {
			throw new ServiceException("该业主的亲情卡已经达到开卡上线，启用失败");
		}

	}
}
