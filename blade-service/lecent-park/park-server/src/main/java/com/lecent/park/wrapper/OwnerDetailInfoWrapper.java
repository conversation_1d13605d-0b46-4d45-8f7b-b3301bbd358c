package com.lecent.park.wrapper;

import com.lecent.park.entity.OwnerDetailInfo;
import com.lecent.park.vo.OwnerDetailInfoVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 楼层业主信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
public class OwnerDetailInfoWrapper extends BaseEntityWrapper<OwnerDetailInfo, OwnerDetailInfoVO>  {

	public static OwnerDetailInfoWrapper build() {
		return new OwnerDetailInfoWrapper();
 	}

	@Override
	public OwnerDetailInfoVO entityVO(OwnerDetailInfo ownerDetailInfo) {
		OwnerDetailInfoVO ownerDetailInfoVO = BeanUtil.copy(ownerDetailInfo, OwnerDetailInfoVO.class);

		//User createUser = UserCache.getUser(ownerDetailInfo.getCreateUser());
		//User updateUser = UserCache.getUser(ownerDetailInfo.getUpdateUser());
		//ownerDetailInfoVO.setCreateUserName(createUser.getName());
		//ownerDetailInfoVO.setUpdateUserName(updateUser.getName());

		return ownerDetailInfoVO;
	}

}
