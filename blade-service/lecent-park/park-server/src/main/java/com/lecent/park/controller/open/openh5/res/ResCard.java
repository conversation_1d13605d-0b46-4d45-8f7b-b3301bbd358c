package com.lecent.park.controller.open.openh5.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 卡套餐
 *
 * <AUTHOR> zxr
 * @date : 2022/8/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResCard {
	/**
	 * 月卡ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 月卡编号
	 */
	private String no;

	/**
	 * 手机号码
	 */
	private String phone;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 关联车场
	 */
	private String parklotIds;

	/**
	 * 关联车场名称
	 */
	private String parklotNames;

	/**
	 * 套餐名称
	 */
	private String categoryName;

	/**
	 * 车牌
	 */
	private String plate;

	/**
	 * 开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startDate;
	/**
	 * 结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endDate;

	/**
	 * 套餐ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long categoryId;

	/**
	 * 是否第一次续费是否第一次续费（0-否;1-是）
	 */
	private Integer firstPay;
}
