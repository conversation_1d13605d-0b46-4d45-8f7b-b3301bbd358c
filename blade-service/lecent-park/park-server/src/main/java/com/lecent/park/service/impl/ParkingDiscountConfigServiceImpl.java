package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.en.coupon.CouponCategory;
import com.lecent.park.entity.ParkingDiscountConfig;
import com.lecent.park.mapper.ParkingDiscountConfigMapper;
import com.lecent.park.service.IParkingDiscountConfigService;
import com.lecent.park.vo.ParkingDiscountConfigVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 停车优惠配置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Service
public class ParkingDiscountConfigServiceImpl extends BaseServiceImpl<ParkingDiscountConfigMapper, ParkingDiscountConfig> implements IParkingDiscountConfigService {

	@Override
	public IPage<ParkingDiscountConfigVO> selectParkingDiscountConfigPage(IPage<ParkingDiscountConfigVO> page, ParkingDiscountConfigVO parkingDiscountConfig) {
		return page.setRecords(baseMapper.selectParkingDiscountConfigPage(page, parkingDiscountConfig));
	}

	@Override
	public ParkingDiscountConfig getDiscountConfig(BigDecimal totalAmount, CouponCategory type) {
		return lambdaQuery().eq(ParkingDiscountConfig::getType, type)
				.lt(ParkingDiscountConfig::getMinAmount, totalAmount)
				.ge(ParkingDiscountConfig::getMaxAmount, totalAmount)
				.last("limit 1")
				.one();
	}

	@Override
	public BigDecimal calculateDiscount(BigDecimal totalAmount, CouponCategory type) {
		return calculateDiscount(totalAmount, getDiscountConfig(totalAmount, type));
	}

	@Override
	public BigDecimal calculateDiscount(BigDecimal totalAmount, ParkingDiscountConfig discountConfig) {
		BigDecimal discountAmount = BigDecimal.ZERO;

		if (Func.notNull(discountConfig)) {
			switch (discountConfig.getDiscountType()) {
				// 按比例折扣
				case AMOUNT_DISC_PERCENT:
					discountAmount = totalAmount.subtract(totalAmount.multiply(discountConfig.getDiscount())
							.setScale(2, BigDecimal.ROUND_FLOOR)).max(BigDecimal.ZERO);
					break;
				// 按固定金额折扣
				case AMOUNT_DISC:
					// 折扣金额，最小为0
					discountAmount = discountConfig.getDiscount().max(BigDecimal.ZERO);
					break;
			}
		}
		return discountAmount;
	}
}
