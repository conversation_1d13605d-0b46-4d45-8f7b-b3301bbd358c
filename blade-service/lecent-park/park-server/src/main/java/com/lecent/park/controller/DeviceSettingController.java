package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.DeviceSetting;
import com.lecent.park.service.IDeviceSettingService;
import com.lecent.park.vo.DeviceSettingVO;
import com.lecent.park.wrapper.DeviceSettingWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 设备语音播报设置表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/devicesetting")
@Api(value = "设备语音播报设置表", tags = "设备语音播报设置表接口")
public class DeviceSettingController extends BladeController {

	private IDeviceSettingService deviceSettingService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceSetting")
	public R<DeviceSettingVO> detail(DeviceSetting deviceSetting) {
		DeviceSetting detail = deviceSettingService.getOne(Condition.getQueryWrapper(deviceSetting));
		return R.data(DeviceSettingWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 设备语音播报设置表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceSetting")
	public R<IPage<DeviceSettingVO>> list(DeviceSetting deviceSetting, Query query) {
		IPage<DeviceSetting> pages = deviceSettingService.page(Condition.getPage(query), Condition.getQueryWrapper(deviceSetting));
		return R.data(DeviceSettingWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 设备语音播报设置表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceSetting")
	public R<IPage<DeviceSettingVO>> page(DeviceSettingVO deviceSetting, Query query) {
		IPage<DeviceSettingVO> pages = deviceSettingService.selectDeviceSettingPage(Condition.getPage(query), deviceSetting);
		return R.data(pages);
	}

	/**
	 * 新增 设备语音播报设置表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceSetting")
	public R save(@Valid @RequestBody DeviceSetting deviceSetting) {
		return R.status(deviceSettingService.save(deviceSetting));
	}

	/**
	 * 修改 设备语音播报设置表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceSetting")
	public R update(@Valid @RequestBody DeviceSetting deviceSetting) {
		return R.status(deviceSettingService.updateById(deviceSetting));
	}

	/**
	 * 新增或修改 设备语音播报设置表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceSetting")
	public R submit(@Valid @RequestBody DeviceSetting deviceSetting) {
		return R.status(deviceSettingService.saveOrUpdate(deviceSetting));
	}


	/**
	 * 删除 设备语音播报设置表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceSettingService.deleteLogic(Func.toLongList(ids)));
	}


}
