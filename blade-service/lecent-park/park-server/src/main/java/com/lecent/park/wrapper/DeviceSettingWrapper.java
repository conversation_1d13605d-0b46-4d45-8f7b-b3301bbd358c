package com.lecent.park.wrapper;

import com.lecent.park.entity.DeviceSetting;
import com.lecent.park.vo.DeviceSettingVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 设备语音播报设置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-28
 */
public class DeviceSettingWrapper extends BaseEntityWrapper<DeviceSetting, DeviceSettingVO>  {

	public static DeviceSettingWrapper build() {
		return new DeviceSettingWrapper();
 	}

	@Override
	public DeviceSettingVO entityVO(DeviceSetting deviceSetting) {
		DeviceSettingVO deviceSettingVO = BeanUtil.copy(deviceSetting, DeviceSettingVO.class);

		//User createUser = UserCache.getUser(deviceSetting.getCreateUser());
		//User updateUser = UserCache.getUser(deviceSetting.getUpdateUser());
		//deviceSettingVO.setCreateUserName(createUser.getName());
		//deviceSettingVO.setUpdateUserName(updateUser.getName());

		return deviceSettingVO;
	}

}
