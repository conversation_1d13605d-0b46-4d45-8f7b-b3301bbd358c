package com.lecent.park.service;

import com.lecent.park.entity.Parklot;
import com.lecent.park.event.channeltodo.ReturnParkPlaceEvent;

/**
 * 车场剩余停车位数量计算接口
 *
 * <AUTHOR>
 */
public interface ICalculateParkRemainNumberService {

	/**
	 * 根据车场id获取剩余数量
	 *
	 * @param parkLotId
	 * @return
	 */
	int getParkRemainNumber(Long parkLotId);

	/**
	 * 获取实时车位数量
	 * @return
	 */
	Integer getRealTimeParkRemainNumber(Long parkLotId);


	/**
	 * 预扣车位/预定车位/判断车位是否足够
	 * 统一预定入口
	 * @param parklot       车场id
	 * @param parkPlaceNumber 车位数量
	 * @return  false均为车位不足
	 */
	Boolean parkPlaceBook(Parklot parklot, Integer parkPlaceNumber);

	/**
	 * 归还预定的车位 如车出场 订单超时返回等等
	 * @param event 归还车位信息
	 * @return
	 */
	Boolean returnParkPlaceBook(ReturnParkPlaceEvent event);



}
