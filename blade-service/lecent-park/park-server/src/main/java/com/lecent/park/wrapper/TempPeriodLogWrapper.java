package com.lecent.park.wrapper;

import com.lecent.park.entity.TempPeriodLog;
import com.lecent.park.vo.TempPeriodLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 修改车牌临停记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public class TempPeriodLogWrapper extends BaseEntityWrapper<TempPeriodLog, TempPeriodLogVO>  {

	public static TempPeriodLogWrapper build() {
		return new TempPeriodLogWrapper();
 	}

	@Override
	public TempPeriodLogVO entityVO(TempPeriodLog tempPeriodLog) {
		TempPeriodLogVO tempPeriodLogVO = BeanUtil.copy(tempPeriodLog, TempPeriodLogVO.class);

		//User createUser = UserCache.getUser(tempPeriodLog.getCreateUser());
		//User updateUser = UserCache.getUser(tempPeriodLog.getUpdateUser());
		//tempPeriodLogVO.setCreateUserName(createUser.getName());
		//tempPeriodLogVO.setUpdateUserName(updateUser.getName());

		return tempPeriodLogVO;
	}

}
