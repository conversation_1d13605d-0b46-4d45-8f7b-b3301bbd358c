package com.lecent.park.service;

import com.lecent.park.entity.Schedules;
import com.lecent.park.vo.SchedulesCalendar;
import com.lecent.park.vo.SchedulesVO;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 排班表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
public interface ISchedulesService extends BaseService<Schedules> {

    /**
     * 获取排班信息
     *
     * @param id 排班表id
     * @return {@link Schedules}
     */
    SchedulesVO detail(Long id);

    /**
     * 获取排班信息列表
     *
     * @param schedules 排班表
     * @return {@link Schedules}
     */
    List<SchedulesVO> getSchedules(SchedulesVO schedules);

    /**
     * 获取排班信息列表
     *
     * @param schedules 排班表
     * @return {@link Schedules}
     */
    List<SchedulesCalendar> getSchedulesCalendar(SchedulesVO schedules);

    /**
     * 保存或更新排班信息，并添加相关的日志记录
     *
     * @param schedules 排班表
     * @return {@link Boolean}
     */
    Boolean saveOrUpdateSchedules(SchedulesVO schedules);

    /**
     * 提交排班信息，支持更新和新建操作，可以处理批量时间范围的排班提交
     *
     * @param schedules 排班表
     * @return {@link Boolean}
     */
    Boolean submitSchedules(SchedulesVO schedules);

    /**
     * 提交排班信息，支持更新和新建操作，可以处理批量时间范围的排班提交
     *
     * @param calendar 日历
     * @return {@link Boolean}
     */
    Boolean submitSchedules(SchedulesCalendar calendar);

    /**
     * 删除排班表
     *
     * @param ids ids
     * @return {@link Boolean}
     */
    Boolean delSchedules(List<Long> ids);

    /**
     * 清空排班表
     *
     * @param schedules 排班表
     * @return {@link Boolean}
     */
    Boolean clean(SchedulesVO schedules);

    /**
     * 导出指定月份的排班表
     *
     * @param response HTTP响应对象
     * @param monthStr 指定的月份字符串，格式为"yyyy-MM"
     */
    void exportSchedules(HttpServletResponse response, String monthStr);
}
