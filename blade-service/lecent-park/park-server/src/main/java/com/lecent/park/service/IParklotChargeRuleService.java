package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParklotChargeRule;
import com.lecent.park.vo.ParklotChargeRuleVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 公司员工车场资源授权表 服务类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface IParklotChargeRuleService extends BaseService<ParklotChargeRule> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotChargeRule
	 * @return
	 */
	IPage<ParklotChargeRuleVO> selectParklotChargeRulePage(IPage<ParklotChargeRuleVO> page, ParklotChargeRuleVO parklotChargeRule);

	boolean saveParklotChargeRule(Long id, Long companyId, Long chargeRuleId);

	ParklotChargeRule getByCondition(Long parklotId, Long companyId, Long chargeRuleId);

	ParklotChargeRule getByCondition(Long parklotId);
}
