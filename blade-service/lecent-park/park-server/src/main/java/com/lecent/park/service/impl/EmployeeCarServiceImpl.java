package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.EmployeeCarDTO;
import com.lecent.park.entity.EmployeeCar;
import com.lecent.park.entity.EmployeeCarParklot;
import com.lecent.park.mapper.EmployeeCarMapper;
import com.lecent.park.service.IEmployeeCarParklotService;
import com.lecent.park.service.IEmployeeCarService;
import com.lecent.park.vo.EmployeeCarParklotVO;
import com.lecent.park.vo.EmployeeCarVO;
import com.lecent.park.wrapper.EmployeeCarWrapper;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 员工车辆 服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Service
public class EmployeeCarServiceImpl extends BaseServiceImpl<EmployeeCarMapper, EmployeeCar> implements IEmployeeCarService {

	@Autowired
	private IEmployeeCarParklotService employeeCarParklotService;

	@Override
	public IPage<EmployeeCarVO> selectEmployeeCarPage(IPage<EmployeeCarVO> page, EmployeeCarDTO employeeCarDto) {
		return page.setRecords(baseMapper.selectEmployeeCarPage(page, employeeCarDto));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(EmployeeCarDTO employeeCarDto) {
		Long id = employeeCarDto.getId();
		if (Func.isNull(id)) {
			checkWorkNo(employeeCarDto);
		}
		translateCertificate(employeeCarDto);
		saveOrUpdate(employeeCarDto);
		List<EmployeeCarParklot> employeeCarParklotList = employeeCarDto.getEmployeeCarParklotList();
		employeeCarParklotService.lambdaUpdate()
			.eq(EmployeeCarParklot::getEmployeeCarId, employeeCarDto.getId())
			.remove();
		employeeCarParklotList.forEach(employeeCarParklot -> employeeCarParklot.setEmployeeCarId(employeeCarDto.getId()));
		employeeCarParklotService.saveBatch(employeeCarParklotList);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean customRemove(String id) {
		removeById(id);
		employeeCarParklotService.lambdaUpdate()
			.eq(EmployeeCarParklot::getEmployeeCarId, id)
			.remove();
		return true;
	}

	@Override
	public EmployeeCarVO detail(String id) {
		EmployeeCar employeeCar = getById(id);
		EmployeeCarVO employeeCarVO = EmployeeCarWrapper.build().entityVO(employeeCar);
		employeeCarVO.setCertificateList(Func.toStrList(employeeCarVO.getCertificate()));
		List<EmployeeCarParklotVO> list = employeeCarParklotService.employeeCarContact(employeeCar.getId());
		if (Func.isNotEmpty(list)) {
			employeeCarVO.setEmployeeCarParklotVOList(list);
		}
		return employeeCarVO;
	}

	@Override
	public boolean removeParklot(String contactId) {
		employeeCarParklotService.removeById(contactId);
		return true;
	}


	private void translateCertificate(EmployeeCarDTO employeeCarDto) {
		List<String> certificateList = employeeCarDto.getCertificateList();
		if (Func.isNotEmpty(certificateList)) {
			employeeCarDto.setCertificate(Func.join(certificateList));
		}
	}

	private void checkWorkNo(EmployeeCarDTO employeeCarDto) {
		Integer count = lambdaQuery().eq(EmployeeCar::getWorkNo, employeeCarDto.getWorkNo())
			.count();
		LecentAssert.isTrue(count < 1, "此工号的员工已有员工车辆，请不要重复添加");
	}

}
