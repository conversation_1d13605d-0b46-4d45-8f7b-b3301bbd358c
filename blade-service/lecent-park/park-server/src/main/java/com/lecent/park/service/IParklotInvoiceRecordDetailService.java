package com.lecent.park.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lecent.park.entity.ParklotInvoiceRecordDetail;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IParklotInvoiceRecordDetailService  extends IService<ParklotInvoiceRecordDetail> {

	List<ParklotInvoiceRecordDetail> getRecordDetailList(Long recordId);

	/**
	 * 查询开票详情里面是否已经包含了 订单信息
	 *
	 * @param orderIds
	 * @param type
	 * @return
	 */
	List<ParklotInvoiceRecordDetail> isIncludeOrder(List<Long> orderIds, Integer type);

	Boolean updateBatchBySerialNum(List<ParklotInvoiceRecordDetail> details);
}
