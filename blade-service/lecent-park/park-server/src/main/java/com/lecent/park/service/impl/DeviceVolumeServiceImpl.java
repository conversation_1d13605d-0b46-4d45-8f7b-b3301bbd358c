package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.DeviceVolume;
import com.lecent.park.entity.PaceDevice;
import com.lecent.park.mapper.DeviceVolumeMapper;
import com.lecent.park.service.IDeviceVolumeService;
import com.lecent.park.vo.DeviceVolumeVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
@Service
public class DeviceVolumeServiceImpl extends BaseServiceImpl<DeviceVolumeMapper, DeviceVolume> implements IDeviceVolumeService {

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveList(DeviceVolumeVO deviceVolumeVo) {
		List<DeviceVolume> list = new ArrayList<>();
		List<PaceDevice> paceDeviceList = baseMapper.selectPlaceDevice(deviceVolumeVo.getParklotId(),deviceVolumeVo.getFloorId(), deviceVolumeVo.getRegionId());
		LecentAssert.notEmpty(paceDeviceList, "此车场该区域下没有可以设置音量的设备");


		if (deviceVolumeVo.getDeviceVolume().size() > 0) {
			for (PaceDevice paceDevice : paceDeviceList) {
				baseMapper.deleteDeviceSn(paceDevice.getDeviceSn());
				baseMapper.deleteByPlaceDeviceId(paceDevice.getId());
			}
		}
		for (PaceDevice pace : paceDeviceList) {
			for (DeviceVolume deviceVolume : deviceVolumeVo.getDeviceVolume()) {
				DeviceVolume dV = new DeviceVolume();
				dV.setPlaceDeviceId(pace.getId());
				dV.setEndTime(deviceVolume.getEndTime());
				dV.setStartTime(deviceVolume.getStartTime());
				dV.setVolumeSize(deviceVolume.getVolumeSize());
				list.add(dV);
			}
		}
		return this.saveBatch(list);
	}

	@Override
	public List<DeviceVolumeVO> selectDeviceList(String deviceId) {
		return baseMapper.selectDeviceVolumeList(deviceId);
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public boolean batchSave(List<DeviceVolume> deviceVolumes) {
		if (deviceVolumes.size() > 0) {
			baseMapper.deleteDeviceSn(deviceVolumes.get(0).getDeviceSn());
			deviceVolumes.stream().forEach(vo -> save(vo));
		}
		return true;
	}

	@Override
	public boolean deleteLogic(@NotEmpty List<Long> ids) {
		return false;
	}

	@Override
	public boolean changeStatus(@NotEmpty List<Long> ids, Integer status) {
		return false;
	}

	@Override
	public int getVoiceLevel(Long placeDeviceId) {
		String nowTime = DateUtil.format(new Date(), "HH:mm");
		int voiceLevel = 13;
		List<DeviceVolume> deviceVolumeList = list(Wrappers.<DeviceVolume>lambdaQuery()
													   .ne(DeviceVolume::getStartTime, nowTime)
													   .ge(DeviceVolume::getEndTime, nowTime)
													   .eq(DeviceVolume::getPlaceDeviceId, placeDeviceId));
		if (CollectionUtil.isNotEmpty(deviceVolumeList)) {
			voiceLevel = Func.toInt(deviceVolumeList.get(0).getVolumeSize());
		}
		return voiceLevel;
	}
}
