package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.UserParklotSettingDTO;
import com.lecent.park.entity.ChannelTree;
import com.lecent.park.entity.UserChannel;
import com.lecent.park.vo.UserChannelVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 公司员工车场资源授权表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public interface IUserChannelService extends BaseService<UserChannel> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userChannel
	 * @return
	 */
	IPage<UserChannelVO> selectUserChannelPage(IPage<UserChannelVO> page, UserChannelVO userChannel);

	/**
	 * 车场通道树
	 *
	 * @param userId 用户ID
	 * @param labels
	 * @return 车场通道树
	 */
	List<ChannelTree> getChannelTree(Long userId, String labels);

	boolean saveUserChannel(UserParklotSettingDTO userParklotSettingDTO);

	/**
	 * 获取车场用户
	 *
	 * @param
	 * @return
	 */
	List<Map> getParkLotUser();

	/**
	 * 查询用户通道
	 *
	 * @param userId 用户ID
	 * @return 用户授权的通道列表
	 */
	List<UserChannel> getUserChannel(Long userId);
}
