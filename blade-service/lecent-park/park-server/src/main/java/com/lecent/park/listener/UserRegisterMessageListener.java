package com.lecent.park.listener;

import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.core.mq.rabbitmq.MessageConstant;
import com.lecent.park.core.mq.rabbitmq.utils.ObjUtil;
import com.lecent.park.discount.coupon.service.ICouponActivityService;
import com.lecent.park.en.coupon.SendConditionEnum;
import com.lecent.park.en.coupon.UserActionEnum;
import com.lecent.park.entity.CouponActivity;
import com.lecent.park.discount.coupon.service.IUserCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.system.user.entity.CUser;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_EXCHANGE_NAME;


/**
 * 建行公众号用户注册监听
 *
 * <AUTHOR>
 */

@Component
@Slf4j
@Order
public class UserRegisterMessageListener {

	@Autowired
	private ICouponActivityService couponActivityService;

	@Autowired
	private IUserCouponService userCouponService;


	/**
	 * 建行公众号用户注册监听(自动发放优惠劵)
	 *
	 * @param message 消息
	 */
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = MessageConstant.LECENT_PARK_USER_REGISTER_QUEUE, durable = "true")
		, exchange = @Exchange(value = LECENT_PARK_EXCHANGE_NAME
		, type = ExchangeTypes.TOPIC), key = MessageConstant.LECENT_PARK_USER_REGISTER_KEY)})
	@RedisLock(value = ParkCacheNames.PARK_REGISTER_COUPON)
	public void userRegisterMessageHandler(Message message) {

		CUser user = ObjUtil.toObjectMessageCharacter(message, CUser.class, "UTF-8");

		log.info("监听用户注册发劵，用户信息：[{}]", JsonUtil.toJson(user));

		//获取正在进行的注册送优惠劵的活动
		List<CouponActivity> onLineActivityList = couponActivityService.getOnLineActivityBySendCondition(SendConditionEnum.USER_ACTION.getValue(), UserActionEnum.NEW_USER.getValue());

		log.info("正在进行的注册送劵的活动：[{}]", JsonUtil.toJson(onLineActivityList));

		for (CouponActivity couponActivity : onLineActivityList) {

			//如果用户再活动中已经发放过，不再发放
			Integer count = userCouponService.getByUserIdActivityId(user.getId(), couponActivity.getId());
			if (count > 0) {
				continue;
			}

			//给用户发劵
			couponActivityService.sendCouponToUser(couponActivity, user, "用户注册自动发放");
		}
	}
}

