package com.lecent.park.controller.open.openh5.req;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 对外临停缴费订单
 *
 * <AUTHOR>
 */
@Data
@ApiModel("月卡支付")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReqCardPay {

	/**
	 * 月卡订单ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@NotNull(message = "月卡订单ID不能为空")
	@ApiModelProperty("月卡订单ID")
	private Long cardOrderId;
}
