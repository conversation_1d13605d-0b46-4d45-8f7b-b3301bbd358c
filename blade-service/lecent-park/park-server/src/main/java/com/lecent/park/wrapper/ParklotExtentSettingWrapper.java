package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotExtentSetting;
import com.lecent.park.vo.ParklotExtentSettingVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场扩展配置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
public class ParklotExtentSettingWrapper extends BaseEntityWrapper<ParklotExtentSetting, ParklotExtentSettingVO>  {

	public static ParklotExtentSettingWrapper build() {
		return new ParklotExtentSettingWrapper();
 	}

	@Override
	public ParklotExtentSettingVO entityVO(ParklotExtentSetting parklotExtentSetting) {
		ParklotExtentSettingVO parklotExtentSettingVO = BeanUtil.copy(parklotExtentSetting, ParklotExtentSettingVO.class);

		//User createUser = UserCache.getUser(parklotExtentSetting.getCreateUser());
		//User updateUser = UserCache.getUser(parklotExtentSetting.getUpdateUser());
		//parklotExtentSettingVO.setCreateUserName(createUser.getName());
		//parklotExtentSettingVO.setUpdateUserName(updateUser.getName());

		return parklotExtentSettingVO;
	}

}
