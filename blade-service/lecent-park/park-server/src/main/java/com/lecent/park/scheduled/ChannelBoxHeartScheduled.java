package com.lecent.park.scheduled;

import com.lecent.park.device.utils.GateActionUtils;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IParklotService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 通道盒子心跳包
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ChannelBoxHeartScheduled {
	@Autowired
	private IParklotService parklotService;

	@Scheduled(cron = "*/10 * * * * ?")
	@RedisLock(value = "lecent:park::timedTask:lock:channelBoxHeartbeat", waitTime = 1L, leaseTime = 9)
	public void channelBoxHeartbeat() {
		try {
			String heartbeat = "{\"commands\":\"HEARTBEAT\"}";
			List<Parklot> parklotList = parklotService.getParkLotList();
			if (CollectionUtil.isNotEmpty(parklotList)) {
				parklotList.forEach(parklot -> {
					GateActionUtils.pushMessage(parklot.getId(), heartbeat);
				});
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error("心跳包发送失败:" + e.getMessage());
		}
	}

}
