package com.lecent.park.service;

import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardOrder;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.payment.vo.MessageModelVO;

/**
 * @Description: TODO
 * @author: cy
 * @date: 2021年06月18日 10:52
 */
public interface MessageWxPushService {

	/**
	 * 通道进出车辆消息通知
	 * @param channelTodo
	 * @return
	 */
	MessageModelVO channelMessage(ChannelTodoVO channelTodo);

	/**
	 * 临停支付成功通知
	 * @param tempOrder
	 * @return
	 */
	MessageModelVO tempPayMessage(TempParkingOrder tempOrder);

	/**
	 * 月卡缴费支付成功消息通知
	 * @param cardOrder
	 * @return
	 */
	MessageModelVO monthCardPayMessage(CardOrder cardOrder);

	/**
	 * 月卡过期消息通知
	 * @param card
	 * @return
	 */
	MessageModelVO monthCardOverMessage(Card card);
}
