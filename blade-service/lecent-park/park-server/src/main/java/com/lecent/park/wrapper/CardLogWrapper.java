package com.lecent.park.wrapper;

import com.lecent.park.entity.CardLog;
import com.lecent.park.vo.CardLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 月卡变更日志表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class CardLogWrapper extends BaseEntityWrapper<CardLog, CardLogVO>  {

	public static CardLogWrapper build() {
		return new CardLogWrapper();
 	}

	@Override
	public CardLogVO entityVO(CardLog cardLog) {
		CardLogVO cardLogVO = BeanUtil.copy(cardLog, CardLogVO.class);

		//User createUser = UserCache.getUser(cardLog.getCreateUser());
		//User updateUser = UserCache.getUser(cardLog.getUpdateUser());
		//cardLogVO.setCreateUserName(createUser.getName());
		//cardLogVO.setUpdateUserName(updateUser.getName());

		return cardLogVO;
	}

}
