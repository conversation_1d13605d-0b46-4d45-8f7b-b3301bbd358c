package com.lecent.park.wrapper;

import com.lecent.park.entity.PaceDevice;
import com.lecent.park.vo.PaceDeviceVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
public class PaceDeviceWrapper extends BaseEntityWrapper<PaceDevice, PaceDeviceVO>  {

	public static PaceDeviceWrapper build() {
		return new PaceDeviceWrapper();
 	}

	@Override
	public PaceDeviceVO entityVO(PaceDevice deviceInfo) {
		PaceDeviceVO deviceInfoVO = BeanUtil.copy(deviceInfo, PaceDeviceVO.class);

		//User createUser = UserCache.getUser(deviceInfo.getCreateUser());
		//User updateUser = UserCache.getUser(deviceInfo.getUpdateUser());
		//deviceInfoVO.setCreateUserName(createUser.getName());
		//deviceInfoVO.setUpdateUserName(updateUser.getName());

		return deviceInfoVO;
	}

}
