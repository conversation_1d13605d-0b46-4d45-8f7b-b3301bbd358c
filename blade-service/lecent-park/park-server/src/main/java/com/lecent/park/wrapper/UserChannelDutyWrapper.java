package com.lecent.park.wrapper;

import com.lecent.park.entity.UserChannelDuty;
import com.lecent.park.vo.UserChannelDutyVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 通道值班表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
public class UserChannelDutyWrapper extends BaseEntityWrapper<UserChannelDuty, UserChannelDutyVO>  {

	public static UserChannelDutyWrapper build() {
		return new UserChannelDutyWrapper();
 	}

	@Override
	public UserChannelDutyVO entityVO(UserChannelDuty userChannelDuty) {
		UserChannelDutyVO userChannelDutyVO = BeanUtil.copy(userChannelDuty, UserChannelDutyVO.class);

		//User createUser = UserCache.getUser(userChannelDuty.getCreateUser());
		//User updateUser = UserCache.getUser(userChannelDuty.getUpdateUser());
		//userChannelDutyVO.setCreateUserName(createUser.getName());
		//userChannelDutyVO.setUpdateUserName(updateUser.getName());

		return userChannelDutyVO;
	}

}
