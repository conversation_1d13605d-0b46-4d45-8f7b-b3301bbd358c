package com.lecent.park.controller.wechat;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.ParklotInvoiceRecord;
import com.lecent.park.service.IParklotInvoiceRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 发票记录 控制器
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wechat/invoice-record")
@Api(value = "发票记录接口", tags = "发票记录接口")
public class WechatInvoiceRecordController extends BladeController {

	private IParklotInvoiceRecordService invoiceRecordService;


	/**
	 * 开票记录
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "开票记录", notes = "开票记录")
	public R<List<ParklotInvoiceRecord>> list(String plate) {
		//因暂时还没有对接任何发票提供商，先返回一个空的，
		List<ParklotInvoiceRecord> list = new ArrayList<>();
		return R.data(list);
	}


}
