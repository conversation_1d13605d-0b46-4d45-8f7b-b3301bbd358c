package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.VehicleDO;
import com.leliven.park.infrastructure.gateway.persistence.vehicle.vo.VehicleVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 车辆信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface IVehicleService extends BaseService<VehicleDO> {

	/**
	 * 自定义分页
	 *
	 * @param page    页面
	 * @param vehicle 车辆
	 * @return {@link IPage}<{@link VehicleVO}>
	 */
	IPage<VehicleVO> selectVehiclePage(IPage<VehicleVO> page, VehicleVO vehicle);

}
