package com.lecent.park.service.impl;

import com.alibaba.fastjson.JSON;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.core.notify.base.BaseMsgReceiver;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IParklotSpaceCalculateService;
import com.lecent.park.service.IParklotSpaceNumberPushService;
import com.lecent.park.vo.PlaceNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.lecent.park.common.constant.ParkMsgConstant.CAR_ENTER;
import static com.lecent.park.common.constant.ParkMsgConstant.PAY_EXIT;
import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.AMQ_TOPIC;
import static com.lecent.park.core.mq.rabbitmq.routing.SendRoutingKeys.LECENT_PARKING_DEVICE_DOWN;
import static org.springblade.common.constant.LecentAppConstant.APPLICATION_PARK_NAME;

/**
 * 车场车位数量推送业务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParklotSpaceNumberPushServiceImpl extends BaseMsgReceiver<ParkingOrder> implements IParklotSpaceNumberPushService {

	private final MqSender mqSender;
	private final IParklotSpaceCalculateService parklotSpaceCalculateService;

	protected ParklotSpaceNumberPushServiceImpl(MqSender mqSender, IParklotSpaceCalculateService parklotSpaceCalculateService) {
		super(APPLICATION_PARK_NAME, CAR_ENTER, PAY_EXIT);
		this.mqSender = mqSender;
		this.parklotSpaceCalculateService = parklotSpaceCalculateService;
	}

	public void push(Parklot parklot) {
		String routingKey = LECENT_PARKING_DEVICE_DOWN.concat(".").concat(String.valueOf(parklot.getId()));
		PlaceNumber placeNumber = PlaceNumber.builder()
			.totalNumber(parklot.getTempLotAmount())
			.remainNumber(this.parklotSpaceCalculateService.getRemainNumber(parklot.getId()))
			.build();
		log.info("推送车位数据:车场名称：{},车位总数{},剩余车位数{}",
			parklot.getFullName(), parklot.getTempLotAmount(), placeNumber.getRemainNumber());
		Map<String, Object> pushMap = new HashMap<>(1);
		pushMap.put("push", placeNumber);
		Map<String, Object> displayMap = new HashMap<>(1);
		displayMap.put("display", pushMap);
		mqSender.sendMessage(JSON.toJSONString(displayMap), routingKey, AMQ_TOPIC);
	}

	@Async
	@Override
	public void asyncPush(Parklot parklot) {
		push(parklot);
	}

	@Override
	public void accept(ParkingOrder parkingOrder) {
		Optional.ofNullable(parkingOrder)
			.filter(t -> Objects.nonNull(t.getParklotId()))
			.map(t -> ParkLotCaches.getParkLot(t.getParklotId()))
			.ifPresent(this::push);
	}
}
