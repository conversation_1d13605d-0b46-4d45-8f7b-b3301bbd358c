package com.lecent.park.wrapper;

import com.lecent.park.entity.BlackList;
import com.lecent.park.vo.BlackListVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 车辆黑名单包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public class BlackListWrapper extends BaseEntityWrapper<BlackList, BlackListVO> {

	public static BlackListWrapper build() {
		return new BlackListWrapper();
 	}

	@Override
	public BlackListVO entityVO(BlackList blackList) {
		BlackListVO blackListVO = Objects.requireNonNull(BeanUtil.copy(blackList, BlackListVO.class));

		//User createUser = UserCache.getUser(blackList.getCreateUser());
		//User updateUser = UserCache.getUser(blackList.getUpdateUser());
		//blackListVO.setCreateUserName(createUser.getName());
		//blackListVO.setUpdateUserName(updateUser.getName());

		return blackListVO;
	}

}
