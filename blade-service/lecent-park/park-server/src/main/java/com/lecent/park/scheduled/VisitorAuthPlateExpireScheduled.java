package com.lecent.park.scheduled;

import cn.hutool.core.collection.CollUtil;
import com.lecent.park.common.enums.card.CardStatusConstants;
import com.lecent.park.entity.VisitorAuth;
import com.lecent.park.service.IVisitorAuthService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 车辆访客授权过期定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class VisitorAuthPlateExpireScheduled {

	private static final String MEMO = "定时将过期的访客授权改为失效";

	private final IVisitorAuthService visitorAuthService;

	/**
	 * 每隔5分钟执行一次
	 */
	@Scheduled(cron = "${lecent.park.visitor.auth.expire-cron:0 0/5 * * * ?}")
	@RedisLock(value = "lecent:park::timedTask:lock:visitorAuthPlateExpireScheduled")
	public void visitorAuthPlateExpireScheduled() {

		List<VisitorAuth> expireList = visitorAuthService.getExpireList();

		if (CollUtil.isEmpty(expireList)) {
			return;
		}

		expireList.forEach(e->{
			e.setStatus(CardStatusConstants.NOT_EFFECTIVE);
			e.setMemo(MEMO);
		});


		visitorAuthService.updateBatchById(expireList);
	}


}
