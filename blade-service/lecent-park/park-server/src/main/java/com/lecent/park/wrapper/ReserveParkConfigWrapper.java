package com.lecent.park.wrapper;

import com.lecent.park.entity.ReserveParkConfig;
import com.lecent.park.vo.ReserveParkConfigVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 月卡变更日志表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
public class ReserveParkConfigWrapper extends BaseEntityWrapper<ReserveParkConfig, ReserveParkConfigVO>  {

	public static ReserveParkConfigWrapper build() {
		return new ReserveParkConfigWrapper();
 	}

	@Override
	public ReserveParkConfigVO entityVO(ReserveParkConfig reserveParkConfig) {
		ReserveParkConfigVO reserveParkConfigVO = BeanUtil.copy(reserveParkConfig, ReserveParkConfigVO.class);

		return reserveParkConfigVO;
	}

}
