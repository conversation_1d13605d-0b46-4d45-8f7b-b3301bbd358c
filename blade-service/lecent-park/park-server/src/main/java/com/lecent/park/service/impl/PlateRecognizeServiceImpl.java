package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.PlateRecognize;
import com.lecent.park.mapper.PlateRecognizeMapper;
import com.lecent.park.service.IPlateRecognizeService;
import com.lecent.park.vo.PlateRecognizeVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 对象存储表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@Service
public class PlateRecognizeServiceImpl extends BaseServiceImpl<PlateRecognizeMapper, PlateRecognize> implements IPlateRecognizeService {

	@Override
	public IPage<PlateRecognizeVO> selectPlateRecognizePage(IPage<PlateRecognizeVO> page, PlateRecognizeVO plateRecognize) {
		return page.setRecords(baseMapper.selectPlateRecognizePage(page, plateRecognize));
	}

}
