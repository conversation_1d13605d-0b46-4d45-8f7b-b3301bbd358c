package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.RefundRecord;
import com.lecent.park.service.IRefundRecordService;
import com.lecent.park.vo.RefundRecordVO;
import com.lecent.park.wrapper.RefundRecordWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 退款记录 控制器
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/refundrecord")
@Api(value = "退款记录", tags = "退款记录接口")
public class RefundRecordController extends BladeController {

	private IRefundRecordService refundRecordService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入refundRecord")
	public R<RefundRecordVO> detail(RefundRecord refundRecord) {
		RefundRecord detail = refundRecordService.getOne(Condition.getQueryWrapper(refundRecord));
		return R.data(RefundRecordWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 退款记录
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入refundRecord")
	public R<IPage<RefundRecordVO>> list(RefundRecord refundRecord, Query query) {
		IPage<RefundRecord> pages = refundRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(refundRecord));
		return R.data(RefundRecordWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 退款记录
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入refundRecord")
	public R<IPage<RefundRecordVO>> page(RefundRecordVO refundRecord, Query query) {
		IPage<RefundRecordVO> pages = refundRecordService.selectRefundRecordPage(Condition.getPage(query), refundRecord);
		return R.data(pages);
	}




	/**
	 * 新增 退款记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入refundRecord")
	public R save(@Valid @RequestBody RefundRecord refundRecord) {
		return R.status(refundRecordService.save(refundRecord));
	}

	/**
	 * 修改 退款记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入refundRecord")
	public R update(@Valid @RequestBody RefundRecord refundRecord) {
		return R.status(refundRecordService.updateById(refundRecord));
	}

	/**
	 * 新增或修改 退款记录
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入refundRecord")
	public R submit(@Valid @RequestBody RefundRecord refundRecord) {
		return R.status(refundRecordService.saveOrUpdate(refundRecord));
	}


	/**
	 * 删除 退款记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(refundRecordService.deleteLogic(Func.toLongList(ids)));
	}


}
