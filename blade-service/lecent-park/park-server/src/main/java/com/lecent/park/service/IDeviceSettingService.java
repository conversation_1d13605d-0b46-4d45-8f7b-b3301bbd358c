package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.DeviceSetting;
import com.lecent.park.vo.DeviceSettingVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 设备语音播报设置表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-28
 */
public interface IDeviceSettingService extends BaseService<DeviceSetting> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceSetting
	 * @return
	 */
	IPage<DeviceSettingVO> selectDeviceSettingPage(IPage<DeviceSettingVO> page, DeviceSettingVO deviceSetting);

}
