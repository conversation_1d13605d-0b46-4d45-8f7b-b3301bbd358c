package com.lecent.park.scheduled;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.service.IParkingOrderService;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.ParkingOrderMapper;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.TempParkingOrderMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 僵尸车清理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class CleanCorpseScheduled {

    @Value(value = "${lecent.park.insidePayExitParkLotIds:0}")
    private String insidePayExitParkLotIds;

    @Autowired
    private IParkingOrderService parkingOrderService;

    @Autowired
    private TempParkingOrderMapper tempParkingOrderMapper;

    @Autowired
    private ParkingOrderMapper parkingOrderMapper;

    /**
     * 每天凌晨定时清理在场记录
     */
    @XxlJob("cleanCorpseScheduled")
    public void cleanCorpse() {
        log.info("每天4点开始清理路边停车车辆，parkLotIds={}", insidePayExitParkLotIds);
        if (Func.isNotBlank(insidePayExitParkLotIds)) {
            List<Long> parkLotIdList = Func.toLongList(insidePayExitParkLotIds);
            for (long parkLotId : parkLotIdList) {
                parkingOrderService.update(Wrappers.<ParkingOrder>lambdaUpdate()
                    .set(ParkingOrder::getParkingStatus, 4)
                    .eq(ParkingOrder::getParkingStatus, 2)
                    .eq(ParkingOrder::getParklotId, parkLotId));
            }
        }
    }

    /**
     * 更新异常金额
     */
    @XxlJob("updateUnusualAmountScheduled")
    public void updateUnusualAmount() {
        log.info("开始执行更新异常金额定时任务");
        try {
            // 查询符合条件的临停订单
            List<TempParkingOrder> tempOrders = tempParkingOrderMapper.selectUnusualAmountOrders();

            if (Func.isNotEmpty(tempOrders)) {
                // 批量更新临停订单异常金额
                tempParkingOrderMapper.batchUpdateUnusualAmount(tempOrders);

                // 批量更新停车记录异常金额
                parkingOrderMapper.batchUpdateUnusualAmount(tempOrders);

                log.info("更新异常金额定时任务执行完成，处理订单数：{}", tempOrders.size());
            } else {
                log.info("没有需要处理的异常金额订单");
            }
        } catch (Exception e) {
            log.error("更新异常金额定时任务执行异常", e);
        }
    }
}

