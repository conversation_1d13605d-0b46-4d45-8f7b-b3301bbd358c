package com.lecent.park.service;

import com.lecent.park.vo.*;

import java.util.List;

/**
 * 首頁服务器类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface IHomePageService {


	/**
	 * 收入统计
	 *
	 * @return
	 */
	IncomeStatisticsVO incomeStatistics();

	/**
	 * 统计首页当日停车收入
	 *
	 * @return IncomeStatisticsVO
	 */
	IncomeStatisticsVO incomeStatisticsOfToday();

	/**
	 * 收入直方图统计
	 *
	 * @param statisticsVO
	 * @return {@link List}<{@link HistogramStatisticsVO}>
	 */
	List<HistogramStatisticsVO> incomeHistogramStatistics(HistogramStatisticsVO statisticsVO);

	/**
	 * 月卡过期预警
	 *
	 * @return
	 */
	List<CardVO> cardEarlyWarning();

	List<FlowRateVO> carFlowRateStatistics(Integer statisticsType);

	/**
	 * 缴费方式统计
	 *
	 * @param parklotType
	 * @return
	 */
	List<PayWayStatisticsVO> payWayStatistics(Integer parklotType);

	/**
	 * 首页出入场记录
	 *
	 * @return
	 */
	List<HomeEnterAndExitVO> enterAndExitRecord();

	/**
	 * 统计首页停车收入
	 *
	 * @param parkLotIds
	 * @return
	 */
	IncomeStatisticsVO statisticAmount(List<Long> parkLotIds);


}
