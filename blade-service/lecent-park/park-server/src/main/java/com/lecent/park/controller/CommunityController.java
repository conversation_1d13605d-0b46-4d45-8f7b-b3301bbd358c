package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.common.constant.OwnerEnum;
import com.lecent.park.entity.OwnerDetailInfo;
import com.lecent.park.service.IOwnerDetailInfoService;
import com.lecent.park.vo.OwnerDetailInfoTreeVO;
import com.lecent.park.vo.OwnerDetailInfoVO;
import com.lecent.park.wrapper.OwnerDetailInfoWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 社区信息管理
 */
@RestController
@AllArgsConstructor
@RequestMapping("/community")
@Api(value = "社区信息管理", tags = "小区信息表接口")
public class CommunityController {

	private IOwnerDetailInfoService ownerDetailInfoService;

	/**
	 * 小区详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "社区详情", notes = "传入id进行查询")
	public R<OwnerDetailInfoVO> housingDetail(OwnerDetailInfo ownerDetailInfo) {
		OwnerDetailInfo detail = ownerDetailInfoService.getOne(Condition.getQueryWrapper(ownerDetailInfo));
		return R.data(OwnerDetailInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 获取小区列表
	 *
	 * @return List<OwnerDetailInfo>
	 */
	@GetMapping("/housing/getCommunityList")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "获取小区列表", notes = "getCommunityList")
	public R<List<OwnerDetailInfo>> getCommunityList() {
		return R.data(this.ownerDetailInfoService.getCommunityList());
	}

	/**
	 * 根据parentId 获取列表
	 *
	 * @return List<OwnerDetailInfo>
	 */
	@GetMapping("/getListByParentId")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据父id获取信息", notes = "getListByParentId")
	public R<List<OwnerDetailInfo>> getListByParentId(String parentId) {
		return R.data(ownerDetailInfoService.list(Wrappers.<OwnerDetailInfo>lambdaQuery()
			.eq(OwnerDetailInfo::getParentId, parentId)
			.eq(OwnerDetailInfo::getStatus, 1)));
	}

	/**
	 * 新增或修改小区信息
	 */
	@PostMapping("/housing/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改小区信息", notes = "传入ownerDetailInfo")
	public R<Boolean> housingSubmit(@Valid @RequestBody OwnerDetailInfo ownerDetailInfo) {
		return R.status(ownerDetailInfoService.addOwnerDetailInfo(ownerDetailInfo.setLevel(OwnerEnum.HOUSING_ESTATE.getValue()).setParentId(Long.valueOf(0))));
	}

	/**
	 * 新增或修改组团信息
	 */
	@PostMapping("/group/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改小区信息", notes = "传入ownerDetailInfo")
	public R<Boolean> groupSubmit(@Valid @RequestBody OwnerDetailInfo ownerDetailInfo) {
		return R.status(ownerDetailInfoService.addOwnerDetailInfo(ownerDetailInfo.setLevel(OwnerEnum.GROUP.getValue())));
	}

	/**
	 * 新增或修改楼栋信息
	 */
	@PostMapping("/tower/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改楼栋信息", notes = "传入ownerDetailInfo")
	public R<Boolean> towerSubmit(@Valid @RequestBody OwnerDetailInfo ownerDetailInfo) {
		return R.status(ownerDetailInfoService.addOwnerDetailInfo(ownerDetailInfo.setLevel(OwnerEnum.TOWER_NUM.getValue())));
	}

	/**
	 * 新增或修改单元信息
	 */
	@PostMapping("/apartment/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改单元信息", notes = "传入ownerDetailInfo")
	public R<Boolean> apartmentSubmit(@Valid @RequestBody OwnerDetailInfo ownerDetailInfo) {
		return R.status(ownerDetailInfoService.addOwnerDetailInfo(ownerDetailInfo.setLevel(OwnerEnum.APARTMENT.getValue())));
	}

	/**
	 * 新增或修改房间信息
	 */
	@PostMapping("/room/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改单元信息", notes = "传入ownerDetailInfo")
	public R<Boolean> roomSubmit(@Valid @RequestBody OwnerDetailInfo ownerDetailInfo) {
		if (StringUtils.isBlank(ownerDetailInfo.getName()) && ownerDetailInfo.getName().split("-").length != 4) {
			throw new ServiceException("房号格式不对，（如1栋1单元1层1号房则写为：1-1-1-1）");
		}
		return R.status(ownerDetailInfoService.addOwnerDetailInfo(ownerDetailInfo.setLevel(OwnerEnum.ROOM_NUM.getValue())));
	}

	/**
	 * 自定义分页 辖区分页查询
	 */
	@GetMapping("/housing/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "辖区信息分页", notes = "传入ownerDetailInfo")
	public R<IPage<OwnerDetailInfoVO>> page(OwnerDetailInfoVO ownerDetailInfo, Query query) {
		/*if (!AuthUtil.isAdmin()) {
			return R.data(null);
		}*/
		IPage<OwnerDetailInfoVO> pages = ownerDetailInfoService.housingPage(Condition.getPage(query), ownerDetailInfo);
		return R.data(pages);
	}


	@GetMapping("/getRoomName")
	@ApiOperation(value = "根据房间号模糊查询房间信息", notes = "房间号")
	public R<List<OwnerDetailInfo>> getOwnerInfo(@ApiParam(value = "房间号", required = true) @RequestParam("name") String name) {
		return R.data(ownerDetailInfoService.list(Wrappers.<OwnerDetailInfo>lambdaQuery()
			.eq(OwnerDetailInfo::getLevel, OwnerEnum.ROOM_NUM.getValue())
			.like(OwnerDetailInfo::getName, name)));
	}


	/**
	 * 根据房间号更新业主信息
	 */
	@PostMapping("/updateOwnerByRoomId")
	@ApiOperation(value = "根据房间号更新业主信息")
	public R<Boolean> updateOwnerByRoomId(@Valid @RequestBody OwnerDetailInfo ownerDetailInfo) {
		return R.status(ownerDetailInfoService.updateById(ownerDetailInfo));
	}

	/**
	 * 社区信息-tree型结构
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "社区信息-tree型结构", notes = "")
	public R<List<OwnerDetailInfoTreeVO>> communityTree(@ApiParam(value = "父级ID", required = true) @RequestParam("parentId") Long parentId, @ApiParam(value = "社区id") String ids) {
		return R.data(ownerDetailInfoService.communityTree(parentId, ids));
	}

}
