package com.lecent.park.wrapper;

import com.lecent.park.entity.UserRelation;
import com.lecent.park.vo.UserRelationVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 微信用户亲友信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
public class UserRelationWrapper extends BaseEntityWrapper<UserRelation, UserRelationVO>  {

	public static UserRelationWrapper build() {
		return new UserRelationWrapper();
 	}

	@Override
	public UserRelationVO entityVO(UserRelation userRelation) {
		UserRelationVO userRelationVO = BeanUtil.copy(userRelation, UserRelationVO.class);

		//User createUser = UserCache.getUser(userRelation.getCreateUser());
		//User updateUser = UserCache.getUser(userRelation.getUpdateUser());
		//userRelationVO.setCreateUserName(createUser.getName());
		//userRelationVO.setUpdateUserName(updateUser.getName());

		return userRelationVO;
	}

}
