package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.dto.CardDTO;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardOrder;
import com.lecent.park.entity.CardTempUnpaidOrder;
import com.lecent.park.mapper.CardMapper;
import com.lecent.park.mapper.CardTempUnpaidOrderMapper;
import com.lecent.park.service.ICardService;
import com.lecent.park.service.ICardTempUnpaidOrderService;
import com.lecent.park.service.IUserParklotService;
import com.lecent.park.vo.CardTempUnpaidOrderVO;
import com.lecent.park.vo.CardVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2023/9/19 14:13
 */
@Slf4j
@Service
public class CardTempUnpaidOrderServiceImpl extends BaseServiceImpl<CardTempUnpaidOrderMapper, CardTempUnpaidOrder> implements ICardTempUnpaidOrderService {

	@Lazy
	@Autowired
	private ICardService cardService ;
	@Autowired
	IUserParklotService userParklotService;

	/**
	 * 分页查询列表
	 * @param page
	 * @param cardTempUnpaidOrderVO
	 * @return
	 */
	public IPage<CardTempUnpaidOrderVO> findCardTempUnpaidOrderPage(IPage<CardTempUnpaidOrderVO> page, CardTempUnpaidOrderVO cardTempUnpaidOrderVO){
		List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIds();
		if (parkLotIds.isEmpty()) {
			return page.setRecords(Collections.emptyList());
		}
		cardTempUnpaidOrderVO.setParklotIds(parkLotIds);
		List<CardTempUnpaidOrderVO> datas = this.baseMapper.findCardTempUnpaidOrderPage(page,cardTempUnpaidOrderVO);
		page.setRecords(datas);
		return page;
	}

	@Override
	public List<CardTempUnpaidOrderVO> findCardTempUnpaidOrder(List<Long> parklotIds,Long cardId){
		CardTempUnpaidOrderVO param = new CardTempUnpaidOrderVO();
		param.setParklotIds(parklotIds);
		param.setCardId(cardId);
		List<CardTempUnpaidOrderVO> cardTempUnpaidOrderVOs = this.baseMapper.findCardTempUnpaidOrder(param);
		if(Func.isEmpty(cardTempUnpaidOrderVOs)){
			return null;
		}
		List<CardTempUnpaidOrderVO> datas = new ArrayList<>();
		for(CardTempUnpaidOrderVO item :cardTempUnpaidOrderVOs){
			findCardTempUnpaidOrder(item);
			datas.addAll(item.getItems());
		}
		return datas;

	}
	@Override
	public CardTempUnpaidOrderVO findCardTempUnpaidOrder(Long cardId) {
		CardTempUnpaidOrderVO param = new CardTempUnpaidOrderVO();
//		param.setParklotId(parklotId);
		param.setCardId(cardId);
		List<CardTempUnpaidOrderVO> cardTempUnpaidOrderVOs = this.baseMapper.findCardTempUnpaidOrder(param);
		if(Func.isEmpty(cardTempUnpaidOrderVOs)){
			return null;
		}
		return findCardTempUnpaidOrder(cardTempUnpaidOrderVOs.get(0));
	}

	private CardTempUnpaidOrderVO findCardTempUnpaidOrder(CardTempUnpaidOrderVO cardTempUnpaidOrderVO){
		if(cardTempUnpaidOrderVO !=null){
			BigDecimal totalAmount = cardTempUnpaidOrderVO.getTotalAmount().subtract(cardTempUnpaidOrderVO.getPaidAmount());
			cardTempUnpaidOrderVO.setTotalAmount(totalAmount);
			if(totalAmount.compareTo(BigDecimal.ZERO)>0){
				//查询卡号
				Card card=cardService.getById(cardTempUnpaidOrderVO.getCardId());
				if(card == null){
					throw  new ServiceException("欠缴订单未查询到月卡信息");
				}
				cardTempUnpaidOrderVO.setCardNo(card.getNo());
				getcardTempUnpaidOrderItem(cardTempUnpaidOrderVO);
				return cardTempUnpaidOrderVO;
			}
		}
		return null;
	}


	private void getcardTempUnpaidOrderItem(CardTempUnpaidOrderVO cardTempUnpaidOrderVO){
		String[] arrearsMonths=  cardTempUnpaidOrderVO.getArrearsMonth().split(",");
		String[] paidMonths = null;
		if(Func.isNotBlank(cardTempUnpaidOrderVO.getPaidMonth())){
			paidMonths= cardTempUnpaidOrderVO.getPaidMonth().split(",");
		}
		List<CardTempUnpaidOrderVO>  items =new ArrayList<>();
		boolean b=true;
		String arrearsDate[]=null ;
		String paidDate[]=null ;

		for(String arrearsMonth:arrearsMonths){
			b=true;
			arrearsDate = arrearsMonth.split("至");
			if(Func.isNotEmpty(paidMonths)){
				for (String paidMonth : paidMonths){
					paidDate =   paidMonth.split("至");
					if(arrearsDate[0].equals(paidDate[0])){
						b=false;
						break;
					}
				}
			}
			if(b){
				CardTempUnpaidOrderVO item = Func.copy(cardTempUnpaidOrderVO,CardTempUnpaidOrderVO.class);
				item.setStartDate(DateUtils.parseFormatByS(arrearsDate[0]+" 00:00:00"));
				item.setEndDate(DateUtils.parseFormatByS(arrearsDate[1]+" 23:59:59"));
				int months = DateUtils.betweenMonth(item.getStartDate(),item.getEndDate());
				item.setMonths(months);
				item.setTotalAmount(cardTempUnpaidOrderVO.getUnitPrice().multiply(BigDecimal.valueOf(item.getMonths())));
				items.add(item);
			}

		}
		cardTempUnpaidOrderVO.setItems(items);

	}


	/**
	 * 支付成功回调更新状态
	 * @param orders
	 * @return
	 */
	@Override
	public boolean paySucceedCallBback(List<CardOrder> orders){
		Long id = Long.parseLong(orders.get(0).getMemo());
		CardTempUnpaidOrder cardTempUnpaidOrder =  getById(id);
		if(cardTempUnpaidOrder ==null || cardTempUnpaidOrder.getStatus() ==3){
			throw new ServiceException("数据已处理");
		}
		String paidMonth =cardTempUnpaidOrder.getPaidMonth();
		if(paidMonth == null){
			paidMonth="";
		}
		BigDecimal paidAmount = cardTempUnpaidOrder.getPaidAmount();
		String paidOrderNo =cardTempUnpaidOrder.getPaidOrderNo();
		if(paidOrderNo ==null ){
			paidOrderNo ="";
		}
		for(CardOrder order : orders){
			paidAmount = order.getTotalAmount().add(paidAmount);
			paidMonth = paidMonth+","+DateUtil.formatDate(order.getStartDate())+"至"+ DateUtil.formatDate(order.getEndDate());
			paidOrderNo = paidOrderNo+","+ order.getTradeNo();
		}
		paidMonth =paidMonth.substring(paidMonth.indexOf(",")+1,paidMonth.length());
		paidOrderNo = paidOrderNo.substring(paidOrderNo.indexOf(",")+1,paidOrderNo.length());
		cardTempUnpaidOrder.setPaidOrderNo(paidOrderNo);
		cardTempUnpaidOrder.setPaidMonth(paidMonth);
		cardTempUnpaidOrder.setPaidAmount(paidAmount);
		cardTempUnpaidOrder.setStatus(2);
		if(cardTempUnpaidOrder.getPaidAmount().compareTo(cardTempUnpaidOrder.getTotalAmount())>=0){
			cardTempUnpaidOrder.setStatus(3);
		}
		return  this.updateById(cardTempUnpaidOrder);
	}



}
