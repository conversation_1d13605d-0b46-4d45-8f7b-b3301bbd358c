package com.lecent.park.wrapper;

import com.lecent.park.entity.RefundRecord;
import com.lecent.park.vo.RefundRecordVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 退款记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
public class RefundRecordWrapper extends BaseEntityWrapper<RefundRecord, RefundRecordVO>  {

	public static RefundRecordWrapper build() {
		return new RefundRecordWrapper();
 	}

	@Override
	public RefundRecordVO entityVO(RefundRecord refundRecord) {
		return BeanUtil.copy(refundRecord, RefundRecordVO.class);
	}

}
