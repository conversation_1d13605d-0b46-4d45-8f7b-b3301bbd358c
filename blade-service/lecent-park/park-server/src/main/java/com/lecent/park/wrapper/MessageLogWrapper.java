package com.lecent.park.wrapper;

import com.lecent.park.entity.MessageLog;
import com.lecent.park.vo.MessageLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 用户端消息日志表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public class MessageLogWrapper extends BaseEntityWrapper<MessageLog, MessageLogVO>  {

	public static MessageLogWrapper build() {
		return new MessageLogWrapper();
 	}

	@Override
	public MessageLogVO entityVO(MessageLog messageLog) {
		MessageLogVO messageLogVO = BeanUtil.copy(messageLog, MessageLogVO.class);

		//User createUser = UserCache.getUser(messageLog.getCreateUser());
		//User updateUser = UserCache.getUser(messageLog.getUpdateUser());
		//messageLogVO.setCreateUserName(createUser.getName());
		//messageLogVO.setUpdateUserName(updateUser.getName());

		return messageLogVO;
	}

}
