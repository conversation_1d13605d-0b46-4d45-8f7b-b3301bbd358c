package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.BlackListRemoveLogDTO;
import com.lecent.park.entity.BlackListRemoveLog;
import com.lecent.park.vo.BlackListRemoveLogVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 车辆黑名单 服务类
 *
 * <AUTHOR>
 * @since 2021-07-14
 */
public interface IBlackListRemoveLogService extends BaseService<BlackListRemoveLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param blackListRemoveLogDTO
	 * @return
	 */
	IPage<BlackListRemoveLogVO> selectBlackListRemoveLogPage(IPage<BlackListRemoveLogVO> page, BlackListRemoveLogDTO blackListRemoveLogDTO);

	/**
	 * 黑名單移除日志
	 *
	 * @param id
	 * @return
	 */
	BlackListRemoveLogVO detail(String id);
}
