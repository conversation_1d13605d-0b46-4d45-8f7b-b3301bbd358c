package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.entity.MCoupon;
import com.lecent.park.vo.MCouponVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 优惠券明细表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
public interface IMCouponService extends BaseService<MCoupon> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param mCoupon
	 * @return
	 */
	IPage<MCouponVO> selectMCouponPage(IPage<MCouponVO> page, MCouponVO mCoupon);


	ProjectCost selectDiscountAmount(Long parkingId);

}
