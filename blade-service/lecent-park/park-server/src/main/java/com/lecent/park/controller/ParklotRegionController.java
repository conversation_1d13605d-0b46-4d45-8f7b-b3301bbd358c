package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.ParklotRegion;
import com.lecent.park.service.IParklotRegionService;
import com.lecent.park.vo.ParklotRegionVO;
import com.lecent.park.vo.RemainPlaceNumVO;
import com.lecent.park.wrapper.ParklotRegionWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 车场区域信息表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@RestController
@AllArgsConstructor
@Validated
@RequestMapping("/parklotregion")
@Api(value = "车场区域信息表", tags = "车场区域信息表接口")
public class ParklotRegionController extends BladeController {

	private IParklotRegionService parklotRegionService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入parklotRegion")
	public R<ParklotRegionVO> detail(ParklotRegion parklotRegion) {
		ParklotRegionVO detail = parklotRegionService.detail(Condition.getQueryWrapper(parklotRegion));
		return R.data(detail);
	}

	/**
	 * 根据楼层获取该楼层还可以配置多少个车位
	 */
	@GetMapping("/getRemainPlaceNum")
	@ApiOperation(value = "根据楼层获取该楼层还可以配置多少个车位", notes = "根据楼层获取该楼层还可以配置多少个车位")
	public R<RemainPlaceNumVO> getRemainPlaceNum(Long floorId) {
		RemainPlaceNumVO vo = parklotRegionService.getRemainPlaceNum(floorId);
		return R.data(vo);
	}

	/**
	 * 分页 车场区域信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入parklotRegion")
	public R<IPage<ParklotRegionVO>> list(ParklotRegion parklotRegion, Query query) {
		IPage<ParklotRegion> pages = parklotRegionService.page(Condition.getPage(query),
			Condition.getQueryWrapper(parklotRegion));
		return R.data(ParklotRegionWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 车场区域信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入parklotRegion")
	public R<IPage<ParklotRegionVO>> page(ParklotRegionVO parklotRegion, Query query) {
		if (!SecureUtil.isAdministrator()) {
			parklotRegion.setUserId(SecureUtil.getUserId());
		}
		IPage<ParklotRegionVO> pages = parklotRegionService.selectParklotRegionPage(Condition.getPage(query),
			parklotRegion);
		return R.data(pages);
	}

	/**
	 * 自定义分页 车场区域信息表
	 */
	@GetMapping("/getByParklotId")
	@ApiOperation(value = "分页", notes = "传入parklotRegion")
	public R<List<ParklotRegion>> getByParklotId(Long parklotId) {
		List<ParklotRegion> list = parklotRegionService.getByParklotId(parklotId);
		return R.data(list);
	}

	/**
	 * 新增 车场区域信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入parklotRegion")
	public R save(@Valid @RequestBody ParklotRegion parklotRegion) {
		return R.status(parklotRegionService.save(parklotRegion));
	}

	/**
	 * 修改 车场区域信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入parklotRegion")
	public R update(@Valid @RequestBody ParklotRegion parklotRegion) {
		return R.status(parklotRegionService.updateById(parklotRegion));
	}

	/**
	 * 新增或修改 车场区域信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入parklotRegion")
	public R submit(@Valid @RequestBody ParklotRegion parklotRegion) {
		return R.status(parklotRegionService.submit(parklotRegion));
	}

	/**
	 * 删除 车场区域信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(parklotRegionService.removeRegion(Func.toLongList(ids)));
	}

	/**
	 * 根据楼层获取区域
	 */
	@GetMapping("/getByFloorIds")
	@ApiOperation(value = "根据楼层获取区域", notes = "根据楼层获取区域")
	public R<List<ParklotRegion>> getByFloorIds(String floorId) {
		List<ParklotRegion> list = new ArrayList<>();
		List<Long> floorIds = Func.toLongList(floorId);
		if (Func.isNotEmpty(floorIds)) {
			list = parklotRegionService.list(new QueryWrapper<ParklotRegion>()
				.lambda().in(ParklotRegion::getFloorId, floorIds));
		}
		return R.data(list);
	}


	/**
	 * 车场区域表(客服调用)
	 */
	@GetMapping("/getParkingLotRegionList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = " 车场区域表(客服调用)", notes = "")
	public R<List<ParklotRegion>> getParkingLotRegionList(@RequestParam(required = false) String name) {
		return R.data(this.parklotRegionService.getParkingLotRegionList(name));
	}

}
