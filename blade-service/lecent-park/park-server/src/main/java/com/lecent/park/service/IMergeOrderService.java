package com.lecent.park.service;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.MergeOrderDTO;
import com.lecent.park.dto.RecoverOrderDTO;
import com.lecent.park.en.MergeOrderType;
import com.lecent.park.entity.MergeOrder;
import com.lecent.park.vo.MergeOrderVO;
import com.lecent.park.vo.RecoverOrderStatistics;
import com.lecent.park.vo.RecoverOrderVO;
import org.springblade.common.payment.PayResult;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 临停合并支付订单服务
 *
 * <AUTHOR>
 * @date 2023/03/06
 */
public interface IMergeOrderService extends BaseService<MergeOrder> {


    /**
     * 根据订单编号获取订单
     *
     * @param tradeNo 贸易编号
     * @return {@link MergeOrder}
     */
    MergeOrder getByTradeNo(String tradeNo);

    /**
     * 创建合并支付订单
     *
     * @param type 订单类型
     * @return {@link MergeOrderVO}
     */
    MergeOrderVO createMergeOrder(MergeOrderType type);

    /**
     * 合并支付
     *
     * @param mergeOrderDTO 合并订单
     * @return {@link PayResult}
     */
    PayResult mergePay(MergeOrderDTO mergeOrderDTO);

    /**
     * 合并退款
     *
     * @param mergeOrderDTO 合并订单
     * @return {@link Boolean}
     */
    Boolean mergeRefund(MergeOrderDTO mergeOrderDTO);

    /**
     * 退款详情
     *
     * @param orderIds 订单id
     * @return {@link MergeOrderVO}
     */
    @Slave
    MergeOrderVO refundDetail(List<Long> orderIds);

    /**
     * 删除过期订单
     *
     * @param tradeNo 订单编号
     */
    void removeExpireOrder(String tradeNo);

    /**
     * 追缴订单页
     *
     * @param page         页面
     * @param recoverOrder 追缴订单
     * @return {@link IPage}<{@link RecoverOrderVO}>
     */
    @Slave
    IPage<RecoverOrderVO> pageRecoverOrder(IPage<RecoverOrderVO> page, RecoverOrderDTO recoverOrder);

    /**
     * 收费员列表
     *
     * @return {@link List}<{@link RecoverOrderVO}>
     */
    @Slave
    List<RecoverOrderVO> listTollCollector();

    /**
     * 统计追缴订单
     *
     * @param recoverOrder 追缴订单
     * @return {@link RecoverOrderStatistics}
     */
    @Slave
    RecoverOrderStatistics countRecoverOrder(RecoverOrderDTO recoverOrder);

    /**
     * 导出追缴订单
     *
     * @param response     响应
     * @param recoverOrder 追缴订单
     */
    void exportRecoverOrder(HttpServletResponse response, RecoverOrderDTO recoverOrder);

    /**
     * 获取追缴 排名 和佣金
     *
     * @return {@link RecoverOrderStatistics}
     */
    RecoverOrderStatistics findRecoverOrderRanking();

    /**
     * 按月统计追缴金额
     *
     * @param recoverOrder 追缴订单
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> countRecoverOrderGroupMonth(RecoverOrderDTO recoverOrder);

    /**
     * 支付成功
     *
     * @param tradeNo 订单编号
     * @return {@link Boolean}
     */
    Boolean paySuccess(String tradeNo);

    /**
     * 取消订单
     *
     * @param tradeNo 订单编号
     * @return {@link Boolean}
     */
    Boolean cancelOrder(String tradeNo);

    /**
     * 查询合并订单列表
     *
     * @param page         页
     * @param mergeOrderVO 合并订单 VO
     * @return {@link IPage }<{@link MergeOrderVO }>
     */
    IPage<MergeOrderVO> findMergeOrderList(IPage<MergeOrderVO> page, MergeOrderVO mergeOrderVO);
}
