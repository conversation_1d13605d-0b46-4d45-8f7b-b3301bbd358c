package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.TempParkingSegmentCfg;
import com.lecent.park.vo.TempParkingSegmentCfgVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 临停分段配置表 服务类
 *
 * <AUTHOR>
 * @since 2020-05-30
 */
public interface ITempParkingSegmentCfgService extends BaseService<TempParkingSegmentCfg> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param tempParkingSegmentCfg
	 * @return
	 */
	IPage<TempParkingSegmentCfgVO> selectTempParkingSegmentCfgPage(IPage<TempParkingSegmentCfgVO> page,
																   TempParkingSegmentCfgVO tempParkingSegmentCfg);

}
