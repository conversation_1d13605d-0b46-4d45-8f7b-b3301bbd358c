package com.lecent.park.controller.open.minijumppay.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 小程序支付
 *
 * <AUTHOR>
 */
@Data
@ApiModel("小程序支付")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReqMiniToPay {

	/**
	 * 订单ID
	 */
	@NotNull(message = "订单ID不能为空")
	@ApiModelProperty("订单ID")
	private Long orderId;

	/**
	 * 订单类型
	 */
	@NotNull(message = "订单类型不能为空")
	@ApiModelProperty("订单类型")
	private String type;

	/**
	 * openId
	 */
	@NotNull(message = "openId不能为空")
	@ApiModelProperty("openId")
	private String openId;

	/**
	 * source
	 */
	@NotNull(message = "source不能为空")
	@ApiModelProperty("source")
	private String source;

	/**
	 * 用户ID
	 */
	private String userId;
}
