package com.lecent.park.wrapper;

import com.lecent.park.entity.TempParkingChargeRule;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场计费规则包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public class TempParkingChargeRuleWrapper extends BaseEntityWrapper<TempParkingChargeRule, TempParkingChargeRuleVO>  {

	public static TempParkingChargeRuleWrapper build() {
		return new TempParkingChargeRuleWrapper();
 	}

	@Override
	public TempParkingChargeRuleVO entityVO(TempParkingChargeRule tempParkingChargeRule) {
		TempParkingChargeRuleVO tempParkingChargeRuleVO = BeanUtil.copy(tempParkingChargeRule, TempParkingChargeRuleVO.class);

		//User createUser = UserCache.getUser(tempParkingChargeRule.getCreateUser());
		//User updateUser = UserCache.getUser(tempParkingChargeRule.getUpdateUser());
		//tempParkingChargeRuleVO.setCreateUserName(createUser.getName());
		//tempParkingChargeRuleVO.setUpdateUserName(updateUser.getName());

		return tempParkingChargeRuleVO;
	}

}
