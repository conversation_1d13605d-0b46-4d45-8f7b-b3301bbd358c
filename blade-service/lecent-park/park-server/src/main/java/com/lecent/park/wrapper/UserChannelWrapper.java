package com.lecent.park.wrapper;

import com.lecent.park.entity.UserChannel;
import com.lecent.park.vo.UserChannelVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 公司员工车场资源授权表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public class UserChannelWrapper extends BaseEntityWrapper<UserChannel, UserChannelVO>  {

	public static UserChannelWrapper build() {
		return new UserChannelWrapper();
 	}

	@Override
	public UserChannelVO entityVO(UserChannel userChannel) {
		UserChannelVO userChannelVO = BeanUtil.copy(userChannel, UserChannelVO.class);

		//User createUser = UserCache.getUser(userChannel.getCreateUser());
		//User updateUser = UserCache.getUser(userChannel.getUpdateUser());
		//userChannelVO.setCreateUserName(createUser.getName());
		//userChannelVO.setUpdateUserName(updateUser.getName());

		return userChannelVO;
	}

}
