package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.system.dto.GuardUserMenuDTO;
import org.springblade.system.entity.GuardUserMenu;
import org.springblade.system.vo.GuardUserMenuVO;

/**
 * 岗亭用户菜单关联表 服务类
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
public interface IGuardUserMenuService extends BaseService<GuardUserMenu> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param guardUserMenu
	 * @return
	 */
	IPage<GuardUserMenuVO> selectGuardUserMenuPage(IPage<GuardUserMenuVO> page, GuardUserMenuVO guardUserMenu);


	/**
	 * 配置移动岗亭端用户菜单
	 *
	 * @param guardUserMenuDto
	 * @return
	 */
	boolean setMobileGuardUserMenu(GuardUserMenuDTO guardUserMenuDto);

	/**
	 * 添加岗亭按钮权限
	 * @param userId
	 * @return
	 */
	Boolean addGuardBtn(Long userId);

	/**
	 *获取按钮权限参数
	 * @param userId
	 * @param menuId
	 * @return
	 */
	String getExtendParam(Long userId,Long menuId);
}
