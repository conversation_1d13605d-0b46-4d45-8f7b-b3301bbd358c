package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.CardDTO;
import com.lecent.park.dto.CardDurationDTO;
import com.lecent.park.dto.ChangeDTO;
import com.lecent.park.entity.*;
import com.lecent.park.service.ICardCategoryService;
import com.lecent.park.service.ICardService;
import com.lecent.park.service.IParkingOrderService;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.CardDurationVO;
import com.lecent.park.vo.CardVO;
import com.lecent.park.wrapper.CardWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.common.utils.StrUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 套餐卡信息表 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/card")
@Api(value = "套餐卡信息表", tags = "套餐卡信息表接口")
@Slf4j
@Validated
public class CardController extends BladeController {

	private ICardService cardService;
	private IParkingOrderService parkingOrderService;
	private ICardCategoryService cardCategoryService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入card")
	public R<CardVO> detail(@RequestParam String id) {
		return R.data(cardService.getInfo(id));
	}

	/**
	 * 分页 套餐卡信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入card")
	public R<IPage<CardVO>> list(Card card, Query query) {
		IPage<Card> pages = cardService.page(Condition.getPage(query), Condition.getQueryWrapper(card));
		return R.data(CardWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 套餐卡信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入card")
	public R<IPage<CardVO>> page(CardDTO cardDTO, Query query) {
		if(Func.isBlank(query.getAscs()) && Func.isBlank(query.getDescs())){
			//默认按照时间排序
			query.setDescs("create_time");
		}else{
			query.setAscs(StrUtil.camelToUnderlines(remainingDaysReplace(query.getAscs())));
			query.setDescs(StrUtil.camelToUnderlines(remainingDaysReplace(query.getDescs())));
		}
		IPage<CardVO> pages = cardService.selectCardPage(Condition.getPage(query), cardDTO);
		return R.data(pages);
	}

	/**
	 * 排序字段替换
	 * @param str
	 * @return
	 */
	private String remainingDaysReplace(String str){
		if(Func.isNotBlank(str)){
			str =str.replaceAll("remainingDays","endDate");
		}
		return str;
	}


	/**
	 * 新增 套餐卡信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入card")
	public R save(@Valid @RequestBody CardDTO cardDTO) {
		return cardService.saveCard(cardDTO);
	}

	/**
	 * 续费
	 */
	@PostMapping("/renewalFee")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "续费", notes = "传入card")
	public synchronized R renewalFee(@Valid @RequestBody CardDTO cardDTO) {
		return R.status(cardService.renewalFee(cardDTO));
	}

	/**
	 * 续费
	 */
	@PostMapping("/recovery")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "追缴", notes = "传入card")
	public synchronized R recovery(@RequestBody CardDTO cardDTO) {
		return R.status(cardService.recovery(cardDTO));
	}

	/**
	 * 修改 套餐卡信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入card")

	public R update(@Valid @RequestBody Card card) {
		return R.status(cardService.updateById(card));
	}

	/**
	 * 变更月卡信息
	 */
	@PostMapping("/updateInfo")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入card")
	public R updateInfo(@Valid @RequestBody Card card) {
		return R.status(cardService.updateInfo(card));
	}

	/**
	 * 新增或修改 套餐卡信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入card")

	public R submit(@Valid @RequestBody Card card) {
		return R.status(cardService.saveOrUpdate(card));
	}



	/**
	 * 注销月卡
	 */
	@PostMapping("/logout")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "注销月卡", notes = "传入card")
	public R logout(@Valid @RequestBody CardDTO cardDTO) {
		return R.status(cardService.logout(cardDTO));
	}
	/**
	 * 删除 套餐卡信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cardService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 禁用月卡
	 */
	@PostMapping("/disableCard")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入card")

	public R disableCard(@Valid @RequestBody CardDTO cardDTO) {
		return R.status(cardService.disableCard(cardDTO));
	}


	/**
	 * 启用
	 */
	@GetMapping("/enableCard")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入card")
	public R enableCard(String id) {
		return R.status(cardService.enableCard(id));
	}

	@PostMapping("/updatePlates")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "变更车牌", notes = "传入card")
	public R updatePlates(@Valid @RequestBody CardDTO cardDTO) {
		return R.status(cardService.updatePlates(cardDTO));
	}

	@PostMapping("/updateEndTime")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "变更结束时间", notes = "传入card")
	public R updateEndTime(@Valid @RequestBody CardDTO cardDTO) {
		return R.status(cardService.updateEndTime(cardDTO));
	}

	@PostMapping("/updatePlace")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "变更车位", notes = "传入card")

	public R updatePlace(@Valid @RequestBody CardDTO cardDTO) {
		return R.status(cardService.updatePlace(cardDTO));
	}

	@GetMapping("/searchRoomList")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "搜索房间", notes = "传入roomNamePrefix")
	public R<List<RoomSelect>> searchRoomList(String roomNamePrefix) {
		return R.data(cardService.searchRoomList(roomNamePrefix));
	}

	@GetMapping("/getPlates")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "获取车牌", notes = "获取车牌")
	public R<String> getPlates(String phone, String roomNum) {
		return R.data(cardService.getPlates(phone, roomNum));
	}

	/**
	 * 月卡导出
	 */
	@PostMapping("/card-export")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "月卡导出", notes = "传入cardDTO")
	public void cardExport(CardDTO cardDTO, HttpServletResponse response) {
		cardService.cardExport(cardDTO, response);
	}

	/**
	 * 月卡详情导出
	 */
	@PostMapping("/card-detail-export")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "月卡详情导出", notes = "传入cardDTO")
	public void cardDetailExport(CardDTO cardDTO, HttpServletResponse response) {
		cardService.exportDetailData(cardDTO, response);
	}

	/**
	 * 新开卡时获取车位编号
	 */
	@PostMapping("/getPlaceCodeWhenAddCard")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "新开卡时获取车位编号", notes = "传入parkingPlace")
	public R<List<ParkingPlace>> getPlaceCodeWhenAddCard(@RequestBody ChangeDTO changeDTO) {
		return R.data(cardService.getPlaceCodeWhenAddCard(changeDTO));
	}

	/**
	 * 下载开卡excel模板
	 */
	@GetMapping("/downloadExcel")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "下载开卡excel模板", notes = "下载开卡excel模板")
	public void downloadExcel(HttpServletResponse response,
							  @RequestParam(value = "parklotId") @NotEmpty String parklotId,
							  @RequestParam(value = "categoryId") @NotEmpty String categoryId
							 ) {
		cardService.downloadExcel(response, parklotId, categoryId);
	}

	@GetMapping("/template/excel")
	@ApiOperation(value = "下载月卡批量导入excel模板",notes = "无需参数下载模板")
	public void downloadTemplate(HttpServletResponse response){
		cardService.downloadTemplate(response);
	}

	@PostMapping("/batch/import/card")
	public R batchOpenCard(@RequestParam("dataFile") MultipartFile dataFile,
						   @RequestParam("parklotId") @NotEmpty String parklotId,
						   @RequestParam("categoryId") @NotEmpty String categoryId
						   ){
		return R.data(cardService.batchOpenCard(dataFile,parklotId,categoryId));
	}

	/**
	 * 车场是否可以批量开卡
	 */
	@GetMapping("/canBatchOpenCard")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "车场是否可以批量开卡", notes = "车场是否可以批量开卡")
	public R canBatchOpenCard(
		@RequestParam(value = "parklotId") @NotEmpty String parklotId,
		@RequestParam(value = "categoryId") @NotEmpty String categoryId
							 ) {
		return R.data(cardService.canBatchOpenCard(parklotId, categoryId));
	}

	/**
	 * 批量导入开卡数据
	 */
	@PostMapping("/batchImportPlace")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "批量导入开卡数据", notes = "批量导入开卡数据")
	public R batchImportPlace(@RequestParam(value = "placeExcel", required = false) MultipartFile placeExcel,
							  @RequestParam(value = "parklotId") @NotEmpty String parklotId,
							  @RequestParam(value = "categoryId") @NotEmpty String categoryId
							 ) {
		return R.status(cardService.batchOpenCard(placeExcel, parklotId, categoryId));
	}

	/**
	 * 月卡失效激活
	 */
	@PostMapping("/activate")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "月卡失效激活", notes = "月卡失效激活")
	public R<Boolean> activate(@Valid @RequestBody CardDTO card) {
		log.debug("激活月卡参数 = {}", card);
		return R.status(cardService.activateCard(card));
	}

	@GetMapping("/getTimeDuration")
	@ApiOperation(value = "获取月卡续费开始结束时间", notes = "获取月卡续费开始结束时间")
	public R<CardDurationVO> getTimeDuration(@Valid CardDurationDTO cardDurationDTO) {
		return R.data(cardService.getTimeDuration(cardDurationDTO));
	}


	@GetMapping("/getCarEnterTime")
	@ApiOperation(value = "根据月卡ID获取车辆进场时间", notes = "传入card")
	public R getCarEnterTime(@NotNull Long id) {
		Card card = cardService.getById(id);
		LecentAssert.notNull(card, "未查询到月卡信息 id=" + id);
		ParkingOrder parkingOrder = parkingOrderService.selectPresentParkingByCardId(id, cardService.getFirstParkLotId(card));
		Date enterDate = parkingOrder != null ? DateUtils.getStartTimeOfDay(parkingOrder.getEnterTime()) : null;
		return R.data(enterDate);
	}


	@PostMapping("/stopCard")
	@ApiOperation(value = "月卡暂停", notes = "传入card")
	public R stopCard(@Valid @RequestBody Card card) {
		return R.status(cardService.stopCard(card));
	}


	@PostMapping("/recoverCard")
	@ApiOperation(value = "月卡暂停恢复", notes = "传入card")
	public R recoverCard(@Valid @RequestBody Card card) {
		return R.status(cardService.recoverCard(card));
	}


	@GetMapping("/getCategoryList")
	@ApiOperation(value = "套餐列表", notes = "传入card")
	public R getCategoryList(Long parkLotId,Long cardId) {
		List<CardCategory> list = cardCategoryService.getCategoryList(parkLotId,cardId);
		return R.data(list);
	}


	@PostMapping("/changeCategory")
	@ApiOperation(value = "变更套餐", notes = "传入card")
	public R changeCategory(@RequestParam("cardId") Long cardId, @RequestParam("categoryId") Long categoryId) {
		Boolean b = cardService.changeCategory(cardId, categoryId);
		return R.status(b);
	}


	@PostMapping("/create-qr-code")
	public R<String> createBindNonePlateQrCode(@NotNull(message = "月卡id不能为空") Long cardId) {
		return R.data(cardService.createNoPlateQr(cardId));
	}


}
