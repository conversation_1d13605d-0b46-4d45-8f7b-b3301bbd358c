package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkMerchantCarStatisticsDTO;
import com.lecent.park.dto.ParkMerchantDTO;
import com.lecent.park.entity.MerchantOrder;
import com.lecent.park.entity.ParkMerchant;
import com.lecent.park.entity.Parklot;
import com.lecent.park.vo.CarDetailStatisticsVO;
import com.lecent.park.vo.ParkMerchantVO;
import org.springblade.core.mp.base.BaseService;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 酒店商户表 服务类
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
public interface IParkMerchantService extends BaseService<ParkMerchant> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parkMerchantDto
	 * @return
	 */
	IPage<ParkMerchantVO> selectParkMerchantPage(IPage<ParkMerchantVO> page, ParkMerchantDTO parkMerchantDto);

	/**
	 * 添加酒店商戶
	 *
	 * @param parkMerchantDto
	 * @return
	 */
	boolean addParkMerchant(ParkMerchantDTO parkMerchantDto);

	/**
	 * 修改商户信息
	 * @param parkMerchantDto
	 * @return
	 */
	public boolean submit(ParkMerchantDTO parkMerchantDto);

	/**
	 * 更新商户管理员信息
	 * @param parkMerchantDto
	 * @return
	 */

	public boolean updateMerchantUser(ParkMerchantDTO parkMerchantDto);

	/**
	 * 根据用户id查询商家
	 *
	 * @param userId 用户id
	 * @return
	 */
	ParkMerchant getByUserId(Long userId);

	/**
	 * 注销酒店商户
	 *
	 * @param toLongList
	 * @return
	 */
	boolean customDel(String toLongList);


	/**
	 * 商户报表
	 *
	 * @param page
	 * @param statisticsDTO
	 * @return
	 */
	IPage<MerchantOrder> merchantReportPage(IPage<MerchantOrder> page, ParkMerchantCarStatisticsDTO statisticsDTO);

	/**
	 * 商户报表统计
	 *
	 * @param statisticsDTO
	 * @return
	 */
	CarDetailStatisticsVO merchantReportStatistics(ParkMerchantCarStatisticsDTO statisticsDTO);

	void merchantReportExport(ParkMerchantCarStatisticsDTO statisticsDTO, HttpServletResponse response);

	List<ParkMerchant> getOptions(Integer ruleType);

	List<Parklot> getParkOptions(Integer ruleType);

	/**
	 * 根据商户id获取商户的车场名称列表
	 *
	 * @param merchantId 商户ID
	 * @return 商户车场名称列表（例如：1，2，3，4）
	 */
	String getParkNames(Long merchantId);

	List<Parklot> getParkListById(Long id);

	/**
	 * 根据车场id获取商家
	 *
	 * @param parklotId
	 * @return
	 */
	List<ParkMerchant> getMerchantByParklotId(Long parklotId);

	Boolean updateMerchant(ParkMerchant parkMerchant);

	/**
	 * 查询商户套餐类型
	 * @return
	 */
	List<Integer> getMerchantMealType();
}
