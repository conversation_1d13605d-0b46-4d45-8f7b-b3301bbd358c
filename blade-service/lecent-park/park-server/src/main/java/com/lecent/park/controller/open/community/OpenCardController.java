package com.lecent.park.controller.open.community;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.entity.CardOrder;
import com.lecent.park.service.ICardCategoryService;
import com.lecent.park.service.ICardOrderService;
import com.lecent.park.service.ICardService;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.CardOrderVO;
import com.lecent.park.wrapper.CardOrderWrapper;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 提供给社区的月卡交费功能（乌当紧急验收，开放接口不走openapi校验）
 *
 * <AUTHOR>
 * @date 2021年11月02日
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/open-community/card")
@Validated
@Api(value = "社区月卡续费")
@Slf4j
public class OpenCardController {

	private final ICardService cardService;
	private final ICardOrderService cardOrderService;
	private final ICardCategoryService cardCategoryService;

	/**
	 * 根据车牌查询月卡
	 */
	@GetMapping("/list")
	public R getCardListByPlate(@NotEmpty(message = "车牌不能为空") String plate,
								@NotNull(message = "车场ID不能为空") Long parkId) {
		return R.data(cardService.list(Wrappers.<Card>lambdaQuery()
			.like(Card::getPlates, plate)
			.like(Card::getParklotIds, parkId)));
	}

	/**
	 * 根据车牌查询续费记录
	 */
	@GetMapping("/order/list")
	public R verificationDetail(@NotEmpty(message = "车牌不能为空") String plate,
								@NotNull(message = "车场ID不能为空") Long parkId) {
		return R.data(cardOrderService.getCardOrderListByPlateParkId(plate, parkId));
	}


	/**
	 * 续费
	 */
	@PostMapping("/order/create")
	public R createOrder(@NotNull(message = "车牌不能为空") Long cardId,
						 @NotNull(message = "车牌不能为空") Integer payMonthNum,
						 @NotNull(message = "车牌不能为空") BigDecimal totalAmount) {
		Card card = cardService.getById(cardId);
		CardCategory category = cardCategoryService.getById(card.getCategoryId());

		Date startDate = cardService.initStartDate(card.getEndDate(), category, null);
		startDate = DateUtils.getDateMinOrMaxTime(startDate, true);
		Date endDate = DateUtil.plusMonths(startDate, payMonthNum);

		endDate = DateUtil.minusSeconds(endDate, 1);
		endDate = cardService.initEndDate(endDate, category);
		CardOrderVO cardOrderVO = CardOrderWrapper.build().param2EntityVO(cardId.toString(), payMonthNum, startDate, endDate, totalAmount);
		return R.data(cardOrderService.createOrder(cardOrderVO));
	}


	/**
	 * 月卡支付回调
	 */
	@GetMapping("/pay/success")
	public R payCallback(@NotEmpty(message = "车牌不能为空") String tradeNo) {
		return R.data(cardOrderService.paySuccess(tradeNo));
	}

	/**
	 * 月卡订单详情
	 */
	@GetMapping("/order/info")
	public R orderInfo(@NotEmpty(message = "车牌不能为空") String tradeNo) {
		return R.data(cardOrderService.getOne(Wrappers.<CardOrder>lambdaQuery().eq(CardOrder::getTradeNo, tradeNo)));
	}

	/**
	 * 月卡详情
	 */
	@GetMapping("/info")
	public R CardInfo(@NotNull(message = "月卡ID不能为空") Long cardId) {
		return R.data(cardService.getDetail(cardId.toString()));
	}
}
