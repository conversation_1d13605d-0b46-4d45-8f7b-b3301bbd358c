package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.CardLog;
import com.lecent.park.service.ICardLogService;
import com.lecent.park.vo.CardLogVO;
import com.lecent.park.wrapper.CardLogWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 月卡变更日志表 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/cardlog")
@Api(value = "月卡变更日志表", tags = "月卡变更日志表接口")
public class CardLogController extends BladeController {

	private ICardLogService cardLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cardLog")
	public R<CardLogVO> detail(CardLog cardLog) {
		CardLog detail = cardLogService.getOne(Condition.getQueryWrapper(cardLog));
		return R.data(CardLogWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 月卡变更日志表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cardLog")
	public R<IPage<CardLogVO>> list(CardLog cardLog, Query query) {
		IPage<CardLog> pages = cardLogService.page(Condition.getPage(query), Condition.getQueryWrapper(cardLog));
		return R.data(CardLogWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 月卡变更日志表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入cardLog")
	public R<IPage<CardLogVO>> page(CardLogVO cardLog, Query query) {
		IPage<CardLogVO> pages = cardLogService.selectCardLogPage(Condition.getPage(query), cardLog);
		return R.data(pages);
	}

	/**
	 * 新增 月卡变更日志表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入cardLog")
	public R save(@Valid @RequestBody CardLog cardLog) {
		return R.status(cardLogService.save(cardLog));
	}

	/**
	 * 修改 月卡变更日志表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入cardLog")
	public R update(@Valid @RequestBody CardLog cardLog) {
		return R.status(cardLogService.updateById(cardLog));
	}

	/**
	 * 新增或修改 月卡变更日志表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入cardLog")
	public R submit(@Valid @RequestBody CardLog cardLog) {
		return R.status(cardLogService.saveOrUpdate(cardLog));
	}


	/**
	 * 删除 月卡变更日志表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cardLogService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 获取商家授权车牌的变更日志记录
	 */
	@GetMapping("/getAuthPlateChangeLogList")
	@ApiOperation(value = "获取商家授权车牌的变更日志记录", notes = "传入id")
	public R<List<CardLog>> getAuthPlateChangeLogList(@RequestParam("id") Long cardId) {
		return R.data(cardLogService.getAuthPlateChangeLogList(cardId));
	}


}
