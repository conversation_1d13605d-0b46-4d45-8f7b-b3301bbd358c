package com.lecent.park.controller;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.discount.coupon.service.ICouponService;
import com.lecent.park.dto.CouponDTO;
import com.lecent.park.entity.Coupon;
import com.lecent.park.excel.CouponUseDetailExcel;
import com.lecent.park.vo.CouponVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@RestController
@AllArgsConstructor
@Validated
@RequestMapping("/coupon")
@Api(value = "优惠劵", tags = "优惠劵接口")
public class CouponController extends BladeController {

	private ICouponService couponService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入couponId")
	public R<CouponVO> detail(@NotNull Long couponId) {
		return R.data(couponService.detail(couponId));
	}


	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入coupon")
	public R<IPage<CouponVO>> page(CouponVO coupon, Query query) {
		IPage<CouponVO> pages = couponService.selectCouponPage(Condition.getPage(query), coupon);
		return R.data(pages);
	}


	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入coupon")
	public R<Coupon> submit(@Valid @RequestBody CouponDTO couponDto) {
		return R.data(couponService.customSubmit(couponDto));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		//return R.status(couponService.deleteLogic(Func.toLongList(ids)));
		return R.status(couponService.remove(ids));
	}


	@GetMapping("/coupon-use-detail")
	@Slave
	@ApiOperation(value = "优惠券使用明细", notes = "优惠券使用明细")
	public R<IPage<CouponVO>> couponUseDetail(Query query, CouponVO couponVO) {
		return R.data(couponService.couponUseDetail(Condition.getPage(query), couponVO));
	}

	@Slave
	@PostMapping("/coupon-export")
	@ApiOperation(value = "优惠券明细导出", notes = "优惠券明细导出")
	public void couponExport(CouponVO couponVO, HttpServletResponse response) {
		List<CouponUseDetailExcel> couponUseDetailExcels = couponService.couponUseDetailExport(couponVO, response);
		String fileName = "优惠券明细";
		String sheetName = "优惠券明细表";
		ExcelUtil.export(response, fileName, sheetName, couponUseDetailExcels, CouponUseDetailExcel.class);

	}
}
