package com.lecent.park.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.lecent.park.entity.ReceiptPrintLog;
import com.lecent.park.vo.ReceiptPrintLogVO;
import java.util.Objects;

/**
 * 小票打印记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public class ReceiptPrintLogWrapper extends BaseEntityWrapper<ReceiptPrintLog, ReceiptPrintLogVO>  {

	public static ReceiptPrintLogWrapper build() {
		return new ReceiptPrintLogWrapper();
 	}

	@Override
	public ReceiptPrintLogVO entityVO(ReceiptPrintLog receiptPrintLog) {
		ReceiptPrintLogVO receiptPrintLogVO = Objects.requireNonNull(BeanUtil.copy(receiptPrintLog, ReceiptPrintLogVO.class));

		//User createUser = UserCache.getUser(receiptPrintLog.getCreateUser());
		//User updateUser = UserCache.getUser(receiptPrintLog.getUpdateUser());
		//receiptPrintLogVO.setCreateUserName(createUser.getName());
		//receiptPrintLogVO.setUpdateUserName(updateUser.getName());

		return receiptPrintLogVO;
	}

}
