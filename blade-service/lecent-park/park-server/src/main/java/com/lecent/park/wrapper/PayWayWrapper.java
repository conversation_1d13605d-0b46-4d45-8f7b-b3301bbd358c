package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotPayWay;
import com.lecent.park.vo.ParklotPayWayVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 支付渠道配置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
public class PayWayWrapper extends BaseEntityWrapper<ParklotPayWay, ParklotPayWayVO> {

	public static PayWayWrapper build() {
		return new PayWayWrapper();
 	}

	@Override
	public ParklotPayWayVO entityVO(ParklotPayWay payWay) {
		ParklotPayWayVO payWayVO = BeanUtil.copy(payWay, ParklotPayWayVO.class);

		//User createUser = UserCache.getUser(payWay.getCreateUser());
		//User updateUser = UserCache.getUser(payWay.getUpdateUser());
		//payWayVO.setCreateUserName(createUser.getName());
		//payWayVO.setUpdateUserName(updateUser.getName());

		return payWayVO;
	}

}
