package com.lecent.park.controller.open.baby;

import com.lecent.park.dto.BabyScanDTO;
import com.lecent.park.service.ParkingBabyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.common.payment.PayResult;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 停车宝宝业务
 *
 * <AUTHOR>
 * @since 2022-03-09
 */
@Slf4j
@RestController
@Validated
@AllArgsConstructor
@RequestMapping(OpenApiConstant.OPEN_API + "/baby")
@Api(value = "停车宝宝支付业务", tags = "停车宝宝支付业务")
public class ParkingBabyController extends BladeController {

	private ParkingBabyService parkingBabyService;


	/**
	 * 确认支付
	 */
	@PostMapping("/v1/parking-order/pay")
	@ApiOperation(value = "确认支付")
	public R<PayResult> createOrder(@Valid @RequestBody BabyScanDTO babyScanDTO) {
		return R.data(parkingBabyService.createOrder(babyScanDTO));
	}

	/**
	 * 查询支付结果
	 */
	@GetMapping("/v1/parking-order/pay-result")
	@ApiOperation(value = "查询支付结果")
	public R<PayResult> queryPayResult(@NotEmpty(message = "流水号不能为空") @RequestParam String tradeNo) {
		return R.data(parkingBabyService.queryPayResult(tradeNo));
	}


}
