package com.lecent.park.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.en.invoice.InvoiceConfigType;
import com.lecent.park.entity.UserInvoiceConfig;
import com.lecent.park.mapper.UserInvoiceConfigMapper;
import com.lecent.park.service.IUserInvoiceConfigService;
import com.lecent.park.vo.UserInvoiceConfigVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.Locale;

/**
 * 开发票抬头配置 服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Service
public class UserInvoiceConfigServiceImpl extends BaseServiceImpl<UserInvoiceConfigMapper, UserInvoiceConfig> implements IUserInvoiceConfigService {

	@Override
	public IPage<UserInvoiceConfigVO> selectUserInvoiceConfigPage(IPage<UserInvoiceConfigVO> page, UserInvoiceConfigVO userInvoiceConfig) {
		userInvoiceConfig.setPhone(AuthUtil.getPhone());
		return page.setRecords(baseMapper.selectUserInvoiceConfigPage(page, userInvoiceConfig));
	}

	@Override
	public Boolean saveConfig(UserInvoiceConfig userInvoiceConfig) {

		nameCharm(userInvoiceConfig);

		userInvoiceConfig.setPhone(AuthUtil.getPhone());
		//修改已有的默认抬头
		Boolean defaulted = userInvoiceConfig.getDefaulted();
		if (defaulted != null && defaulted) {
			updateDefaulted(userInvoiceConfig);
		}
		return save(userInvoiceConfig);
	}


	@Override
	public Boolean updateInvoiceConfig(UserInvoiceConfig userInvoiceConfig) {
		//修改已有的默认抬头
		Boolean defaulted = userInvoiceConfig.getDefaulted();
		if (defaulted != null && defaulted) {
			updateDefaulted(userInvoiceConfig);
		}
		//效验抬头
		nameCharm(userInvoiceConfig);

		return updateById(userInvoiceConfig);
	}

	private void nameCharm(UserInvoiceConfig userInvoiceConfig) {
		Integer count = lambdaQuery().eq(UserInvoiceConfig::getTitleName, userInvoiceConfig.getTitleName())
			.eq(UserInvoiceConfig::getPhone, AuthUtil.getPhone())
			.eq(UserInvoiceConfig::getTitleType, userInvoiceConfig.getTitleType()).count();
		if (count > 0) {
			throw new ServiceException("发票抬头名称不能重复");
		}

		if (InvoiceConfigType.ENTERPRISE.getKey().equals(userInvoiceConfig.getTitleType())) {
			Assert.notNull(userInvoiceConfig.getTaxNum(), "企业税号不能为空");

			//去除字符串的全部空格
			String taxNum = userInvoiceConfig.getTaxNum().replace(" ", "");
			// 将字符串大写
			taxNum = taxNum.toUpperCase();
			userInvoiceConfig.setTaxNum(taxNum);
			count = lambdaQuery().eq(UserInvoiceConfig::getTaxNum, userInvoiceConfig.getTaxNum())
				.eq(UserInvoiceConfig::getPhone, AuthUtil.getPhone())
				.eq(UserInvoiceConfig::getTitleType, userInvoiceConfig.getTitleType()).count();
			if (count > 0) {
				throw new ServiceException("税号不能重复");
			}
		}
	}

	private void updateDefaulted(UserInvoiceConfig userInvoiceConfig) {
		if (Func.isEmpty(userInvoiceConfig.getPhone())) {
			throw new ServiceException("未获取到用户电话号码");
		}
		lambdaUpdate().eq(UserInvoiceConfig::getPhone, userInvoiceConfig.getPhone())
			.set(UserInvoiceConfig::getDefaulted, false).update();

	}


}
