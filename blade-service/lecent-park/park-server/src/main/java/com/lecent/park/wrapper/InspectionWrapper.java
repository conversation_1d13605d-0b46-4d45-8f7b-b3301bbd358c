package com.lecent.park.wrapper;

import com.lecent.park.entity.Inspection;
import com.lecent.park.vo.InspectionVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 巡检记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
public class InspectionWrapper extends BaseEntityWrapper<Inspection, InspectionVO>  {

	public static InspectionWrapper build() {
		return new InspectionWrapper();
 	}

	@Override
	public InspectionVO entityVO(Inspection inspection) {
		InspectionVO inspectionVO = BeanUtil.copy(inspection, InspectionVO.class);

		//User createUser = UserCache.getUser(inspection.getCreateUser());
		//User updateUser = UserCache.getUser(inspection.getUpdateUser());
		//inspectionVO.setCreateUserName(createUser.getName());
		//inspectionVO.setUpdateUserName(updateUser.getName());

		return inspectionVO;
	}

}
