package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.OwnerDetailInfoDTO;
import com.lecent.park.entity.OwnerDetailInfo;
import com.lecent.park.vo.OwnerDetailInfoTreeVO;
import com.lecent.park.vo.OwnerDetailInfoVO;
import org.springblade.core.mp.base.BaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 楼层业主信息表 服务类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
public interface IOwnerDetailInfoService extends BaseService<OwnerDetailInfo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param ownerDetailInfo
	 * @return
	 */
	IPage<OwnerDetailInfoVO> selectOwnerDetailInfoPage(IPage<OwnerDetailInfoVO> page, OwnerDetailInfoDTO ownerDetailInfo);

	/**
	 * 自定义分页
	 *
	 * @param ownerDetailInfo
	 * @return
	 */
	List<OwnerDetailInfoVO> selectMyPage(OwnerDetailInfoDTO ownerDetailInfo);

	/**
	 * 添加/更新小区业主信息
	 *
	 * @param ownerDetailInfo
	 * @return
	 */
	boolean addOwnerDetailInfo(OwnerDetailInfo ownerDetailInfo);

	/**
	 * 导出小区信息excel模板
	 *
	 * @param response
	 * @param id
	 */
	void exportExcel(HttpServletResponse response, Long id);

	/**
	 * 批量导入业主信息
	 *
	 * @param ownerExcel
	 * @param id
	 * @param isCreate
	 * @return
	 */
	boolean importExcel(MultipartFile ownerExcel, Long id, String isCreate);

	/**
	 * 社区信息分页查询
	 *
	 * @param page
	 * @param ownerDetailInfo
	 * @return
	 */
	IPage<OwnerDetailInfoVO> housingPage(IPage page, OwnerDetailInfo ownerDetailInfo);

	/**
	 * 递归删除
	 *
	 * @param ids
	 * @return
	 */
	Boolean deleteRecursion(List<Long> ids);

	/**
	 * 社区树
	 *
	 * @return
	 */
	List<OwnerDetailInfoTreeVO> communityTree(Long parentId, String ids);

	/**
	 * 导出业主信息
	 *
	 * @param response
	 * @param ownerDetailInfoDTO
	 */
	void exportExcelOwnerInfo(HttpServletResponse response, OwnerDetailInfoDTO ownerDetailInfoDTO);

	/**
	 * 查询用户名称
	 *
	 * @param createUser
	 * @return
	 */
	String getCreateUserName(Long createUser);

	/**
	 * 获取小区列表
	 *
	 * @return
	 */
	List<OwnerDetailInfo> getCommunityList();
}
