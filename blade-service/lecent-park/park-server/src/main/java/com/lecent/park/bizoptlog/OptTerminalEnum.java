package com.lecent.park.bizoptlog;

/**
 * 操作终端
 *
 * <AUTHOR>
 * @date 2020-06-03 09:52
 */
public enum OptTerminalEnum {

	WEB(0, "物业端"),

	GUARD(1, "岗亭端"),

	APP_GUARD(2, "移动岗亭端");

	Integer value;
	String name;

	OptTerminalEnum(Integer value, String name) {
		this.value = value;
		this.name = name;
	}

	public Integer getValue() {
		return value;
	}

	public String getName() {
		return name;
	}


	public static String getNameByKey(Integer key) {
		String ret = "";
		OptTerminalEnum[] values = values();
		for (OptTerminalEnum optTerminalEnum : values) {
			if (optTerminalEnum.getValue().equals(key)) {
				ret = optTerminalEnum.getName();
			}
		}
		return ret;
	}
}
