package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.dto.FestivalConfigDTO;
import com.lecent.park.entity.ParklotExtentSetting;
import com.lecent.park.mapper.ParklotExtentSettingMapper;
import com.lecent.park.service.IParklotExtentSettingService;
import com.lecent.park.vo.ParklotExtentSettingVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 车场扩展配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
@Service
public class ParklotExtentSettingServiceImpl extends BaseServiceImpl<ParklotExtentSettingMapper,
	ParklotExtentSetting> implements IParklotExtentSettingService {

	@Override
	public IPage<ParklotExtentSettingVO> selectParklotExtentSettingPage(IPage<ParklotExtentSettingVO> page,
																		ParklotExtentSettingVO parklotExtentSetting) {
		return page.setRecords(baseMapper.selectParklotExtentSettingPage(page, parklotExtentSetting));
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public boolean submit(ParklotExtentSetting parklotExtentSetting) {
		Long parklotId = parklotExtentSetting.getParklotId();
		LecentAssert.notNull(parklotId, "车场id不能为空");
		LambdaUpdateWrapper<ParklotExtentSetting> updateCondition =
			Wrappers.<ParklotExtentSetting>update().lambda()
				.set(ParklotExtentSetting::getDisableOverTime,
					parklotExtentSetting.getDisableOverTime())
				.set(ParklotExtentSetting::getOverAllowTime,
					parklotExtentSetting.getOverAllowTime())
				.set(ParklotExtentSetting::getAutoUpload,
					parklotExtentSetting.getAutoUpload())
				.eq(ParklotExtentSetting::getParklotId,
					parklotExtentSetting.getParklotId());
		if (!update(updateCondition)) {
			save(parklotExtentSetting);
		}
		return true;
	}

	@Override
	public ParklotExtentSetting getByParklotId(Long parklotId) {
		return this.getOne(Wrappers.<ParklotExtentSetting>lambdaQuery()
			.eq(ParklotExtentSetting::getParklotId, parklotId));
	}

	@Override
	public void updateSettingWeekend(Long parklotId, Integer isSettingWeekend) {
		ParklotExtentSetting parklotExtentSetting = this.getByParklotId(parklotId);
		if (Func.isEmpty(parklotExtentSetting)) {
			ParklotExtentSetting entity = new ParklotExtentSetting();
			entity.setParklotId(parklotId);
			entity.setIsSettingWeekend(isSettingWeekend);
			this.save(entity);
			return;
		}

		LambdaUpdateWrapper<ParklotExtentSetting> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.set(ParklotExtentSetting::getIsSettingWeekend, isSettingWeekend)
			.eq(ParklotExtentSetting::getId, parklotExtentSetting.getId());
		this.update(updateWrapper);
	}

	@Override
	public boolean submitFestivalInfo(FestivalConfigDTO dto) {
		ParklotExtentSetting parklotExtentSetting = this.getByParklotId(dto.getParklotId());
		if (Func.isEmpty(parklotExtentSetting)) {
			ParklotExtentSetting entity = new ParklotExtentSetting();
			entity.setParklotId(dto.getParklotId());
			entity.setIsSettingWeekend(dto.getIsSettingWeekend());
			entity.setFestivalIds(dto.getFestivalIds());
			this.save(entity);
			return true;
		}

		LambdaUpdateWrapper<ParklotExtentSetting> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.set(ParklotExtentSetting::getIsSettingWeekend, dto.getIsSettingWeekend())
			.set(ParklotExtentSetting::getFestivalIds, dto.getFestivalIds())
			.eq(ParklotExtentSetting::getId, parklotExtentSetting.getId());
		this.update(updateWrapper);
		return true;
	}

	@Override
	public ParklotExtentSetting selectExtentSettingByParklotId(Long parklotId) {
		return this.getOne(Wrappers.<ParklotExtentSetting>query().lambda()
			.eq(ParklotExtentSetting::getParklotId, parklotId));
	}

	@Override
	public boolean isFestival(long parklot,String  dateStr) {
		return baseMapper.isFestival(parklot,dateStr);
	}
}
