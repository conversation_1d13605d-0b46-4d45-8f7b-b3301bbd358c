package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.AnswerRecord;
import com.lecent.park.vo.AnswerRecordVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 客服接听记录  服务类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
public interface IAnswerRecordService extends BaseService<AnswerRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param answerRecord
	 * @return
	 */
	IPage<AnswerRecordVO> selectAnswerRecordPage(IPage<AnswerRecordVO> page, AnswerRecordVO answerRecord);

	/**
	 * 接口记录保存
	 *
	 * @param answerRecord
	 * @return
	 */
	boolean customSave(AnswerRecord answerRecord);
}
