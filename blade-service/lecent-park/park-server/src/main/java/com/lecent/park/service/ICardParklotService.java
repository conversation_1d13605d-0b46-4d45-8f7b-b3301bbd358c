package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.CardParklot;
import com.lecent.park.vo.CardParklotVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 套餐车场表 服务类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface ICardParklotService extends BaseService<CardParklot> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cardParklot
	 * @return
	 */
	IPage<CardParklotVO> selectCardParklotPage(IPage<CardParklotVO> page, CardParklotVO cardParklot);

}
