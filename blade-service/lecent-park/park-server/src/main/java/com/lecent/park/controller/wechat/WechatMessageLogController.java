package com.lecent.park.controller.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.MessageLogDTO;
import com.lecent.park.entity.MessageLog;
import com.lecent.park.service.IMessageLogService;
import com.lecent.park.vo.MessageLogVO;
import com.lecent.park.wrapper.MessageLogWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户端消息日志表 控制器
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wechat/messagelog")
@Api(value = "用户端消息日志表", tags = "用户端消息日志表接口")
public class WechatMessageLogController extends BladeController {

	private IMessageLogService messageLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入messageLog")
	public R<MessageLogVO> detail(MessageLog messageLog) {
		MessageLog detail = messageLogService.getOne(Condition.getQueryWrapper(messageLog));
		return R.data(MessageLogWrapper.build().entityVO(detail));
	}


	/**
	 * 分页获取用户相关的消息
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入messageLog")
	public R<IPage<MessageLogVO>> page(MessageLogDTO messageLogDto, Query query) {
		IPage<MessageLogVO> pages = messageLogService.selectMessageLogPage(Condition.getPage(query), messageLogDto);
		return R.data(pages);
	}

	/**
	 * 新增 用户端消息日志表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入messageLog")
	public R save(@Valid @RequestBody MessageLog messageLog) {
		return R.status(messageLogService.save(messageLog));
	}

	/**
	 * 修改 用户端消息日志表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入messageLog")
	public R update(@Valid @RequestBody MessageLog messageLog) {
		return R.status(messageLogService.updateById(messageLog));
	}

	/**
	 * 新增或修改 用户端消息日志表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入messageLog")
	public R submit(@Valid @RequestBody MessageLog messageLog) {
		return R.status(messageLogService.saveOrUpdate(messageLog));
	}


	/**
	 * 删除 用户端消息日志表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(messageLogService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 设置消息为已读
	 */
	@GetMapping("/setRead")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "设置消息为已读", notes = "传入主键")
	public R setRead(@ApiParam(value = "主键", required = true) @RequestParam String id) {
		return R.status(messageLogService.setRead(id));
	}

}
