package com.lecent.park.controller.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.UserRelation;
import com.lecent.park.service.IUserRelationService;
import com.lecent.park.vo.UserRelationVO;
import com.lecent.park.wrapper.UserRelationWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 微信用户亲友信息 控制器
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wechat/user-relation")
@Api(value = "微信用户亲友信息", tags = "微信用户亲友信息接口")
public class WechatUserRelationController extends BladeController {

	private IUserRelationService userRelationService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入userRelation")
	public R<UserRelationVO> detail(UserRelation userRelation) {
		UserRelation detail = userRelationService.getOne(Condition.getQueryWrapper(userRelation));
		return R.data(UserRelationWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 微信用户亲友信息
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入userRelation")
	public R<IPage<UserRelationVO>> list(UserRelation userRelation, Query query) {
		IPage<UserRelation> pages = userRelationService.page(Condition.getPage(query), Condition.getQueryWrapper(userRelation));
		return R.data(UserRelationWrapper.build().pageVO(pages));
	}


	/**
	 * 获取我的亲友列表
	 */
	@GetMapping("/listMineRelation")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取我的亲友列表", notes = "")
	public R<List<UserRelationVO>> listMineRelation() {
		List<UserRelationVO> list = userRelationService.listMineRelation();
//		List<UserRelation> list = userRelationService.lambdaQuery()
//			.eq(UserRelation::getUserId, SecureUtil.getUserId())
//			.list();
		return R.data(list);
	}


	/**
	 * 自定义分页 微信用户亲友信息
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入userRelation")
	public R<IPage<UserRelationVO>> page(UserRelationVO userRelation, Query query) {
		IPage<UserRelationVO> pages = userRelationService.selectUserRelationPage(Condition.getPage(query), userRelation);
		return R.data(pages);
	}

	/**
	 * 新增 微信用户亲友信息
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入userRelation")
	public R save(@Valid @RequestBody UserRelation userRelation) {

		String phone = userRelation.getPhone();
		if (Func.isNotEmpty(phone) && phone.equals(SecureUtil.getPhone())) {
			throw new ServiceException("好友手机号不能添加自己的");
		}
		return R.status(userRelationService.save(userRelation));
	}

	/**
	 * 修改 微信用户亲友信息
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入userRelation")
	public R update(@Valid @RequestBody UserRelation userRelation) {
		return R.status(userRelationService.updateById(userRelation));
	}

	/**
	 * 新增或修改 微信用户亲友信息
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入userRelation")
	public R submit(@Valid @RequestBody UserRelation userRelation) {

		String phone = userRelation.getPhone();
		if (Func.isNotEmpty(phone) && phone.equals(SecureUtil.getPhone())) {
			throw new ServiceException("好友手机号不能添加自己的");
		}

		userRelation.setUserId(SecureUtil.getUserId());
		return userRelation.getId() == null ? this.save(userRelation) : this.update(userRelation);
	}


	/**
	 * 删除 微信用户亲友信息
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userRelationService.deleteLogic(Func.toLongList(ids)));
	}


}
