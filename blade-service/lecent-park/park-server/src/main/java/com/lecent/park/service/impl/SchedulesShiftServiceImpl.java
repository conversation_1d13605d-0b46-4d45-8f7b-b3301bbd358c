package com.lecent.park.service.impl;

import com.lecent.park.entity.SchedulesShift;
import com.lecent.park.vo.SchedulesShiftVO;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.SchedulesShiftMapper;
import com.lecent.park.service.ISchedulesShiftService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 排班班次表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Service
public class SchedulesShiftServiceImpl extends BaseServiceImpl<SchedulesShiftMapper, SchedulesShift> implements ISchedulesShiftService {

	@Override
	public IPage<SchedulesShiftVO> selectSchedulesShiftPage(IPage<SchedulesShiftVO> page, SchedulesShiftVO schedulesShift) {
		return page.setRecords(baseMapper.selectSchedulesShiftPage(page, schedulesShift));
	}

}
