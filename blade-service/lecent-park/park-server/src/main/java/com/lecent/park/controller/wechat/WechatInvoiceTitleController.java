package com.lecent.park.controller.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.InvoiceTitle;
import com.lecent.park.service.IInvoiceTitleService;
import com.lecent.park.vo.InvoiceTitleVO;
import com.lecent.park.wrapper.InvoiceTitleWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 发票抬头表 控制器
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wechat/invoice-title")
@Api(value = "发票抬头表", tags = "发票抬头表接口")
public class WechatInvoiceTitleController extends BladeController {

	private IInvoiceTitleService invoiceTitleService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入invoiceTitle")
	public R<InvoiceTitleVO> detail(InvoiceTitle invoiceTitle) {
		InvoiceTitle detail = invoiceTitleService.getOne(Condition.getQueryWrapper(invoiceTitle));
		LecentAssert.notNull(detail, "id=[" + invoiceTitle.getId() + "]的发票不存在");
		return R.data(InvoiceTitleWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 发票抬头表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入invoiceTitle")
	public R<IPage<InvoiceTitleVO>> list(InvoiceTitle invoiceTitle, Query query) {
		IPage<InvoiceTitle> pages = invoiceTitleService.page(Condition.getPage(query), Condition.getQueryWrapper(invoiceTitle));
		return R.data(InvoiceTitleWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 发票抬头表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入invoiceTitle")
	public R<IPage<InvoiceTitleVO>> page(InvoiceTitleVO invoiceTitle, Query query) {
		IPage<InvoiceTitleVO> pages = invoiceTitleService.selectInvoiceTitlePage(Condition.getPage(query), invoiceTitle);
		return R.data(pages);
	}

	/**
	 * 新增 发票抬头表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入invoiceTitle")
	public R save(@Valid @RequestBody InvoiceTitle invoiceTitle) {
		invoiceTitle.setUserId(SecureUtil.getUserId());
		return R.status(invoiceTitleService.saveTitle(invoiceTitle));
	}

	/**
	 * 修改 发票抬头表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入invoiceTitle")
	public R update(@Valid @RequestBody InvoiceTitle invoiceTitle) {
		invoiceTitle.setUserId(SecureUtil.getUserId());
		return R.status(invoiceTitleService.customUpdate(invoiceTitle));
	}

	/**
	 * 新增或修改 发票抬头表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入invoiceTitle")
	public R submit(@Valid @RequestBody InvoiceTitle invoiceTitle) {
		invoiceTitle.setUserId(SecureUtil.getUserId());
		return R.status(invoiceTitleService.saveOrUpdate(invoiceTitle));
	}


	/**
	 * 删除 发票抬头表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(invoiceTitleService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 获取用户的发票抬头
	 */
	@GetMapping("/list-user-invoices")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "详情", notes = "传入invoiceTitle")
	public R<List<InvoiceTitleVO>> listUserInvoices() {
		return R.data(invoiceTitleService.listUserInvoices());
	}


}
