package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.InvoiceConfig;
import com.lecent.park.mapper.InvoiceConfigMapper;
import com.lecent.park.service.IInvoiceConfigService;
import com.lecent.park.vo.InvoiceConfigVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

/**
 * 车场-开票相关配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Service
public class InvoiceConfigServiceImpl extends BaseServiceImpl<InvoiceConfigMapper, InvoiceConfig> implements IInvoiceConfigService {

	@Override
	public IPage<InvoiceConfigVO> selectInvoiceConfigPage(IPage<InvoiceConfigVO> page, InvoiceConfigVO invoiceConfig) {
		return page.setRecords(baseMapper.selectInvoiceConfigPage(page, invoiceConfig));
	}

	@Override
	public InvoiceConfig queryConfigByParkLotId(String parkLotId) {
		return baseMapper.queryByParkLotId(Func.toLongList(parkLotId));
	}

}
