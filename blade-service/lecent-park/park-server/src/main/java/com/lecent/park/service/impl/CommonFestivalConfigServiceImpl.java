package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.dto.CommonFestivalConfigDTO;
import com.lecent.park.entity.CommonFestivalConfig;
import com.lecent.park.entity.ParklotWeekendConfig;
import com.lecent.park.mapper.CommonFestivalConfigMapper;
import com.lecent.park.service.*;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.CommonFestivalConfigVO;
import com.lecent.park.vo.FestivalConfigVO;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
@Service
@AllArgsConstructor
public class CommonFestivalConfigServiceImpl extends BaseServiceImpl<CommonFestivalConfigMapper, CommonFestivalConfig> implements ICommonFestivalConfigService {

	private IParklotWeekendConfigService weekendConfigService;
	private IUserParklotService userParklotService;
	private IParklotService parklotService;
	private IParklotExtentSettingService extentSettingService;

	@Override
	public IPage<CommonFestivalConfigVO> selectParklotFestivalConfigPage(IPage<CommonFestivalConfigVO> page, CommonFestivalConfig parklotFestivalConfig) {
		return page.setRecords(baseMapper.selectCommonFestivalConfigPage(page, parklotFestivalConfig));
	}

    @Override
	@Transactional(rollbackFor = Exception.class)
    public boolean submitConfig(CommonFestivalConfigDTO festivalConfigDTO) {

		this.saveConfig(festivalConfigDTO.getFestivalDurations());

		return true;
    }

	@Override
	public FestivalConfigVO getConfigInfo() {
		List<CommonFestivalConfig> list = this.list();
		ParklotWeekendConfig weekendConfig = weekendConfigService.getOne(Wrappers.emptyWrapper());
		FestivalConfigVO festivalConfigVO = new FestivalConfigVO();
		festivalConfigVO.setFestivalDurations(list);
		if (Func.isNotEmpty(weekendConfig)) {
			festivalConfigVO.setIsSelected(weekendConfig.getIsSelected() == 1);
		}
		return festivalConfigVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveConfig(List<CommonFestivalConfig> festivalDurations) {

		this.remove(Wrappers.emptyWrapper());

		festivalDurations.forEach(t-> {
			CommonFestivalConfig commonFestivalConfig = new CommonFestivalConfig();
			commonFestivalConfig.setFestivalName(t.getFestivalName());
			commonFestivalConfig.setStartDate(t.getStartDate());
			commonFestivalConfig.setEndDate(t.getEndDate());
			this.save(commonFestivalConfig);
		});

	}


	@Override
	public List<CommonFestivalConfig> getListByVacationIds(String[] festivalIds) {
		if (festivalIds.length < 1) {
			return null;
		}
		return baseMapper.getListByVacationIds(festivalIds);
	}

	@Override
	public boolean isFestival(Date date) {

		return baseMapper.isFestival(DateUtil.format(date, DateUtils.DATE_FORMAT_8)) > 0;
	}

	/**
	 * 根据日期获取节日
	 *
	 * @param now 日期
	 * @return 节日
	 */
	@Override
	public CommonFestivalConfig getFestivalByDate(Date now) {
		return baseMapper.getFestivalByDate(DateUtil.format(now, DateUtils.DATE_FORMAT_8));
	}

	@Override
	public List<CommonFestivalConfigVO> periodHolidayList(Date startDate, Date exitDate) {
		String startTime = DateUtil.format(startDate, "yyyyMMdd");
		String endTime = DateUtil.format(exitDate, "yyyyMMdd");
		List<CommonFestivalConfigVO> list = baseMapper.periodHolidayList(startTime, endTime);
		if (Func.isNotEmpty(list)){
			list.forEach(l->{
				Date sDate = DateUtil.parse(l.getStartDate(),"yyyyMMdd");
				l.setStartTime(DateUtils.getDateMinOrMaxTime(sDate, true));

				Date eDate = DateUtil.parse(l.getEndDate(),"yyyyMMdd");
				l.setEndTime(DateUtils.getDateMinOrMaxTime(eDate, false));
			});
		}
		return list;
	}

}
