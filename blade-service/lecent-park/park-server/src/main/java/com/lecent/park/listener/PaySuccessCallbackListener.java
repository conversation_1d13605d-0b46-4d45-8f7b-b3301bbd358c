package com.lecent.park.listener;


import com.lecent.park.dto.NoTouchChangeNotify;
import com.lecent.park.entity.PayOrder;
import com.lecent.park.entity.ReservePark;
import com.lecent.park.event.PaymentEvent;
import com.lecent.park.service.*;
import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PAYMENT_EXCHANGE;
import static com.lecent.park.core.mq.rabbitmq.queue.Queues.*;
import static com.lecent.park.core.mq.rabbitmq.routing.ReceiveRoutingKeys.*;
import static org.springframework.amqp.core.ExchangeTypes.TOPIC;

/**
 * 支付成功回调统一监听类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Order
public class PaySuccessCallbackListener {
	@Autowired
	private ClientService clientService;
	@Autowired
	private IReserveParkService reserveParkService;
	@Autowired
	private ICardOrderService cardOrderService;
	@Autowired
	private IParkMerchantParklotService merchantParkLotService;
	@Autowired
	private IRabbitMqLogsService rabbitMqLogsService;
	@Autowired
	private IBUserPlateService userPlateService;


	/**
	 * 监听支付成功事件
	 *
	 * @param event
	 */
	@EventListener
	public void receivedPlateResult(PaymentEvent event) {
		log.info("监听到rabbitMQ支付成功发送的数据为：" + event.toString());
		rabbitMqLogsService.saveReceiveRabbitMqLogs(event.getTradeNo());
		clientService.mqPaySuccessCallback(event.getTradeNo());
	}


	/**
	 * 预约订单和追缴订单处理
	 *
	 * @param payOrder
	 */
	private void payOrderHandler(PayOrder payOrder) {
		List<ReservePark> reserveParks = reserveParkService.getListByWxPayOrderId(payOrder.getPayOrderId());
		if (!reserveParks.isEmpty()) {
			reserveParkService.reserveMoneyPayAfter(payOrder.getPayOrderId());
		}
	}


	/**
	 * 月卡缴费成功回调监听
	 */
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = LECENT_PARK_CARD_PAYMENT_QUEUE, durable = "false"),
		exchange = @Exchange(value = LECENT_PAYMENT_EXCHANGE, type = TOPIC),
		key = LECENT_USER_PARK_CARD_PAYMENT)})
	public void cardCallbackMessage(Message message) {
		try {
			String msgBody = new String(message.getBody(), "UTF-8");
			log("cardCallbackMessage", msgBody);
			PaymentSuccessPayload payload = PaymentSuccessPayload.ofMqMsgV1(msgBody);
			if (payload == null) {
				return;
			}
			cardOrderService.paySuccess(payload.getTradeNo(), payload);
		} catch (Exception e) {
			log.info("业务处理失败：{}", e.getMessage());
		}
	}


	/**
	 * 商户缴费成功回调监听
	 */
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = LECENT_PARK_MERCHANT_PAYMENT_QUEUE, durable = "false"),
		exchange = @Exchange(value = LECENT_PAYMENT_EXCHANGE, type = TOPIC),
		key = LECENT_PARK_MERCHANT_PAYMENT)})
	public void merchantCallbackMessage(Message message) {
		try {
			String msgBody = new String(message.getBody(), "UTF-8");
			log("merchantCallbackMessage", msgBody);
			PaymentSuccessPayload payload = PaymentSuccessPayload.ofMqMsgV1(msgBody);
			if (payload == null) {
				return;
			}
			merchantParkLotService.paySuccess(payload);
		} catch (Exception e) {
			log.info("业务处理失败：{}", e.getMessage());
		}
	}

	/**
	 * 无感支付状态变更回调监听
	 */
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = LECENT_PARK_CHANGE_NO_TOUCH_QUEUE, durable = "false"),
			exchange = @Exchange(value = LECENT_PAYMENT_EXCHANGE, type = TOPIC),
			key = LECENT_PARK_CHANGE_NO_TOUCH_ROUTE)})
	public void changeNoTouchCallbackMessage(Message message) {
		try {
			String jsonStr = new String(message.getBody(), StandardCharsets.UTF_8);
			log("changeNoTouchCallbackMessage", jsonStr);
			NoTouchChangeNotify notify = JsonUtil.parse(jsonStr, NoTouchChangeNotify.class);
			if (notify != null){
				if (notify.bind()) {
					userPlateService.openNoTouch(notify);
				} else if (notify.unbind()) {
					userPlateService.cancelNoTouch(notify);
				} else {
					log.info("changeNoTouchCallbackMessage 车牌[{}] 未知状态[{}]无处理流程", notify.getPlate(), notify.getBindState());
				}
			} else {
				log.info("changeNoTouchCallbackMessage 回调数据为空");
			}
		} catch (Exception e) {
			log.info("changeNoTouchCallbackMessage 业务处理失败：{}", e.getMessage());
		}
	}

	private void log(String name, String messageBody) {
		log.info(
			"\n-----------------------------------------------------------------------------------------------------\n"
				+ "接收{}支付上发消息为：: messageBody = {} \n"
				+ "-----------------------------------------------------------------------------------------------------"
			, name, messageBody
		);
	}

}
