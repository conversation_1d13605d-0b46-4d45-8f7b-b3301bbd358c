package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.CardCategoryDTO;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.vo.CardCategoryVO;
import com.lecent.park.vo.CardRuleVO;
import com.lecent.park.vo.PayMonth;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 套餐卡类型 服务类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface ICardCategoryService extends BaseService<CardCategory> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param
	 * @return
	 */
	IPage<CardCategoryVO> selectCardCategoryPage(IPage<CardCategoryVO> page, CardCategoryDTO cardCategoryDTO);

	List<CardCategory> CardCategoryDict(Long userId);

	CardCategoryVO getDetail(Long id);

	boolean submit(CardCategoryDTO cardCategoryDTO);

	/**
	 * @param parklotId
	 * @return
	 */
	List<CardCategory> getCardRuleByParklotId(Long parklotId);

	boolean removeCardCategory(List<Long> toLongList);

	CardCategory customById(Long categoryId);

	/**
	 * 根据车场id获取月卡套餐
	 *
	 * @param parklotIds 车场id
	 * @return 月卡套餐列表
	 */
	List<CardRuleVO> getByParklotIds(List<Long> parklotIds);

	/**
	 * 校验非空并返回
	 *
	 * @param categoryId 套餐ID
	 * @return
	 */
	CardCategory checkId(Long categoryId);

	/**
	 * 根据车场id获取月卡套餐
	 *
	 * @param parklotId 车场id
	 * @return 月卡套餐列表
	 */
	List<CardRuleVO> getByParklotId(Long parklotId);

	/**
	 * \
	 *
	 * @param parkLotId
	 * @param cardId
	 * @return
	 */
	List<CardCategory> getCategoryList(Long parkLotId, Long cardId);

	/**
	 * 修改
	 *
	 * @param cardCategory
	 * @return
	 */
	boolean unifySaveOrUpdate(CardCategory cardCategory);

	/**
	 * 获取月卡缴费月份价格
	 *
	 * @param cardCategory
	 * @return
	 */
	List<PayMonth> getMonthPrice(CardCategory cardCategory);

	/**
	 * 获取可在线开卡的套餐
	 *
	 * @param parklotId 车场ID
	 * @return 套餐列表
	 */
	List<CardCategory> onlineOpenCategories(Long parklotId);
}
