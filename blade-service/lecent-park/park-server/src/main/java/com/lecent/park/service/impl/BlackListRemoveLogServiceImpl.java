package com.lecent.park.service.impl;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.BlackListRemoveLogDTO;
import com.lecent.park.entity.BlackListRemoveLog;
import com.lecent.park.entity.Parklot;
import com.lecent.park.mapper.BlackListRemoveLogMapper;
import com.lecent.park.service.IBlackListRemoveLogService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IUserParklotService;
import com.lecent.park.vo.BlackListRemoveLogVO;
import com.lecent.park.wrapper.BlackListRemoveLogWrapper;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车辆黑名单 服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-14
 */
@Service
public class BlackListRemoveLogServiceImpl extends
	BaseServiceImpl<BlackListRemoveLogMapper, BlackListRemoveLog> implements IBlackListRemoveLogService {

	@Autowired
	private IParklotService parklotService;

	@Autowired
	private IUserParklotService userParklotService;

	@Slave
	@Override
	public IPage<BlackListRemoveLogVO> selectBlackListRemoveLogPage(IPage<BlackListRemoveLogVO> page, BlackListRemoveLogDTO logDTO) {
		List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIds(logDTO.getParklotId());
		if (CollectionUtil.isEmpty(parkLotIds)) {
			return page.setRecords(Collections.EMPTY_LIST);
		}
		logDTO.setParklotIdList(parkLotIds);
		List<BlackListRemoveLogVO> blackListRemoveLogVOS = baseMapper.selectBlackListRemoveLogPage(page, logDTO);
		blackListRemoveLogVOS.forEach(blackListRemoveLogVO -> {
			blackListRemoveLogVO.setParklotCount(1);
		});
		return page.setRecords(blackListRemoveLogVOS);
	}

	@Override
	public BlackListRemoveLogVO detail(String id) {
		BlackListRemoveLog blackListRemoveLog = getById(id);
		LecentAssert.notNull(blackListRemoveLog, "该移除日志不存在");
		String parklotIds = String.valueOf(blackListRemoveLog.getParklotId());
		List<String> ids = Func.toStrList(parklotIds);
		List<Parklot> parklotList = parklotService.listByIds(ids);
		String parklotNames = parklotList.stream().map(Parklot::getName).collect(Collectors.joining(","));
		BlackListRemoveLogVO blackListRemoveLogVO = BlackListRemoveLogWrapper.build().entityVO(blackListRemoveLog);
		blackListRemoveLogVO.setParklotNames(parklotNames);
		blackListRemoveLogVO.setParklotIdList(ids);
		blackListRemoveLogVO.setCertificateList(Func.toStrList(blackListRemoveLog.getCertificate()));
		return blackListRemoveLogVO;
	}


}
