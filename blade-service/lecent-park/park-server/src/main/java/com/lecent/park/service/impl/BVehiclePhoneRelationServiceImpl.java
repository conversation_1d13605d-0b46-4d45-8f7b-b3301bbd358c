package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lecent.park.common.enums.plate.PlatePhoneSourceEnum;
import com.lecent.park.entity.BUserPlate;
import com.lecent.park.entity.BVehiclePhoneRelation;
import com.lecent.park.vo.vehiclePhoneRelation.*;
import com.lecent.park.mapper.VehiclePhoneRelationMapper;
import com.lecent.park.service.IBVehiclePhoneRelationService;
import com.lecent.pay.core.enums.PayChannel;
import com.leliven.park.domain.vehicle.support.VehicleDomainService;
import com.leliven.vehicle.enums.PlateColor;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车牌手机号采集表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Service
public class BVehiclePhoneRelationServiceImpl extends BaseServiceImpl<VehiclePhoneRelationMapper, BVehiclePhoneRelation> implements IBVehiclePhoneRelationService {

	@Resource
	private VehicleDomainService vehicleDomainService;

	@Override
	public boolean syncPhoneByBUserPlate(BUserPlate userPlate, PlatePhoneSourceEnum phoneSourceEnum) {
		// 手机号或车牌为空不处理
		if (Func.isEmpty(userPlate.getPhone()) || Func.isBlank(userPlate.getPlate()) ) {
			return false;
		}
		BVehiclePhoneRelation platePhone = new BVehiclePhoneRelation();
		platePhone.setPlate(userPlate.getPlate());
		platePhone.setPhone(String.valueOf(userPlate.getPhone()));
		platePhone.setSource(phoneSourceEnum.getValue());
		platePhone.setVehicleId(userPlate.getVehicleId());
		platePhone.setTenantId(userPlate.getTenantId());
		appAddOrUpdate(platePhone);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean appAddOrUpdate(BVehiclePhoneRelation relation) {
		relation.setCreateTime(new Date());
		// 车辆信息 id 补充
		Long vehicleId = Optional.ofNullable(relation.getVehicleId())
			.orElse(getVehicleIdByPlate(relation.getPlate()));
		relation.setVehicleId(vehicleId);
		// 查询是否已经存在
		BVehiclePhoneRelation platePhone = selectByVehicleIdAndPhone(relation);
		if (Objects.nonNull(platePhone)) {
			relation.setId(platePhone.getId());
			relation.setCreateTime(platePhone.getCreateTime());
			relation.setSource(platePhone.getSource());
		}
		return saveOrUpdate(relation);
	}

	private Long getVehicleIdByPlate(String plate) {
		LecentAssert.notBlank(plate, "车牌号不能为空");
		return vehicleDomainService.getOrCreateOrUpdate(plate, PlateColor.UNKNOWN).getId();
	}

	private BVehiclePhoneRelation selectByVehicleIdAndPhone(BVehiclePhoneRelation relation) {
		List<BVehiclePhoneRelation> platePhones = lambdaQuery().eq(BVehiclePhoneRelation::getPhone, relation.getPhone())
			.eq(BVehiclePhoneRelation::getVehicleId, relation.getVehicleId())
			.eq(BVehiclePhoneRelation::getSource, relation.getSource())
			.list();
		return Func.isNotEmpty(platePhones) ? platePhones.get(0) : null;
	}

	@Override
	@TenantIgnore(tenants = TenantConstant.ADMIN_CODE)
	public IPage<CSCBVehiclePhoneRelationVO> bVehiclePhoneRelationPage(IPage<CSCBVehiclePhoneRelationVO> page, VehiclePhoneRelationQueryVO queryVO) {
		List<CSCBVehiclePhoneRelationVO> platePhoneVOS = baseMapper.bVehiclePhoneRelationPage(page, queryVO);
		if (Func.isEmpty(platePhoneVOS)) {
			return page;
		}
		List<Long> vehicleIdList = platePhoneVOS.stream().map(CSCBVehiclePhoneRelationVO::getVehicleId).collect(Collectors.toList());
		queryVO.setVehicleIdList(vehicleIdList);
		List<CSCBVehiclePhoneRelationVO> platePhonePageVOList = selectVehiclePhoneRelationList(queryVO);
		return page.setRecords(platePhonePageVOList);
	}

	/**
	 * 数据列表查询
	 * @param queryVO
	 * @return
	 */
	private List<CSCBVehiclePhoneRelationVO> selectVehiclePhoneRelationList(VehiclePhoneRelationQueryVO queryVO) {
		List<CSCBVehiclePhoneRelationVO> platePhonePageVOList = baseMapper.bVehiclePhoneRelation(queryVO);
		// 返回数据处理
		Map<String, String> unconsciousTypeDictMap = Arrays.stream(PayChannel.values())
			.collect(Collectors.toMap(PayChannel::getName, PayChannel::getTitle));
		for (CSCBVehiclePhoneRelationVO item : platePhonePageVOList) {
			// 无感类型字典映射
			String unconsciousTypes = item.getUnconsciousRelationList().stream()
				.map(ur -> unconsciousTypeDictMap.get(ur.getUnconsciousType()))
				.collect(Collectors.joining(","));
			item.setUnconsciousTypes(unconsciousTypes);
			// 行驶证图片
			String vehicleLicenseUrl = Optional.ofNullable(item.getBUserPlateList())
				.map(k -> k.stream()
					.map(BUserPlate::getVehicleLicenseUrl)
					.filter(Func::isNotBlank).collect(Collectors.joining(",")))
				.orElse(null);
			item.setVehicleLicenseUrl(vehicleLicenseUrl);
			// 行驶证认证状态
			Boolean isAuth = Optional.ofNullable(item.getBUserPlateList())
				.map(k -> k.get(0).getIsAuth()).orElse(Boolean.FALSE);
			item.setIsAuth(isAuth);
			// 用户名
			String userName = Optional.ofNullable(item.getCUserRelList())
				.map(k -> k.stream()
					.map(CUserRel::getName)
					.filter(Func::isNotBlank).distinct().collect(Collectors.joining(",")))
				.orElse(null);
			item.setUserName(userName);
			// openId
			String openId = Optional.ofNullable(item.getCUserThirdRelList())
				.map(k -> k.stream()
					.map(CUserThirdRel::getOpenId)
					.filter(Func::isNotBlank).distinct().collect(Collectors.joining(",")))
				.orElse(null);
			item.setOpenId(openId);
		}
		return platePhonePageVOList;
	}

	@Override
	public CSCBVehiclePhoneRelationVO getDetailByVehicleId(Long vehicleId) {
		VehiclePhoneRelationQueryVO queryVO = new VehiclePhoneRelationQueryVO();
		queryVO.setVehicleIdList(Func.toLongList(String.valueOf(vehicleId)));
		List<CSCBVehiclePhoneRelationVO> relations = selectVehiclePhoneRelationList(queryVO);
		return relations.stream().findFirst()
			.orElseThrow(() -> new ServiceException("未查询到相关车辆数据"));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean cscEdit(UpdateVehiclePhoneVO platePhoneVO) {
		List<UpdateVehiclePhoneVO.UpdatePhoneItem> relationList = Optional.ofNullable(platePhoneVO.getPhoneRelationList())
			.orElse(Collections.emptyList());
		Set<Long> ids = relationList.stream().map(UpdateVehiclePhoneVO.UpdatePhoneItem::getId)
			.collect(Collectors.toSet());
		// 1. 删除数据
		LambdaQueryWrapper<BVehiclePhoneRelation> qw = (LambdaQueryWrapper<BVehiclePhoneRelation>) lambdaQuery()
			.eq(BVehiclePhoneRelation::getVehicleId, platePhoneVO.getVehicleId()).getWrapper();
		List<Long> deleteIds = list(qw).stream().map(BVehiclePhoneRelation::getId)
			.filter(id -> !ids.contains(id)).collect(Collectors.toList());
		removeByIds(deleteIds);
		// 2. 修改数据
		if (Func.isNotEmpty(relationList)) {
			List<BVehiclePhoneRelation> platePhones = Func.copyProperties(relationList, BVehiclePhoneRelation.class);
			updateBatchById(platePhones);
		}
		return Boolean.TRUE;
	}

	@Override
	public String getLastPhoneByVehicleId(Long vehicleId) {
		if (Func.isEmpty(vehicleId)) {
			return null;
		}
		return baseMapper.getLastPhoneByVehicleId(vehicleId);
	}

}
