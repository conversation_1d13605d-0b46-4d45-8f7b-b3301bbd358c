package com.lecent.park.scheduled;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.discount.coupon.service.ICouponActivityService;
import com.lecent.park.en.coupon.SendConditionEnum;
import com.lecent.park.en.coupon.SendDayTypeEnum;
import com.lecent.park.entity.CommonFestivalConfig;
import com.lecent.park.entity.CouponActivity;
import com.lecent.park.entity.UserCoupon;
import com.lecent.park.service.ICommonFestivalConfigService;
import com.lecent.park.discount.coupon.service.IUserCouponService;
import com.lecent.park.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.CUser;
import org.springblade.system.user.feign.ICUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 优惠劵 节假日/周末发放
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponSendScheduled {

	@Autowired
	private ICouponActivityService couponActivityService;
	@Autowired
	private ICommonFestivalConfigService festivalConfigService;
	@Autowired
	private ICUserClient cUserClient;
	@Autowired
	private IUserCouponService userCouponService;

	private static final String WEEKEND = "周末";


	/**
	 * 暂定每天早晨1点触发
	 * 节假日和周末发放优惠劵
	 */
	@Scheduled(cron = "${lecent.park.coupon.send-cron:0 0 1 * * ?}")
	@RedisLock(value = "lecent:park::timedTask:lock:sendCouponOnWeekend")
	public void sendCouponOnWeekend() {

		//是否周末或者节假日，不是则不发
		CommonFestivalConfig festival = festivalConfigService.getFestivalByDate(DateUtil.now());
		boolean todayIsFestival = Func.isNotEmpty(festival);
		boolean todayIsWeekend = DateUtils.isWeekend(DateUtil.now());
		if (!todayIsFestival && !todayIsWeekend) {
			log.info("今天不是周末/节假日，不发劵！");
			return;
		}


		//获取正在进行中的活动，没有则不发
		List<CouponActivity> onLineActivityList = couponActivityService.getOnLineActivityBySendCondition(SendConditionEnum.TIME.getValue(), null);
		if (Func.isEmpty(onLineActivityList)) {
			log.info("周末/节假日没有正在进行的活动");
			return;
		}

		log.info("------------周末/节假日开始发放优惠劵---------------");

		for (CouponActivity a : onLineActivityList) {

			List<Integer> sendDayTypeList = Func.toIntList(a.getDateConditionValue());

			if (sendDayTypeList.size() == 1) {
				//节假日发劵
				if (sendDayTypeList.get(0) == SendDayTypeEnum.FESTIVAL.getValue() && todayIsFestival) {
					couponActivityService.sendCouponToUser(a, this.getCUserList(a), "平台节假日定时发放");
				}
				//周末发劵
				if (sendDayTypeList.get(0) == SendDayTypeEnum.WEEKEND.getValue() && todayIsWeekend) {
					couponActivityService.sendCouponToUser(a, this.getCUserList(a), "平台周末定时发放");
				}
			}

			if (sendDayTypeList.size() == 2) {
				//发劵
				couponActivityService.sendCouponToUser(a, this.getCUserList(a), "平台周末/节假日定时发放");

			}
		}

		log.info("------------周末/节假日发放优惠劵结束---------------");
	}


	/**
	 * 根据活动获取发放的用户以及数量
	 * 1。活动预计发放人数大于等于系统用户，按照系统用户发放，
	 * 2。活动预计发放人数小于系统用户，按照活动人数发放（随机抽取用户发放）
	 *
	 * @param couponActivity 优惠劵活动
	 * @return 用户列表
	 */
	private List<CUser> getCUserList(CouponActivity couponActivity) {

		R<List<CUser>> list = cUserClient.listByTenantId(couponActivity.getTenantId());
		if (list.isSuccess() && Func.isNotEmpty(list.getData())) {

			List<CUser> randomUserList = list.getData();
			//将用户列表随机打乱
			Collections.shuffle(randomUserList);

			//发放用户数小于系统用户数量，以发放人数为准
			Integer userAmount = couponActivity.getProbablyUserAmount();
			if (userAmount < randomUserList.size()) {
				return randomUserList.subList(0, userAmount);
			} else {
				//获取已经发放过优惠劵的用户
				List<Long> sendUserList = userCouponService
					.list(Wrappers.<UserCoupon>lambdaQuery()
						.eq(UserCoupon::getActivityId, couponActivity.getId()))
					.stream()
					.map(UserCoupon::getUserId)
					.collect(Collectors.toList());

				//发放用户中去掉已经发放过优惠劵的用户
				if (Func.isNotEmpty(sendUserList)) {
					randomUserList = randomUserList
						.stream()
						.filter(u ->
							!sendUserList.contains(u.getId())
						).collect(Collectors.toList());
				}


			}

			return randomUserList;
		}
		return Collections.emptyList();
	}

}
