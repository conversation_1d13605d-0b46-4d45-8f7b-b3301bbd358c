package com.lecent.park.service;

import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.ParkingPlaceStatusDO;
import com.lecent.park.vo.ParkingPlaceStatusVO;
import com.leliven.park.domain.order.parking.event.ParkingOrderDomainEvent;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 车位状况服务类
 *
 * <AUTHOR>
 */
public interface ParkingPlaceStatusService extends BaseService<ParkingPlaceStatusDO> {

	/**
	 * 根据车位 id 添加
	 *
	 * @param places 车位
	 */
	void batchCreateByParkingPlace(List<ParkingPlace> places);

	/**
	 * 根据车位 id 查询
	 *
	 * @param placeId 车位id
	 * @return {@link ParkingPlaceStatusDO}
	 */
	ParkingPlaceStatusDO getByPlaceId(Long placeId);

	/**
	 * 处理停车订单事件
	 *
	 * @param event 停车订单领域事件
	 */
	void onParkingOrderEvent(ParkingOrderDomainEvent event);

	/**
	 * 车位长时间无订单
	 *
	 * @return
	 */
    List<ParkingPlaceStatusVO> listBizLongtimeNoOrder();

}
