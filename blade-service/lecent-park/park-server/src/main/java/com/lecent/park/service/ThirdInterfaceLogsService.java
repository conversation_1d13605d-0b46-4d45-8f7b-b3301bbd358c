package com.lecent.park.service;

import com.lecent.park.entity.ThirdInterfaceLogs;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;


/**
 * 第三方接口日志服务
 * <AUTHOR>
 */
public interface ThirdInterfaceLogsService extends BaseService<ThirdInterfaceLogs> {



	/**
	 *
	 * @param name 接口名称
	 * @param type 接口普类型
	 * @param url 接口地址
	 * @param requestParam 请求参数
	 * @param responseData 响应数据
	 * @param requestTime 请求时间
	 * @param responseTime 响应时间
	 */
	 void saveLogs(String name, Integer type, String url, Object requestParam, Object responseData, Date requestTime,Date responseTime);

}
