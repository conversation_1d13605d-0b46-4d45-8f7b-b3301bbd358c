package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.entity.ChangeDataLog;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.mapper.ChangeDataLogMapper;
import com.lecent.park.service.IChangeDataLogService;
import com.lecent.park.vo.ChangeDataLogVO;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 更改数据日志 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
@Service
public class ChangeDataLogServiceImpl extends BaseServiceImpl<ChangeDataLogMapper, ChangeDataLog> implements IChangeDataLogService {




	@Override
	public IPage<ChangeDataLogVO> selectChangeDataLogPage(IPage<ChangeDataLogVO> page, ChangeDataLogVO changeDataLog) {
		return page.setRecords(baseMapper.selectChangeDataLogPage(page, changeDataLog));
	}

	public ChangeDataLogServiceImpl() {
		super();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean modifyPlateInsertLog(ChannelTodo channelTodo, String oldPlate) {
		if (StringUtils.isNotBlank(oldPlate) && oldPlate.equals(channelTodo.getPlate())) {
			return false;
		}
		if (ObjectUtil.isEmpty(channelTodo.getParkingId())) {
			return false;
		}

		ChangeDataLog log = baseMapper.selectByParkingId(channelTodo.getParkingId());
		if (ObjectUtil.isEmpty(log)) {
			log = new ChangeDataLog();
		}

		log.setChangeType(1);
		log.setChannelId(channelTodo.getChannelId());
		log.setOldData(oldPlate);
		log.setNewData(channelTodo.getPlate());
		log.setParklotId(channelTodo.getParklotId());
		log.setCreateUser(SecureUtil.getUserId());
		log.setParkingId(channelTodo.getParkingId());

		return this.saveOrUpdate(log);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean matchingEnterModifyPalteInsertLog(ParkingOrder parking, ChannelTodo channelTodo, ParkingOrderDTO parkingOrderDTO) {

		String modifyPlate = parkingOrderDTO.getPlate();
		String enterPlate = parking.getPlate();
		if (StringUtils.isBlank(modifyPlate)) {
			return false;
		}

		ChangeDataLog log = baseMapper.selectByParkingId(parking.getId());
		if (ObjectUtil.isEmpty(log)) {
			log = new ChangeDataLog();
		}

		log.setChangeType(1);
		log.setNewData(modifyPlate);
		log.setParklotId(channelTodo.getParklotId());
		log.setCreateUser(SecureUtil.getUserId());
		log.setParkingId(parking.getId());

		//出场识别错误
		if (modifyPlate.equals(enterPlate)) {
			log.setChannelId(channelTodo.getChannelId());
			log.setOldData(channelTodo.getPlate());
		}//进场识别错误
		else {
			log.setChannelId(parking.getEnterChannelId());
			log.setOldData(parking.getPlate());
		}

		return this.saveOrUpdate(log);
	}


//	public static void main(String[] args) {
//		System.out.println(1<<31);
//		//System.out.println(Integer.MAX_VAL)
//	}


}


















