package com.lecent.park.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.CustomResultCode;
import com.lecent.park.common.constant.IsEnabledConstants;
import com.lecent.park.common.exception.NoTodoException;
import com.lecent.park.common.exception.RepeatSubmitException;
import com.lecent.park.constant.ChannelConstant;
import com.lecent.park.device.utils.GateActionUtils;
import com.lecent.park.dto.*;
import com.lecent.park.dto.req.ReqParkingPay;
import com.lecent.park.en.PassTemplate;
import com.lecent.park.en.channeltodo.TypeEnum;
import com.lecent.park.entity.Channel;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.Parklot;
import com.lecent.park.event.app.AppRecognitionEvent;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.marketing.service.IPromoteVisitLogService;
import com.lecent.park.platerecognize.OcrRecognizeService;
import com.lecent.park.service.*;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.ParkingOrderVO;
import com.lecent.park.vo.TempParkingOrderVO;
import com.lecent.park.vo.VehicleInfoVO;
import com.lecent.payment.dto.QueryPayListDTO;
import com.lecent.payment.feign.IPaymentClient;
import com.lecent.payment.vo.PayProductWay;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springblade.common.enums.UserAgent;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.system.cache.SysCache;
import org.springblade.system.entity.Tenant;
import org.springblade.system.feign.ISysClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@RestController
@AllArgsConstructor
@Validated
@RequestMapping("/client")
@Api(value = "ClientController（微信扫码进出场接口）", tags = "微信扫码进出场接口")
@Slf4j
public class ClientController {

	private ClientService clientService;
	private ITempParkingOrderService tempParkingOrderService;
	private IParkingOrderService parkingOrderService;
	private ICardService cardService;
	private IParklotService parklotService;
	private ICalculateParkRemainNumberService calculateParkRemainNumberService;
	private ParkingBabyService parkingBabyService;
	private IChannelService channelService;
	private ISysClient sysClient;
	private IBUserPlateService userPlateService;
	private IChannelTodoService todoService;
	private ThirdInterfaceLogsService thirdLogsService;
	private IParklotAuxiliaryService parklotAuxiliarService;
	private IPromoteVisitLogService promoteVisitLogService;
	private OcrRecognizeService ocrRecognizeService;
	private IPaymentClient paymentClient;

	@PostMapping("/getChargingRules")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取收费规则", notes = "获取收费规则")
	public R getChargingRules(@RequestBody ClientTodo clientTodo) {
		return R.data(clientService.getChargingRules(clientTodo));
	}


	/**
	 * 获取有效车牌
	 *
	 * @param channelId 通道ID
	 * @return 有效车牌
	 */
	@GetMapping("/getChannelValidPlate")
	@ApiOperation(value = "getChannelValidPlate", notes = "获取有效车牌")
	public R<String> getChannelValidPlate(Long channelId) {
		System.out.println("============================================================================================");
		System.out.println("客户端请求参数channelId:" + channelId);
		System.out.println("============================================================================================");
		String plate = clientService.getChannelValidPlate(channelId);

		return R.data(plate);
	}

	/**
	 * 通道扫码进场判断车场是否启用无牌车输入车牌
	 *
	 * @param clientTodo 请求参数
	 * @return 是否启用
	 */
	@PostMapping("/clientQueryParklot")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "通道扫码进场判断车场是否启用无牌车输入车牌", notes = "ClientTodo")
	public R clientQueryParklot(@RequestBody ClientTodo clientTodo) {
		try {
			Channel channel = ParkLotCaches.getChannel(clientTodo.getChannelId());
			LecentAssert.notNull(channel, "通道不存在!");
			Parklot parklot = ParkLotCaches.getParkLot(channel.getParklotId());
			LecentAssert.notNull(parklot, "车场不存在!");
			ChannelTodoVO recentlyTodo = todoService.selectCurrentValidTodo(channel.getId(), channel.getTimeouts());
			if (null == recentlyTodo) {
				return R.data(IsEnabledConstants.NO_CAR);
			}
			if (parklot.getIsPlateInput() != null && IsEnabledConstants.ON == parklot.getIsPlateInput()) {
				List<ParkingOrder> parkingOrderList = clientService.getPlateByOpenId(clientTodo, parklot);
				LecentAssert.isEmpty(parkingOrderList, "该微信号当前已绑定在场车!");
			}
			return R.data(parklot.getIsPlateInput() != null ? parklot.getIsPlateInput() : IsEnabledConstants.OFF);
		} catch (Exception e) {
			log.error("ClientController.clientQueryParklot(),扫码进场异常：" + e.getMessage());
			throw new ServiceException(StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "扫码进场异常");
		}
	}

	/**
	 * 通道扫码出场无牌车多个车牌绑定同一个微信
	 *
	 * @param clientTodo 请求参数
	 * @return 一堆停车记录
	 */
	@PostMapping("/getPlateByOpenId")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "通道扫码出场无牌车多个车牌绑定同一个微信", notes = "ClientTodo")
	public R getPlateByOpenId(@RequestBody ClientTodo clientTodo) {

		Map<String, Object> map = new HashMap<>();
		Channel channel = ParkLotCaches.getChannel(clientTodo.getChannelId());
		LecentAssert.notNull(channel, "通道不存在!");
		Parklot parklot = ParkLotCaches.getParkLot(channel.getParklotId());
		LecentAssert.notNull(parklot, "车场不存在!");
		ChannelTodoVO recentlyTodo = todoService.selectCurrentValidTodo(channel.getId(), channel.getTimeouts());
		if (null == recentlyTodo) {
			throw new NoTodoException();
		}
		try {
			List<ParkingOrder> parkingOrderList = clientService.getPlateByOpenId(clientTodo, parklot);
			if (null == parkingOrderList) {
				map.put("isSelect", 0);
			} else {
				map.put("isSelect", parkingOrderList.size());
			}
			map.put("parkingOrderList", parkingOrderList);
			return R.data(map);
		} catch (Exception e) {
			log.error("ClientController.getPlateByOpenId(),扫码出场异常：" + e.getMessage());
			throw new ServiceException("扫码出场异常");
		}


	}

	/**
	 * 通道扫码进出场
	 *
	 * @param clientTodo 请求参数
	 * @return 代办详情
	 */
	//@ApiStatsLog("通道扫码进出场")
	@PostMapping("/clientTrigger")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "通道扫码进出场", notes = "ClientTodo")
	public R clientTrigger(@RequestBody ClientTodo clientTodo) {

		ChannelTodoVO channelTodoVO = clientService.clientTriggerCreateTodo(clientTodo);

		String type = channelTodoVO.getChannelType() == ChannelConstant.CHANNEL_TYPE_ENTER ? "进场" : "出场";

		if (channelTodoVO.getStatus() != ChannelConstant.TODO_STATUS_CREATE && channelTodoVO.getStatus() != ChannelConstant.TODO_STATUS_DISABLE_ENTER) {
			return R.data(200, new ClientR(1, channelTodoVO.getDate()), type + "成功！");

		} else if (TypeEnum.LEAVE_STOP_PAYING.getName().equals(channelTodoVO.getType())) {
			return R.data(200, clientService.resultDataPackage(channelTodoVO), TypeEnum.getMsg(channelTodoVO.getType()));
		} else {
			String msg = TypeEnum.getMsg(channelTodoVO.getType());
			if (Func.isEmpty(msg)) {
				msg = PassTemplate.getMsg(channelTodoVO.getType());
			}
			throw new ServiceException(msg);
		}
	}

	/**
	 * 确认付款
	 */
	@PostMapping("/payment")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "payment", notes = "确认付款")
	public R payment(@RequestBody ReqParkingPay reqParkingPay) {
		return R.data(clientService.payment(reqParkingPay));
	}

	/**
	 * 场内扫码计费(InternalScanningPayment)
	 *
	 * @param clientTodo 请求参数
	 * @return 代办详情
	 */
	@PostMapping("/codeScanning")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "codeScanning", notes = "场内扫码")
	public R codeScanning(@RequestBody ClientTodo clientTodo) {
		ChannelTodoVO channelTodoVO = clientService.codeScanning(clientTodo);

		if (channelTodoVO.getReceiveAmount().compareTo(BigDecimal.ZERO) < 1) {
			throw new ServiceException("未产生费用，请直接出场");
		} else if (TypeEnum.LEAVE_STOP_PAYING.getName().equals(channelTodoVO.getType())) {
			return R.data(200, clientService.resultDataPackage(channelTodoVO), TypeEnum.getMsg(channelTodoVO.getType()));
		} else {
			throw new ServiceException(TypeEnum.getMsg(channelTodoVO.getType()));
		}
	}

	/**
	 * 通道付款是否成功
	 *
	 * @param outTradeNo 交易流水号
	 * @return 付款结果
	 */
	@GetMapping("/isSuccessOrFail")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "isSuccessOrFail", notes = "付款是否成功")
	public R isSuccessOrFail(String outTradeNo) {
		return clientService.payCallback(outTradeNo);
	}

	/**
	 * 场内付款是否成功
	 *
	 * @param outTradeNo 交易流水号
	 * @return 付款结果
	 */
	@GetMapping("/siteSuccessOrFail")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "siteSuccessOrFail", notes = "付款是否成功")
	public R siteSuccessOrFail(String outTradeNo) {
		return clientService.siteSuccessOrFail(outTradeNo);
	}

	/**
	 * 微信停车记录详情
	 *
	 * @param parkingId
	 * @return
	 */
	@GetMapping("/parkingDetail")
	@ApiOperation(value = "微信停车记录详情", notes = "微信停车记录详情")
	public R<ParkingOrderVO> parkingDetail(Long parkingId) {
		return R.data(parkingOrderService.selectParkingDetailOne(parkingId));
	}

	/**
	 * 车牌手机号校验
	 *
	 * @param plate
	 * @param phone
	 * @return
	 */
	@GetMapping("/plateCheck")
	@ApiOperation(value = "queryWeChatPlatePage", notes = "车牌手机号校验")
	public R plateBindingCheck(String plate, String phone) {
		return R.data(userPlateService.plateBindingCheck(plate, phone));
	}

	/**
	 * 摄像头设别触发
	 */
	@PostMapping("/cameraTrigger")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "摄像头识别触发", notes = "摄像头设别触发")
	public R<Map<String, Object>> cameraTrigger(@RequestBody String base64) {
		R<Map<String, Object>> r;
		long startTime = System.currentTimeMillis();
		try {
			String[] split = base64.split("#");

			String jsonStr;
			if (split.length > 1) {
				jsonStr = Base64Util.decode(split[1]);
			} else {
				jsonStr = new String(Base64Util.decodeFromString(base64), "GBK");
			}

			ParkChannelMessageEvent event = JSON.parseObject(jsonStr, ParkChannelMessageEvent.class);
			Map<String, Object> map = todoService.cameraHttpTrigger(event);

			thirdLogsService.saveLogs(event.getPlate() + "摄像头识别车牌调用", 0, "client/cameraTrigger", base64, map, new Date(startTime), new Date());

			log.info("摄像头识别车牌http接口调用plate={},共花费{}毫秒", event.getPlate(), System.currentTimeMillis() - startTime);
			r = R.data(map);

		} catch (RepeatSubmitException e) {
			log.error("ClientController-cameraTrigger", e);
			r = R.fail(CustomResultCode.REPEAT_SUBMIT_ERROR, e.getMessage());
		} catch (Exception e) {
			log.error("ClientController-cameraTrigger", e);
			r = R.fail(e.getMessage());
		}
		log.info("ClientController-cameraTrigger result:{}", Func.toJson(r));
		return r;
	}


	/**
	 * 微信端停车记录分页
	 *
	 * @param parkingOrderDTO 请求参数
	 * @param query           查询条件
	 * @return 记录分页
	 */
	@PostMapping("/clientUserParkingPage")
	@ApiOperation(value = "clientUserParkingPage", notes = "微信端停车记录分页")
	public R<IPage<ParkingOrderVO>> clientUserParkingPage(@RequestBody ParkingOrderDTO parkingOrderDTO, @RequestBody Query query) {
		IPage<ParkingOrderVO> pages = parkingOrderService.selectUserParkingPage(Condition.getPage(query), parkingOrderDTO);
		return R.data(pages);
	}


	/**
	 * 停车缴费详细订单
	 *
	 * @param parkingId 停车订单ID
	 * @return 停车缴费详细订单
	 */
	@PostMapping("/tempParkingOrderDetail")
	@ApiOperation(value = "chargeOrder", notes = "停车缴费详细订单")
	public R<List<TempParkingOrderVO>> tempParkingOrderDetail(@NotNull Long parkingId) {

		try {

			List<TempParkingOrderVO> tempParkingOrderList = tempParkingOrderService.getListByParkingId(parkingId);
			return R.data(tempParkingOrderList);

		} catch (Exception e) {
			log.error("tempParkingOrderDetail: ", e);
			return R.fail("服务器异常!");
		}
	}


	/**
	 * 月卡绑定微信ID
	 *
	 * @param clientTodo 请求参数
	 * @return R<Integer> 绑定结果
	 */
	@PostMapping("/cardBindingOpenId")
	@ApiOperation(value = "cardBindingOpenId", notes = "月卡绑定微信ID")
	public R<Integer> cardBindingOpenId(@RequestBody ClientTodo clientTodo) {
		LecentAssert.notNull(clientTodo.getCardId(), "月卡id不允许空！");
		LecentAssert.notNull(clientTodo.getOpenId(), "微信id不允许空！");

		try {
			boolean b = cardService.cardBindingOpenId(clientTodo);
			if (b) {
				return R.data(1, "绑定成功!");
			} else {
				return R.data(0, "绑定失败!");
			}
		} catch (Exception e) {
			log.error("card binding openId: ", e);
			return R.data(0, "业务异常!");
		}
	}

	/**
	 * 分页
	 */
	@GetMapping("/queryParkingInfoList")
	@ApiOperation(value = "queryParkingInfoList", notes = "停车记录信息")
	public R<List<Map<String, Object>>> queryParkingInfoList(ParkingOrderDTO parkingOrderDTO) {
		List<Map<String, Object>> parkingOrderList = parkingOrderService.queryParkingInfoList(parkingOrderDTO);
		return R.data(parkingOrderList);
	}

	/**
	 * 获取停车场数量（在场数量、剩余数量、总车位数）
	 */
	@GetMapping("/getParkNum")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取停车场数量（在场数量、剩余数量、总车位数）", notes = "获取停车场数量（在场数量、剩余数量、总车位数）")
	public R<Map<String, Integer>> getParkNum(Long id) {
		if (Func.isEmpty(id)) {
			throw new ServiceException("停车场编号不能为空");
		}
		Parklot detail = ParkLotCaches.getParkLot(id);
		if (Func.isEmpty(detail)) {
			throw new ServiceException(StrUtil.format("id为：[{}]的停车场不存在！", id));
		}
		Map<String, Integer> result = new HashedMap<>();
		result.put("totalNum", detail.getTempLotAmount());
		int parkRemainNumber = calculateParkRemainNumberService.getParkRemainNumber(id);
		result.put("remainNum", parkRemainNumber);
		result.put("inParkNum", detail.getTempLotAmount() - parkRemainNumber);
		return R.data(result);
	}

	/**
	 * 停车宝宝被扫支付（用户出示付款码，商家用扫码枪扫描）
	 */
	@PostMapping("/scan")
	@ApiOperation(value = "停车宝宝被扫支付（用户出示付款码，商家用扫码枪扫描）", notes = "停车宝宝被扫支付（用户出示付款码，商家用扫码枪扫描）")
	public R<Boolean> scan(@RequestBody @Valid BabyScanDTO babyScanDto) {
		return R.status(parkingBabyService.scanPayOrder(babyScanDto));
	}

	@GetMapping("/parklotDropListByTenantId")
	@ApiOperation(value = "根据租户获取车场列表", notes = "根据租户获取车场列表")
	public R<List<Parklot>> dropListByTenantId(@NotNull(message = "tenantId不能为空") @RequestParam Long tenantId) {
		//tenantId 从登陆用户中取
		return R.data(parklotService.dropDownList());
	}

	@GetMapping("/getByParklotNo")
	@ApiOperation(value = "根据车场编号获取车场信息", notes = "根据车场编号获取车场信息")
	public R<Parklot> getByParklotNo(@NotEmpty(message = "parklotNo不能为空") @RequestParam String parklotNo) {
		//tenantId 从登陆用户中取
		return R.data(parklotService.getParklotByNo(parklotNo));
	}

	@GetMapping("/getChannelListByParklotId")
	@ApiOperation(value = "根据车场id获取通道列表", notes = "根据车场id获取通道列表")
	public R<List<Channel>> getChannelListByParklotId(@NotNull(message = "tenantId不能为空") @RequestParam Long parklotId) {
		//tenantId 从登陆用户中取
		return R.data(channelService.getListByParklotId(parklotId));
	}

	@GetMapping("/tenantDropList")
	@ApiOperation(value = "租户下拉列表", notes = "租户下拉列表")
	public R<List<Tenant>> tenantDropList() {
		return sysClient.tenantDropList();
	}

	@PostMapping("/pedestrianDetection")
	@ApiOperation(value = "行人检测", notes = "检测到有人")
	public void pedestrianDetection(@RequestBody Map<String, Object> param) {
		String body = param.get("body").toString();
		if (Func.isNoneBlank(body) && body.contains("person_stay_result={alarm_state=1}")) {
			AppRecognitionEvent event = AppRecognitionEvent
				.builder()
				.step(9)
				.parkLotNo("3613")
				.channelNo(0)
				.build();
			//推送短域名到停车宝宝
			GateActionUtils.noticeAppUrl(event);
		}
	}

	/**
	 * 将图片上传阿里云识别车牌
	 */
	@GetMapping("/pictureToPlate")
	@ApiOperation(value = "将图片上传阿里云识别车牌", notes = "将图片上传阿里云识别车牌")
	public R<String> pictureToPlate(@RequestParam("url") String url, @RequestParam(value = "channel", required = false) String channel) {
		VehicleInfoVO vehicleInfoVO= ocrRecognizeService.pictureToPlate(url, channel);
		return R.data(vehicleInfoVO.getPlate());
	}

	/**
	 * 将图片上传阿里云识别车牌
	 */
	@GetMapping("/pictureToVehicleInfoVO")
	@ApiOperation(value = "图片识别车牌", notes = "图片识别车牌")
	public R<VehicleInfoVO> pictureToVehicleInfoVO(@RequestParam("url") String url, @RequestParam(value = "channel", required = false) String channel){
		return R.data(ocrRecognizeService.pictureToPlate(url, channel));
	}



	/**
	 * 自定义分页车场信息表
	 */
	@GetMapping("/getParkLotInfoPage")
	@ApiOperation(value = "分页", notes = "传入parklot")
	public R<IPage<ClientParkLot>> getParkLotInfoPage(ParklotDTO parklot, Query query) {
		IPage<ClientParkLot> pages = parklotService.getParkLotInfoPage(Condition.getPage(query), parklot);
		return R.data(pages);
	}

	@GetMapping("/channelScanSub")
	@ApiOperation(value = "通道扫码关注公众号", notes = "传入通道ID")
	public R<ClientR> channelScanSub(ClientTodo clientTodo) {
		// 判断浏览器类型
		UserAgent userAgent = CommonUtil.getUserAgent();
		if (UserAgent.WX != userAgent) {
			return R.data(new ClientR(false, null), "非微信打开，不需要关注!");
		}

		boolean notBlank = clientTodo.getChannelId() != null && Func.isNotBlank(clientTodo.getOpenId());
		LecentAssert.isTrue(notBlank, "通道ID和OpenId不能为空");

		// 判断待办是否存在
		//ChannelTodoVO recentlyTodo = ParkLotCaches.getChannelTodoByChannelId(clientTodo.getChannelId(), 5);
		ChannelTodoVO recentlyTodo = todoService.selectCurrentValidTodo(clientTodo.getChannelId(), 5);
		LecentAssert.notNull(recentlyTodo, "摄像头未抓拍到车辆，不允许扫码!");

		try {
			// 判断车场是否开启扫码关注
			Boolean parkLotOpenScanSub = parklotAuxiliarService.checkParkLotOpenScanSub(recentlyTodo.getParklotId());
			if (!parkLotOpenScanSub) {
				log.error("车场未开启扫码关注功能");
				return R.data(new ClientR(false, null), "车场未开启扫码关注功能!");
			}

			Boolean isSubscribe = promoteVisitLogService.subscribeStatus(recentlyTodo.getTenantId(), clientTodo.getOpenId());
			if (isSubscribe) {
				return R.data(new ClientR(false, null), "该用户已关注过公众号!");
			}

			recentlyTodo.setOpenId(clientTodo.getOpenId());
			Parklot parklot = parklotService.getById(recentlyTodo.getParklotId());

			if (parklot != null) {
				recentlyTodo.setParklotName(parklot.getName());
			}

			promoteVisitLogService.channelScanSave(recentlyTodo);
			Tenant tenant = SysCache.getTenant(recentlyTodo.getTenantId());
			if (null != tenant) {
				String mqUrl = tenant.getMqUrl();
				return R.data(new ClientR(true, mqUrl), "扫码关注");
			}
		} catch (Exception ex) {
			log.error("ClientController.channelScanSub", ex);
		}
		return R.data(new ClientR(false, null), "获取公众号二维码失败");
	}

	/**
	 * 获取支付方式列表
	 */
	@GetMapping("/getByPayWaysList")
	@ApiOperation(value = "获取支付列表", notes = "获取支付列表")
	public R<List<PayProductWay>> getByPayWaysList(@RequestParam Long businessId) {
		try {
			QueryPayListDTO queryPayListDTO = new QueryPayListDTO();
			queryPayListDTO.setUserAgent(WebUtil.getHeader(WebUtil.USER_AGENT_HEADER));
			queryPayListDTO.setMchId(businessId);
			return paymentClient.getByPayWaysList(queryPayListDTO);
		} catch (Exception e) {
			return R.fail("系统异常：" + e.getMessage());
		}
	}
}



