package com.lecent.park.wrapper;

import com.lecent.park.entity.ChangeDataLog;
import com.lecent.park.vo.ChangeDataLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 更改数据日志包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
public class ChangeDataLogWrapper extends BaseEntityWrapper<ChangeDataLog, ChangeDataLogVO>  {

	public static ChangeDataLogWrapper build() {
		return new ChangeDataLogWrapper();
 	}

	@Override
	public ChangeDataLogVO entityVO(ChangeDataLog changeDataLog) {
		ChangeDataLogVO changeDataLogVO = BeanUtil.copy(changeDataLog, ChangeDataLogVO.class);

		//User createUser = UserCache.getUser(changeDataLog.getCreateUser());
		//User updateUser = UserCache.getUser(changeDataLog.getUpdateUser());
		//changeDataLogVO.setCreateUserName(createUser.getName());
		//changeDataLogVO.setUpdateUserName(updateUser.getName());

		return changeDataLogVO;
	}

}
