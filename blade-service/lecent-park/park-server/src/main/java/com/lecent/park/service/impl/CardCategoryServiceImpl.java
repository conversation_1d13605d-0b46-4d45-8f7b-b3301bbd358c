package com.lecent.park.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.bizoptlog.AroundOpt;
import com.lecent.park.bizoptlog.AroundOptHeader;
import com.lecent.park.bizoptlog.OptTypeEnum;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.MultipleChargeTypeEnum;
import com.lecent.park.dto.CardCategoryDTO;
import com.lecent.park.en.card.PaymentMonth;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.entity.CardCategoryDiscount;
import com.lecent.park.entity.Parklot;
import com.lecent.park.mapper.CardCategoryDiscountMapper;
import com.lecent.park.mapper.CardCategoryMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.CardCategoryVO;
import com.lecent.park.vo.CardRuleVO;
import com.lecent.park.vo.PayMonth;
import com.lecent.park.wrapper.CardCategoryWrapper;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 套餐卡类型 服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Service
public class CardCategoryServiceImpl extends BaseServiceImpl<CardCategoryMapper, CardCategory> implements ICardCategoryService {

	@Autowired
	@Lazy
	private IParklotService parklotService;
	@Autowired
	@Lazy
	private ICardService cardService;
	@Autowired
	private ICardCategoryDiscountService discountService;
	@Resource
	private CardCategoryDiscountMapper discountMapper;
	@Autowired
	private IUserParklotService userParklotService;

	/**
	 * 统一保存或更新
	 *
	 * @param saveBean 更新实体
	 * @return true 成功
	 */
	@Override
	public boolean unifySaveOrUpdate(CardCategory saveBean) {
		if (null != saveBean.getId()) {
			ParkLotCaches.delMonthCardCategory(saveBean.getId());
		}
		return super.saveOrUpdate(saveBean);
	}

	@Override
	public IPage<CardCategoryVO> selectCardCategoryPage(IPage<CardCategoryVO> page, CardCategoryDTO queryBean) {
		List<Long> bindParkLotIds = userParklotService.getCurrentUserBindParkLotIds(queryBean.getParklotId());
		// 无关联车场直接返回空
		if (CollectionUtil.isEmpty(bindParkLotIds)) {
			return page.setRecords(Collections.emptyList());
		}
		queryBean.setParkLotIds(bindParkLotIds);
		return page.setRecords(baseMapper.selectCardCategoryPage(page, queryBean));
	}

	@Override
	public List<CardCategory> CardCategoryDict(Long userId) {
		// 查询用户关联车场
		List<Long> parkLotIds = userParklotService.getParkLotIds(userId);

		if (parkLotIds.isEmpty()) {
			return Collections.emptyList();
		}
		return this.list(Wrappers.<CardCategory>lambdaQuery()
			.in(CardCategory::getParklotId, parkLotIds)
			.eq(CardCategory::getStatus, Constants.ONE));
	}

	@Override
	public CardCategoryVO getDetail(Long id) {
		CardCategory cardCategory = getById(id);
		LecentAssert.notNull(cardCategory, "没有此此月卡套餐");
		List<CardCategoryDiscount> CardCategoryDiscountList = discountService.list(Wrappers.<CardCategoryDiscount>lambdaQuery().eq(CardCategoryDiscount::getCategoryId, cardCategory.getId()));
		CardCategoryVO cardCategoryVO = CardCategoryWrapper.build().entityVO(cardCategory);
		cardCategoryVO.setCardCategoryDiscountList(CardCategoryDiscountList);
		transformTimeStr(cardCategoryVO);
		return cardCategoryVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@AroundOpt(serviceClass = ICardCategoryService.class, optType = OptTypeEnum.EDIT, title = "月卡收费规则",
		headers = {
			@AroundOptHeader(headerClass = IParklotService.class, headerId = "parklotId", headerName = "name"),
			@AroundOptHeader(headerClass = ICardCategoryService.class, headerId = "id", headerName = "name")
		}
	)
	public boolean submit(CardCategoryDTO cardCategoryDTO) {

		//设置默认多次多位类型
		if (StrUtil.isBlank(cardCategoryDTO.getMultipleChargeType())) {
			cardCategoryDTO.setMultipleChargeType(MultipleChargeTypeEnum.FRONT.getValue().toString());
		}

		final CardCategory cardCategory = getOneByName(cardCategoryDTO.getParklotId(), cardCategoryDTO.getName());

		final boolean isExist = Objects.nonNull(cardCategory);

		boolean nameIsRepeat = Optional.ofNullable(cardCategoryDTO.getId())
			.map(t -> isExist && !t.equals(cardCategory.getId()))
			.orElse(isExist);

		LecentAssert.isFalse(nameIsRepeat, "月卡规则名称重复");
		//transformTimeInt(cardCategoryDTO);
//		cardCategoryDTO.setSupportOnlineRenewal("1");

		cardCategoryDTO.setBeginTime(cardCategoryDTO.getCategoryStartTime());
		cardCategoryDTO.setEndTime(cardCategoryDTO.getCategoryEndTime());

		//保存套餐信息
		boolean isSave = Objects.isNull(cardCategoryDTO.getId());
		boolean isCategory;
		if (isSave) {
			isCategory = unifySaveOrUpdate(cardCategoryDTO);
			parklotService.updateCardChargeRule(cardCategoryDTO.getParklotId(), cardCategoryDTO.getId());
		} else {
			isCategory = unifySaveOrUpdate(cardCategoryDTO);
		}

		//获取套餐折扣信息对象
		List<CardCategoryDiscount> cardCategoryDiscountList = cardCategoryDTO.getCardCategoryDiscountList();
		//根据月卡套餐id删除之前折扣信息
		discountMapper.deleteByCategoryId(cardCategoryDTO.getId());
		if (cardCategoryDiscountList == null || cardCategoryDiscountList.isEmpty()) {
			return isCategory;
		}
		cardCategoryDiscountList.forEach(item -> item.setCategoryId(cardCategoryDTO.getId()));
		//保存新的则扣信息
		boolean isDiscount = discountService.saveBatch(cardCategoryDiscountList);
		if (isCategory && isDiscount) {
			return true;
		} else {
			//断言的同时回滚事务
			LecentAssert.alertException("保存失败");
			return false;
		}

	}

	@Override
	public List<CardCategory> getCardRuleByParklotId(Long parklotId) {
		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		LecentAssert.notNull(parklot, "车场已经被删除！");

		List<CardCategory> result = new ArrayList<>();

		String cardTypes = parklot.getCardTypes();
		if (Func.isEmpty(cardTypes)) {
			return result;
		}

		return this.baseMapper.getByCardTypes(cardTypes);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeCardCategory(List<Long> idList) {
		idList.forEach(id -> {
			int cardCount = cardService.getCountByCardRuleId(id);
			if (cardCount > 0) {
				CardCategory cardCategory = this.getById(id);
				throw new ServiceException(String.format("[%s]已绑定月卡, 请先解绑", cardCategory.getName()));
			}

			List<Parklot> parklotList = parklotService.getCountByCardRuleId(id);
			if (Func.isNotEmpty(parklotList)) {
				List<Parklot> updatedParklotList = parklotList.stream()
						.filter(parklot -> {
							List<Long> rules = new ArrayList<>(Func.toLongList(parklot.getCardRules()));
							boolean removed = rules.remove(id);
							if (removed) {
								parklot.setCardRules(Func.join(rules));
							}
							return removed;
						})
						.collect(Collectors.toList());

				if (Func.isNotEmpty(updatedParklotList)) {
					parklotService.updateBatchById(updatedParklotList);
					updatedParklotList.stream()
							.map(Parklot::getId)
							.forEach(ParkLotCaches::delParkLot);
				}
			}

			// 删除月卡套餐缓存
			ParkLotCaches.delMonthCardCategory(id);
		});

		return this.deleteLogic(idList);
	}

	@Override
	public CardCategory customById(Long categoryId) {
		return this.baseMapper.customById(categoryId);
	}

	@Override
	public List<CardRuleVO> getByParklotId(Long parklotId) {

		List<CardCategory> cardCategories = this.list(Wrappers.<CardCategory>lambdaQuery().eq(CardCategory::getParklotId, parklotId));
		if (CollectionUtil.isEmpty(cardCategories)) {
			return Collections.emptyList();
		}

		List<CardRuleVO> result = new ArrayList<>();
		for (CardCategory cardCategory : cardCategories) {
			CardRuleVO cardRuleVO = new CardRuleVO();
			cardRuleVO.setCardRuleId(cardCategory.getId());
			cardRuleVO.setCardRuleName(cardCategory.getName());
			result.add(cardRuleVO);
		}

		return result;

	}

	@Override
	public List<CardRuleVO> getByParklotIds(List<Long> parklotIds) {
		List<CardCategory> cardCategories = this.list(Wrappers.<CardCategory>lambdaQuery().in(CardCategory::getParklotId, parklotIds));
		if (CollectionUtil.isEmpty(cardCategories)) {
			return Collections.emptyList();
		}

		List<CardRuleVO> result = new ArrayList<>();
		for (CardCategory cardCategory : cardCategories) {
			CardRuleVO cardRuleVO = new CardRuleVO();
			cardRuleVO.setCardRuleId(cardCategory.getId());
			cardRuleVO.setUnitPrice(cardCategory.getUnitPrice());
			cardRuleVO.setPeriodType(cardCategory.getPeriodType());
			cardRuleVO.setCardRuleName(cardCategory.getName());
			cardRuleVO.setParklotId(cardCategory.getParklotId());
			cardRuleVO.setParklotName(Optional.ofNullable(ParkLotCaches.getParkLot(cardCategory.getParklotId()))
					.map(Parklot::getName).orElse(""));
			result.add(cardRuleVO);
		}
		return result;
	}

	@Override
	public CardCategory checkId(Long categoryId) {
		CardCategory cardCategory = this.getById(categoryId);
		LecentAssert.notNull(cardCategory, "月卡套餐不存在");
		return cardCategory;
	}


	private CardCategory getOneByName(Long parkLotId, String name) {
		List<CardCategory> list = list(Wrappers.<CardCategory>lambdaQuery()
			.eq(CardCategory::getParklotId, parkLotId)
			.eq(CardCategory::getName, name));

		if (Func.isNotEmpty(list)) {
			return list.get(0);
		}

		return null;
	}

	private void transformTimeInt(CardCategoryDTO cardCategoryDTO) {
		String beginTime = cardCategoryDTO.getBeginTimeStr();
		String endTime = cardCategoryDTO.getEndTimeStr();
		String[] split_b = beginTime.split(":");
		String[] split_e = endTime.split(":");
		Integer beginInt = Integer.parseInt(split_b[0]) * 60 + Integer.parseInt(split_b[1]);
		Integer endInt = Integer.parseInt(split_e[0]) * 60 + Integer.parseInt(split_e[1]);
		cardCategoryDTO.setBeginTime(beginInt);
		cardCategoryDTO.setEndTime(endInt);
	}


	private void transformTimeStr(CardCategoryVO cardCategoryVO) {
		Integer beginTime = cardCategoryVO.getBeginTime();
		Integer endTime = cardCategoryVO.getEndTime();
		Integer b_hour = beginTime / 60;
		Integer b_min = beginTime % 60;
		Integer e_hour = endTime / 60;
		Integer e_min = endTime % 60;
		cardCategoryVO.setBeginTimeStr((b_hour < 10 ? ("0".concat(String.valueOf(b_hour))) : b_hour) + ":" + (b_min < 10 ? ("0".concat(String.valueOf(b_min))) : b_min));
		cardCategoryVO.setEndTimeStr((e_hour < 10 ? ("0".concat(String.valueOf(e_hour))) : e_hour) + ":" + (e_min < 10 ? ("0".concat(String.valueOf(e_min))) : e_min));
	}


	@Override
	public List<CardCategory> getCategoryList(Long parkLotId, Long cardId) {
		List<Long> parkLotIds = new ArrayList<>();
		parkLotIds.add(parkLotId);

		if (cardId != null) {
			Card card = cardService.getById(cardId);
			if (card != null) {
				parkLotIds.addAll(Func.toLongList(card.getParklotIds()));
			}
		}
		return list(Wrappers.<CardCategory>lambdaQuery().in(CardCategory::getParklotId, parkLotIds));
	}

	@Override
	public List<PayMonth> getMonthPrice(CardCategory cardCategory) {
		List<PayMonth> payMonthList = new ArrayList<>();
		Integer minPayMonth = cardCategory.getMinPayMonth();
		for (PaymentMonth pm : PaymentMonth.values()) {
			int duration = pm.getKey();
			if (duration < minPayMonth) {
				continue;
			}
			BigDecimal unitPrice = cardCategory.getUnitPrice();
			CardCategoryDiscount cardCategoryDiscount = discountService.discountByDuration(duration, cardCategory.getId());
			if (cardCategoryDiscount != null) {
				// 存在
				unitPrice = cardCategoryDiscount.getPrice();
			}
			PayMonth build = PayMonth.builder()
				.month(pm.getKey())
				.monthName(pm.getName())
				.unitPrice(unitPrice)
				.totalPrice(unitPrice.multiply(new BigDecimal(pm.getKey())))
				.build();
			payMonthList.add(build);
		}
		return payMonthList;
	}

	@Override
	public List<CardCategory> onlineOpenCategories(Long parklotId) {
		return list(Wrappers.<CardCategory>lambdaQuery().eq(CardCategory::getParklotId, parklotId)
			.eq(CardCategory::getOnlineOpenCardEnabled, Boolean.TRUE)
			.orderByAsc(CardCategory::getOnlineOpenCardSort));
	}
}
