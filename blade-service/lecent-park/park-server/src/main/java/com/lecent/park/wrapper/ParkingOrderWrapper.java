package com.lecent.park.wrapper;

import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.vo.ParkingOrderVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class ParkingOrderWrapper extends BaseEntityWrapper<ParkingOrder, ParkingOrderVO>  {

	public static ParkingOrderWrapper build() {
		return new ParkingOrderWrapper();
 	}

	@Override
	public ParkingOrderVO entityVO(ParkingOrder parking) {
		ParkingOrderVO parkingVO = BeanUtil.copy(parking, ParkingOrderVO.class);

		//User createUser = UserCache.getUser(parking.getCreateUser());
		//User updateUser = UserCache.getUser(parking.getUpdateUser());
		//parkingVO.setCreateUserName(createUser.getName());
		//parkingVO.setUpdateUserName(updateUser.getName());

		return parkingVO;
	}

}
