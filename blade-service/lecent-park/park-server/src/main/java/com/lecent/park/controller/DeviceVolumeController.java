package com.lecent.park.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.DeviceVolume;
import com.lecent.park.service.IDeviceVolumeService;
import com.lecent.park.vo.DeviceVolumeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/devicevolume")
@Api(value = "", tags = "设备音量管理接口")
public class DeviceVolumeController extends BladeController {

	private IDeviceVolumeService deviceVolumeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceVolume")
	public R<DeviceVolume> detail(DeviceVolume deviceVolume) {
		DeviceVolume detail = deviceVolumeService.getOne(Condition.getQueryWrapper(deviceVolume));
		return R.data(detail);
	}


	/**
	 * 单设备新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "单设备新增", notes = "传入deviceVolume")
	public R save(@Valid @RequestBody List<DeviceVolume> deviceVolumes) {
		return R.status(deviceVolumeService.batchSave(deviceVolumes));
	}

	/**
	 * 批量单设备新增
	 */
	@PostMapping("/saveLists")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "批量单设备新增", notes = "传入deviceVolume")
	public R saveLists(@Valid @RequestBody DeviceVolumeVO deviceVolumeVO) {
		return R.status(deviceVolumeService.saveList(deviceVolumeVO));
	}


	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceVolume")
	public R update(@Valid @RequestBody DeviceVolume deviceVolume) {
		return R.status(deviceVolumeService.updateById(deviceVolume));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceVolume")
	public R submit(@Valid @RequestBody DeviceVolume deviceVolume) {
		return R.status(deviceVolumeService.saveOrUpdate(deviceVolume));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceVolumeService.deleteLogic(Func.toLongList(ids)));
	}


}
