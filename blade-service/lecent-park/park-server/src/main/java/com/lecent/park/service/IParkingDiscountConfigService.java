package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.en.coupon.CouponCategory;
import com.lecent.park.entity.ParkingDiscountConfig;
import com.lecent.park.vo.ParkingDiscountConfigVO;
import org.springblade.core.mp.base.BaseService;

import java.math.BigDecimal;

/**
 * 停车优惠配置 服务类
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
public interface IParkingDiscountConfigService extends BaseService<ParkingDiscountConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page                  页
	 * @param parkingDiscountConfig 停车折扣配置
	 * @return {@link IPage}<{@link ParkingDiscountConfigVO}>
	 */
	IPage<ParkingDiscountConfigVO> selectParkingDiscountConfigPage(IPage<ParkingDiscountConfigVO> page, ParkingDiscountConfigVO parkingDiscountConfig);

	/**
	 * 获取优惠配置
	 *
	 * @param totalAmount 总额
	 * @param type        类型
	 * @return {@link ParkingDiscountConfig}
	 */
	ParkingDiscountConfig getDiscountConfig(BigDecimal totalAmount, CouponCategory type);

	/**
	 * 计算优惠
	 *
	 * @param totalAmount 总金额
	 * @param type        类型
	 * @return 优惠金额
	 */
	BigDecimal calculateDiscount(BigDecimal totalAmount, CouponCategory type);

	/**
	 * 计算优惠
	 *
	 * @param totalAmount    总金额
	 * @param discountConfig 优惠配置
	 * @return 优惠金额
	 */
	BigDecimal calculateDiscount(BigDecimal totalAmount, ParkingDiscountConfig discountConfig);
}
