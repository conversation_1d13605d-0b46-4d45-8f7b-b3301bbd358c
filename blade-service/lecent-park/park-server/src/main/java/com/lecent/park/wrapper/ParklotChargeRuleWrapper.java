package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotChargeRule;
import com.lecent.park.vo.ParklotChargeRuleVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 公司员工车场资源授权表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class ParklotChargeRuleWrapper extends BaseEntityWrapper<ParklotChargeRule, ParklotChargeRuleVO>  {

	public static ParklotChargeRuleWrapper build() {
		return new ParklotChargeRuleWrapper();
 	}

	@Override
	public ParklotChargeRuleVO entityVO(ParklotChargeRule parklotChargeRule) {
		ParklotChargeRuleVO parklotChargeRuleVO = BeanUtil.copy(parklotChargeRule, ParklotChargeRuleVO.class);

		//User createUser = UserCache.getUser(parklotChargeRule.getCreateUser());
		//User updateUser = UserCache.getUser(parklotChargeRule.getUpdateUser());
		//parklotChargeRuleVO.setCreateUserName(createUser.getName());
		//parklotChargeRuleVO.setUpdateUserName(updateUser.getName());

		return parklotChargeRuleVO;
	}

}
