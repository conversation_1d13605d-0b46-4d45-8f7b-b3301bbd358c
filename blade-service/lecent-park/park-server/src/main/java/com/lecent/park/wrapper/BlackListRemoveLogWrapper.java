package com.lecent.park.wrapper;

import com.lecent.park.entity.BlackListRemoveLog;
import com.lecent.park.vo.BlackListRemoveLogVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 车辆黑名单包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-07-14
 */
public class BlackListRemoveLogWrapper extends BaseEntityWrapper<BlackListRemoveLog, BlackListRemoveLogVO> {

	public static BlackListRemoveLogWrapper build() {
		return new BlackListRemoveLogWrapper();
 	}

	@Override
	public BlackListRemoveLogVO entityVO(BlackListRemoveLog blackListRemoveLog) {
		BlackListRemoveLogVO blackListRemoveLogVO = Objects.requireNonNull(BeanUtil.copy(blackListRemoveLog, BlackListRemoveLogVO.class));

		//User createUser = UserCache.getUser(blackListRemoveLog.getCreateUser());
		//User updateUser = UserCache.getUser(blackListRemoveLog.getUpdateUser());
		//blackListRemoveLogVO.setCreateUserName(createUser.getName());
		//blackListRemoveLogVO.setUpdateUserName(updateUser.getName());

		return blackListRemoveLogVO;
	}

}
