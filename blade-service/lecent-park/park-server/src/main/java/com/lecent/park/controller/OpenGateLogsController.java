package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.service.IOpenGateLogsService;
import com.lecent.park.vo.OpenGateLogsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开闸日志 控制器
 *
 * <AUTHOR>
 * @since 2021-02-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/openGateLogs")
@Api(value = "开闸日志", tags = "开闸日志接口")
public class OpenGateLogsController extends BladeController {

	private IOpenGateLogsService openGateLogsService;


	/**
	 * 自定义分页 开闸日志
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入openGateLogs")
	public R<IPage<OpenGateLogsVO>> page(OpenGateLogsVO openGateLogs, Query query) {
		IPage<OpenGateLogsVO> pages = openGateLogsService.selectOpenGateLogsPage(Condition.getPage(query), openGateLogs);
		return R.data(pages);
	}


}
