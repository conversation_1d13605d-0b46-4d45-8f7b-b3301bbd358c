package com.lecent.park.service.impl;

import com.lecent.park.entity.ParkingPlaceSub;
import com.lecent.park.vo.ParkingPlaceSubVO;
import com.lecent.park.mapper.ParkingPlaceSubMapper;
import com.lecent.park.service.IParkingPlaceSubService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 车位预约 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Service
public class ParkingPlaceSubServiceImpl extends BaseServiceImpl<ParkingPlaceSubMapper, ParkingPlaceSub> implements IParkingPlaceSubService {

	@Override
	public IPage<ParkingPlaceSubVO> selectParkingPlaceSubPage(IPage<ParkingPlaceSubVO> page, ParkingPlaceSubVO parkingPlaceSub) {
		return page.setRecords(baseMapper.selectParkingPlaceSubPage(page, parkingPlaceSub));
	}

}
