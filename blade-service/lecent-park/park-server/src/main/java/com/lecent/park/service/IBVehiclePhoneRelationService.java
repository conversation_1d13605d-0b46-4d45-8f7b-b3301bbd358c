package com.lecent.park.service;

import com.lecent.park.common.enums.plate.PlatePhoneSourceEnum;
import com.lecent.park.entity.BUserPlate;
import com.lecent.park.entity.BVehiclePhoneRelation;
import com.lecent.park.vo.vehiclePhoneRelation.UpdateVehiclePhoneVO;
import com.lecent.park.vo.vehiclePhoneRelation.CSCBVehiclePhoneRelationVO;
import com.lecent.park.vo.vehiclePhoneRelation.VehiclePhoneRelationQueryVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 车辆手机号采集表 服务类
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface IBVehiclePhoneRelationService extends BaseService<BVehiclePhoneRelation> {

	/**
	 * 从用户绑定车牌同步手机号
	 * @param userPlate
	 * @param phoneSourceEnum
	 * @return
	 */
	boolean syncPhoneByBUserPlate(BUserPlate userPlate, PlatePhoneSourceEnum phoneSourceEnum);

	/**
	 * app 补录或修改
	 * @param BVehiclePhoneRelation
	 * @return
	 */
	boolean appAddOrUpdate(BVehiclePhoneRelation BVehiclePhoneRelation);

	/**
	 * 客服车辆信息管理列表
	 * @param page
	 * @param queryVO
	 * @return
	 */
	IPage<CSCBVehiclePhoneRelationVO> bVehiclePhoneRelationPage(IPage<CSCBVehiclePhoneRelationVO> page, VehiclePhoneRelationQueryVO queryVO);

	CSCBVehiclePhoneRelationVO getDetailByVehicleId(Long userPlatId);

	Boolean cscEdit(UpdateVehiclePhoneVO platePhoneVO);

	/**
	 * 获取车辆最新手机号
	 *
	 * @param vehicleId 车辆id
	 * @return 手机号
	 */
	String getLastPhoneByVehicleId(Long vehicleId);
}
