package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.InvoiceTitle;
import com.lecent.park.vo.InvoiceTitleVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 发票抬头表 服务类
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
public interface IInvoiceTitleService extends BaseService<InvoiceTitle> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param invoiceTitle
	 * @return
	 */
	IPage<InvoiceTitleVO> selectInvoiceTitlePage(IPage<InvoiceTitleVO> page, InvoiceTitleVO invoiceTitle);

	/**
	 * 获取用户的发票抬头
	 *
	 * @return
	 */
	List<InvoiceTitleVO> listUserInvoices();

	/**
	 * 自定义更新
	 *
	 * @param invoiceTitle
	 * @return
	 */
	boolean customUpdate(InvoiceTitle invoiceTitle);

	Boolean saveTitle(InvoiceTitle invoiceTitle);
}
