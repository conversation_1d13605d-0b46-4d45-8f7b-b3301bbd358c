package com.lecent.park.service.impl;

import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.dto.ParkingSpaceCountDetailDTO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.ICardService;
import com.lecent.park.service.IParkingPlaceService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IParklotSpaceCalculateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 车场车位计算业务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParklotSpaceCalculateServiceImpl implements IParklotSpaceCalculateService {

	private static final String REMAIN_NUMBER_CACHE_NAME = "lecent:park:cache:place:remain_number:";
	@Resource
	private IParkingPlaceService parkingPlaceService;
	@Resource
	private IParklotService parklotService;
	@Resource
	private ICardService cardService;

	@Override
	public int getRemainNumber(Long parklotId) {
		return getRemainNumber(ParkLotCaches.existParkLot(parklotId));
	}

	@Override
	public int getRemainNumber(Parklot parklot) {
		return Math.max(parklot.getTempLotAmount() - getUsedNumber(parklot), 0);
	}

	/**
	 * 获取路边停车  车位数量
	 * @param parklotId
	 * @param status 0 暂停 1 启用  null 全部
	 * @return remainNumber 剩余车位数量 totalParkingNum 总车位数量   usedNumber 占用车位数量
	 */
	@Override
	public Map<String, Integer> getRoadPlace(Long parklotId, Integer status) {
		int remainNumber;
		int totalParkingNum;
		if (status == null) {
			totalParkingNum = parkingPlaceService.getListByParklotId(parklotId);
		} else {
			totalParkingNum = parkingPlaceService.getListByParklotId(parklotId, status);
		}
		int usedNumber = this.parkingPlaceService.usedNumber(parklotId);
		remainNumber = totalParkingNum - usedNumber;
		Map<String, Integer> map = new HashMap<>(3);
		map.put("remainNumber", remainNumber);
		map.put("totalParkingNum", totalParkingNum);
		map.put("usedNumber", usedNumber);
		return map;
	}

	@Override
	public int getUsedNumber(Parklot parklot) {
		if (parklot.isRoadSide()) {
			return this.parkingPlaceService.usedNumber(parklot.getId());
		}

		return parklotService.spaceUsedNumber(parklot.getId());
	}

	@Override
	public int getReservedNumber(Long id) {
		return cardService.getReservedNumber(id);
	}

	/**
	 * 查询指定车场的剩余车位数和占用车位数
	 * @param parklot 车场实体
	 * @return ParkingSpaceCountDetailDTO，包含 remainNumber（剩余车位数）、usedNumber（占用车位数）
	 */
	@Override
	public ParkingSpaceCountDetailDTO getParkingSpaceCountDetail(Parklot parklot) {
		int usedNumber = getUsedNumber(parklot);
		int remainNumber = Math.max(parklot.getTempLotAmount() - usedNumber, 0);
		ParkingSpaceCountDetailDTO dto = new ParkingSpaceCountDetailDTO();
		dto.setRemainNumber(remainNumber);
		dto.setUsedNumber(usedNumber);
		return dto;
	}
}
