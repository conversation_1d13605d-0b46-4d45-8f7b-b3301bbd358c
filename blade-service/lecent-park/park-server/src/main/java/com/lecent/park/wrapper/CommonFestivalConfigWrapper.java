package com.lecent.park.wrapper;

import com.lecent.park.entity.CommonFestivalConfig;
import com.lecent.park.vo.CommonFestivalConfigVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
public class CommonFestivalConfigWrapper extends BaseEntityWrapper<CommonFestivalConfig, CommonFestivalConfigVO>  {

	public static CommonFestivalConfigWrapper build() {
		return new CommonFestivalConfigWrapper();
 	}

	@Override
	public CommonFestivalConfigVO entityVO(CommonFestivalConfig parklotFestivalConfig) {
		CommonFestivalConfigVO parklotFestivalConfigVO = BeanUtil.copy(parklotFestivalConfig, CommonFestivalConfigVO.class);
		return parklotFestivalConfigVO;
	}

}
