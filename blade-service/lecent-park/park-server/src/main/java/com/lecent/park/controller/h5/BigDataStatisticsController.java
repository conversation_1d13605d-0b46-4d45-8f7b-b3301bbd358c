package com.lecent.park.controller.h5;


import com.baomidou.dynamic.datasource.annotation.Slave;
import com.lecent.park.service.IParkingOrderService;
import com.lecent.park.service.ITempParkingOrderService;
import com.lecent.park.vo.JHParkingOrderVO;
import com.lecent.park.vo.PayTypePercentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description： 建行大数据云平台统计接口
 * <AUTHOR>
 * @Date: 2021/5/10 11:07
 */
@Slave
@RestController
@AllArgsConstructor
@RequestMapping("/big-data-statistics")
@Api(value = "建行大数据云平台统计Controller", tags = "建行大数据云平台统计Controller")
public class BigDataStatisticsController {

	private ITempParkingOrderService tempParkingOrderService;
	private IParkingOrderService parkingOrderService;

	/**
	 * 停车支付方式比例
	 */
	@GetMapping("/pay-type-percent")
	@ApiOperation(value = "停车支付方式比例", notes = "停车支付方式比例")
	public R<List<PayTypePercentVO>> payTypePercent(String parklotId) {
		List<PayTypePercentVO> result = tempParkingOrderService.payTypePercent(parklotId);
		return R.data(result);
	}

	/**
	 * 获取车场最新的出入场记录
	 */
	@GetMapping("/last-parking-order")
	@ApiOperation(value = "获取车场最新的出入场记录", notes = "获取车场最新的出入场记录")
	public R<JHParkingOrderVO> getLastParkingOrder(String parklotId) {
		JHParkingOrderVO result = parkingOrderService.getLastParkingOrder(parklotId);
		return R.data(result);
	}
}
