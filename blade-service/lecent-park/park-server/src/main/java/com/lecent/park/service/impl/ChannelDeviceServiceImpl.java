package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.device.entity.DeviceManage;
import com.lecent.device.feign.IDeviceClient;
import com.lecent.park.entity.ChannelDevice;
import com.lecent.park.mapper.ChannelDeviceMapper;
import com.lecent.park.service.IChannelDeviceService;
import com.lecent.park.service.IChannelService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.ChannelDeviceVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2021-01-04
 */
@Service
public class ChannelDeviceServiceImpl extends BaseServiceImpl<ChannelDeviceMapper, ChannelDevice> implements IChannelDeviceService {

	@Resource
	private IDeviceClient deviceClient;
	@Autowired
	private IChannelService channelService;
	@Autowired
	private IParklotService parkLotService;

	@Override
	public List<ChannelDevice> listByChannelId(Long channelId) {
//		List<ChannelDevice> list = this.list(Wrappers.<ChannelDevice>lambdaQuery().eq(ChannelDevice::getChannelId, channelId));
//		if (list.isEmpty() || list.size()<=0){
//			return null;
//		}
//
//		for (ChannelDevice p : list) {
//			R<DeviceManage> deviceR = deviceClient.getBySeriesNo(p.getDeviceSn());
//			if (deviceR.isSuccess() && Func.isNotEmpty(deviceR.getData())){
//				p.setDeviceId(deviceR.getData().getId());
//				p.setDeviceName(deviceR.getData().getDeviceName());
//				p.setSeriesNo(deviceR.getData().getSeriesNo());
//				p.setMacAddress(deviceR.getData().getMac());
//				p.setType(deviceR.getData().getDeviceType());
//			}
//		}
		return Collections.emptyList();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ChannelDevice saveChannelDevice(ChannelDevice build) {
//		DeviceManage device = DeviceManage.builder()
//			.seriesNo(build.getDeviceSn())
//			.deviceName(build.getDeviceName())
//			.deviceType(build.getDeviceType())
//			.build();
//		deviceClient.register(device);
//		R<DeviceManage> deviceR = deviceClient.getBySeriesNo(build.getDeviceSn());
//		if (deviceR.isSuccess() && Func.isNotEmpty(deviceR.getData())){
//			build.setDeviceId(deviceR.getData().getId());
//		}
//		this.save(build);
		return build;
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public Boolean saveOrUpdateChannelDevice(ChannelDeviceVO channelDevice) {

//		List<ChannelDevice> list = this.list(Wrappers.<ChannelDevice>lambdaQuery().eq(ChannelDevice::getChannelId, channelDevice.getChannelId()));
//		if (Func.isNotEmpty(list) && list.size()>0){
//			for (ChannelDevice device : list) {
//				deviceClient.bindDevice(device.getDeviceSn(),0);
//			}
//		}
//
//		baseMapper.removeByChannelId(channelDevice.getChannelId());
//		List<DeviceManage> deviceList = channelDevice.getDeviceList();
//		if (isReturn(deviceList)){
//			return true;
//		}
//
//		Channel channel = channelService.getById(channelDevice.getChannelId());
//		String deviceName="";
//		if (Func.isNotEmpty(channel)){
//			Parklot parklot = ParkLotCaches.getParkLot(channel.getParklotId());
//			if (Func.isNotEmpty(parklot)){
//				deviceName=parklot.getName()+"|"+channel.getName();
//			}
//		}
//		for (DeviceManage d :channelDevice.getDeviceList() ) {
//			ChannelDevice build = ChannelDevice.builder()
//				.channelId(channelDevice.getChannelId())
//				.deviceId(d.getId())
//				.deviceSn(d.getSeriesNo())
//				.deviceType(d.getDeviceType())
//				.build();
//			this.saveOrUpdate(build);
//			deviceClient.bindDevice(d.getSeriesNo(),1);
//			R<DeviceManage> deviceR = deviceClient.getBySeriesNo(d.getSeriesNo());
//			if (deviceR.isSuccess() && Func.isNotEmpty(deviceR.getData())){
//				deviceClient.updateNameBySn(deviceName + "|" + DeviceType.getName(deviceR.getData().getDeviceType()), d.getSeriesNo(), null);
//			}
//		}
		return true;
	}


	public static Boolean isReturn(List<DeviceManage> deviceList) {
		if (deviceList.isEmpty() || deviceList.size()<=0){
			return true;

		}

		for (DeviceManage device : deviceList) {
			if (Func.isBlank(device.getSeriesNo())){
				throw new ServiceException("序列号不能为空！");
			}
		}

		List<String> list = deviceList.stream().
			collect(Collectors.groupingBy(d -> d.getSeriesNo(), Collectors.counting()))
			.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(entry -> entry.getKey())
			.collect(Collectors.toList());

		if (list.size()>0){
			throw new ServiceException("序列号"+list.get(0)+"重复！");
		}
		return false;
	}

	@Override
	public ChannelDevice getByChannelId(Long channelId, Integer deviceType) {
		return this.getOne(Wrappers.<ChannelDevice>lambdaQuery()
			.eq(ChannelDevice::getChannelId,channelId)
			.eq(ChannelDevice::getDeviceType,deviceType)
			.last("limit 1"));
	}

	@Override
	public ChannelDevice getByDeviceSn(String deviceSn) {
		return this.getOne(Wrappers.<ChannelDevice>lambdaQuery()
			.eq(ChannelDevice::getDeviceSn,deviceSn)
			.last("limit 1"));
	}
}
