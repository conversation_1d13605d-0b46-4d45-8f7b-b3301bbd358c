package com.lecent.park.controller.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.CarBrand;
import com.lecent.park.service.ICarBrandService;
import com.lecent.park.vo.CarBrandVO;
import com.lecent.park.wrapper.CarBrandWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/carbrand")
@Api(value = "", tags = "接口")
public class CarBrandController extends BladeController {

	private ICarBrandService carBrandService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入carBrand")
	public R<CarBrandVO> detail(CarBrand carBrand) {
		CarBrand detail = carBrandService.getOne(Condition.getQueryWrapper(carBrand));
		return R.data(CarBrandWrapper.build().entityVO(detail));
	}

	/**
	 * 车品牌查询
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "车品牌查询", notes = "传入carBrand")
	public R<List<CarBrand>> list(CarBrand carBrand) {
		return R.data(carBrandService.searchCarBrand(carBrand));
	}


	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入carBrand")
	public R<IPage<CarBrandVO>> page(CarBrandVO carBrand, Query query) {
		IPage<CarBrandVO> pages = carBrandService.selectCarBrandPage(Condition.getPage(query), carBrand);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入carBrand")
	public R save(@Valid @RequestBody CarBrand carBrand) {
		return R.status(carBrandService.save(carBrand));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入carBrand")
	public R update(@Valid @RequestBody CarBrand carBrand) {
		return R.status(carBrandService.updateById(carBrand));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入carBrand")
	public R submit(@Valid @RequestBody CarBrand carBrand) {
		return R.status(carBrandService.saveOrUpdate(carBrand));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(carBrandService.deleteLogic(Func.toLongList(ids)));
	}

	/*
	@PostMapping("/excel")
	public void importExcel(@RequestBody MultipartFile file){
		List<BrandExcel> read = ExcelUtil.read(file, BrandExcel.class);
		List<CarBrand> copy = BeanUtil.copy(read, CarBrand.class);
		carBrandService.saveBatch(copy);
	}
	*/
}
