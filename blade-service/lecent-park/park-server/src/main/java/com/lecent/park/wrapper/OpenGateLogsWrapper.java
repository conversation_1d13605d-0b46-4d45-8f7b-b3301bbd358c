package com.lecent.park.wrapper;

import com.lecent.park.entity.OpenGateLogs;
import com.lecent.park.vo.OpenGateLogsVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 开闸日志包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-02-09
 */
public class OpenGateLogsWrapper extends BaseEntityWrapper<OpenGateLogs, OpenGateLogsVO>  {

	public static OpenGateLogsWrapper build() {
		return new OpenGateLogsWrapper();
 	}

	@Override
	public OpenGateLogsVO entityVO(OpenGateLogs openGateLogs) {
		OpenGateLogsVO openGateLogsVO = BeanUtil.copy(openGateLogs, OpenGateLogsVO.class);

		//User createUser = UserCache.getUser(openGateLogs.getCreateUser());
		//User updateUser = UserCache.getUser(openGateLogs.getUpdateUser());
		//openGateLogsVO.setCreateUserName(createUser.getName());
		//openGateLogsVO.setUpdateUserName(updateUser.getName());

		return openGateLogsVO;
	}

}
