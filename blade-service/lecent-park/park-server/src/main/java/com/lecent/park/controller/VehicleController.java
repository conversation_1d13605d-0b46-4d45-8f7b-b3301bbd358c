package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.VehicleDO;
import com.lecent.park.service.IVehicleService;
import com.leliven.park.infrastructure.gateway.persistence.vehicle.vo.VehicleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 车辆信息表 控制器
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/vehicle")
@Api(value = "车辆信息表", tags = "车辆信息表接口")
public class VehicleController extends BladeController {

    private final IVehicleService vehicleService;

    @Resource
    private BladeRedis bladeRedis;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入vehicle")
    public R<VehicleDO> detail(VehicleDO vehicle) {
        VehicleDO detail = vehicleService.getOne(Condition.getQueryWrapper(vehicle));
        return R.data(detail);
    }

    /**
     * 分页 车辆信息表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入vehicle")
    public R<IPage<VehicleDO>> list(VehicleDO vehicle, Query query) {
        IPage<VehicleDO> pages = vehicleService.page(Condition.getPage(query), Condition.getQueryWrapper(vehicle));
        return R.data(pages);
    }

    /**
     * 自定义分页 车辆信息表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入vehicle")
    public R<IPage<VehicleVO>> page(VehicleVO vehicle, Query query) {
        IPage<VehicleVO> pages = vehicleService.selectVehiclePage(Condition.getPage(query), vehicle);
        return R.data(pages);
    }

    /**
     * 新增 车辆信息表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入vehicle")
    public R<Boolean> save(@Valid @RequestBody VehicleDO vehicle) {
        boolean save = vehicleService.save(vehicle);
        bladeRedis.set("lecent:park:cache:vehicle:" + vehicle.getId(), Func.toJson(vehicle));
        return R.status(save);
    }

    /**
     * 修改 车辆信息表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入vehicle")
    public R<Boolean> update(@Valid @RequestBody VehicleDO vehicle) {
        boolean update = vehicleService.updateById(vehicle);
        vehicle = vehicleService.getById(vehicle.getId());
        bladeRedis.set("lecent:park:cache:vehicle:" + vehicle.getId(), Func.toJson(vehicle));
        return R.status(update);
    }

    /**
     * 新增或修改 车辆信息表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入vehicle")
    public R<Boolean> submit(@Valid @RequestBody VehicleDO vehicle) {
        return R.status(vehicleService.saveOrUpdate(vehicle));
    }

    /**
     * 删除 车辆信息表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        List<Long> idList = Func.toLongList(ids);
        boolean deleted = vehicleService.deleteLogic(idList);
        for (Long id : idList) {
            bladeRedis.del("lecent:park:cache:vehicle:" + id);
        }
        return R.status(deleted);
    }
}
