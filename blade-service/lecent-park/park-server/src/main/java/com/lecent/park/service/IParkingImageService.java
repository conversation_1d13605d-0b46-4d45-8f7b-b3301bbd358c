package com.lecent.park.service;

import com.lecent.park.entity.ParkingImageDO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 停车图片 服务类
 *
 * <AUTHOR>
 */
public interface IParkingImageService extends BaseService<ParkingImageDO> {

	/**
	 * 根据停车订单ID查找图片列表
	 * 并根据停车进出场状态分组
	 *
	 * @param parkingOrderId 停车订单ID
	 * @return Map<Integer, List < ParkingImageDO>>
	 */
	Map<Integer, List<ParkingImageDO>> listGroupByParkingStatus(Long parkingOrderId);

	/**
	 * 根据停车订单ID查找图片列表
	 * 并根据停车进出场状态分组
	 *
	 * @param parkingOrderIds 停车订单ID
	 * @return Map<Integer, List < ParkingImageDO>>
	 */
	List<ParkingImageDO> listGroupByParkingStatus(List<Long> parkingOrderIds);

	/**
	 * 根据停车订单ID查找图片列表
	 * 并根据图片类型分组
	 *
	 * @param parkingOrderId 停车订单ID
	 * @return Map<Integer, List < ParkingImageDO>>
	 */
	Map<Integer, List<ParkingImageDO>> listGroupByImageType(Long parkingOrderId);

}
