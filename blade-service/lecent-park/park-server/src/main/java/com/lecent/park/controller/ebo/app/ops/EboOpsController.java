package com.lecent.park.controller.ebo.app.ops;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.biz.IEboParklotBiz;
import com.lecent.park.biz.IParklotDeviceRetBiz;
import com.lecent.park.controller.ebo.mini.resp.EboParkLot;
import com.lecent.park.dto.ParklotDTO;
import com.lecent.park.dto.RecoverOrderDTO;
import com.lecent.park.dto.TempParkingOrderDTO;
import com.lecent.park.dto.space.ParkingPlaceDTO;
import com.lecent.park.entity.ParkingImageDO;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotDeviceRet;
import com.lecent.park.service.*;
import com.lecent.park.vo.*;
import com.leliven.park.infrastructure.gateway.persistence.basic.query.ParklotDeviceRetDTO;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotDeviceRetVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.entity.KeyValueVo;
import org.springblade.common.payment.PayResult;
import org.springblade.common.utils.StrUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * e泊运维端接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@AllArgsConstructor
@RequestMapping("/ebo/ops")
@Api(value = "e泊运维端接口", tags = "e泊运维端接口")
public class EboOpsController extends BladeController {

	private final IEboParklotBiz eboOpsBiz;

	private final IParklotService parklotService;

	@Lazy
	private final IParkingPlaceService parkingPlaceService;

	private final IParklotDeviceRetBiz parklotDeviceRetBiz;

	private final IParklotDeviceRetService parklotDeviceRetService;

	private final IUserParklotService userParklotService;
	private final ITempParkingUnpaidOrderService unpaidOrderService;

	private final  IMergeOrderService mergeOrderService;

	private final  ITempParkingOrderService tempParkingOrderService;

	private final IParkingImageService parkingImageService;

	@GetMapping("/v1/space/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位分页列表", notes = "车位分页列表")
	public R<IPage<ParkingPlaceVO>> spacePage(ParkingPlaceDTO parkingPlaceDTO, Query query) {
		return R.data(parkingPlaceService.selectParkingPlacePage(Condition.getPage(query), parkingPlaceDTO));
	}

	@GetMapping("/v1/space/detail/{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位详情", notes = "传入车位id")
	public R<ParkingPlaceVO> detail(@PathVariable("id") Long id) {
		return R.data(parkingPlaceService.detailById(id));
	}

	@GetMapping("/v1/space/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位详情", notes = "传入shortUrl")
	public R<ParkingPlaceVO> detailByShortUrl(@RequestParam("shortUrl") String shortUrl) {
		return R.data(parkingPlaceService.detailByShortUrl(shortUrl));
	}

	@GetMapping("/v1/space/detail/ByPayCode")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位详情", notes = "传入payCode")
	public R<ParkingPlaceVO> detailByPayCode(@RequestParam("payCode") String payCode) {
		return R.data(parkingPlaceService.detailByPayCode(payCode));
	}

	@PostMapping("/v1/space/enable")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位启用", notes = "传入id")
	public R<Boolean> enableSpace(@RequestBody ParkingPlace parkingPlace) {
		return R.data(this.parkingPlaceService.enableSpace(parkingPlace.getId(), parkingPlace.getMemo()));
	}

	@PostMapping("/v1/space/disable")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位停用", notes = "传入id")
	public R<Boolean> disableSpace(@RequestBody ParkingPlace parkingPlace) {
		return R.data(this.parkingPlaceService.disableSpace(parkingPlace.getId(), parkingPlace.getMemo()));
	}

	@PostMapping("/v1/space/location/update")
	@ApiOperation(value = "更新车位位置信息", notes = "传入id, lng, lat")
	public R<Boolean> updateSpaceLocation(@RequestBody ParkingPlaceDTO dto) {
		return R.data(this.parkingPlaceService.updateSpaceLocation(dto));
	}

	@PostMapping("/v1/space/device/bind")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位绑定设备", notes = "车位绑定设备")
	public R<Boolean> bind(@RequestBody ParklotDeviceRetDTO retDTO) {
		return R.data(this.parklotDeviceRetBiz.bindParkingSpaceDevice(retDTO));
	}

	@PostMapping("/v1/space/device/unbind")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位绑定设备", notes = "车位解绑设备")
	public R<Boolean> unbind(@RequestBody ParklotDeviceRetDTO retDTO) {
		return R.data(this.parklotDeviceRetService.unbindSpaceDevice(retDTO));
	}

	@PostMapping("/v1/space/device/change")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位更换绑定设备", notes = "传入parkPlaceId,新设备deviceId、deviceType")
	public R<ParklotDeviceRet> changeSpaceDevice(@RequestBody ParklotDeviceRetDTO retDTO) {
		return R.data(this.parklotDeviceRetService.changeSpaceDevice(retDTO));
	}

	@GetMapping("/device/page")
	@ApiOperation(value = "设备列表", notes = "设备列表")
	public R<IPage<ParklotDeviceRetVO>> queryDeviceList(ParklotDeviceRetDTO ret, Query query) {
		if (Func.isNotBlank(query.getAscs()) || Func.isNotBlank(query.getDescs())) {
			query.setAscs(StrUtil.camelToUnderlines(query.getAscs()));
			query.setDescs(StrUtil.camelToUnderlines(query.getDescs()));
		} else {
			query.setAscs("device_name");
		}
		return R.data(parklotDeviceRetService.queryDeviceList(Condition.getPage(query), ret));
	}

	@GetMapping("/gateway/child")
	@ApiOperation(value = "查询网关子设备", notes = "查询网关子设备")
	public R<List<ParkSpaceLockVO>> queryGatewayChild(@RequestParam("id") Long deviceId) {
		return R.data(parklotDeviceRetService.queryGatewayChild(deviceId));
	}

	@GetMapping("/v1/parklot/page")
	@ApiOperation(value = "查询用户绑定的车场分页列表", notes = "查询用户绑定的车场分页列表")
	public R<IPage<EboParkLot>> parkLotPage(ParklotDTO parklot, Query query) {
		return R.data(eboOpsBiz.selectEboParklotPage(parklot, query));
	}

	@GetMapping("/parklot/list")
	@ApiOperation(value = "查询用户绑定的车场", notes = "查询用户绑定的车场")
	public R<List<KeyValueVo>> queryUserBindParkLot() {
		return R.data(userParklotService.getCurrentUserBindParkLot());
	}

	@PostMapping("/v1/parklot/location/update")
	@ApiOperation(value = "更新车场位置信息", notes = "传入id、lat、lng、address")
	public R<Boolean> updateParklotLocation(@RequestBody Parklot parklot) {
		return R.data(this.parklotService.updateLocation(parklot));
	}

	@GetMapping("/v1/unpaidOrderBy/page")
	@ApiOperation(value = "获取用户未支付订单详情", notes = "传入车牌")
	public R<IPage<UserUnpaidOrderByParklotIdVO>> getUserUnPayList(UserUnpaidOrderByParklotIdVO unpaidOrderByParklotIdVo, Query query) {
		IPage<UserUnpaidOrderByParklotIdVO> list = unpaidOrderService.getUserUnPayList(unpaidOrderByParklotIdVo, Condition.getPage(query));
		return R.data(list);
	}

	@GetMapping("/v1/unpaidOrderBy")
	@ApiOperation(value = "根据车牌 商户id 获取订单详情")
	public R<List<UserUnpaidOrderByParklotIdListVo>> getUnpaidOrderByPlateAndMerchantId(UnpaidOrderByPlateAndMerchantIdVO unpaidOrderByPlateAndmerchantIdVO) {
		List<UserUnpaidOrderByParklotIdListVo> order = unpaidOrderService.getUnpaidOrderByPlateAndMerchantId(unpaidOrderByPlateAndmerchantIdVO);
		return R.data(order);
	}

	@PostMapping("/v1/recovered/order")
	@ApiOperation(value = "追缴订单", notes = "payDTO")
	public R<RecoveredPayVO> recoveredOrder(@RequestBody RecoveredPayDTO payDTO) {
		return R.data(eboOpsBiz.recoveredOrder(payDTO));
	}

	@PostMapping("/v1/recovered/pay")
	@ApiOperation(value = "追缴支付", notes = "payDTO")
	public R<PayResult> recoveredPay(@RequestBody @Valid RecoveredPayDTO payDTO) {
		return R.data(eboOpsBiz.recoveredPay(payDTO));
	}

	@GetMapping("/v1/my/earnings")
	@ApiOperation(value = "我的收益")
	public R<EboParkLot> recoveredPay() {
		return R.data(eboOpsBiz.myEarnings());
	}

	@GetMapping("/v1/findRecoverOrderRanking")
	@ApiOperation(value = "获取追缴 排名 和佣金")
	public R<RecoverOrderStatistics> findRecoverOrderRanking(){
		return R.data(mergeOrderService.findRecoverOrderRanking());
	}

	@GetMapping("/v1/countRecoverOrderBroupMonth")
	@ApiOperation(value = "按月统计追缴金额")
	public R<List<Map<String,Object>>> countRecoverOrderGroupMonth(RecoverOrderDTO recoverOrder){
		return R.data(mergeOrderService.countRecoverOrderGroupMonth(recoverOrder));
	}

	@PostMapping("/v1/recovered/scan/pay")
	@ApiOperation(value = "追缴扫码支付", notes = "payDTO")
	public R<PayResult> recoveredScanPay(@RequestBody @Valid RecoveredPayDTO payDTO) {
		return R.data(eboOpsBiz.scanPay(payDTO));
	}

	@GetMapping("/v1/recovered/paySuccess")
	@ApiOperation(value = "支付成功")
	public R<Boolean> paySuccess(@RequestParam("tradeNo") String tradeNo){
		return R.data(mergeOrderService.paySuccess(tradeNo));
	}
	@GetMapping("/v1/recovered/cancel/order")
	@ApiOperation(value = "取消订单")
	public R<Boolean> cancelOrder(@RequestParam("tradeNo") String tradeNo){
		return R.data(mergeOrderService.cancelOrder(tradeNo));
	}

	@GetMapping("/v1/findMergeOrderPage")
	@ApiOperation(value = "查询合并订单")
	public R<IPage<MergeOrderVO>> findMergeOrderPage(MergeOrderVO mergeOrderVO, Query query){
		return R.data(mergeOrderService.findMergeOrderList(Condition.getPage(query),mergeOrderVO));
	}

	@Slave
	@GetMapping("/v1/findTempOrderPage")
	@ApiOperation(value = "查询临停订单", notes = "订单分页统计")
	public R<IPage<OrderPageStatisticVO>> findTempOrderPage(TempParkingOrderDTO parkingOrder, Query query) {
		parkingOrder.setOrderType(1);
		if(Func.isBlank(query.getAscs()) && Func.isBlank(query.getDescs())){
			//默认按照时间排序
			query.setDescs("pay_time");
		}else{
			query.setAscs(StrUtil.camelToUnderlines(query.getAscs()));
			query.setDescs(StrUtil.camelToUnderlines(query.getDescs()));
		}
		IPage<OrderPageStatisticVO> voPage = tempParkingOrderService.customPage(Condition.getPage(query),parkingOrder);
		return R.data(voPage);
	}

	@Slave
	@GetMapping("/v1/parking/images")
	@ApiOperation(value = "根据停车订单ID查找图片列表")
	public R<Map<Integer, List<ParkingImageDO>>> findParkingImages(@RequestParam Long parkingId){
		return R.data(parkingImageService.listGroupByParkingStatus(parkingId));
	}
}
