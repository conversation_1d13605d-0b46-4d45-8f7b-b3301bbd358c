package com.lecent.park.service;

import com.lecent.park.en.coupon.CouponCategory;
import com.lecent.park.entity.ParkingDiscountRecord;
import com.lecent.park.vo.ParkingDiscountRecordVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 停车优惠记录 服务类
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
public interface IParkingDiscountRecordService extends BaseService<ParkingDiscountRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page                  页
	 * @param parkingDiscountRecord 停车折扣记录
	 * @return {@link IPage}<{@link ParkingDiscountRecordVO}>
	 */
	IPage<ParkingDiscountRecordVO> selectParkingDiscountRecordPage(IPage<ParkingDiscountRecordVO> page, ParkingDiscountRecordVO parkingDiscountRecord);

	/**
	 * 完成优惠
	 *
	 * @param tradeNo 订单编号
	 * @return {@link Boolean}
	 */
	Boolean finishDiscount(String tradeNo);

	/**
	 * 统计优惠记录
	 *
	 * @param plate 车牌
	 * @param type  类型
	 * @return {@link Integer}
	 */
	Integer countDiscountRecord(String plate, CouponCategory type);
}
