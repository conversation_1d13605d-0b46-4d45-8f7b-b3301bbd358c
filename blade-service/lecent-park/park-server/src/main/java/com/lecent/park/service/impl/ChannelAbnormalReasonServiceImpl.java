package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ChannelAbnormalReason;
import com.lecent.park.entity.Parklot;
import com.lecent.park.mapper.ChannelAbnormalReasonMapper;
import com.lecent.park.service.IChannelAbnormalReasonService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IUserParklotService;
import com.lecent.park.vo.ChannelAbnormalReasonVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 通道异常原因信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-30
 */
@Service
public class ChannelAbnormalReasonServiceImpl extends BaseServiceImpl<ChannelAbnormalReasonMapper, ChannelAbnormalReason> implements IChannelAbnormalReasonService {

	@Autowired
	private IUserParklotService userParkLotService;

	@Resource
	private IParklotService parkLotService;


	@Override
	public IPage<ChannelAbnormalReasonVO> selectChannelAbnormalReasonPage(IPage<ChannelAbnormalReasonVO> page, ChannelAbnormalReasonVO channelAbnormalReason) {

		if (Objects.nonNull(channelAbnormalReason.getParklotId())) {
			channelAbnormalReason.setParkLotIds(Collections.singletonList(channelAbnormalReason.getParklotId()));
		} else {
			List<Long> parkLotIds = userParkLotService.getParkLotIds(SecureUtil.getUserId());
			if (Func.isEmpty(parkLotIds)) {
				parkLotIds.add(0L);
			}
			channelAbnormalReason.setParkLotIds(parkLotIds);
		}
		return page.setRecords(baseMapper.selectChannelAbnormalReasonPage(page, channelAbnormalReason));
	}

	@Override
	public boolean submit(ChannelAbnormalReason channelAbnormalReason) {
		channelAbnormalReason.setTenantId(SecureUtil.getTenantId());
		validateReason(channelAbnormalReason);
		return this.saveOrUpdate(channelAbnormalReason);
	}

	@Override
	public List<ChannelAbnormalReason> selectList(String tenantId) {

		List<Long> parkLotIds = userParkLotService.getParkLotIds(SecureUtil.getUserId());
		if (Func.isEmpty(parkLotIds)){
			parkLotIds.add(0L);
		}
		return baseMapper.selectList(tenantId, parkLotIds);
	}

	@Override
	public List<ChannelAbnormalReason> listOutReason(ChannelAbnormalReason reason) {
		List<Long> parkLotList = new ArrayList<>();
		if (Func.isEmpty(reason.getParklotId())) {
			List<Parklot> list = parkLotService.userParklotOutList(AuthUtil.getUserId());
			parkLotList = list.stream().map(Parklot::getId).collect(Collectors.toList());
		} else {
			parkLotList.add(reason.getParklotId());
		}
		return baseMapper.listOutReason(parkLotList);
	}

	@Override
	public List<ChannelAbnormalReason> listReason() {
		return baseMapper.listReason();
	}

	private void validateReason(ChannelAbnormalReason channelAbnormalReason) {
		int count = baseMapper.validateReason(channelAbnormalReason);
		if (count > 0) {
			throw new ServiceException("异常原因名称已经存在，请重新填写！");
		}
	}

}
