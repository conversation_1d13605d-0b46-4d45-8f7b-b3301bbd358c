package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.ParklotChargeRule;
import com.lecent.park.mapper.ParklotChargeRuleMapper;
import com.lecent.park.service.IParklotChargeRuleService;
import com.lecent.park.vo.ParklotChargeRuleVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;


/**
 * 公司员工车场资源授权表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Service
public class ParklotChargeRuleServiceImpl extends BaseServiceImpl<ParklotChargeRuleMapper, ParklotChargeRule> implements IParklotChargeRuleService {

	@Override
	public IPage<ParklotChargeRuleVO> selectParklotChargeRulePage(IPage<ParklotChargeRuleVO> page, ParklotChargeRuleVO parklotChargeRule) {
		return page.setRecords(baseMapper.selectParklotChargeRulePage(page, parklotChargeRule));
	}

	@Override
	public boolean saveParklotChargeRule(Long parklotId, Long companyId, Long chargeRuleId) {
		ParklotChargeRule record =  this.getByCondition(parklotId);
		if (Func.isNotEmpty(record)) {
			this.removeById(record);
		}
		ParklotChargeRule parklotChargeRule = new ParklotChargeRule();
		parklotChargeRule.setParklotId(parklotId);
		parklotChargeRule.setChargeRuleId(chargeRuleId);
		return this.save(parklotChargeRule);
	}

	@Override
	public ParklotChargeRule getByCondition(Long parklotId, Long companyId, Long chargeRuleId) {
		return this.getOne(Wrappers.<ParklotChargeRule>query().lambda()
						.eq(ParklotChargeRule::getParklotId,parklotId)
						.eq(ParklotChargeRule::getChargeRuleId,chargeRuleId)
						.eq(ParklotChargeRule::getStatus, Constants.ONE));
	}

	@Override
	public ParklotChargeRule getByCondition(Long parklotId) {
		return this.getOne(Wrappers.<ParklotChargeRule>query().lambda()
			.eq(ParklotChargeRule::getParklotId,parklotId)
			.eq(ParklotChargeRule::getStatus, Constants.ONE));
	}

}
