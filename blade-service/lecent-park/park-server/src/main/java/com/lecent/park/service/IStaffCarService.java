package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.StaffCarDTO;
import com.lecent.park.entity.StaffCar;
import com.lecent.park.vo.StaffCarVO;
import org.springblade.core.mp.base.BaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 员工车辆 服务类
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
public interface IStaffCarService extends BaseService<StaffCar> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param staffCarDto
	 * @return
	 */
	IPage<StaffCarVO> selectStaffCarPage(IPage<StaffCarVO> page, StaffCarDTO staffCarDto);

	/**
	 * 保存员工车辆
	 *
	 * @param staffCarDto
	 * @return
	 */
	boolean customSave(StaffCarDTO staffCarDto);

	/**
	 * 修改员工车辆
	 *
	 * @param staffCarDto
	 * @return
	 */
	boolean customUpdate(StaffCarDTO staffCarDto);

	/**
	 * 关联车场
	 *
	 * @param staffCarDto
	 * @return
	 */
	boolean relateParklot(StaffCarDTO staffCarDto);

	/**
	 * 删除关联
	 *
	 * @param relatedId
	 * @return
	 */
	boolean delRelateParklot(Long relatedId);

	void downloadTemp(HttpServletResponse response);

	/**
	 * 批量导入员工车辆
	 *
	 * @param staffExcel
	 * @param parklotId
	 * @param ruleId
	 * @return
	 */
	boolean batchImportStaff(MultipartFile staffExcel, Long parklotId, Long ruleId);

	/**
	 * 自定义删除
	 *
	 * @param ids
	 * @return
	 */
	boolean customDel(List<Long> ids);

	/**
	 * 导出员工车辆
	 *
	 * @param response
	 * @param staffCarDto
	 */
	void exportStaff(HttpServletResponse response, StaffCarDTO staffCarDto);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	StaffCarVO detail(String id);
}
