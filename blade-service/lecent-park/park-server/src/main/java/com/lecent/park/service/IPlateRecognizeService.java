package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.PlateRecognize;
import com.lecent.park.vo.PlateRecognizeVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 对象存储表 服务类
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
public interface IPlateRecognizeService extends BaseService<PlateRecognize> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param plateRecognize
	 * @return
	 */
	IPage<PlateRecognizeVO> selectPlateRecognizePage(IPage<PlateRecognizeVO> page, PlateRecognizeVO plateRecognize);

}
