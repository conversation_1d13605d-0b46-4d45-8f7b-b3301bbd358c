package com.lecent.park.wrapper;

import com.lecent.park.entity.ChannelDevice;
import com.lecent.park.vo.ChannelDeviceVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-01-04
 */
public class ChannelDeviceWrapper extends BaseEntityWrapper<ChannelDevice, ChannelDeviceVO>  {

	public static ChannelDeviceWrapper build() {
		return new ChannelDeviceWrapper();
 	}

	@Override
	public ChannelDeviceVO entityVO(ChannelDevice channelDevice) {
		ChannelDeviceVO channelDeviceVO = BeanUtil.copy(channelDevice, ChannelDeviceVO.class);

		//User createUser = UserCache.getUser(channelDevice.getCreateUser());
		//User updateUser = UserCache.getUser(channelDevice.getUpdateUser());
		//channelDeviceVO.setCreateUserName(createUser.getName());
		//channelDeviceVO.setUpdateUserName(updateUser.getName());

		return channelDeviceVO;
	}

}
