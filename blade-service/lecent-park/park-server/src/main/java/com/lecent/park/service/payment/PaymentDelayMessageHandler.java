package com.lecent.park.service.payment;

import com.lecent.park.common.constant.ParkingConstant;
import com.lecent.park.core.notify.api.IDelayMsgHandler;
import com.lecent.park.core.notify.api.IMsgSender;
import com.lecent.park.core.notify.domain.MsgRequest;
import com.lecent.park.service.ICardOrderService;
import com.lecent.park.service.ITempParkingOrderService;
import com.lecent.payment.enums.PayStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.payment.PayResult;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 支付处理
 *
 * <AUTHOR> zxr
 * @date : 2022/3/17
 */
@Slf4j
@Component
public class PaymentDelayMessageHandler implements IDelayMsgHandler<PaymentDelayMessageHandler.PayQueryEvent> {
	/**
	 * 临停支付
	 */
	private final String TEMP_PARKING_PAY = "temp_parking_pay";

	/**
	 * 月卡支付
	 */
	private final String MONTH_CARD_PAY = "month_card_pay";

	@Autowired
	private IMsgSender msgSender;

	@Lazy
	@Autowired
	private ITempParkingOrderService tempParkingOrderService;

	@Autowired
	private ICardOrderService cardOrderService;

	@Override
	public void handler(String type, PayQueryEvent message) {
		switch (type) {
			// 临停缴费
			case TEMP_PARKING_PAY:
				tempParkingPayHandler(message);
				break;

			// 月卡缴费
			case MONTH_CARD_PAY:
				monthCardPayHandler(message);
				break;

			default:
				log.warn("不存在的处理类型type={},data={}", type, Func.toJson(message));
		}
	}

	/**
	 * 月卡回调查询
	 *
	 * @param message msg
	 */
	private void monthCardPayHandler(PayQueryEvent message) {
		PayResult payResult = cardOrderService.queryMonthCardPayResult(message.getTradeNo());
		if (PayStatus.paySuccess(payResult.getPayStatus())) {
			log.info("月卡订单：{}已支付成功", message.getTradeNo());
			return;
		}
		long delayTime = ParkingConstant.nextLoopTime(message.getCallbackNum());
		if (delayTime < 0) {
			log.info("月卡订单:{}未查询到支付结果，放弃轮询", message.getTradeNo());
			return;
		}
		int queryCount = message.getCallbackNum() + 1;
		log.info("月卡订单:{}未查询到支付结果，{}毫秒后触发第{}次轮询...", message.getTradeNo(), delayTime, queryCount);
		addCallbackQuery(message.getTradeNo(), MONTH_CARD_PAY, delayTime, queryCount);
	}

	/**
	 * 临停缴费主查询
	 *
	 * @param message msg
	 */
	private void tempParkingPayHandler(PayQueryEvent message) {
		PayResult payResult = tempParkingOrderService.queryParkingOrderPayResult(message.getTradeNo());
		// 判断是否需要重新放入延时队列中
		if (!PayStatus.paySuccess(payResult.getPayStatus())) {
			// 查询10
			long delayTime = ParkingConstant.nextLoopTime(message.getCallbackNum());
			if (delayTime > 0) {
				int queryNum = message.getCallbackNum() + 1;
				log.info("临停订单[{}]未查询到支付结果，{}毫秒后触发第{}次轮询...", message.getTradeNo(), delayTime, queryNum);
				addCallbackQuery(message.getTradeNo(), TEMP_PARKING_PAY, delayTime, queryNum);
			} else {
				log.info("临停订单[{}]长时间未查询到支付结果，放弃轮询...", message.getTradeNo());
			}
		} else {
			log.info("临停订单[{}]支付成功...", message.getTradeNo());
		}
	}


	/**
	 * 添加临停订单支付查询回调
	 *
	 * @param tradeNo 流水号
	 */
	public void addTempParkingPayQuery(String tradeNo) {
		log.info("添加临停缴费主动查询tradeNo={}", tradeNo);
		addCallbackQuery(tradeNo, TEMP_PARKING_PAY, ParkingConstant.PAY_FIRST_CALL_TIME, 1);
	}

	/**
	 * 添加月卡订单支付查询回调
	 *
	 * @param tradeNo 流水号
	 */
	public void addMonthCardPayQuery(String tradeNo) {
		log.info("添加月卡缴费主动查询tradeNo={}", tradeNo);
		addCallbackQuery(tradeNo, MONTH_CARD_PAY, ParkingConstant.PAY_FIRST_CALL_TIME, 1);
	}

	/**
	 * 添加回调查询
	 *
	 * @param tradeNo     流水号
	 * @param type        类型
	 * @param delayTime   延时时间
	 * @param callbackNum 回调次数
	 */
	private void addCallbackQuery(String tradeNo, String type, long delayTime, int callbackNum) {
		msgSender.sendDelayMessage(MsgRequest.builder()
				.handler(this.getClass())
				.code(type)
				.body(new PayQueryEvent(tradeNo, callbackNum))
				.build(),
			delayTime);
	}

	/**
	 * 支付查询回调事件
	 */
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class PayQueryEvent implements Serializable {
		/**
		 * 流水号
		 */
		private String tradeNo;

		/**
		 * 回调次数
		 */
		private Integer callbackNum;
	}
}
