package com.lecent.park.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lecent.park.common.constant.OwnerEnum;
import com.lecent.park.common.utils.IdGen;
import com.lecent.park.dto.OwnerDetailInfoDTO;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.OwnerDetailInfo;
import com.lecent.park.entity.Parklot;
import com.lecent.park.excel.OwnerExcel;
import com.lecent.park.excel.OwnerInfoExcel;
import com.lecent.park.excel.OwnerInfoExportExcel;
import com.lecent.park.excel.OwnerInfoImportExcel;
import com.lecent.park.mapper.OwnerDetailInfoMapper;
import com.lecent.park.service.ICardService;
import com.lecent.park.service.IOwnerDetailInfoService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.OwnerDetailInfoTreeVO;
import com.lecent.park.vo.OwnerDetailInfoVO;
import jodd.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 楼层业主信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@Service
@Slf4j
public class OwnerDetailInfoServiceImpl extends BaseServiceImpl<OwnerDetailInfoMapper, OwnerDetailInfo> implements IOwnerDetailInfoService {

	@Resource
	@Lazy
	private ICardService cardService;

	@Resource
	private IParklotService parklotService;


	@Override
	public IPage<OwnerDetailInfoVO> selectOwnerDetailInfoPage(IPage<OwnerDetailInfoVO> page, OwnerDetailInfoDTO ownerDetailInfo) {
		if (!AuthUtil.isAdmin()) {
			setCommunityInfo(ownerDetailInfo);
		}
		if (org.apache.commons.lang3.StringUtils.isNotBlank(ownerDetailInfo.getCardNum())) {
			String[] num = ownerDetailInfo.getCardNum().split(",");
			for (String item : num) {
				if (Integer.parseInt(item) >= 4) {
					ownerDetailInfo.setIsCardNumOut(4);
					break;
				}
			}
		}
		if (org.apache.commons.lang3.StringUtils.isNotBlank(ownerDetailInfo.getCarNum())) {
			String[] num = ownerDetailInfo.getCarNum().split(",");
			for (String item : num) {
				if (Integer.parseInt(item) >= 4) {
					ownerDetailInfo.setIsCarNumOut(4);
					break;
				}
			}
		}
		page.setRecords(baseMapper.selectOwnerDetailInfoPage(page, ownerDetailInfo));
		return page;
	}

	@Override
	public List<OwnerDetailInfoVO> selectMyPage(OwnerDetailInfoDTO ownerDetailInfo) {
		return baseMapper.selectOwnerDetailInfoPage(new Page<>(1, Integer.MAX_VALUE), ownerDetailInfo);
	}

	private void setCommunityInfo(OwnerDetailInfoDTO ownerDetailInfo) {
		if (null != ownerDetailInfo && StringUtils.isBlank(ownerDetailInfo.getIds())) {
			ownerDetailInfo.setIds(this.parklotService.getCommunityIds(AuthUtil.getUserId()));
		}
	}

	/**
	 * 添加小区 ，业主信息
	 *
	 * @param ownerDetailInfo
	 * @return
	 */
	@Override
	public boolean addOwnerDetailInfo(OwnerDetailInfo ownerDetailInfo) {
		validateName(ownerDetailInfo);
		//设置社区编号
		resolveCommunityNo(ownerDetailInfo);
		//添加全路径地址信息
		if (null != ownerDetailInfo.getParentId() || StringUtils.isBlank(ownerDetailInfo.getRegionName())) {
			List<OwnerDetailInfo> parentOwnerDetailInfos = baseMapper.selectOwnerParentInfoById((ownerDetailInfo.getParentId() == null) ? 0 : ownerDetailInfo.getParentId());
			StringBuilder buffer = new StringBuilder();
			parentOwnerDetailInfos.forEach(item -> buffer.append(item.getName() + "/"));
			//
			String regionName = buffer.toString() + ownerDetailInfo.getName();
			ownerDetailInfo.setRegionName(regionName);
		}
		return customSubmit(ownerDetailInfo);
	}

	private boolean customSubmit(OwnerDetailInfo ownerDetailInfo) {
		if (Func.isNull(ownerDetailInfo.getId())) {
			fillDefault(ownerDetailInfo);
			resolveExtFiled(ownerDetailInfo);
			return save(ownerDetailInfo);
		}
		return updateById(ownerDetailInfo);
	}

	private void resolveExtFiled(OwnerDetailInfo ownerDetailInfo) {
		if (OwnerEnum.HOUSING_ESTATE.getValue().equals(ownerDetailInfo.getLevel())) {
			ownerDetailInfo.setCommunityId(ownerDetailInfo.getId());
		}
		if (OwnerEnum.GROUP.getValue().equals(ownerDetailInfo.getLevel())) {
			ownerDetailInfo.setGroupId(ownerDetailInfo.getId());
			ownerDetailInfo.setCommunityId(ownerDetailInfo.getParentId());
		}
		if (OwnerEnum.TOWER_NUM.getValue().equals(ownerDetailInfo.getLevel())) {
			ownerDetailInfo.setFloorId(ownerDetailInfo.getId());
			ownerDetailInfo.setGroupId(ownerDetailInfo.getParentId());
			ownerDetailInfo.setCommunityId(getParentId(ownerDetailInfo.getParentId()));
		}
		if (OwnerEnum.APARTMENT.getValue().equals(ownerDetailInfo.getLevel())) {
			ownerDetailInfo.setApartmentId(ownerDetailInfo.getId());
			Long floorId = ownerDetailInfo.getParentId();
			ownerDetailInfo.setFloorId(floorId);
			Long groupId = getParentId(floorId);
			ownerDetailInfo.setGroupId(groupId);
			Long CommunityId = getParentId(groupId);
			ownerDetailInfo.setCommunityId(CommunityId);
		}
		if (OwnerEnum.ROOM_NUM.getValue().equals(ownerDetailInfo.getLevel())) {
			if (null == ownerDetailInfo.getApartmentId()) {
				Long apartmentId = ownerDetailInfo.getParentId();
				ownerDetailInfo.setApartmentId(apartmentId);
				Long floorId = getParentId(apartmentId);
				ownerDetailInfo.setFloorId(floorId);
				Long groupId = getParentId(floorId);
				ownerDetailInfo.setGroupId(groupId);
				Long CommunityId = getParentId(groupId);
				ownerDetailInfo.setCommunityId(CommunityId);
			}

		}
	}

	private Long getParentId(Long parentId) {
		OwnerDetailInfo ownerDetailInfo = getById(parentId);
		return Func.isNull(ownerDetailInfo) ? 0L : ownerDetailInfo.getParentId();
	}

	private void fillDefault(BaseEntity entity) {
		BladeUser user = AuthUtil.getUser();
		Date now = DateUtil.now();
		if (user != null) {
			entity.setCreateUser(user.getUserId());
			List<Long> deptList = Func.toLongList(user.getDeptId());
			if (CollectionUtil.isNotEmpty(deptList)) {
				entity.setCreateDept((Long) deptList.iterator().next());
			}

			entity.setUpdateUser(user.getUserId());
		}

		if (entity.getStatus() == null) {
			entity.setStatus(1);
		}
		entity.setCreateTime(now);
		Long id = IdGen.genId(entity);
		entity.setId(id);
	}

	private void resolveCommunityNo(OwnerDetailInfo ownerDetailInfo) {
		//修改直接返回
		if (Func.isNotEmpty(ownerDetailInfo.getId())) {
			return;
		}
		//不是小区则返回
		if (!OwnerEnum.HOUSING_ESTATE.getValue().equals(ownerDetailInfo.getLevel())) {
			return;
		}
		String communityNo;
		do {
			communityNo = String.valueOf(MathUtil.randomInt(1000, 9999));
		} while (this.getCountByCommunityNo(communityNo) > 0);

		ownerDetailInfo.setCommunityNo(communityNo);

	}

	private int getCountByCommunityNo(String communityNo) {
		return this.count(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getCommunityNo, communityNo));
	}


	private void validateName(OwnerDetailInfo ownerDetailInfo) {
		if (Func.isEmpty(ownerDetailInfo.getParentId())) {
			ownerDetailInfo.setParentId(0L);
		}

		List<OwnerDetailInfo> ownerDetailInfoList = getNameNumByParentId(ownerDetailInfo);
		if (ownerDetailInfoList != null && ownerDetailInfoList.size() >= 1) {
			if (!ownerDetailInfoList.get(0).getId().equals(ownerDetailInfo.getId())) {
				if (OwnerEnum.HOUSING_ESTATE.getValue().equals(ownerDetailInfo.getLevel())) {
					throw new ServiceException("小区名字已存在，请重新录入！");
				}
				if (OwnerEnum.GROUP.getValue().equals(ownerDetailInfo.getLevel())) {
					throw new ServiceException("该小区下组团名字已存在，请重新录入！");
				}
				if (OwnerEnum.TOWER_NUM.getValue().equals(ownerDetailInfo.getLevel())) {
					throw new ServiceException("该组团下楼栋名字已存在，请重新录入！");
				}
				if (OwnerEnum.APARTMENT.getValue().equals(ownerDetailInfo.getLevel())) {
					throw new ServiceException("该楼栋下单元名字已存在，请重新录入！");
				}
				if (OwnerEnum.ROOM_NUM.getValue().equals(ownerDetailInfo.getLevel())) {
					throw new ServiceException("该单元下房间号已存在，请重新录入！");
				}
			}
		}
	}

	private List<OwnerDetailInfo> getNameNumByParentId(OwnerDetailInfo ownerDetailInfo) {
		return this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getName, ownerDetailInfo.getName()).eq(OwnerDetailInfo::getParentId, ownerDetailInfo.getParentId()));
	}

	/**
	 * 导出小区信息模板
	 *
	 * @param response
	 * @param id
	 */
	@Override
	public void exportExcel(HttpServletResponse response, Long id) {
		List<OwnerInfoExportExcel> ownerInfoExportExcelList = new ArrayList<>();
		OwnerInfoExportExcel ownerInfoExportExcel = new OwnerInfoExportExcel();
		ownerInfoExportExcel.setNum(1);
		ownerInfoExportExcel.setGroup("示例(如有组团则填写，没有组团则不填,批量上传时此条示例删除)");
		ownerInfoExportExcel.setRoomNum("1-1-1-1（如1栋1单元1层1号房则写为）");
		ownerInfoExportExcel.setName("张三;李四(多个用';'，隔开)");
		ownerInfoExportExcel.setPhone("15902545663;15902545336(多个用';'，隔开)");
		ownerInfoExportExcel.setArea("111.1(单是平方位米)");
		ownerInfoExportExcel.setPlates("贵A66666;贵A88888(多个用';'，隔开)");
		ownerInfoExportExcelList.add(ownerInfoExportExcel);
		ExcelUtil.export(response, "业主信息批量导入模板", "业主信息批量导入模板", ownerInfoExportExcelList, OwnerInfoExportExcel.class);
		/*OwnerDetailInfo ownerDetailInfo = baseMapper.selectById(id);
		LecentAssert.notNull(ownerDetailInfo, "没有此小区");
		//判断小区状态
		LecentAssert.isTrue(ownerDetailInfo.getStatus() == 1, "此小区无效");
		//开始导出数据
		List<OwnerExcel> ownerExcels = exportData(id);
		ExcelUtil.export(response, "业主信息批量导入模板", "业主信息批量导入模板", ownerExcels, OwnerExcel.class);*/
	}

	/**
	 * 批量导入业主信息
	 *
	 * @param ownerExcel
	 * @param id
	 * @param isCreate
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = {Exception.class})
	public boolean importExcel(MultipartFile ownerExcel, Long id, String isCreate) {
		List<OwnerInfoImportExcel> ownerExcels = ExcelUtil.read(ownerExcel, OwnerInfoImportExcel.class);

		OwnerDetailInfo getCommunity = baseMapper.selectOne(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getId, id));
		if (ObjectUtil.isEmpty(getCommunity)) {
			LecentAssert.alertException("没有此小区");
		}

		for (int i = 0; i < ownerExcels.size(); i++) {
			OwnerDetailInfo community = getCommunity;
			OwnerInfoImportExcel excel = ownerExcels.get(i);
			// 检查数据是否合法
			checkOwnerExcelData(excel, i);
			//校验小区和房号关系
			OwnerDetailInfo room = checkAndGetRoomInfo(community, excel, isCreate);
			if (StringUtils.isNotBlank(room.getOwnerPhone()) && !room.getOwnerPhone().equals(excel.getPhone())) {
				LecentAssert.alertException(String.format("第%s行对应的业主信息已存在且电话号不一致！", i));
			}
			room.setOwnerName(excel.getName());
			room.setOwnerPhone(excel.getPhone());
			room.setArea(excel.getArea());
			updateById(room);
			if (StringUtils.isNotBlank(excel.getPlates())) {
				List<Parklot> parklotList = parklotService.list(Wrappers.<Parklot>lambdaQuery().eq(Parklot::getCommunityId, id));
				Long parkLotId = null;
				if (null != parklotList && parklotList.size() > 0) {
					parkLotId = parklotList.get(0).getId();
					for (String plate : excel.getPlates().split(";")) {
						Card card = cardService.getOne(Wrappers.<Card>lambdaQuery().like(Card::getPlates, plate).like(Card::getParklotIds, parkLotId).eq(Card::getStatus, 1));
						if (null != card) {
							card.setOwnerUserId(room.getId());
							card.setRoomNum(String.format("%s-%S", room.getName(), room.getOwnerName()));
							if (StringUtils.isNotBlank(room.getOwnerPhone())) {
								card.setPhone(room.getOwnerPhone().split(";")[0]);
							}
							card.setName(room.getOwnerName());
							cardService.saveOrUpdate(card);
						}
					}
				}
			}

			/*String apartment = excel.getApartment();//单元
			//根据单元名称查出单元信息
			List<OwnerDetailInfo> infos = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery()
				.eq(OwnerDetailInfo::getName, apartment));
			if (infos.isEmpty())
				LecentAssert.alertException(i+"行["+apartment+"]单元不存在");
			//遍历根据单元名称查出单元信息与小区地址比较，唯一确定excel业主所在单元id
			for (int i1 = 0; i1 < infos.size(); i1++) {
				OwnerDetailInfo info = infos.get(i1);
				log.debug("excel上传的小区地址[{}],查出数据库中单元信息[{}]",excel.getHousingEstate(),info.getRegionName());
				if (info.getRegionName().startsWith(excel.getHousingEstate())){
					if (info.getStatus() == 0)
						LecentAssert.alertException("第"+i+"行["+excel.getHousingEstate()+"]单元已失效");

					OwnerDetailInfo ownerDetailInfo = new OwnerDetailInfo();
					ownerDetailInfo.setLevel(OwnerEnum.ROOM_NUM.getValue()) //level 5
						.setOwnerName(excel.getName()) //业主姓名
						.setOwnerPhone(excel.getPhone()) //业主联系方式
						.setParentId(info.getId()) //父级id  (对应单元id)
						.setName(excel.getRoomNum());//房间号

					if (addOwnerDetailInfo(ownerDetailInfo)) // 插入成功结束循环
						break;
					else
						LecentAssert.alertException("插入失败");
				}
			}*/
		}
		log.debug("读取的excel内容{}", ownerExcels);
		return true;
	}

	private OwnerDetailInfo checkAndGetRoomInfo(OwnerDetailInfo community, OwnerInfoImportExcel ownerExcel, String isCreate) {
		String group = ownerExcel.getGroup();

		String towerNum = ownerExcel.getTowerNum();
		String apartment = ownerExcel.getApartment();
		String roomNum = ownerExcel.getRoomNum();

		if (StringUtils.isBlank(group)) {
			group = "默认组团";
			ownerExcel.setGroup(group);
		}

		String regionName = community.getName().concat(StringPool.SLASH).concat(group).concat(StringPool.SLASH).concat(towerNum).concat(StringPool.SLASH).concat(apartment).concat(StringPool.SLASH).concat(roomNum);

		List<OwnerDetailInfo> list = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getRegionName, regionName));

		OwnerDetailInfo ownerDetailInfo = new OwnerDetailInfo();
		if (list.size() != 1) {
			try {
				//构建社区信息
				ownerDetailInfo = saveCommunityInfo(community, ownerExcel);
			} catch (Exception e) {
				log.error("OwnerDetailInfoServiceImpl.importExcel批量导入业主信息失败失败：" + e.getMessage());
				throw new ServiceException("批量导入失败！");
			}
			/*if ("true".equals(isCreate)) {
				if (StringUtils.isBlank(towerNum) || StringUtils.isBlank(apartment) || StringUtils.isBlank(roomNum)) {
					throw new ServiceException("楼栋、单元、房号不能为空！");
				}
				try {
					//构建社区信息
					ownerDetailInfo = saveCommunityInfo(community, ownerExcel);
				} catch (Exception e) {
					log.error("OwnerDetailInfoServiceImpl.importExcel批量导入业主信息失败失败：" + e.getMessage());
					throw new ServiceException("批量导入失败！");
				}
			} else {
				throw new ServiceException("小区找不到该房号信息");
			}*/
		} else {
			ownerDetailInfo = list.get(0);
		}
		return ownerDetailInfo;
	}

	private OwnerDetailInfo saveCommunityInfo(OwnerDetailInfo community, OwnerInfoImportExcel ownerExcel) {

		String communityName = community.getName();
		String regionName = communityName.concat(StringPool.SLASH).concat(ownerExcel.getGroup());
		Long communityId = community.getId();
		Long groupId = 0L;
		Long towerNumId = 0L;
		Long apartmentId = 0L;

		OwnerDetailInfo roomInfo = new OwnerDetailInfo();
		List<OwnerDetailInfo> list = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getRegionName, regionName));
		roomInfo.setCommunityId(communityId);
		if (list.size() != 1) {
			saveOwnerDetailInfo(roomInfo, community, regionName, ownerExcel, OwnerEnum.GROUP.getValue());
		} else {
			community = list.get(0);
			groupId = community.getId();

			regionName = regionName.concat(StringPool.SLASH).concat(ownerExcel.getTowerNum());
			list = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getRegionName, regionName));
			roomInfo.setGroupId(groupId);
			if (list.size() != 1) {
				saveOwnerDetailInfo(roomInfo, community, regionName, ownerExcel, OwnerEnum.TOWER_NUM.getValue());
			} else {
				community = list.get(0);
				towerNumId = community.getId();
				regionName = regionName.concat(StringPool.SLASH).concat(ownerExcel.getApartment());
				list = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getRegionName, regionName));
				roomInfo.setFloorId(towerNumId);
				if (list.size() != 1) {
					saveOwnerDetailInfo(roomInfo, community, regionName, ownerExcel, OwnerEnum.APARTMENT.getValue());
				} else {
					community = list.get(0);
					apartmentId = community.getId();
					regionName = regionName.concat(StringPool.SLASH).concat(ownerExcel.getRoomNum());
					list = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getRegionName, regionName));
					roomInfo.setApartmentId(apartmentId);
					if (list.size() != 1) {
						saveOwnerDetailInfo(roomInfo, community, regionName, ownerExcel, OwnerEnum.ROOM_NUM.getValue());
					}
				}
			}
		}

		return roomInfo;
	}

	private void saveOwnerDetailInfo(OwnerDetailInfo roomInfo, OwnerDetailInfo community, String regionName, OwnerInfoImportExcel ownerExcel, Integer level) {
		Long parentId = community.getId();
		OwnerDetailInfo groupInfo = new OwnerDetailInfo();
		OwnerDetailInfo towerInfo = new OwnerDetailInfo();
		OwnerDetailInfo apartmentInfo = new OwnerDetailInfo();
		String towerNum = ownerExcel.getTowerNum();
		String apartment = ownerExcel.getApartment();
		//构建组团
		if (level <= OwnerEnum.GROUP.getValue()) {
			buildOwnerDetailInfo(groupInfo, parentId, ownerExcel, regionName, OwnerEnum.GROUP.getValue());
			groupInfo.setName(ownerExcel.getGroup());
			groupInfo.setCommunityId(roomInfo.getCommunityId());
			this.save(groupInfo);
			parentId = groupInfo.getId();
		}

		//构建楼栋
		if (level <= OwnerEnum.TOWER_NUM.getValue()) {
			if (level < OwnerEnum.TOWER_NUM.getValue()) {
				regionName = regionName.concat(StringPool.SLASH).concat(ownerExcel.getTowerNum());
			}
			buildOwnerDetailInfo(towerInfo, parentId, ownerExcel, regionName, OwnerEnum.TOWER_NUM.getValue());
			towerInfo.setName(towerNum);
			towerInfo.setCommunityId(roomInfo.getCommunityId());
			towerInfo.setGroupId(parentId);

			this.save(towerInfo);
			parentId = towerInfo.getId();
		}

		//构建单元
		if (level <= OwnerEnum.APARTMENT.getValue()) {
			if (level < OwnerEnum.APARTMENT.getValue()) {
				regionName = regionName.concat(StringPool.SLASH).concat(ownerExcel.getApartment());
			}

			buildOwnerDetailInfo(apartmentInfo, parentId, ownerExcel, regionName, OwnerEnum.APARTMENT.getValue());
			apartmentInfo.setName(apartment);
			apartmentInfo.setCommunityId(roomInfo.getCommunityId());
			apartmentInfo.setGroupId(null == roomInfo.getGroupId() ? groupInfo.getId() : roomInfo.getGroupId());
			apartmentInfo.setFloorId(parentId);
			this.save(apartmentInfo);
			parentId = apartmentInfo.getId();
		}

		//构建房号
		if (level <= OwnerEnum.ROOM_NUM.getValue()) {
			if (level < OwnerEnum.ROOM_NUM.getValue()) {
				regionName = regionName.concat(StringPool.SLASH).concat(ownerExcel.getRoomNum());
			}

			buildOwnerDetailInfo(roomInfo, parentId, ownerExcel, regionName, OwnerEnum.ROOM_NUM.getValue());
			roomInfo.setName(ownerExcel.getRoomNum());
			roomInfo.setArea(ownerExcel.getArea());
			roomInfo.setCommunityId(roomInfo.getCommunityId());
			roomInfo.setGroupId(null == roomInfo.getGroupId() ? groupInfo.getId() : roomInfo.getGroupId());
			roomInfo.setFloorId(null == roomInfo.getFloorId() ? towerInfo.getId() : roomInfo.getFloorId());
			roomInfo.setApartmentId(parentId);
			this.save(roomInfo);
		}

	}

	private void buildOwnerDetailInfo(OwnerDetailInfo ownerDetailInfo, Long parentId, OwnerInfoImportExcel ownerExcel, String regionName, Integer level) {

		/*String communityNo;
		do {
			communityNo = String.valueOf(MathUtil.randomInt(1000, 9999));
		} while (this.getCountByCommunityNo(communityNo) > 0);
		ownerDetailInfo.setCommunityNo(communityNo);*/
		ownerDetailInfo.setParentId(parentId);
		ownerDetailInfo.setLevel(level);
		ownerDetailInfo.setRegionName(regionName);
	}

	/*private OwnerDetailInfo checkAndGetRoomInfo(String communityName, OwnerExcel ownerExcel) {
		String group = ownerExcel.getGroup();
		String towerNum = ownerExcel.getTowerNum();
		String apartment = ownerExcel.getApartment();
		String roomNum = ownerExcel.getRoomNum();

		String regionName = communityName.concat(StringPool.SLASH)
			.concat(group).concat(StringPool.SLASH)
			.concat(towerNum).concat(StringPool.SLASH)
			.concat(apartment).concat(StringPool.SLASH)
			.concat(roomNum);

		List<OwnerDetailInfo> list = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery()
			.eq(OwnerDetailInfo::getRegionName, regionName));

		if (list.size() != 1) {
			throw new ServiceException("小区找不到该房号信息");
		}
		return list.get(0);
	}*/

	@Override
	public IPage<OwnerDetailInfoVO> housingPage(IPage page, OwnerDetailInfo ownerDetailInfo) {
		String ids = null;
		if (!AuthUtil.isAdmin()) {
			ids = this.parklotService.getCommunityIds(AuthUtil.getUserId());
		}

		//查出小区信息
		IPage<OwnerDetailInfoVO> housings = baseMapper.housingPage(page, ownerDetailInfo, ids);
		return housings;
	}

	@Override
	public Boolean deleteRecursion(List<Long> ids) {

		List<Long> idList = new ArrayList<>();
		idList.addAll(ids);

		List<OwnerDetailInfo> list = this.listByIds(ids);
		if (Func.isEmpty(list)) {
			return true;
		}

		for (OwnerDetailInfo o : list) {
			List<OwnerDetailInfo> communityList = null;
			if (o.getLevel() == 1) {
				communityList = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getCommunityId, o.getId()));
				if (Func.isEmpty(communityList)) {
					continue;
				}
			}

			if (o.getLevel() == 2) {
				communityList = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getGroupId, o.getId()));
				if (Func.isEmpty(communityList)) {
					continue;
				}
			}

			if (o.getLevel() == 3) {
				communityList = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getFloorId, o.getId()));
				if (Func.isEmpty(communityList)) {
					continue;
				}
			}

			if (o.getLevel() == 4) {
				communityList = this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getApartmentId, o.getId()));
				if (Func.isEmpty(communityList)) {
					continue;
				}
			}
			if (Func.isNotEmpty(communityList)) {
				idList.addAll(communityList.stream().map(OwnerDetailInfo::getId).collect(Collectors.toList()));
			}

		}

		this.deleteLogic(idList);

		return true;
	}

	@Override
	public List<OwnerDetailInfoTreeVO> communityTree(Long parentId, String ids) {
		if (!AuthUtil.isAdmin() && StringUtils.isBlank(ids) && 0 == parentId) {
			ids = this.parklotService.getCommunityIds(AuthUtil.getUserId());
		}
		return ForestNodeMerger.merge(this.baseMapper.getOwnerDetailInfoList(parentId, ids));
	}

	@Override
	public void exportExcelOwnerInfo(HttpServletResponse response, OwnerDetailInfoDTO ownerDetailInfoDTO) {
		ownerDetailInfoDTO.setExportExcelType("1");
		//setCommunityInfo(ownerDetailInfoDTO);
		if (!AuthUtil.isAdmin()) {
			setCommunityInfo(ownerDetailInfoDTO);
		}
		if (org.apache.commons.lang3.StringUtils.isNotBlank(ownerDetailInfoDTO.getCardNum())) {
			String[] num = ownerDetailInfoDTO.getCardNum().split(",");
			for (String item : num) {
				if (Integer.parseInt(item) >= 4) {
					ownerDetailInfoDTO.setIsCardNumOut(4);
					break;
				}
			}
		}
		if (org.apache.commons.lang3.StringUtils.isNotBlank(ownerDetailInfoDTO.getCarNum())) {
			String[] num = ownerDetailInfoDTO.getCarNum().split(",");
			for (String item : num) {
				if (Integer.parseInt(item) >= 4) {
					ownerDetailInfoDTO.setIsCarNumOut(4);
					break;
				}
			}
		}
		List<OwnerDetailInfoVO> oDiVOList = baseMapper.selectOwnerDetailInfoPage(null, ownerDetailInfoDTO);
		List<OwnerInfoExcel> ownerDetailInfoVOList = new ArrayList<>();
		for (OwnerDetailInfoVO item : oDiVOList) {
			String[] regionNames = item.getRegionName().split("/");
			OwnerInfoExcel ownerInfoExcel = new OwnerInfoExcel();
			ownerInfoExcel.setPlates(item.getPlates());
			ownerInfoExcel.setHousingEstate(regionNames[0]);
			ownerInfoExcel.setGroup(regionNames[1]);
			ownerInfoExcel.setTowerNum(regionNames[2]);
			ownerInfoExcel.setApartment(regionNames[3]);
			ownerInfoExcel.setRoomNum(regionNames[4]);
			ownerInfoExcel.setArea(item.getArea());
			ownerInfoExcel.setPhone(item.getOwnerPhone());
			ownerInfoExcel.setName(item.getOwnerName());
			ownerDetailInfoVOList.add(ownerInfoExcel);
		}
		ExcelUtil.export(response, "业主信息批量导出", "业主信息批量导出", ownerDetailInfoVOList, OwnerInfoExcel.class);
	}

	@Override
	public String getCreateUserName(Long createUser) {
		return this.baseMapper.getCreateUserName(createUser);
	}

	@Override
	public List<OwnerDetailInfo> getCommunityList() {
		if (AuthUtil.isAdmin()) {
			return this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getLevel, OwnerEnum.HOUSING_ESTATE.getValue()).eq(OwnerDetailInfo::getStatus, 1));
		} else {
			String ids = this.parklotService.getCommunityIds(AuthUtil.getUserId());
			return this.list(Wrappers.<OwnerDetailInfo>lambdaQuery().eq(OwnerDetailInfo::getLevel, OwnerEnum.HOUSING_ESTATE.getValue()).eq(OwnerDetailInfo::getStatus, 1).in(OwnerDetailInfo::getId, Func.toLongList(ids)));
		}

	}


	/**
	 * 递归获取小区的数据
	 *
	 * @param ownerDetailInfoVOS
	 * @param copy
	 * @param level
	 */
	private void recursionOwner(List<OwnerDetailInfoVO> ownerDetailInfoVOS, OwnerDetailInfoVO copy, Integer level) {
		if (ownerDetailInfoVOS == null) {
			return;
		}

		for (int i = 0; i < ownerDetailInfoVOS.size(); i++) {
			if (level == 2) {
				// 组团数
				copy.setGroupNum(copy.getGroupNum() == null ? 1 : copy.getGroupNum() + 1);
			}
			if (level == 3) {
				//楼栋数
				copy.setTowerNum(copy.getTowerNum() == null ? 1 : copy.getTowerNum() + 1);
			}
			if (level == 4) {
				// 单元数
				copy.setApartmentNum(copy.getApartmentNum() == null ? 1 : copy.getApartmentNum() + 1);
			}
			if (level == 5) {
				// 房间数
				copy.setRoomNum(copy.getRoomNum() == null ? 1 : copy.getRoomNum() + 1);
			}
			recursionOwner(ownerDetailInfoVOS.get(i).getChildren(), copy, level + 1);
		}
	}

	private void checkOwnerExcelData(OwnerInfoImportExcel excel, int row) {
		if (StringUtils.isNotBlank(excel.getGroup()) && excel.getGroup().contains("示例")) {
			LecentAssert.alertException(row + "行示例数据请删除后再上传");
		}
		if (StringUtils.isBlank(excel.getRoomNum())) {
			LecentAssert.alertException(row + "行的房间号不能为空");
		}
		String[] roomNums = excel.getRoomNum().split("-");
		if (roomNums.length != 4) {
			LecentAssert.alertException(row + "行的房间号格式不对");
		} else {
			excel.setTowerNum(roomNums[0].contains("栋") ? roomNums[0] : roomNums[0] + "栋");
			excel.setApartment(roomNums[1].contains("单元") ? roomNums[1] : roomNums[1] + "单元");
		}
		if (StringUtils.isBlank(excel.getName())) {
			LecentAssert.alertException(row + "行的业主姓名不能为空");
		} else {
			excel.setName(excel.getName().replaceAll("；", ";"));
		}
		if (StringUtils.isNotBlank(excel.getPhone())) {
			excel.setPhone(excel.getPhone().replaceAll("；", ";"));
		}
		/*String[] phones = excel.getPhone().split(",");
		for (String phone : phones) {
			//手机号包含字符
			if (!StringUtils.isNumeric(phone)) {
				LecentAssert.alertException(row + "行的联系方式包含特殊字符");
			}
		}*/
		if (StringUtils.isNotBlank(excel.getPlates())) {
			excel.setPlates(excel.getPlates().replaceAll("；", ";"));
		}

	}

	/**
	 * 组织导出数据
	 *
	 * @param id
	 * @return
	 */
	private List<OwnerExcel> exportData(Long id) {
		List<OwnerExcel> ownerExcels = new LinkedList<>();
		//查询该小区下的所有单元信息
		List<OwnerDetailInfoVO> ownerDetailInfos = baseMapper.selectOwnerChildrenInfo(id);
		//level递归深度
		recursionOwnerChildren(ownerDetailInfos, ownerExcels, new LinkedList<>(), 1);
		return ownerExcels;
	}

	/**
	 * 递归获取 OwnerExcel 对应的值
	 *
	 * @param ownerDetailInfoVOS
	 * @param ownerExcels
	 * @param list
	 * @return
	 */
	private List<OwnerExcel> recursionOwnerChildren(List<OwnerDetailInfoVO> ownerDetailInfoVOS, List<OwnerExcel> ownerExcels, List<String> list, int level) {
		if (ownerDetailInfoVOS != null) {
			for (int i = 0; i < ownerDetailInfoVOS.size(); i++) {
				list.add(ownerDetailInfoVOS.get(i).getName());
				// 递归调用
				recursionOwnerChildren(ownerDetailInfoVOS.get(i).getChildren(), ownerExcels, list, level + 1);
				OwnerExcel excel = new OwnerExcel();
				//list保存了树节点的name值
				for (int j = 0; j < list.size(); j++) {
					if (j == 0) {
						excel.setHousingEstate(list.get(0));
					} else if (j == 1) {
						excel.setGroup(list.get(1));
					} else if (j == 2) {
						excel.setTowerNum(list.get(2));
					} else if (j == 3) {
						excel.setApartment(list.get(3));
					} else if (j == 4) {
						excel.setRoomNum(list.get(4));
					}

				}
				//保存递归深度为4的节点，也就是递归到单元
				if (level == 5 && Func.isEmpty(excel.getPhone())) {
					ownerExcels.add(excel);
				}
				//移除最后一个元素，保存前面的路径
				list.remove(ownerDetailInfoVOS.get(i).getName());

			}

		}
		return ownerExcels;
	}

}
