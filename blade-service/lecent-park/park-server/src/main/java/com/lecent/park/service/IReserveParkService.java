package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ReserveParkDTO;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.ReservePark;
import com.lecent.park.vo.ReserveParkVO;
import com.lecent.park.vo.ReserveParkingOrderVO;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;


/**
 * 预约车位表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
public interface IReserveParkService extends BaseService<ReservePark> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param reservePark
	 * @return
	 */
	IPage<ReserveParkVO> selectReserveParkPage(IPage<ReserveParkVO> page, ReserveParkDTO reservePark);

	void setRefundInfo(ReserveParkVO reserveParkVO);

	/**
	 * 停车记录
	 *
	 * @param page
	 * @param reservePark
	 * @return
	 */
	IPage<ReserveParkingOrderVO> selectParkPage(IPage<ReserveParkingOrderVO> page, ReservePark reservePark);

	/**
	 * 预约车位
	 *
	 * @param reservePark
	 * @return
	 */
	ReserveParkVO reserveList(ReserveParkDTO reservePark);

	/**
	 * 通过车牌判断该车是否为有效的预约车
	 *
	 * @param plate
	 * @param parkId
	 * @return
	 */
	ReservePark getValidPlate(String plate, Long parkId);

	/**
	 * 根据id获取订单信息
	 *
	 * @param id
	 * @return
	 */
	ReserveParkVO getVoById(Long id);

	/**
	 * 根据订单编号获取订单信息
	 *
	 * @param orderId
	 * @return
	 */
	ReservePark getOneByOrderId(String orderId);

	/**
	 * 预约过期回调：redis监听
	 *
	 * @param orderId
	 */
	void expiringReserve(String orderId);

	/**
	 * 支付订单的过期回调： redis监听
	 *
	 * @param wxPayOrderId
	 */
	void expiringWxPayOrder(String wxPayOrderId);

	/**
	 * 车辆进场时改变订单状态
	 *
	 * @param id 预约记录ID
	 * @return
	 */
	boolean changeOrderStatus(Long id);

	List<ReservePark> getListByWxPayOrderId(String wxPayOrderId);

	/**
	 * 支付之后的回调
	 *
	 * @param orderId
	 * @return
	 */
	ReserveParkVO reserveMoneyPayAfter(String orderId);

	/**
	 * 预约订单补缴
	 * 对预约订单进行补缴，由于产品设计这里是订单拆分，所以补缴时再生成一个支付订单
	 *
	 * @param resource
	 * @return
	 */
	String repairPayReserveMoney(ReserveParkDTO resource);

	/**
	 * 支付之前的回调
	 *
	 * @param reservePark
	 * @return
	 */
	String reservePayBefore(ReserveParkDTO reservePark);

	/**
	 * 支付成功之后修改订单状态
	 *
	 * @param orderId
	 * @return
	 */
	void orderPayAfter(String orderId);

	/**
	 * 车辆出场后调用修改订单状态
	 *
	 * @param parkingOrder
	 * @return
	 */
	boolean outPartAfter(ParkingOrder parkingOrder);

	/**
	 * 取消预约
	 *
	 * @param id
	 * @return
	 */
	boolean cancelReserve(Long id);

	/**
	 * 退款回调
	 *
	 * @param refundOrderId
	 * @param status
	 * @return
	 */
	boolean refundCallback(String refundOrderId, int status);

	/**
	 * 手动退款
	 *
	 * @param orderId
	 */
	boolean refundReserveOrder(String orderId);

	TempParkingChargeRuleVO getParkChargeRuler(Long parkId);

	/**
	 * 获取详情
	 *
	 * @param id
	 * @return
	 */
	ReservePark getReserveParkById(Long id);
}
