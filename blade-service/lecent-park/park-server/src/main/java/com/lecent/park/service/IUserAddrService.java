package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.UserAddr;
import com.lecent.park.vo.UserAddrVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 发票抬头表 服务类
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
public interface IUserAddrService extends BaseService<UserAddr> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userAddr
	 * @return
	 */
	IPage<UserAddrVO> selectUserAddrPage(IPage<UserAddrVO> page, UserAddrVO userAddr);

	/**
	 * 自定义修增增或修改对象
	 *
	 * @param userAddr
	 * @return
	 */
	boolean customSubmit(UserAddr userAddr);

	List<UserAddrVO> listUserAddr();

}
