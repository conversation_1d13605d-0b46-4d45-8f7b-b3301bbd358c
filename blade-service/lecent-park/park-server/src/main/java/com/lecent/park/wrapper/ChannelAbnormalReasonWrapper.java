package com.lecent.park.wrapper;

import com.lecent.park.entity.ChannelAbnormalReason;
import com.lecent.park.vo.ChannelAbnormalReasonVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 通道异常原因信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-30
 */
public class ChannelAbnormalReasonWrapper extends BaseEntityWrapper<ChannelAbnormalReason, ChannelAbnormalReasonVO>  {

	public static ChannelAbnormalReasonWrapper build() {
		return new ChannelAbnormalReasonWrapper();
 	}

	@Override
	public ChannelAbnormalReasonVO entityVO(ChannelAbnormalReason channelAbnormalReason) {
		ChannelAbnormalReasonVO channelAbnormalReasonVO = BeanUtil.copy(channelAbnormalReason, ChannelAbnormalReasonVO.class);

		//User createUser = UserCache.getUser(channelAbnormalReason.getCreateUser());
		//User updateUser = UserCache.getUser(channelAbnormalReason.getUpdateUser());
		//channelAbnormalReasonVO.setCreateUserName(createUser.getName());
		//channelAbnormalReasonVO.setUpdateUserName(updateUser.getName());

		return channelAbnormalReasonVO;
	}

}
