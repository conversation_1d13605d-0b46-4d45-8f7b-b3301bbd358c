package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.dto.CommunityParklotDTO;
import com.lecent.park.entity.CommunityParklot;
import com.lecent.park.mapper.CommunityParklotMapper;
import com.lecent.park.service.ICommunityParklotService;
import com.lecent.park.vo.CommunityParklotVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社区与车场关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
@Service
public class CommunityParklotServiceImpl extends BaseServiceImpl<CommunityParklotMapper, CommunityParklot> implements ICommunityParklotService {

	@Override
	public IPage<CommunityParklotVO> selectCommunityParklotPage(IPage<CommunityParklotVO> page, CommunityParklotVO communityParklot) {
		return page.setRecords(baseMapper.selectCommunityParklotPage(page, communityParklot));
	}

	@Override
	public List<String> listByCommunityId(Long communityId) {
		List<CommunityParklot> list = lambdaQuery().eq(CommunityParklot::getCommunityId, communityId).list();
		List<String> ret = list.stream().map(CommunityParklot::getParklotId)
			.map(Object::toString).collect(Collectors.toList());
		return Func.isNotEmpty(ret) ? ret : Collections.emptyList();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveRelation(CommunityParklotDTO communityParklotDto) {
		Long communityId = communityParklotDto.getCommunityId();
		remove(Wrappers.<CommunityParklot>lambdaQuery()
			.eq(CommunityParklot::getCommunityId, communityId));
		List<String> parklotIdList = communityParklotDto.getParklotIdList();
		List<CommunityParklot> communityParklotList = parklotIdList.stream().map(id -> {
			CommunityParklot communityParklot = new CommunityParklot();
			communityParklot.setCommunityId(communityId);
			communityParklot.setParklotId(Long.valueOf(id));
			return communityParklot;
		}).collect(Collectors.toList());
		saveBatch(communityParklotList);
		return true;
	}

}
