package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.bizoptlog.AroundOpt;
import com.lecent.park.dto.ChannelDTO;
import com.lecent.park.entity.Channel;
import com.lecent.park.service.IChannelService;
import com.lecent.park.vo.ChannelVO;
import com.lecent.park.wrapper.ChannelWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringEscapeUtils;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static com.lecent.park.cache.ParkCacheNames.LECENT_PARK_CACHE;
import static com.lecent.park.cache.ParkCacheNames.LECENT_PARK_CHANNEL_QR;

/**
 * 车场通道 控制器
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/channel")
@Api(value = "车场通道", tags = "车场通道接口")
public class ChannelController extends BladeController {

	private IChannelService channelService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入channel")
	public R<ChannelVO> detail(Channel channel) {
		Channel detail = channelService.detail(channel);
		return R.data(ChannelWrapper.build().entityVO(detail));
	}

	/**
	 * 详情
	 */
	@GetMapping("/getById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入channel")
	public R<Channel> getById(Channel channel) {
		return R.data(channelService.getById(channel.getId()));
	}

	/**
	 * 根据ID查询（不限制租户）
	 * @param channelId
	 * @return
	 */
	@GetMapping("/getByIdExcludeTenantId")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ID查询（不限制租户）", notes = "传入channelId")
	@TenantIgnore(tenants = TenantConstant.ADMIN_CODE)
	public R<Channel> getByIdExcludeTenantId(Long channelId) {
		return R.data(channelService.getById(channelId));
	}

	/**
	 * 获取车场下所有通道
	 */
	@GetMapping("/getByParkLotId")
	@ApiOperation(value = "获取车场下所有通道", notes = "获取车场下所有通道")
	public R<List<Channel>> getByParkLotId(Long parkLotId) {
		return R.data(channelService.list(Wrappers.<Channel>lambdaQuery().eq(Channel::getParklotId, parkLotId)));
	}

	/**
	 * 分页 车场通道
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入channel")
	public R<IPage<ChannelVO>> list(Channel channel, Query query) {
		IPage<Channel> pages = channelService.page(Condition.getPage(query), Condition.getQueryWrapper(channel));
		return R.data(ChannelWrapper.build().pageVO(pages));
	}

	/**
	 * 运维端分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入channel")
	public R<IPage<ChannelVO>> page(ChannelVO channel, Query query) {
		IPage<ChannelVO> pages = channelService.selectChannelPage(Condition.getPage(query), channel);
		return R.data(pages);
	}

	/**
	 * 物业端分页
	 */
	@GetMapping("/pageByPropertyMgr")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入channel")
	public R<IPage<ChannelVO>> pageByPropertyMgr(ChannelVO channel, Query query) {
		if (!SecureUtil.isAdministrator()) {
			channel.setUserId(SecureUtil.getUserId());
		}
		IPage<ChannelVO> pages = channelService.selectChannelPage(Condition.getPage(query), channel);
		return R.data(pages);
	}

	/**
	 * 新增 车场通道
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入channel")
	public R save(@Valid @RequestBody Channel channel) {
		return R.status(channelService.save(channel));
	}

	/**
	 * 修改 车场通道
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入channel")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, allEntries = true)
	@AroundOpt(serviceClass = IChannelService.class,title = "车场通道配置")
	public R update(@Valid @RequestBody Channel channel) {
		return R.status(channelService.unifySaveOrUpdate(channel));
	}

	/**
	 * 新增或修改 车场通道
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入channel")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, allEntries = true)
	public R submit(@Valid @RequestBody Channel channel) {
		return R.status(channelService.submit(channel));
	}

	/**
	 * 删除 车场通道
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@CacheEvict(cacheNames = {LECENT_PARK_CACHE}, allEntries = true)
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(channelService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 查询用户管理的通道列表
	 *
	 * @return
	 */
	@GetMapping("/userChannels")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "查询用户管理的通道列表", notes = "查询用户管理的通道列表")
	public R userChannels() {
		return R.data(channelService.getChannelByCompanyIdUserId(SecureUtil.getUserId()));
	}

	/**
	 * 保存通道短连接
	 */
	@PostMapping("/saveShortUrl")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "保存通道短连接", notes = "传入channel")
	@CacheEvict(cacheNames = {LECENT_PARK_CHANNEL_QR}, allEntries = true)
	public R saveShortUrl(@Valid @RequestBody ChannelDTO channelDTO) {
		String shortUrl = channelDTO.getShortUrl();
		String unescapeHtml = StringEscapeUtils.unescapeHtml(shortUrl);
		channelDTO.setShortUrl(unescapeHtml);
		Channel channel = channelService.getById(channelDTO.getId());
		LecentAssert.notNull(channel, "不存在此通道");
		return R.status(channelService.unifySaveOrUpdate(channelDTO));
	}


}
