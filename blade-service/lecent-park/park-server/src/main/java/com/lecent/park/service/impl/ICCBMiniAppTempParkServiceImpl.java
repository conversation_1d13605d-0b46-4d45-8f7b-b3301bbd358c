package com.lecent.park.service.impl;

import com.lecent.park.dto.ClientTodo;
import com.lecent.park.dto.req.ReqParkingPay;
import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.service.ClientService;
import com.lecent.park.service.ICCBMiniAppTempParkService;
import com.lecent.park.service.IChannelTodoService;
import com.lecent.park.service.IParkingOrderService;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.wrapper.ChannelTodoWrapper;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021-03-29 14:25
 */
@Service
public class ICCBMiniAppTempParkServiceImpl implements ICCBMiniAppTempParkService {

	@Autowired
	private ClientService clientService;

	@Autowired
	private IChannelTodoService todoService;

	@Autowired
	private IParkingOrderService parkingOrderService;

	@Override
	public ChannelTodoVO detail(String plate) {
		ParkingOrder parkingOrder = parkingOrderService.getLatestRecord(plate);
		ChannelTodoVO channelTodoVO = clientService.createPayTodo(parkingOrder.getParklotId(), plate, "");
		channelTodoVO = clientService.resultDataPackage(channelTodoVO);
		channelTodoVO.setCreateWay(CreateWay.INSIDE.getValue());
		todoService.saveOrUpdate(channelTodoVO);
		return channelTodoVO;
	}

	@Override
	public R pay(ClientTodo clientTodo) {
		ReqParkingPay reqParkingPay = new ReqParkingPay();
		reqParkingPay.setChannelTodoId(clientTodo.getChannelTodoId());
		reqParkingPay.setOpenId(clientTodo.getOpenId());
		reqParkingPay.setReturnUrl(clientTodo.getReturnUrl());
		reqParkingPay.setSource(clientTodo.getSource());
		reqParkingPay.setUserCouponId(clientTodo.getUserCouponId());

		return clientService.payment(reqParkingPay);
	}

	@Override
	public ChannelTodoVO detailByTodoId(String todoId) {
		ChannelTodo channelTodo = todoService.getById(todoId);
		ChannelTodoVO channelTodoVO = ChannelTodoWrapper.build().entityVO(channelTodo);
		return channelTodoVO;
	}

	@Override
	public R payCallback(String outTradeNo) {
		return clientService.payCallback(outTradeNo);
	}
}
