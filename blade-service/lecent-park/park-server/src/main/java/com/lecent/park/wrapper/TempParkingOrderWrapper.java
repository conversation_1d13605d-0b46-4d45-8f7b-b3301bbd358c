package com.lecent.park.wrapper;

import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.vo.TempParkingOrderVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 临停缴费订单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class TempParkingOrderWrapper extends BaseEntityWrapper<TempParkingOrder, TempParkingOrderVO>  {

	public static TempParkingOrderWrapper build() {
		return new TempParkingOrderWrapper();
 	}

	@Override
	public TempParkingOrderVO entityVO(TempParkingOrder parkingOrder) {
		TempParkingOrderVO parkingOrderVO = BeanUtil.copy(parkingOrder, TempParkingOrderVO.class);

		//User createUser = UserCache.getUser(parkingOrder.getCreateUser());
		//User updateUser = UserCache.getUser(parkingOrder.getUpdateUser());
		//parkingOrderVO.setCreateUserName(createUser.getName());
		//parkingOrderVO.setUpdateUserName(updateUser.getName());

		return parkingOrderVO;
	}

}
