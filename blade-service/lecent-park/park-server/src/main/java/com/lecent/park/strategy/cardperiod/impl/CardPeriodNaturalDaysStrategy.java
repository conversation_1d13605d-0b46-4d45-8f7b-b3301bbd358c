package com.lecent.park.strategy.cardperiod.impl;

import com.lecent.park.dto.CardDurationDTO;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.strategy.cardperiod.CardPeriodHelper;
import com.lecent.park.strategy.cardperiod.CardPeriodPeriodStrategy;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.CardDurationVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * 月卡对日续费（续费任意天数）
 */
@Component
public class CardPeriodNaturalDaysStrategy implements CardPeriodPeriodStrategy {





	@Override
	public CardDurationVO getCardPeriod(CardDurationDTO cardDurationDTO, Card card, CardCategory cardCategory) {

		Date startDate = cardDurationDTO.getStartDate();
		Date endDate = cardDurationDTO.getEndDate();

		LecentAssert.isTrue(Func.notNull(startDate) && Func.notNull(endDate), "请选择开始结束时间");
		LecentAssert.isTrue(startDate.before(endDate), "结束时间必须大于开始时间");

		startDate = DateUtils.getStartTimeOfDay(startDate);
		endDate = DateUtils.getEndTimeOfDay(endDate);

		//新的结束时间，用于计算月和天
		Date newEndDate = DateUtils.getStartTimeOfNextDay(endDate);

		//套餐单价
		BigDecimal unitPrice = cardCategory.getUnitPrice();
		BigDecimal priceOfDay = CardPeriodHelper.getPriceOfDay(unitPrice);

		BigDecimal totalAmount;


		long months = cn.hutool.core.date.DateUtil.betweenMonth(startDate, newEndDate, false);

		//续费不足整月
		if (months == 0) {

			//续费天数
			long renewDays = CardPeriodHelper.getRenewalDays(startDate,newEndDate);
			//总费用
			totalAmount = priceOfDay.multiply(new BigDecimal(renewDays)).setScale(0,BigDecimal.ROUND_HALF_UP);

		} else {
			//整月的费用
			BigDecimal monthAmount = unitPrice.multiply(BigDecimal.valueOf(months));

			Date newStartDate = DateUtil.plusMonths(startDate, (int) months);
			long days = DateUtil.between(newStartDate, newEndDate).toDays();

			BigDecimal dayAmount = priceOfDay.multiply(BigDecimal.valueOf(days)).setScale(0,BigDecimal.ROUND_HALF_UP);

			totalAmount = monthAmount.add(dayAmount);
		}

		return CardDurationVO.builder()
			.startDate(startDate)
			.endDate(endDate)
			.amount(totalAmount)
			.build();




	}
}
