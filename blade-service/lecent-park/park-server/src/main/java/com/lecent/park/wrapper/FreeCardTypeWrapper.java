package com.lecent.park.wrapper;

import com.lecent.park.entity.FreeCardType;
import com.lecent.park.vo.FreeCardTypeVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 公司角色表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public class FreeCardTypeWrapper extends BaseEntityWrapper<FreeCardType, FreeCardTypeVO>  {

	public static FreeCardTypeWrapper build() {
		return new FreeCardTypeWrapper();
 	}

	@Override
	public FreeCardTypeVO entityVO(FreeCardType freeCardType) {
		FreeCardTypeVO freeCardTypeVO = BeanUtil.copy(freeCardType, FreeCardTypeVO.class);

		//User createUser = UserCache.getUser(freeCardType.getCreateUser());
		//User updateUser = UserCache.getUser(freeCardType.getUpdateUser());
		//freeCardTypeVO.setCreateUserName(createUser.getName());
		//freeCardTypeVO.setUpdateUserName(updateUser.getName());

		return freeCardTypeVO;
	}

}
