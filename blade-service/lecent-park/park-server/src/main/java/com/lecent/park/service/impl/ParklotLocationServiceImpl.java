package com.lecent.park.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.constant.BoundaryConstant;
import com.lecent.park.common.constant.ParklotLocatinConstant;
import com.lecent.park.dto.ParklotLocationDTO;
import com.lecent.park.dto.distancecalculation.DistanceCalculationDTO;
import com.lecent.park.dto.distancecalculation.Element;
import com.lecent.park.dto.distancecalculation.Row;
import com.lecent.park.en.parklot.ParkLotType;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.ICalculateParkRemainNumberService;
import com.lecent.park.service.IParklotLocationService;
import com.lecent.park.service.IParklotService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.common.utils.LocationUtils;
import org.springblade.core.map.tencent.TencentMapApi;
import org.springblade.core.map.tencent.request.DistanceCalculationRequest;
import org.springblade.core.map.tencent.request.LocationSearchRequest;
import org.springblade.core.map.tencent.response.LocationSearchResponse;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 车场位置服务业务实现层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParklotLocationServiceImpl implements IParklotLocationService {

    @Autowired
    private IParklotService parklotService;
    @Autowired
    private ICalculateParkRemainNumberService iCalculateParkRemainNumberService;
    @Value("${tenCentMap.key}")
    private String tenCentMapKey;


    public void parklotLocation(LocationSearchResponse locationSearchResponse, String from) {

		String locationArrStr = getLocationArrStr(locationSearchResponse);
		log.info("获取坐标点 locationArrStr: {}", locationArrStr);

		DistanceCalculationDTO distanceCalculation = distanceCalculation(tenCentMapKey, from, locationArrStr);
		List<LocationSearchResponse.Poi> data = locationSearchResponse.getData();

		if (Func.notNull(distanceCalculation) && distanceCalculation.getStatus() == 0) {
			List<Row> mRows = distanceCalculation.getResult().getRows();
			//搜索地点结果集
			for (int i = 0; i < data.size(); i++) {
				log.info("需要把多对多查询结果返回到搜索结果里面 mRows={}", mRows);
				LocationSearchResponse.Poi poi = data.get(i);
				poi.setDistance(mRows.get(0).getElements().get(i).getDistance());
			}
		} else {
			if (Func.isNotBlank(from)) {
				String[] split = from.split(",");
				Point2D pointA = new Point2D.Double(Double.parseDouble(split[1]), Double.parseDouble(split[0]));
				for (LocationSearchResponse.Poi poi : data) {
					Point2D pointB = new Point2D.Double(Double.parseDouble(poi.getLocation().getLng()), Double.parseDouble(poi.getLocation().getLat()));
					Double distanceM = LocationUtils.getDistanceM(pointA, pointB);
					poi.setDistance(distanceM);
				}
			}
			log.info("一对多驾驶距离计算失败！,计算返回结果 result={}", distanceCalculation);
		}
	}

    @Override
    public LocationSearchResponse search(ParklotLocationDTO parklotLocationDTO) {
		log.info("搜索请求参数， parklotLocationDTO={}", parklotLocationDTO);

        LocationSearchRequest request = new LocationSearchRequest();
        //默认为停车场为搜索关键字
        request.setKeyword("停车场");
        request.setKey(tenCentMapKey);
        request.setPageIndex(parklotLocationDTO.getPageIndex());
        request.setPageSize(parklotLocationDTO.getPageSize());
        //分类使用默认为停车场
        request.setFilter("category=停车场");

        //是否搜索位置查询
        String from;
        if (parklotLocationDTO.getIsDistanceSearch()) {
            from = parklotLocationDTO.getDistanceSearchDTO().getTo();
        } else {
            from = parklotLocationDTO.getDistanceSearchDTO().getFrom();
        }

        String boundary = BoundaryConstant.BOUNDARY_NEARBY_FRONT.concat(from).concat(BoundaryConstant.BOUNDARY_NEARBY_AFTER);
        request.setBoundary(boundary);

		log.info("locationSearch 请求头为： request={}", request);
        LocationSearchResponse locationSearchResponse = TencentMapApi.locationSearch(request);
        LecentAssert.isTrue(locationSearchResponse.statusIsOk(), locationSearchResponse.getMessage());

        if (locationSearchResponse.getCount() > 0) {
			log.info("搜索结果不为空的情况下,都做一次多对多查询距离,因为搜索结果中的距离结果是直线距离,而多对多查询返回的是驾车距离,搜索结果为 locationSearchResponse={}", locationSearchResponse);
			parklotLocation(locationSearchResponse, parklotLocationDTO.getDistanceSearchDTO().getFrom());
        }
        List<LocationSearchResponse.Poi> data = locationSearchResponse.getData();
        List<LocationSearchResponse.Poi> collect = data.stream().sorted(Comparator.comparing(LocationSearchResponse.Poi::getDistance)).collect(Collectors.toList());
        locationSearchResponse.setData(collect);

        //查询公司停车场,此处逻辑为,如果是当前定位就使用from,如果是搜索就使用to
        List<LocationSearchResponse.Poi> companyParkLot = companyParkLot(parklotLocationDTO.getDistanceSearchDTO().getFrom(), from);
		log.info("================查询公司停车场返回结果为：companyParkLot={}", companyParkLot);
        if (companyParkLot.size() > 0) {
			companyParkLot = companyParkLot.stream().sorted(Comparator.comparing(LocationSearchResponse.Poi::getDistance)).collect(Collectors.toList());
			companyParkLot.addAll(collect);
			locationSearchResponse.setData(companyParkLot);
		}

        log.info("搜索返回结果 locationSearchResponse={}", JsonUtil.toJson(locationSearchResponse));
        return locationSearchResponse;
    }


    private DistanceCalculationDTO distanceCalculation(String key, String from, String to) {
        DistanceCalculationRequest request = new DistanceCalculationRequest();
        request.setKey(key);
        request.setFrom(from);
        request.setTo(to);
        String result = TencentMapApi.distanceCalculation(request);
        DistanceCalculationDTO parse = JsonUtil.parse(result, DistanceCalculationDTO.class);
        log.info("多对多 距离结果集 distanceCalculation : key={}, from={}, to={}, result={}", key, from, to, result);
        return parse;
    }


    private List<LocationSearchResponse.Poi> companyParkLot(String from, String to) {
		List<Parklot> pList = parklotService.list(Wrappers.<Parklot>lambdaQuery().eq(Parklot::getIsDeleted, 0).gt(Parklot::getLat, 0).gt(Parklot::getLng, 0));
		log.info("开始公司停车场距离计算 from={}，to={}", from, to);
		if (Func.isEmpty(pList)) {
			return new ArrayList<>();
		}
		if (Func.isNotBlank(to)) {
			String[] split = to.split(",");
			Point2D pointA = new Point2D.Double(Double.parseDouble(split[1]), Double.parseDouble(split[0]));
			for (Parklot p : pList) {
				Point2D pointB = new Point2D.Double(Double.parseDouble(p.getLng().toString()), Double.parseDouble(p.getLat().toString()));
				Double distance = LocationUtils.getDistanceM(pointA, pointB);
				p.setDistance(distance);
			}
		}
		pList = pList.stream().sorted(Comparator.comparing(Parklot::getDistance)).collect(Collectors.toList());
		if (pList.size() > 5) {
			pList = pList.subList(0, 5);
		}

		//过滤之后的结果,再和当前from坐标点做一次距离计算
		return companyParkLot(pList, from);
	}


    private List<LocationSearchResponse.Poi> companyParkLot(List<Parklot> parkLotList, String from) {
		log.info("============公司停车场过滤结果为：parkLotList={}", parkLotList);
        String collect = getLocationArrStr(parkLotList);
        //再做一次距离计算,计算的距离是from点到过滤结果集的距离
        DistanceCalculationDTO distanceCalculation = distanceCalculation(tenCentMapKey, from, collect);

        List<LocationSearchResponse.Poi> poiList = new ArrayList<>();
        if (Func.notNull(distanceCalculation) && distanceCalculation.getStatus() == 0) {
            List<Row> rows = distanceCalculation.getResult().getRows();
			if (CollectionUtil.isNotEmpty(rows)) {
				List<Element> elements = rows.get(0).getElements();
				for (int i = 0; i < elements.size(); i++) {
					Element element = elements.get(i);
					Long distance = element.getDistance();
					Parklot parklot = parkLotList.get(i);
					LocationSearchResponse.Poi poi = coverParklotToPoi(parklot, distance);
					poiList.add(poi);
				}
			}
		} else {
			if (Func.isNotBlank(from)) {
				String[] split = from.split(",");
				Point2D pointA = new Point2D.Double(Double.parseDouble(split[1]), Double.parseDouble(split[0]));
				for (Parklot p : parkLotList) {
					Point2D pointB = new Point2D.Double(Double.parseDouble(p.getLng().toString()), Double.parseDouble(p.getLat().toString()));
					Double distance = LocationUtils.getDistanceM(pointA, pointB);
					LocationSearchResponse.Poi poi = coverParklotToPoi(p, distance.longValue());
					poiList.add(poi);
				}
			}
		}
        return poiList;
    }


    private LocationSearchResponse.Poi coverParklotToPoi(Parklot parklot, Long distance) {
        LocationSearchResponse.Poi poi = BeanUtil.copy(parklot, LocationSearchResponse.Poi.class);
        //获取当前剩余车位
        LocationSearchResponse.Poi.LocationEntity locationEntity = new LocationSearchResponse.Poi.LocationEntity();
        setParklotStatus(parklot, poi);
        locationEntity.setLat(parklot.getLat().toString());
        locationEntity.setLng(parklot.getLng().toString());
        poi.setId(parklot.getId().toString());
        poi.setTitle(parklot.getName());
        poi.setAddress(parklot.getAddress());
        poi.setCompany(true);
        poi.setDistance(distance);
        poi.setLocation(locationEntity);
        return poi;
    }


	/**
	 * 停车场状态 0 - 不可预约 .1 - 车位充足  2 - 车位紧张
	 *
	 * @param parklot 车场
	 * @param poi     LocationSearchResponse.Poi
	 */
    private void setParklotStatus(Parklot parklot, LocationSearchResponse.Poi poi) {
        int parkRemainNumber = iCalculateParkRemainNumberService.getParkRemainNumber(parklot.getId());

        Integer type = parklot.getParklotType();
        if (parkRemainNumber <= 0 || ObjectUtil.isEmpty(type) || type.equals(ParkLotType.ROAD_IN.getValue())) {
            poi.setRemainingParkingNum(0);
            poi.setRemainingParkingStatus(ParklotLocatinConstant.REMAINING_PARKING_STATUS_NONE);
            poi.setRemainingStatus(ParklotLocatinConstant.REMAINING_STATUS_REFUSE);
        } else {
            //总车位数
            Integer lotAmount = parklot.getTempLotAmount();
            double i = parkRemainNumber / lotAmount.doubleValue();
            if (0.2 < i) {
                poi.setRemainingParkingNum(parkRemainNumber);
                poi.setRemainingParkingStatus(ParklotLocatinConstant.REMAINING_PARKING_STATUS_SUFFICIENT);
            }

            if (0.0 < i && i <= 0.2) {
                poi.setRemainingParkingNum(parkRemainNumber);
                poi.setRemainingParkingStatus(ParklotLocatinConstant.REMAINING_PARKING_STATUS_TENSION);
            }
            poi.setRemainingStatus(ParklotLocatinConstant.REMAINING_STATUS_ALLOW);
        }

        poi.setAllowParkingNum(parklot.getTempLotAmount());

    }


    private String getLocationArrStr(LocationSearchResponse locationSearchResponse) {
		return locationSearchResponse.getData().stream()
			.filter(t -> Objects.nonNull(t.getLocation()))
			.map(t -> t.getLocation().getLat() + StringPool.COMMA + t.getLocation().getLng())
			.collect(Collectors.joining(";"));
    }

    private String getLocationArrStr(List<Parklot> list) {
		return list.stream()
			.map(t -> t.getLat() + StringPool.COMMA + t.getLng())
			.collect(Collectors.joining(";"));
    }
}
