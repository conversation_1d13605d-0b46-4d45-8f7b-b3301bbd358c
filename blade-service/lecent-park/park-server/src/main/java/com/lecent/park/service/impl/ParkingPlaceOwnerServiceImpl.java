package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.BUserPlate;
import com.lecent.park.entity.ParkingPlaceOwner;
import com.lecent.park.mapper.ParkingPlaceOwnerMapper;
import com.lecent.park.service.IBUserPlateService;
import com.lecent.park.service.ICardOrderService;
import com.lecent.park.service.IParkingPlaceOwnerService;
import com.lecent.park.vo.CardOrderVO;
import com.lecent.park.vo.ParkingPlaceOwnerVO;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 我的车位 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
@AllArgsConstructor
public class ParkingPlaceOwnerServiceImpl extends BaseServiceImpl<ParkingPlaceOwnerMapper, ParkingPlaceOwner> implements IParkingPlaceOwnerService {

    private final ICardOrderService cardOrderService;
	private final IBUserPlateService userPlateService;

	@Override
	public IPage<ParkingPlaceOwnerVO> selectParkingPlaceOwnerPage(IPage<ParkingPlaceOwnerVO> page, ParkingPlaceOwnerVO parkingPlaceOwner) {
		return page.setRecords(baseMapper.selectParkingPlaceOwnerPage(page, parkingPlaceOwner));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean submit(ParkingPlaceOwnerVO parkingPlaceOwner) {
		parkingPlaceOwner.setUserId(AuthUtil.getUserId());
		return this.saveOrUpdate(parkingPlaceOwner);
	}

	@Override
	public ParkingPlaceOwnerVO detail(Long id) {
		ParkingPlaceOwner parkingPlaceOwner = getById(id);
		ParkingPlaceOwnerVO placeOwnerVO = BeanUtil.copy(parkingPlaceOwner, ParkingPlaceOwnerVO.class);
		Optional.ofNullable(placeOwnerVO).ifPresent(vo -> {
			List<BUserPlate> list = userPlateService.lambdaQuery().eq(BUserPlate::getUserId, vo.getUserId()).list();
			List<CardOrderVO> orders = new ArrayList<>();
			list.forEach(userPlate -> {
				orders.addAll(cardOrderService.getPayRecord(userPlate.getPlate()));
			});
			vo.setOrders(orders);
		});
		return placeOwnerVO;
	}
}
