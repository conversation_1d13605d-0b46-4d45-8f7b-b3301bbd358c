package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.Channel;
import com.lecent.park.entity.ChannelOpengateLog;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.vo.ChannelOpengateLogVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 异常开闸日志记录表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface IChannelOpengateLogService extends BaseService<ChannelOpengateLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param channelOpengateLog
	 * @return
	 */
	IPage<ChannelOpengateLogVO> selectChannelOpengateLogPage(IPage<ChannelOpengateLogVO> page, ChannelOpengateLogVO channelOpengateLog);

	void createLog(ChannelOpengateLogVO openGateLogVo, Channel channel);


	/**
	 * 紧急开闸抓拍
	 * @param event
	 * @return
	 */
	void triggerUrgentSnap(ParkChannelMessageEvent event);
}
