package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.constant.ChannelConstant;
import com.lecent.park.dto.MessageLogDTO;
import com.lecent.park.entity.*;
import com.lecent.park.mapper.MessageLogMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.MessageLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.DateUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户端消息日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Service
@Slf4j
public class MessageLogServiceImpl extends BaseServiceImpl<MessageLogMapper, MessageLog> implements IMessageLogService {

	@Autowired
	private IBUserPlateService userPlateService;
	@Autowired
	private IParklotService parkLotService;
	@Lazy
	@Autowired
	private IFreeCardService freeCardService;
	@Lazy
	@Autowired
	private IChannelTodoService todoService;

	@Override
	public IPage<MessageLogVO> selectMessageLogPage(IPage<MessageLogVO> page, MessageLogDTO messageLogDto) {
		String phone = SecureUtil.getPhone();
		if (Func.isNotBlank(phone)) {
			messageLogDto.setPhone(phone);
		}
		return page.setRecords(baseMapper.selectMessageLogPage(page, messageLogDto));
	}

	@Override
	public boolean setRead(String id) {
		MessageLog messageLog = getById(id);
		LecentAssert.notNull(messageLog, "id=[" + id + "]的消息不存在");
		messageLog.setMessageStatus(true);
		updateById(messageLog);
		return true;
	}


	@Override
	@Async
	public void channelCarMessageCall(ChannelTodoVO todo) {
		try {
			if (!todoService.isPass(todo.getStatus()) && todo.getReceiveAmount().compareTo(BigDecimal.ZERO) <= 0) {
				return;
			}
			log.info("保存消息通知 todo ={}", todo);
			Parklot parklot = ParkLotCaches.getParkLot(todo.getParklotId());
			String msgContent = todo.getPlate().concat(StringPool.COMMA).concat(DateUtils.format(todo.getDate()));
			if (todo.getChannelType() == ChannelConstant.CHANNEL_TYPE_ENTER) {
				msgContent = msgContent.concat("进入");
			} else {
				msgContent = msgContent.concat("离开");
			}
			if (Func.isNotEmpty(parklot)) {
				msgContent = msgContent.concat("车场【").concat(parklot.getName()).concat("】");
			}

			String typeDes = todo.getTypeDes()==null?"":todo.getTypeDes();
			msgContent = msgContent.concat(",停车类型为【").concat(typeDes).concat("】");
			if (todo.getChannelType() == ChannelConstant.CHANNEL_TYPE_LEAVE) {
				String duration = todo.getDuration()==null?"":todo.getDuration();
				msgContent = msgContent.concat(",").concat("停车时长").concat(duration);
			}
			if (todo.getStatus() == 0 && todo.getReceiveAmount().compareTo(BigDecimal.ZERO) > 0) {
				msgContent = msgContent.concat("请缴费").concat(todo.getReceiveAmount() + "").concat("元");
			}

			this.saveMessage(todo.getPlate(), msgContent, 3);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("保存进出场原因失败:", e);
		}

	}


	@Override
	@Async
	public void tempPayMessageCall(TempParkingOrder order) {
		Parklot parkLot = ParkLotCaches.getParkLot(order.getParklotId());
		String msgContent = order.getPlate().concat(",").concat(DateUtils.format(order.getUpdateTime()));
		if (Func.isNotEmpty(parkLot)) {
			msgContent = msgContent.concat("在车场【").concat(parkLot.getName()).concat("】");
		}
		msgContent = msgContent.concat("缴临停费").concat(order.getPaidAmount() + "").concat("元");
		this.saveMessage(order.getPlate(), msgContent, 2);
	}

	@Override
	public void cardPayMessageCall(Card card, CardOrder cardOrder) {


		String msgContent = card.getPlates().concat(",").concat(DateUtils.format(cardOrder.getUpdateTime()));

		msgContent = msgContent.concat("在车场【").concat(parkLotService.getNameByIds(Func.toLongList(cardOrder.getParklotIds()))).concat("】");

		msgContent = msgContent.concat("缴月卡费").concat(cardOrder.getPayAmount() + "").concat("元");
		this.saveMessage(card.getPlates(), msgContent, 2);
	}


	@Override
	@Async
	public void merchantAuthOverdueMsgCall(ParkMerchantParklotPlate authPlate) {
		Parklot parkLot = ParkLotCaches.getParkLot(authPlate.getParklotId());

		String msgContent = authPlate.getPlate().concat(",").concat(DateUtils.format(authPlate.getAuthEndTime()));
		if (Func.isNotEmpty(parkLot)) {
			msgContent = msgContent.concat("在车场【").concat(parkLot.getName()).concat("】");
		}
		msgContent = msgContent.concat("商户授权已过期");

		this.saveMessage(authPlate.getPlate(), msgContent, 1);
	}

	@Override
	@Async
	public void cardOverdueMsgCall(Card newCard) {
		Parklot parkLot = ParkLotCaches.getParkLot(newCard.getParklotIds());
		String msgContent = newCard.getPlates().concat(",").concat(DateUtils.format(newCard.getEndDate()));
		if (Func.isNotEmpty(parkLot)) {
			msgContent = msgContent.concat("在车场【").concat(parkLot.getName()).concat("】");
		}
		msgContent = msgContent.concat("办理的月卡已过期");

		this.saveMessage(newCard.getPlates(), msgContent, 1);
	}

	@Override
	@Async
	public void freeCardOverdueMsgCall(FreeCardAuth cardAuth) {
		FreeCard freeCard = freeCardService.getById(cardAuth.getFreeCardId());
		if (Func.isEmpty(freeCard)) {
			return;
		}
		Parklot parkLot = ParkLotCaches.getParkLot(cardAuth.getParklotId());
		String msgContent = freeCard.getPlate().concat(",").concat(DateUtils.format(cardAuth.getEndTime()));
		if (Func.isNotEmpty(parkLot)) {
			msgContent = msgContent.concat("在车场【").concat(parkLot.getName()).concat("】");
		}
		msgContent = msgContent.concat("授权的免费卡已过期");

		this.saveMessage(freeCard.getPlate(), msgContent, 1);


	}


	public void saveMessage(String plate, String msgContent, Integer msgType) {
		List<BUserPlate> list = userPlateService.list(Wrappers.<BUserPlate>lambdaQuery()
			.eq(BUserPlate::getPlate, plate)
			.groupBy(BUserPlate::getCreateUser));
		if (Func.isEmpty(list)) {
			return;
		}

		for (BUserPlate userPlate : list) {
			MessageLog msg = new MessageLog();
			msg.setMessageType(msgType);
			msg.setMessageStatus(false);
			msg.setCreateUser(userPlate.getCreateUser());
			msg.setUpdateUser(userPlate.getUpdateUser());
			msg.setPhone(userPlate.getPhone() + "");
			msg.setMessageContent(msgContent);
			this.save(msg);
		}
	}

}
