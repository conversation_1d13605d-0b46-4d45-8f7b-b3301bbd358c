package com.lecent.park.service;


import com.lecent.park.dto.ClientTodo;
import com.lecent.park.vo.ChannelTodoVO;
import org.springblade.core.tool.api.R;

/**
 * <AUTHOR>
 * @since 2020-07-06
 */
public interface ICCBMiniAppTempParkService {


	/**
	 * 获取临停收费详情
	 *
	 * @param plate
	 * @return
	 */
	ChannelTodoVO detail(String plate);

	/**
	 * 临停缴费
	 *
	 * @param clientTodo
	 * @return
	 */
	R pay(ClientTodo clientTodo);

	/**
	 * 根据代办id获取临停缴费详情信息
	 * @param todoId
	 * @return
	 */
	ChannelTodoVO detailByTodoId(String todoId);

	R payCallback(String outTradeNo);

}
