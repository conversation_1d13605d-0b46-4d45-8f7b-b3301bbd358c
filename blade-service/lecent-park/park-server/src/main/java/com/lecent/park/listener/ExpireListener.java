package com.lecent.park.listener;


import com.lecent.park.core.mq.rabbitmq.utils.ObjUtil;
import com.lecent.park.discount.coupon.service.ICouponActivityService;
import com.lecent.park.discount.coupon.service.IUserCouponService;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.ParkMerchantParklotPlate;
import com.lecent.park.service.ICardService;
import com.lecent.park.service.IFreeCardAuthService;
import com.lecent.park.service.IParkMerchantParklotPlateService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE;
import static com.lecent.park.core.mq.rabbitmq.queue.Queues.*;
import static com.lecent.park.core.mq.rabbitmq.routing.SendRoutingKeys.*;

/**
 * 消息过期统一监听类
 * <AUTHOR>
 */
@Component
@Slf4j
@Order
public class ExpireListener {

	@Autowired
	private IParkMerchantParklotPlateService parkLotPlateService;
	@Autowired
	private ICardService cardService;
	@Autowired
	private IFreeCardAuthService freeCardAuthService;
	@Autowired
	private IUserCouponService userCouponService;
	@Autowired
	private ICouponActivityService couponActivityService;

	/**
	 * 商户车牌授权过期消息监听
	 *
	 * @param message
	 */
	@RedisLock(value = MERCHANT_AUTH_PLATE_EXPIRE)
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = MERCHANT_AUTH_PLATE_EXPIRE_QUEUE, durable = "true"),
		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
		key = MERCHANT_AUTH_PLATE_EXPIRE)})
	public void merchantAuthPlateExpireMessageHandler(Message message) {
		try {
			ParkMerchantParklotPlate authPlate = ObjUtil.toObjectMessageCharacter(message, ParkMerchantParklotPlate.class, "UTF-8");
			parkLotPlateService.releaseParkingSpace(authPlate);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("接收商户车牌授权过期消息转码失败：{}", message.getBody());
		}
	}



	/**
	 * 月卡车牌过期消息监听
	 * @param message
	 */
	@RedisLock(value = PARK_CARD_PLATE_EXPIRE)
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = PARK_CARD_PLATE_EXPIRE_QUEUE, durable = "true"),
		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
		key = PARK_CARD_PLATE_EXPIRE)})
	public void cardExpireMessageHandler(Message message) {
		try {
			Card card = ObjUtil.toObjectMessageCharacter(message, Card.class, "UTF-8");
			cardService.updateCardInvalid(card);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("接收月卡车牌过期消息转码失败：{}", message.getBody());
		}
	}

	/**
	 * 月卡暂停消息监听
	 *
	 * @param message
	 */
	@RedisLock(value = PARK_CARD_STOP_START)
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = PARK_CARD_START_STOP_QUEUE, durable = "true"),
		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
		key = PARK_CARD_STOP_START)})
	public void cardStartStopMessageHandler(Message message) {
		try {
			Card card = ObjUtil.toObjectMessageCharacter(message, Card.class, "UTF-8");
			log.info("收到月卡暂停延迟队列消息 message={}", card);
			cardService.updateStartStopStatus(card);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("接收月卡暂停消息转码失败：{}", message.getBody());
		}
	}


	/**
	 * 月卡暂停恢复消息监听
	 *
	 * @param message
	 */
	@RedisLock(value = PARK_CARD_STOP_RECOVER)
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = PARK_CARD_RECOVER_STOP_QUEUE, durable = "true"),
		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
		key = PARK_CARD_STOP_RECOVER)})
	public void cardRecoverStopMessageHandler(Message message) {
		try {
			Card card = ObjUtil.toObjectMessageCharacter(message, Card.class, "UTF-8");
			log.info("收到月卡恢复延迟队列消息 message={}", card);
			cardService.updateRecoverStopStatus(card);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("接收月卡暂停消息转码失败：{}", message.getBody());
		}
	}



	/**
	 * 免费车过期消息监听
	 * @param message
	 */
	@RedisLock(value = PARK_FREE_CARD_PLATE_EXPIRE)
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = PARK_FREE_CARD_PLATE_EXPIRE_QUEUE, durable = "true"),
		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
		key = PARK_FREE_CARD_PLATE_EXPIRE)})
	public void freeCardExpireMessageHandler(Message message) {
		try {
			//	FreeCardAuth cardAuth = ObjUtil.toObjectMessageCharacter(message, FreeCardAuth.class, "UTF-8");
			//	freeCardAuthService.updateCardInvalid(cardAuth);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("接收免费车牌授权过期消息转码失败：{}", message.getBody());
		}
	}

	/**
	 * 将用户的优惠劵改为过期
	 *
	 * @param message 优惠劵id
	 */
//	@RedisLock(value = PARK_COUPON_EXPIRE)
//	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = PARK_COUPON_EXPIRE_QUEUE, durable = "true"),
//		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
//		key = PARK_COUPON_EXPIRE)})
//	public void couponMessageHandler(Message message) {
//
//		Coupon coupon = ObjUtil.toObjectMessageCharacter(message, Coupon.class, "UTF-8");
//
//		log.info("优惠劵：[{}]结束时间:[{}]已到，将用户的优惠劵改为过期!", coupon.getCouponName(), DateUtil.formatDateTime(DateUtil.now()));
//
//		//将用户的优惠劵改为过期
//		userCouponService.updateCouponToOverdue(coupon.getId());
//
//	}

	/**
	 * 当优惠劵活动结束时，自动将活动下线
	 *
	 * @param message 活动id
	 */
//	@RedisLock(value = PARK_COUPON_ACTIVITY_EXPIRE)
//	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = PARK_COUPON_ACTIVITY_EXPIRE_QUEUE, durable = "true"),
//		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
//		key = PARK_COUPON_ACTIVITY_EXPIRE)})
//	public void couponActivityMessageHandler(Message message) {
//
//		CouponActivity couponActivity = ObjUtil.toObjectMessageCharacter(message, CouponActivity.class, "UTF-8");
//
//
//		log.info("优惠劵活动：[{}]结束时间:[{}]已到，将活动下线!", couponActivity.getActivityName(), DateUtil.formatDateTime(couponActivity.getStartDate()));
//
//		//活动下线
//		couponActivityService.closeActivity(couponActivity.getId());
//	}

	/**
	 * 当优惠劵活动开始时，自动将活动改为进行中状态
	 *
	 * @param message 活动id
	 */
//	@RedisLock(value = PARK_COUPON_ACTIVITY_EXPIRE)
//	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = PARK_COUPON_ACTIVITY_START_QUEUE, durable = "true"),
//		exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
//		key = PARK_COUPON_ACTIVITY_START)})
//	public void couponActivityStartMessageHandler(Message message) {
//
//		CouponActivity couponActivity = ObjUtil.toObjectMessageCharacter(message, CouponActivity.class, "UTF-8");
//
//
//		log.info("优惠劵活动：[{}]开始时间:[{}]已到，将活动改为进行中!", couponActivity.getActivityName(), DateUtil.formatDateTime(couponActivity.getStartDate()));
//
//		//活动上线
//		couponActivityService.startActivity(couponActivity.getId());
//	}


}
