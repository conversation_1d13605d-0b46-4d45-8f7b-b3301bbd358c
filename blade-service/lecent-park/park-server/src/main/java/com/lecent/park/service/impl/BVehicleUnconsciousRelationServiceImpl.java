package com.lecent.park.service.impl;

import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.BVehicleUnconsciousRelation;
import com.lecent.park.vo.BVehicleUnconsciousRelationVO;
import com.lecent.park.mapper.BVehicleUnconsciousRelationMapper;
import com.lecent.park.service.IBVehicleUnconsciousRelationService;
import com.lecent.pay.core.enums.PayChannel;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户无感支付信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Service
public class BVehicleUnconsciousRelationServiceImpl extends BaseServiceImpl<BVehicleUnconsciousRelationMapper, BVehicleUnconsciousRelation> implements IBVehicleUnconsciousRelationService {

	@Override
	public IPage<BVehicleUnconsciousRelationVO> selectBVehicleUnconsciousRelationPage(IPage<BVehicleUnconsciousRelationVO> page, BVehicleUnconsciousRelationVO relationVO) {
		return page.setRecords(baseMapper.selectBVehicleUnconsciousRelationRelationPage(page, relationVO));
	}

	@Override
	@Async
	public void createByParkingOrder(ParkingOrder parkingOrder, PayChannel payChannel, boolean isAuthed) {
		if (Func.isBlank(parkingOrder.getPlate())) {
			return;
		}
		// 校验是否已经存在绑定信息
		List<BVehicleUnconsciousRelation> unconsciousList = lambdaQuery().eq(BVehicleUnconsciousRelation::getPlate, parkingOrder.getPlate())
			.eq(BVehicleUnconsciousRelation::getUnconsciousType, payChannel.getName())
			.list();
		// 没有绑定过新增
		BVehicleUnconsciousRelation unconscious = unconsciousList.stream().findFirst()
			.orElse(new BVehicleUnconsciousRelation());
		if (isAuthed && Func.isEmpty(unconscious.getId())) {
			// 开通
			unconscious.setUserId(parkingOrder.getCreateUser());
			unconscious.setVehicleId(parkingOrder.getVehicleId());
			unconscious.setPlate(parkingOrder.getPlate());
			unconscious.setUnconsciousType(payChannel.getName());
			unconscious.setOpenTime(LocalDateTime.now());
			unconscious.setTenantId(parkingOrder.getTenantId());
			unconscious.setStatus(1);
			save(unconscious);
		} else if (!isAuthed && Func.isNotEmpty(unconscious.getId())){
			// 关闭
			unconscious.setCloseTime(LocalDateTime.now());
			unconscious.setStatus(0);
			updateById(unconscious);
		}
	}
}
