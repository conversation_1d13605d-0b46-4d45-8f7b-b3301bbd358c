package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.CustomerInvoiceDTO;
import com.lecent.park.dto.invoice.InvoiceStatementDTO;
import com.lecent.park.entity.ParklotInvoiceRecord;
import com.lecent.park.vo.InvoiceRecordDetailVO;
import com.lecent.park.vo.InvoiceStatementVO;
import com.lecent.park.vo.ParklotInvoiceRecordDetailVO;
import com.lecent.park.vo.ParklotInvoiceRecordVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 车场-发票开票记录 服务类
 *
 * <AUTHOR>
 * @since 2020-09-17
 */
public interface IParklotInvoiceRecordService extends BaseService<ParklotInvoiceRecord> {

	/**
	 * 自定义分页
	 *
	 * @param parklotInvoiceRecord
	 * @return
	 */
	List<ParklotInvoiceRecordVO> selectParklotInvoiceRecordPage(ParklotInvoiceRecordVO parklotInvoiceRecord);


	/**
	 * 开发票
	 *
	 * @param customerInvoiceDTO
	 * @return
	 */
	Boolean invoice(CustomerInvoiceDTO customerInvoiceDTO);

	/**
	 * 失败重试开票
	 * @param customerInvoiceDTO
	 * @return
	 */
	Boolean failAnewInvoice(CustomerInvoiceDTO customerInvoiceDTO);

	Boolean anewInvoice(CustomerInvoiceDTO customerInvoiceDTO);

	/**
	 * 发票详情
	 *
	 * @param parklotInvoiceRecord
	 * @return
	 */
	InvoiceRecordDetailVO invoiceDetail(ParklotInvoiceRecord parklotInvoiceRecord);

	/**
	 * 发票订单详情
	 *
	 * @param vo
	 * @return
	 */
	List<ParklotInvoiceRecordDetailVO> recordDetail(ParklotInvoiceRecordDetailVO vo);

	/**
	 * 根据流水号 批量更新开票记录
	 *
	 * @param records
	 * @return
	 */
	Boolean updateBatchBySerialNum(List<ParklotInvoiceRecord> records);

	List<InvoiceStatementVO> statement(IPage page, InvoiceStatementDTO invoiceStatementDTO);
}
