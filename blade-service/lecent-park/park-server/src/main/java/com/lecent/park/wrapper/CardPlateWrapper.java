package com.lecent.park.wrapper;

import com.lecent.park.entity.CardPlate;
import com.lecent.park.vo.CardPlateVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 套餐车牌表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class CardPlateWrapper extends BaseEntityWrapper<CardPlate, CardPlateVO>  {

	public static CardPlateWrapper build() {
		return new CardPlateWrapper();
 	}

	@Override
	public CardPlateVO entityVO(CardPlate cardPlate) {
		CardPlateVO cardPlateVO = BeanUtil.copy(cardPlate, CardPlateVO.class);

		//User createUser = UserCache.getUser(cardPlate.getCreateUser());
		//User updateUser = UserCache.getUser(cardPlate.getUpdateUser());
		//cardPlateVO.setCreateUserName(createUser.getName());
		//cardPlateVO.setUpdateUserName(updateUser.getName());

		return cardPlateVO;
	}

}
