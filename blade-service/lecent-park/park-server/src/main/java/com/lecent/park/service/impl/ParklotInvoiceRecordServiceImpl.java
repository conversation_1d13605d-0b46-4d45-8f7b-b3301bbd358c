package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.invoice.entity.Body;
import com.lecent.invoice.entity.Config;
import com.lecent.invoice.entity.Title;
import com.lecent.invoice.entity.entity51.Config51;
import com.lecent.invoice.entity.entity51.ConfigAisino;
import com.lecent.invoice.eum.InvoiceListFlag;
import com.lecent.invoice.eum.InvoiceTypeEnum;
import com.lecent.invoice.eum.InvoiveInterfaceTypeEnum;
import com.lecent.invoice.eum.TaxFlagEnum;
import com.lecent.invoice.util.InvoiceUtil;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.dto.CustomerInvoiceDTO;
import com.lecent.park.dto.invoice.InvoiceStatementDTO;
import com.lecent.park.en.invoice.InvoiceStatus;
import com.lecent.park.en.invoice.InvoiceType;
import com.lecent.park.entity.*;
import com.lecent.park.mapper.ParklotInvoiceRecordMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.InvoiceRecordDetailVO;
import com.lecent.park.vo.InvoiceStatementVO;
import com.lecent.park.vo.ParklotInvoiceRecordDetailVO;
import com.lecent.park.vo.ParklotInvoiceRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 车场-发票开票记录 服务实现类
 *
 * <AUTHOR>
 * @since 2020-09-17
 */
@Service
@Slf4j
public class ParklotInvoiceRecordServiceImpl extends BaseServiceImpl<ParklotInvoiceRecordMapper, ParklotInvoiceRecord> implements IParklotInvoiceRecordService {

	/**
	 * 开票相关配置
	 */
	@Autowired
	private IParklotService parklotService;

	/**
	 * 详细明细
	 */
	@Autowired
	private IParklotInvoiceRecordDetailService detailService;

	@Autowired
	private ICardOrderService cardOrderService;

	@Autowired
	private ITempParkingOrderService tempParkingOrderService;

	@Autowired
	private IUserInvoiceConfigService userInvoiceConfigService;

	@Autowired
	private IInvoiceConfigService invoiceConfigService;

	@Autowired
	private IInvoiceParklotService invoiceParklotService;

	@Autowired
	private IUserParklotService userParkLotService;

	@Override
	public List<ParklotInvoiceRecordVO> selectParklotInvoiceRecordPage(ParklotInvoiceRecordVO parklotInvoiceRecord) {
		if (Func.isEmpty(parklotInvoiceRecord.getPhone())) {
			parklotInvoiceRecord.setPhone(AuthUtil.getPhone());
		}
		LecentAssert.notNull(parklotInvoiceRecord.getPhone(), "电话号码不能为空");
		return baseMapper.selectParklotInvoiceRecordPage(null, parklotInvoiceRecord);
	}


	@Override
	public List<ParklotInvoiceRecordDetailVO> recordDetail(ParklotInvoiceRecordDetailVO vo) {
		List<ParklotInvoiceRecordDetailVO> detailVOS = baseMapper.recordDetail(vo);

		//开票记录详情无车牌，车牌替换为月卡
		for (ParklotInvoiceRecordDetailVO invoice : detailVOS) {
			if (Func.isEmpty(invoice.getPlate())) {
				invoice.setPlate("月卡");
			}
		}

		return detailVOS;
	}

	@Override
	public Boolean updateBatchBySerialNum(List<ParklotInvoiceRecord> records) {
		if (Func.isEmpty(records)) {
			return false;
		}
		return baseMapper.updateBatchBySerialNum(records);
	}

	@Override
	public List<InvoiceStatementVO> statement(IPage page, InvoiceStatementDTO queryBean) {
		List<Long> bindParkLotIds = userParkLotService.getCurrentUserBindParkLotIds(queryBean.getParkLotId());
		if (Func.isEmpty(bindParkLotIds)) {
			return Collections.emptyList();
		}
		queryBean.setParkLotId(bindParkLotIds.get(0));

		return baseMapper.statement(page, queryBean);
	}

	@Override
	public InvoiceRecordDetailVO invoiceDetail(ParklotInvoiceRecord parklotInvoiceRecord) {
		return baseMapper.invoiceDetail(parklotInvoiceRecord);
	}


	@Override
	// 对应并发时保证幂等性（出现一单开多次）
	@RedisLock(value = ParkCacheNames.INVOICE_ORDER_LOCK, param = "#customerInvoiceDTO.phone")
	@Transactional(rollbackFor = Exception.class)
	public Boolean invoice(CustomerInvoiceDTO customerInvoiceDTO) {
		// 车场 月卡开票时，月卡可能有多个车场，暂时取第一个
		String parkLotId = this.getFirstParkLotId(customerInvoiceDTO);
		// 订单
		List<ParklotInvoiceRecordDetail> orders = customerInvoiceDTO.getOrders();
		// 抬头
		Long titleId = customerInvoiceDTO.getTitleId();
		Parklot parklot = ParkLotCaches.getParkLot(parkLotId);
		LecentAssert.notNull(parklot, "车场不存在!");
		InvoiceConfig invoiceConfig = invoiceConfigService.queryConfigByParkLotId(parkLotId);
		LecentAssert.notNull(invoiceConfig, "本车场不支持开发票!");
		UserInvoiceConfig invoiceTitle = userInvoiceConfigService.getById(titleId);
		LecentAssert.notNull(invoiceTitle, "选择正确的发票抬头");
		List<CardOrder> cardOrders = getCardOrder(orders, parkLotId);
		List<TempParkingOrder> tempOrders = getTempOrder(orders, parkLotId);
		// 流水号
		String serialNum = getSerialNum();
		ParklotInvoiceRecord record = getInvoiceRecord(serialNum, customerInvoiceDTO, invoiceTitle, cardOrders, tempOrders);
		Title title = getTitle(invoiceTitle, record);
		Config config = getConfig(serialNum, invoiceConfig);
		Body body = getBody(serialNum, invoiceConfig, InvoiceTypeEnum.INVOICE_TYPE_BULE.code, record);
		// 保存开票记录
		saveRecord(record);
		List<ParklotInvoiceRecordDetail> recordDetail = getRecordDetail(record, customerInvoiceDTO);
		if (recordDetail.isEmpty()) {
			LecentAssert.alertException("订单异常");
		}
		saveRecordDetail(recordDetail);
		// 异步调用开票
		ParklotInvoiceRecordServiceImpl bean = SpringUtil.getBean(this.getClass());
		bean.sendInvoiceRequest(config.getInterfaceMode(), title, body, config);
		return true;
	}

	/**
	 * 失败重试开票
	 * @param customerInvoiceDTO
	 * @return
	 */
	@Override
	public Boolean failAnewInvoice(CustomerInvoiceDTO customerInvoiceDTO){
		LecentAssert.notNull(customerInvoiceDTO.getRecordId(), "选择一项发票记录重开");
		// 开票记录
		ParklotInvoiceRecord record = this.getById(customerInvoiceDTO.getRecordId());
		if(record ==null || !InvoiceStatus.KP_FAIL.code.equals(record.getStatus())){
			throw new ServiceException("只有开票失败的才可以重新开！");
		}
		List<ParklotInvoiceRecordDetail> recordDetails = detailService.getRecordDetailList(record.getId());
		if(Func.isEmpty(recordDetails)){
			throw new ServiceException("请选择订单信息！");
		}
		//判断是否已经有成功的发票
		List<Long> orderIds =new ArrayList<>();
		for(ParklotInvoiceRecordDetail recordDetail:recordDetails){
			orderIds.add(Long.parseLong(recordDetail.getOrderId()));
		}
		Integer type=recordDetails.get(0).getOrderType();
		List<ParklotInvoiceRecordDetail> oldRecordDetails= detailService.isIncludeOrder(orderIds,type);
		Set<Long> recordIds=new HashSet<>();
		if(Func.isNotEmpty(oldRecordDetails)){
			for(ParklotInvoiceRecordDetail oldRecordDetail:oldRecordDetails){
				Boolean b=true;
				for(ParklotInvoiceRecordDetail recordDetail:recordDetails){
					if(oldRecordDetail.getId().equals(recordDetail.getId())){
						b=false;
						break;
					}
				}
				if(b){
					recordIds.add(oldRecordDetail.getRecordId());
				}
			}
		}
		if(recordIds.size()>0){
			for(Long recordId:recordIds){
				ParklotInvoiceRecord oldRecord = this.getById(recordId);
				if(oldRecord!=null && !InvoiceStatus.KP_FAIL.code.equals(record.getStatus())){
					throw new ServiceException("该开票记录中的订单存在已经开票成功的发票不可重新开票");
				}
			}
		}
		UserInvoiceConfig invoiceTitle = userInvoiceConfigService.getById(record.getTitleId());
		LecentAssert.notNull(invoiceTitle, "填写发票抬头");
		InvoiceConfig invoiceConfig = invoiceConfigService.queryConfigByParkLotId(record.getParklotId().toString());
		LecentAssert.notNull(invoiceConfig, "本车场不支持开发票!");
		Title title = getTitle(invoiceTitle, record);
		// 从新生成流水号
		String serialNum = getSerialNum();
		record.setFplsh(serialNum);
		Config config = getConfig(record.getFplsh(), invoiceConfig);
		Body body = getBody(record.getFplsh(), invoiceConfig, InvoiceTypeEnum.INVOICE_TYPE_BULE.code, record);
		record.setStatus(InvoiceStatus.KP_PROCEED.code);
		this.updateById(record);
		// 异步调用开票
		ParklotInvoiceRecordServiceImpl bean = SpringUtil.getBean(this.getClass());
		bean.sendInvoiceRequest(config.getInterfaceMode(), title, body, config);
		return true;
	}

	private String getFirstParkLotId(CustomerInvoiceDTO customerInvoiceDTO) {
		String parkLotId = customerInvoiceDTO.getParkLotId();
		LecentAssert.notBlank(parkLotId, "车场参数为空");

		return Func.toStrList(parkLotId).get(0);
	}

	@Override
	public Boolean anewInvoice(CustomerInvoiceDTO customerInvoiceDTO) {
		Long recordId = customerInvoiceDTO.getRecordId();
		LecentAssert.notNull(recordId, "选择一项发票记录重开");
		Long titleId = customerInvoiceDTO.getTitleId();
		// 开票记录
		ParklotInvoiceRecord record = this.getById(recordId);
		LecentAssert.notNull(record, "开票记录不存在");
		String parkLotId = record.getParklotId().toString();
		UserInvoiceConfig invoiceTitle = userInvoiceConfigService.getById(titleId);
		LecentAssert.notNull(invoiceTitle, "填写发票抬头");
		InvoiceConfig invoiceConfig = invoiceConfigService.queryConfigByParkLotId(parkLotId);
		InvoiceParklot invoiceParklot = invoiceParklotService.queryByParkLotId(parkLotId);
		LecentAssert.isTrue(invoiceParklot.getSupportAnew() == 1, "本车场不支持重开");
		List<ParklotInvoiceRecordDetail> recordDetailList = detailService.getRecordDetailList(record.getId());
		// 流水号
		String serialNum = getSerialNum();
		Title title = getTitle(invoiceTitle, record);
		Config config = getConfig(serialNum, invoiceConfig);
		Body body = getBody(serialNum, invoiceConfig, InvoiceTypeEnum.INVOICE_TYPE_RED.code, record);
		ParklotInvoiceRecord newRecord = new ParklotInvoiceRecord();
		newRecord.setStatus(InvoiceStatus.KP_PROCEED.code);
		newRecord.setTitleId(titleId);
		newRecord.setFplsh(serialNum);
		newRecord.setTotalAmount(record.getTotalAmount());
		newRecord.setParklotId(record.getParklotId());
		newRecord.setPhone(record.getPhone());
		newRecord.setEmail(customerInvoiceDTO.getEmail());
		newRecord.setRemark(record.getRemark());
		saveRecord(newRecord);
		List<ParklotInvoiceRecordDetail> newRecordDetails = newRecordDetail(recordDetailList, newRecord);
		saveRecordDetail(newRecordDetails);
		// 异步调用重开票
		ParklotInvoiceRecordServiceImpl bean = SpringUtil.getBean(this.getClass());
		bean.sendAnewInvoiceRequest(config.getInterfaceMode(), title, body, config);
		return true;
	}


	/**
	 * 异步调用开票
	 *
	 * @param type
	 * @param title
	 * @param body
	 * @param config
	 */
	@Async
	public void sendInvoiceRequest(int type, Title title, Body body, Config config) {
		InvoiceUtil.invoice(type, title, body, config);
	}

	/**
	 * 异步调用重开票
	 *
	 * @param type
	 * @param title
	 * @param body
	 * @param config
	 */
	@Async
	public void sendAnewInvoiceRequest(int type, Title title, Body body, Config config) {
		InvoiceUtil.anewInvoice(type, title, body, config);
	}

	// 获取流水号
	private String getSerialNum() {
		return UUID.randomUUID().toString().replaceAll("-", "");
	}

	// 获取配置
	private Config getConfig(String serialNum, InvoiceConfig invoiceConfig) {
		Config config = BeanUtil.copy(invoiceConfig, Config.class);

		if (InvoiveInterfaceTypeEnum.INTERFACE_TYPE_51.code.equals(config.getInterfaceMode())) {
			Config51 config51 = BeanUtil.copy(config, Config51.class);
			config51.setSerialNum(serialNum);
			return config51;
		} else if (InvoiveInterfaceTypeEnum.INTERFACE_TYPE_NUO.code.equals(config.getInterfaceMode())) {

		} else if (InvoiveInterfaceTypeEnum.INTERFACE_TYPE_AISINO.code.equals(config.getInterfaceMode())) {
			ConfigAisino configAisino = BeanUtil.copy(config, ConfigAisino.class);
			configAisino.setSerialNum(serialNum);
			return configAisino;
		}
		return config;
	}

	// 获取body数据
	private Body getBody(String serialNum, InvoiceConfig invoiceConfig, String invoiceType, ParklotInvoiceRecord record) {
		Body body = new Body();
		body.setOrderNo(serialNum);
		body.setTypeCode(invoiceConfig.getTypeCode());
		body.setInvoiceType(invoiceType);
		body.setListFlag(InvoiceListFlag.INVOICE_List_FLAG_N.code);
		String fpDm = StringUtil.isEmpty(record.getFpDm()) ? "" : record.getFpDm();
		body.setOriginalCode(fpDm);
		String fpHm = StringUtil.isEmpty(record.getFpHm()) ? "" : record.getFpHm();
		body.setOriginalNum(fpHm);
		List<Body.InvoiceDetail> details = new ArrayList<>();
		Body.InvoiceDetail invoiceDetail = new Body().new InvoiceDetail();
		invoiceDetail.setGoodsCode(invoiceConfig.getProductCode());
		invoiceDetail.setGoodsName(invoiceConfig.getProductName());
		invoiceDetail.setWithTaxFlag(TaxFlagEnum.TAX_FLAG_Y.code);
		invoiceDetail.setTaxIncludedAmount(record.getTotalAmount().toPlainString());
		invoiceDetail.setTaxRate(invoiceConfig.getTaxRate().toPlainString());
		invoiceDetail.setFavouredPolicyFlag(invoiceConfig.getFavouredPolicyFlag());
		invoiceDetail.setFavouredPolicyName(invoiceConfig.getFavouredPolicyName());
		details.add(invoiceDetail);
		body.setDetails(details);

		return body;
	}

	// 获取抬头
	private Title getTitle(UserInvoiceConfig userInvoiceConfig, ParklotInvoiceRecord record) {
		Title title = new Title();
		title.setTitleName(userInvoiceConfig.getTitleName());
		title.setAddress(userInvoiceConfig.getRegisterAddress());
		title.setBankAccount(userInvoiceConfig.getBankAccount());
		title.setBankName(userInvoiceConfig.getBankName());
		title.setTaxNum(userInvoiceConfig.getTaxNum());
		title.setEmail(record.getEmail());
		title.setPhone(record.getPhone());
		return title;
	}

	/**
	 * 开票记录
	 *
	 * @param cardOrders
	 * @param tempOrders
	 * @return
	 */
	private ParklotInvoiceRecord getInvoiceRecord(String serialNum, CustomerInvoiceDTO customerInvoiceDTO, UserInvoiceConfig titleConfig, List<CardOrder> cardOrders, List<TempParkingOrder> tempOrders) {
		ParklotInvoiceRecord record = new ParklotInvoiceRecord();
		record.setTitleId(titleConfig.getId());
		record.setFplsh(serialNum);
		/**
		 * 月卡订单可能会有多个车场id，取第1个车场保存
		 */
		record.setParklotId(Func.toLong(this.getFirstParkLotId(customerInvoiceDTO)));
		record.setInvoiceType(1);
		record.setStatus(InvoiceStatus.KP_PROCEED.code);
		record.setInvoiceDate(new Date());
		record.setPhone(customerInvoiceDTO.getPhone());
		record.setEmail(customerInvoiceDTO.getEmail());
		record.setRemark(customerInvoiceDTO.getRemark());
		BigDecimal totalAmount = BigDecimal.ZERO;
		for (CardOrder cardOrder : cardOrders) {
			BigDecimal payAmount = cardOrder.getPayAmount();
			totalAmount = totalAmount.add(payAmount);
		}
		for (TempParkingOrder tempOrder : tempOrders) {
			BigDecimal receiveAmount = tempOrder.getReceiveAmount();
			totalAmount = totalAmount.add(receiveAmount);
		}
		record.setTotalAmount(totalAmount.stripTrailingZeros());
		return record;
	}

	/**
	 * 获取月卡的订单详情
	 *
	 * @param orders
	 * @return
	 */
	private List<CardOrder> getCardOrder(List<ParklotInvoiceRecordDetail> orders, String parkLotId) {
		List<Long> cardOrders = new ArrayList<>();
		for (ParklotInvoiceRecordDetail order : orders) {
			Integer type = order.getOrderType();
			String orderId = order.getOrderId();
			if (InvoiceType.CARD_ORDER.code.equals(type)) {
				cardOrders.add(Long.valueOf(orderId));
			}
		}
		if (cardOrders.isEmpty()) {
			return new ArrayList<>();
		}
		List<ParklotInvoiceRecordDetail> includeOrder = detailService.isIncludeOrder(cardOrders, InvoiceType.CARD_ORDER.code);
		LecentAssert.isTrue(includeOrder.isEmpty(), "订单已经开过票");
		/**
		 * 月卡订单中车场ids可能会有多个车场ID，传任何一个车场id都可以查到
		 */
		return cardOrderService.getByIdsParklotId(parkLotId, cardOrders);
		//return cardOrderService.list(Wrappers.<CardOrder>lambdaQuery().eq(CardOrder::getParklotId, parkLotId).in(CardOrder::getId, cardOrders));
	}

	/**
	 * 获取临停的订单详情
	 *
	 * @param orders
	 * @return
	 */
	private List<TempParkingOrder> getTempOrder(List<ParklotInvoiceRecordDetail> orders, String parkLotId) {
		List<Long> tempOrders = new ArrayList<>();
		for (ParklotInvoiceRecordDetail order : orders) {
			Integer type = order.getOrderType();
			String orderId = order.getOrderId();
			if (InvoiceType.TEMP_ORDER.code.equals(type)) {
				tempOrders.add(Long.valueOf(orderId));
			}
		}
		if (tempOrders.isEmpty()) {
			return new ArrayList<>();
		}
		List<ParklotInvoiceRecordDetail> includeOrder = detailService.isIncludeOrder(tempOrders, InvoiceType.TEMP_ORDER.code);
		LecentAssert.isTrue(includeOrder.isEmpty(), "订单已经开过票");
		return tempParkingOrderService.list(Wrappers.<TempParkingOrder>lambdaQuery().eq(TempParkingOrder::getParklotId, parkLotId).in(TempParkingOrder::getId, tempOrders));
	}

	/**
	 * @param record
	 */
	private void saveRecord(ParklotInvoiceRecord record) {
		// 保存或者更新record并且获取到id
		this.save(record);
	}

	private void updateRecordDetail(List<ParklotInvoiceRecordDetail> recordDetails, Integer status) {
		if (status != null) {
			for (ParklotInvoiceRecordDetail parklotInvoiceRecordDetail : recordDetails) {
				parklotInvoiceRecordDetail.setStatus(status);
			}
		}
		detailService.updateBatchById(recordDetails);
	}

	private void saveRecordDetail(List<ParklotInvoiceRecordDetail> recordDetailList) {
		detailService.saveBatch(recordDetailList);
	}

	private List<ParklotInvoiceRecordDetail> newRecordDetail(List<ParklotInvoiceRecordDetail> oldRecordDetailList, ParklotInvoiceRecord record) {
		List<ParklotInvoiceRecordDetail> newRecordDetails = new ArrayList<>();
		for (ParklotInvoiceRecordDetail parklotInvoiceRecordDetail : oldRecordDetailList) {
			ParklotInvoiceRecordDetail detail = new ParklotInvoiceRecordDetail();
			detail.setRecordId(record.getId());
			detail.setParklotId(record.getParklotId());
			detail.setAmount(parklotInvoiceRecordDetail.getAmount());
			detail.setOrderId(parklotInvoiceRecordDetail.getOrderId());
			detail.setPlate(parklotInvoiceRecordDetail.getPlate());
			detail.setOrderType(parklotInvoiceRecordDetail.getOrderType());
			newRecordDetails.add(detail);
		}
		return newRecordDetails;
	}

	/**
	 * 获取开票详情
	 *
	 * @param record
	 * @param customerInvoiceDTO
	 */
	private List<ParklotInvoiceRecordDetail> getRecordDetail(ParklotInvoiceRecord record, CustomerInvoiceDTO customerInvoiceDTO) {
		List<ParklotInvoiceRecordDetail> recordDetailList = new ArrayList<>();
		List<ParklotInvoiceRecordDetail> orders = customerInvoiceDTO.getOrders();
		List<CardOrder> cardOrder = getCardOrder(orders, record.getParklotId().toString());
		List<TempParkingOrder> tempOrder = getTempOrder(orders, record.getParklotId().toString());
		for (CardOrder order : cardOrder) {
			ParklotInvoiceRecordDetail detail = new ParklotInvoiceRecordDetail();
			detail.setFplsh(record.getFplsh());
			detail.setParklotId(Func.toLong(this.getFirstParkLotId(customerInvoiceDTO)));
			detail.setStatus(record.getStatus());
			detail.setInvoiceDate(record.getInvoiceDate());
			detail.setAmount(order.getPayAmount());
			detail.setRecordId(record.getId());
			detail.setOrderId(order.getId().toString());
			detail.setLocalPdfUrl(record.getPdfUrl());
			detail.setOrderType(InvoiceType.CARD_ORDER.code);
			recordDetailList.add(detail);
		}
		for (TempParkingOrder order : tempOrder) {
			ParklotInvoiceRecordDetail detail = new ParklotInvoiceRecordDetail();
			detail.setFplsh(record.getFplsh());
			detail.setParklotId(Func.toLong(customerInvoiceDTO.getParkLotId()));
			detail.setStatus(record.getStatus());
			detail.setInvoiceDate(record.getInvoiceDate());
			detail.setAmount(order.getReceiveAmount());
			detail.setPlate(order.getPlate());
			detail.setRecordId(record.getId());
			detail.setOrderId(order.getId().toString());
			detail.setLocalPdfUrl(record.getPdfUrl());
			detail.setOrderType(InvoiceType.TEMP_ORDER.code);
			recordDetailList.add(detail);
		}
		if (recordDetailList.isEmpty()) {
			LecentAssert.alertException("订单异常");
		}
		return recordDetailList;
	}

}
