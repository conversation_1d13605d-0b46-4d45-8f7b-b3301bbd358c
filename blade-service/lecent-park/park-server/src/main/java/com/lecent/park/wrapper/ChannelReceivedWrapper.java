package com.lecent.park.wrapper;

import com.lecent.park.entity.ChannelReceived;
import com.lecent.park.vo.ChannelReceivedVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 通道设备识别结果包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public class ChannelReceivedWrapper extends BaseEntityWrapper<ChannelReceived, ChannelReceivedVO>  {

	public static ChannelReceivedWrapper build() {
		return new ChannelReceivedWrapper();
 	}

	@Override
	public ChannelReceivedVO entityVO(ChannelReceived channelReceived) {
		ChannelReceivedVO channelReceivedVO = BeanUtil.copy(channelReceived, ChannelReceivedVO.class);

		//User createUser = UserCache.getUser(channelReceived.getCreateUser());
		//User updateUser = UserCache.getUser(channelReceived.getUpdateUser());
		//channelReceivedVO.setCreateUserName(createUser.getName());
		//channelReceivedVO.setUpdateUserName(updateUser.getName());

		return channelReceivedVO;
	}

}
