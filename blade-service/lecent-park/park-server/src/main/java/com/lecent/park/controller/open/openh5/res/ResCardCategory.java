package com.lecent.park.controller.open.openh5.res;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lecent.park.vo.PayMonth;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 卡套餐
 *
 * <AUTHOR> zxr
 * @date : 2022/8/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResCardCategory {
	/**
	 * 卡套餐ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 卡套餐名称
	 */
	private String name;

	/**
	 * 卡套餐价格
	 */
	private BigDecimal unitPrice;

	/**
	 * 可开卡数
	 */
	private Integer surplusCardNum;

	/**
	 * 缴费时段类型
	 * {@link com.lecent.park.en.card.CardPeriodTypeEnum}
	 */
	@ApiModelProperty(value = "缴费时段类型（1-自然月(月初-月末); 2-天对天(8.8-9.8); 3-天对天结束少一天（8.8-9.7);4-日卡）")
	private Integer periodType;

	/**
	 * 卡套餐信息
	 */
	private List<PayMonth> monthPrice;
}
