package com.lecent.park.wrapper;

import com.lecent.park.dto.MerchantParklotAddPlateDTO;
import com.lecent.park.entity.ParkMerchant;
import com.lecent.park.entity.ParkMerchantParklot;
import com.lecent.park.entity.ParkMerchantParklotPlate;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.ParkMerchantParklotPlateVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.User;

/**
 * 商家车场授权车牌表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
public class ParkMerchantParklotPlateWrapper extends BaseEntityWrapper<ParkMerchantParklotPlate, ParkMerchantParklotPlateVO>  {

	public static ParkMerchantParklotPlateWrapper build() {
		return new ParkMerchantParklotPlateWrapper();
 	}

	@Override
	public ParkMerchantParklotPlateVO entityVO(ParkMerchantParklotPlate parkMerchantParklotPlate) {
		ParkMerchantParklotPlateVO parkMerchantParklotPlateVO = BeanUtil.copy(parkMerchantParklotPlate, ParkMerchantParklotPlateVO.class);

		//User createUser = UserCache.getUser(parkMerchantParklotPlate.getCreateUser());
		//User updateUser = UserCache.getUser(parkMerchantParklotPlate.getUpdateUser());
		//parkMerchantParklotPlateVO.setCreateUserName(createUser.getName());
		//parkMerchantParklotPlateVO.setUpdateUserName(updateUser.getName());

		return parkMerchantParklotPlateVO;
	}

	public ParkMerchantParklotPlate addDTO2entity(MerchantParklotAddPlateDTO plateDTO, ParkMerchant merchant, ParkMerchantParklot merchantParklot, User user) {
		if (Func.isEmpty(plateDTO.getAuthDuration())) {
			plateDTO.setAuthDuration(DateUtils.getDiffDate(plateDTO.getAuthStartTime(), plateDTO.getAuthEndTime()));
		}
		ParkMerchantParklotPlate parkMerchantParklotPlate = BeanUtil.copyProperties(plateDTO, ParkMerchantParklotPlate.class);
		parkMerchantParklotPlate.setMerchantId(merchant.getId());
		parkMerchantParklotPlate.setMerchantParklotId(merchantParklot.getId());
		parkMerchantParklotPlate.setTenantId(merchant.getTenantId());
		parkMerchantParklotPlate.setStatus(plateDTO.getStatus());
		parkMerchantParklotPlate.setCreateUserAccount(user.getAccount());
		parkMerchantParklotPlate.setClient(plateDTO.getClient());
		return parkMerchantParklotPlate;
	}
}
