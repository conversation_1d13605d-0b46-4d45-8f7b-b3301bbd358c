package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotInvoiceConfig;
import com.lecent.park.vo.ParklotInvoiceConfigVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
public class ParklotInvoiceConfigWrapper extends BaseEntityWrapper<ParklotInvoiceConfig, ParklotInvoiceConfigVO>  {

	public static ParklotInvoiceConfigWrapper build() {
		return new ParklotInvoiceConfigWrapper();
 	}

	@Override
	public ParklotInvoiceConfigVO entityVO(ParklotInvoiceConfig parklotInvoiceConfig) {
		ParklotInvoiceConfigVO parklotInvoiceConfigVO = BeanUtil.copy(parklotInvoiceConfig, ParklotInvoiceConfigVO.class);

		//User createUser = UserCache.getUser(parklotInvoiceConfig.getCreateUser());
		//User updateUser = UserCache.getUser(parklotInvoiceConfig.getUpdateUser());
		//parklotInvoiceConfigVO.setCreateUserName(createUser.getName());
		//parklotInvoiceConfigVO.setUpdateUserName(updateUser.getName());

		return parklotInvoiceConfigVO;
	}

}
