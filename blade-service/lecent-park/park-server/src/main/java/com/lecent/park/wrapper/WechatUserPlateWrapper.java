package com.lecent.park.wrapper;

import com.lecent.park.entity.WechatUserPlate;
import com.lecent.park.vo.WechatUserPlateVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车位预约配置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public class WechatUserPlateWrapper extends BaseEntityWrapper<WechatUserPlate, WechatUserPlateVO>  {

	public static WechatUserPlateWrapper build() {
		return new WechatUserPlateWrapper();
 	}

	@Override
	public WechatUserPlateVO entityVO(WechatUserPlate wechatUserPlate) {
		return BeanUtil.copy(wechatUserPlate, WechatUserPlateVO.class);
	}

}
