package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotSmsSetting;
import com.lecent.park.vo.ParklotSmsSettingVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场移动短信配置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
public class DParklotSmsSettingWrapper extends BaseEntityWrapper<ParklotSmsSetting, ParklotSmsSettingVO>  {

	public static DParklotSmsSettingWrapper build() {
		return new DParklotSmsSettingWrapper();
 	}

	@Override
	public ParklotSmsSettingVO entityVO(ParklotSmsSetting parklotSmsSetting) {
		ParklotSmsSettingVO parklotSmsSettingVO = BeanUtil.copy(parklotSmsSetting, ParklotSmsSettingVO.class);

		//User createUser = UserCache.getUser(dParklotSmsSetting.getCreateUser());
		//User updateUser = UserCache.getUser(dParklotSmsSetting.getUpdateUser());
		//dParklotSmsSettingVO.setCreateUserName(createUser.getName());
		//dParklotSmsSettingVO.setUpdateUserName(updateUser.getName());

		return parklotSmsSettingVO;
	}

}
