package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.StaffCarDTO;
import com.lecent.park.service.IStaffCarService;
import com.lecent.park.vo.StaffCarVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 员工车辆 控制器
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/staff-car")
@Api(value = "员工车辆", tags = "员工车辆接口")
@Validated
public class StaffCarController extends BladeController {

	private IStaffCarService staffCarService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<StaffCarVO> detail(String id) {
		return R.data(staffCarService.detail(id));
	}

	/**
	 * 自定义分页 员工车辆
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入staffCar")
	public R<IPage<StaffCarVO>> page(StaffCarDTO staffCarDto, Query query) {
		IPage<StaffCarVO> pages = staffCarService.selectStaffCarPage(Condition.getPage(query), staffCarDto);
		return R.data(pages);
	}

	/**
	 * 新增 员工车辆
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入staffCar")
	public R save(@Valid @RequestBody StaffCarDTO staffCarDto) {
		return R.status(staffCarService.customSave(staffCarDto));
	}

	/**
	 * 修改 员工车辆
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入staffCar")
	public R update(@Valid @RequestBody StaffCarDTO staffCarDto) {
		return R.status(staffCarService.customUpdate(staffCarDto));
	}

	/**
	 * 新增关联车场
	 */
	@PostMapping("/relate-parklot")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "新增关联车场", notes = "新增关联车场")
	public R relateParklot(@Valid @RequestBody StaffCarDTO staffCarDto) {
		return R.status(staffCarService.relateParklot(staffCarDto));
	}

	/**
	 * 删除关联车场
	 */
	@PostMapping("/del-relate-parklot")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "删除关联车场", notes = "删除关联车场")
	public R delRelateParklot(@NotNull @RequestParam(value = "relatedId") Long relatedId) {
		return R.status(staffCarService.delRelateParklot(relatedId));
	}

	/**
	 * 删除 员工车辆
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(staffCarService.customDel(Func.toLongList(ids)));
	}

	/**
	 * 下载模板
	 */
	@GetMapping("/downloadTemp")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "下载模板", notes = "下载模板")
	public void downloadTemp(HttpServletResponse response) {
		staffCarService.downloadTemp(response);
	}


	/**
	 * 导出员工车辆
	 */
	@PostMapping("/exportStaff")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "导出员工车辆", notes = "导出员工车辆")
	public void exportStaff(StaffCarDTO staffCarDto, HttpServletResponse response) {
		staffCarService.exportStaff(response, staffCarDto);
	}

	/**
	 * 批量导入员工车辆
	 */
	@PostMapping("/batchImportStaff")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "批量导入员工车辆", notes = "批量导入员工车辆")
	public R<Boolean> batchImportStaff(@RequestParam(value = "staffExcel", required = false) MultipartFile staffExcel,
									   @RequestParam(value = "parklotId") @NotNull(message = "parklotId不能为空") Long parklotId,
									   @RequestParam(value = "ruleId") @NotNull(message = "ruleId不能为空") Long ruleId) {
		return R.status(staffCarService.batchImportStaff(staffExcel, parklotId, ruleId));
	}

}
