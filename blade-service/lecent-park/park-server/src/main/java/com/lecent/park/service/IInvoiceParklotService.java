package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.InvoiceParklot;
import com.lecent.park.vo.InvoiceParklotVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 车场和开票信息关联表 服务类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface IInvoiceParklotService extends BaseService<InvoiceParklot> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param invoiceParklot
	 * @return
	 */
	IPage<InvoiceParklotVO> selectInvoiceParklotPage(IPage<InvoiceParklotVO> page, InvoiceParklotVO invoiceParklot);

	InvoiceParklot queryByParkLotId(String parkLotId);
}
