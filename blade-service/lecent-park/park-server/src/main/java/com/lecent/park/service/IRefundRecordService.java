package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.RefundRecord;
import com.lecent.park.vo.RefundRecordVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 退款记录 服务类
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
public interface IRefundRecordService extends BaseService<RefundRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param refundRecord
	 * @return
	 */
	IPage<RefundRecordVO> selectRefundRecordPage(IPage<RefundRecordVO> page, RefundRecordVO refundRecord);

	RefundRecord getByOutId(String outsideId);

	RefundRecord getOneByOrderId(String refundOrderId);
}
