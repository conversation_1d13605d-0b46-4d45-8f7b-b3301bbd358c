package com.lecent.park.wrapper;

import com.lecent.device.vo.DeviceVO;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.Parklot;
import com.lecent.park.vo.ParkingPlaceVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.List;

/**
 * 车位信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public class ParkingPlaceWrapper extends BaseEntityWrapper<ParkingPlace, ParkingPlaceVO>  {

	public static ParkingPlaceWrapper build() {
		return new ParkingPlaceWrapper();
 	}

	@Override
	public ParkingPlaceVO entityVO(ParkingPlace parkingPlace) {
		ParkingPlaceVO parkingPlaceVO = BeanUtil.copy(parkingPlace, ParkingPlaceVO.class);
		LecentAssert.notNull(parkingPlaceVO, "业务操作失败，原因：对象转换失败");
		return parkingPlaceVO;
	}

	public ParkingPlaceVO entityVO(ParkingPlace parkingPlace, Parklot parklot, List<DeviceVO> devices) {
		ParkingPlaceVO placeVO = entityVO(parkingPlace);
		placeVO.setParklotName(parklot.getName());
		placeVO.setDeviceList(devices);
		return placeVO;
	}
}
