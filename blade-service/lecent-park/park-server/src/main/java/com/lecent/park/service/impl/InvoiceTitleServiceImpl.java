package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.constant.DefaultConstants;
import com.lecent.park.entity.InvoiceTitle;
import com.lecent.park.mapper.InvoiceTitleMapper;
import com.lecent.park.service.IInvoiceTitleService;
import com.lecent.park.vo.InvoiceTitleVO;
import com.lecent.park.wrapper.InvoiceTitleWrapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发票抬头表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Service
public class InvoiceTitleServiceImpl extends BaseServiceImpl<InvoiceTitleMapper, InvoiceTitle> implements IInvoiceTitleService {

	@Override
	public IPage<InvoiceTitleVO> selectInvoiceTitlePage(IPage<InvoiceTitleVO> page, InvoiceTitleVO invoiceTitle) {
		return page.setRecords(baseMapper.selectInvoiceTitlePage(page, invoiceTitle));
	}

	@Override
	public List<InvoiceTitleVO> listUserInvoices() {
		Long userId = SecureUtil.getUserId();
		List<InvoiceTitle> invoiceTitles = lambdaQuery().eq(InvoiceTitle::getUserId, userId).list();
		return Func.isNotEmpty(invoiceTitles) ? InvoiceTitleWrapper.build().listVO(invoiceTitles) : null;
	}

	@Override
	public boolean customUpdate(InvoiceTitle invoiceTitle) {

		updateDefaultTitle(invoiceTitle);

		updateById(invoiceTitle);
		return true;
	}

	@Override
	public Boolean saveTitle(InvoiceTitle invoiceTitle) {
		//更新默认抬头
		updateDefaultTitle(invoiceTitle);

		return save(invoiceTitle);
	}

	/**
	 * 如果当前发票是默认发票，将该用户其他的发票全部改为非默认发票
	 *
	 * @param invoiceTitle
	 */
	private void updateDefaultTitle(InvoiceTitle invoiceTitle) {
		if (invoiceTitle.getIsDefault() == DefaultConstants.NOT_DEFAULT) {
			return;
		}

		List<InvoiceTitleVO> titleList = this.listUserInvoices();
		if (Func.isEmpty(titleList)) {
			return;
		}
		for (InvoiceTitleVO title : titleList) {
			title.setIsDefault(DefaultConstants.NOT_DEFAULT);
			updateById(title);
		}
	}

}
