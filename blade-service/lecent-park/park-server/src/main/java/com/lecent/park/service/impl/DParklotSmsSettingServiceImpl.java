package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.constant.IntervalTypeConstant;
import com.lecent.park.device.config.DeviceConfig;
import com.lecent.park.entity.ParklotExtentSetting;
import com.lecent.park.entity.ParklotSmsSetting;
import com.lecent.park.entity.TempParkingUnpaidOrder;
import com.lecent.park.event.parking.NoticeTempOrderUnPayEvent;
import com.lecent.park.mapper.ParklotSmsSettingMapper;
import com.lecent.park.service.IBUserPlateService;
import com.lecent.park.service.IDParklotSmsSettingService;
import com.lecent.park.service.IParklotExtentSettingService;
import com.lecent.park.service.ITempParkingUnpaidOrderService;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.ParklotSmsSettingVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.resource.feign.ISmsClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 车场移动短信配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-04
 */
@Service
@Slf4j
public class DParklotSmsSettingServiceImpl extends BaseServiceImpl<ParklotSmsSettingMapper, ParklotSmsSetting> implements IDParklotSmsSettingService {

	@Autowired
	private IParklotExtentSettingService parklotExtentSettingService;
	@Autowired
	@Lazy
	private IBUserPlateService userPlateService;
	/**
	 * 设备相关配置
	 */
	@Autowired
	private DeviceConfig deviceConfig;


	/**
	 * 短信服务
	 */
	@Autowired
	private ISmsClient smsClient;

	@Override
	public IPage<ParklotSmsSettingVO> selectDParklotSmsSettingPage(IPage<ParklotSmsSettingVO> page, ParklotSmsSettingVO dParklotSmsSetting) {
		return page.setRecords(baseMapper.selectDParklotSmsSettingPage(page, dParklotSmsSetting));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean add(ParklotSmsSettingVO parklotSmsSettingVO) {
		LecentAssert.notBlank(parklotSmsSettingVO.getSmTextarea().trim(), "短信模板不能为空");
		ArrayList<ParklotSmsSetting> smsList = new ArrayList<>();
		//添加短信时段配置
		this.createParklotSmsSetting(parklotSmsSettingVO, smsList, new ArrayList(), new ArrayList());
		//添加移车短信模板
		parklotSmsSettingVO.setTimeIntervalType(IntervalTypeConstant.MOVING_CARS);
		return createSmsTemplate(parklotSmsSettingVO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addCall(ParklotSmsSettingVO parklotSmsSettingVO) {
		LecentAssert.notBlank(parklotSmsSettingVO.getHistoryCallSmTextarea().trim(), "短信模板不能为空");
		ArrayList<ParklotSmsSetting> callPeriodList = new ArrayList<>();
		ArrayList<ParklotSmsSetting> callhistoryList = new ArrayList<>();
		//添加配置记录
		this.createParklotSmsSetting(parklotSmsSettingVO, new ArrayList(), callPeriodList, callhistoryList);
		//添加催缴短信模板
		parklotSmsSettingVO.setTimeIntervalType(IntervalTypeConstant.HISTORY_CALL);
		return createSmsTemplate(parklotSmsSettingVO);
	}

	@Override
	public ParklotSmsSettingVO getDateDetail(Long parklotId) {
		List<ParklotSmsSetting> parklotSmsSettingList = baseMapper.selectListByParklotId(parklotId);
		ParklotExtentSetting parklotExtentSetting = parklotExtentSettingService.selectExtentSettingByParklotId(parklotId);
		ArrayList<ParklotSmsSettingVO> nonStopPeriodTimes = new ArrayList();
		ArrayList<ParklotSmsSettingVO> historyNonStopPeriodTimes = new ArrayList();
		ArrayList<ParklotSmsSettingVO> callNonStopPeriodTimes = new ArrayList();
		ParklotSmsSettingVO zongParklotSmsSettingVO = new ParklotSmsSettingVO();
		for (int i = 0; i < parklotSmsSettingList.size(); i++) {
			if (parklotSmsSettingList.get(i).getTimeIntervalType().equals(IntervalTypeConstant.MOVING_CARS)) {
				ParklotSmsSettingVO nonStopPeriodParklotSmsSettingVO = new ParklotSmsSettingVO();
				nonStopPeriodParklotSmsSettingVO.setNonParkingTime(parklotSmsSettingList.get(i).getTimeInterval());
				nonStopPeriodParklotSmsSettingVO.setTimeType(parklotSmsSettingList.get(i).getTimeIntervalCompany());
				nonStopPeriodTimes.add(nonStopPeriodParklotSmsSettingVO);
			} else if (parklotSmsSettingList.get(i).getTimeIntervalType().equals(IntervalTypeConstant.DAY_CALL)) {
				ParklotSmsSettingVO callNonStopPeriodParklotSmsSettingVO = new ParklotSmsSettingVO();
				callNonStopPeriodParklotSmsSettingVO.setCallNonParkingTime(parklotSmsSettingList.get(i).getTimeInterval());
				callNonStopPeriodParklotSmsSettingVO.setTimeType(parklotSmsSettingList.get(i).getTimeIntervalCompany());
				zongParklotSmsSettingVO.setStartDisturbTime(parklotSmsSettingList.get(0).getStartDisturbTime());
				zongParklotSmsSettingVO.setEndDisturbTime(parklotSmsSettingList.get(0).getEndDisturbTime());
				zongParklotSmsSettingVO.setIsStart(parklotSmsSettingList.get(0).getIsStart());
				callNonStopPeriodTimes.add(callNonStopPeriodParklotSmsSettingVO);
			} else {
				ParklotSmsSettingVO historyNonStopPeriodParklotSmsSettingVO = new ParklotSmsSettingVO();
				historyNonStopPeriodParklotSmsSettingVO.setHistoryPointOfTime(parklotSmsSettingList.get(i).getPointOfTime());
				historyNonStopPeriodParklotSmsSettingVO.setHistoryNonParkingTime(parklotSmsSettingList.get(i).getTimeInterval());
				historyNonStopPeriodParklotSmsSettingVO.setTimeType(parklotSmsSettingList.get(i).getTimeIntervalCompany());
				historyNonStopPeriodTimes.add(historyNonStopPeriodParklotSmsSettingVO);
			}
		}
		zongParklotSmsSettingVO.setParklotId(parklotId);
		zongParklotSmsSettingVO.setNonStopPeriodTimes(nonStopPeriodTimes);
		zongParklotSmsSettingVO.setCallNonStopPeriodTimes(callNonStopPeriodTimes);
		zongParklotSmsSettingVO.setHistoryNonStopPeriodTimes(historyNonStopPeriodTimes);
		if (parklotExtentSetting != null) {
			zongParklotSmsSettingVO.setSmTextarea(parklotExtentSetting.getMobileSmsTemplate());
			zongParklotSmsSettingVO.setHistoryCallSmTextarea(parklotExtentSetting.getSmsPaymentTemplate());
		}
		return zongParklotSmsSettingVO;
	}

	/**
	 * 创建配置记录
	 *
	 * @param parklotSmsSettingVO
	 * @param smsList
	 * @param callPeriodList
	 * @param callhistoryList
	 */
	public void createParklotSmsSetting(ParklotSmsSettingVO parklotSmsSettingVO, List<ParklotSmsSetting> smsList, List<ParklotSmsSetting> callPeriodList, List<ParklotSmsSetting> callhistoryList) {
		//移车短信提醒
		if (parklotSmsSettingVO != null && parklotSmsSettingVO.getNonStopPeriodTimes() != null && parklotSmsSettingVO.getNonStopPeriodTimes().size() > 0) {
			//删除
			baseMapper.DeletedByParklotId(parklotSmsSettingVO.getParklotId(), IntervalTypeConstant.MOVING_CARS);
			for (int i = 0; i < parklotSmsSettingVO.getNonStopPeriodTimes().size(); i++) {
				ParklotSmsSetting parklotSmsSetting = new ParklotSmsSetting();
				parklotSmsSetting.setParklotId(parklotSmsSettingVO.getParklotId());
				parklotSmsSetting.setTimeInterval(parklotSmsSettingVO.getNonStopPeriodTimes().get(i).getNonParkingTime());
				parklotSmsSetting.setTimeIntervalCompany(parklotSmsSettingVO.getNonStopPeriodTimes().get(i).getTimeType());
				parklotSmsSetting.setTimeIntervalType(IntervalTypeConstant.MOVING_CARS);
				smsList.add(parklotSmsSetting);
			}
			smsList.forEach(parklotSmsSetting -> {
				saveOrUpdate(parklotSmsSetting);
			});
		}
		//当天欠费催缴
		if (parklotSmsSettingVO != null && parklotSmsSettingVO.getCallNonStopPeriodTimes() != null && parklotSmsSettingVO.getCallNonStopPeriodTimes().size() > 0) {
			//删除
			baseMapper.DeletedByParklotId(parklotSmsSettingVO.getParklotId(), IntervalTypeConstant.DAY_CALL);
			for (int i = 0; i < parklotSmsSettingVO.getCallNonStopPeriodTimes().size(); i++) {
				ParklotSmsSetting parklotSmsSetting = new ParklotSmsSetting();
				parklotSmsSetting.setParklotId(parklotSmsSettingVO.getParklotId());
				parklotSmsSetting.setStartDisturbTime(DateUtils.formtH(DateUtils.paramH(parklotSmsSettingVO.getStartDisturbTime())));
				parklotSmsSetting.setEndDisturbTime(DateUtils.formtH(DateUtils.paramH(parklotSmsSettingVO.getEndDisturbTime())));
				parklotSmsSetting.setIsStart(parklotSmsSettingVO.getIsStart());
				parklotSmsSetting.setTimeInterval(parklotSmsSettingVO.getCallNonStopPeriodTimes().get(i).getCallNonParkingTime());
				parklotSmsSetting.setTimeIntervalCompany(parklotSmsSettingVO.getCallNonStopPeriodTimes().get(i).getTimeType());
				parklotSmsSetting.setTimeIntervalType(IntervalTypeConstant.DAY_CALL);
				callPeriodList.add(parklotSmsSetting);
			}
			callPeriodList.forEach(parklotSmsSetting -> {
				saveOrUpdate(parklotSmsSetting);
			});
		}
		//历史欠费催缴
		if (parklotSmsSettingVO != null && parklotSmsSettingVO.getHistoryNonStopPeriodTimes() != null && parklotSmsSettingVO.getHistoryNonStopPeriodTimes().size() > 0) {
			//删除
			baseMapper.DeletedByParklotId(parklotSmsSettingVO.getParklotId(), IntervalTypeConstant.HISTORY_CALL);
			for (int i = 0; i < parklotSmsSettingVO.getHistoryNonStopPeriodTimes().size(); i++) {
				ParklotSmsSetting parklotSmsSetting = new ParklotSmsSetting();
				parklotSmsSetting.setParklotId(parklotSmsSettingVO.getParklotId());
				parklotSmsSetting.setStartDisturbTime(DateUtils.formtH(DateUtils.paramH(parklotSmsSettingVO.getStartDisturbTime())));
				parklotSmsSetting.setEndDisturbTime(DateUtils.formtH(DateUtils.paramH(parklotSmsSettingVO.getEndDisturbTime())));
				parklotSmsSetting.setIsStart(parklotSmsSettingVO.getIsStart());
				parklotSmsSetting.setPointOfTime(parklotSmsSettingVO.getHistoryNonStopPeriodTimes().get(i).getHistoryPointOfTime());
				parklotSmsSetting.setTimeInterval(parklotSmsSettingVO.getHistoryNonStopPeriodTimes().get(i).getHistoryNonParkingTime());
				parklotSmsSetting.setTimeIntervalCompany(parklotSmsSettingVO.getHistoryNonStopPeriodTimes().get(i).getTimeType());
				parklotSmsSetting.setTimeIntervalType(IntervalTypeConstant.HISTORY_CALL);
				callhistoryList.add(parklotSmsSetting);
			}
			callhistoryList.forEach(parklotSmsSetting -> {
				saveOrUpdate(parklotSmsSetting);
			});
		}
	}

	@Override
	public List<ParklotSmsSetting> getByParklotId(Long parklotId) {
		return this.list(Wrappers.<ParklotSmsSetting>lambdaQuery()
			.eq(ParklotSmsSetting::getParklotId, parklotId));
	}

	/**
	 * 创建短信模板
	 *
	 * @param parklotSmsSettingVO
	 * @return
	 */
	public boolean createSmsTemplate(ParklotSmsSettingVO parklotSmsSettingVO) {
		ParklotExtentSetting parklotExtentSetting = parklotExtentSettingService.selectExtentSettingByParklotId(parklotSmsSettingVO.getParklotId());
		ParklotExtentSetting parklotExtentSettingnew = new ParklotExtentSetting();
		if (parklotExtentSetting == null) {
			parklotExtentSettingnew.setParklotId(parklotSmsSettingVO.getParklotId());
			if (parklotSmsSettingVO.getTimeIntervalType().equals(IntervalTypeConstant.MOVING_CARS)) {
				parklotExtentSettingnew.setMobileSmsTemplate(parklotSmsSettingVO.getSmTextarea());
			} else {
				parklotExtentSettingnew.setSmsPaymentTemplate(parklotSmsSettingVO.getHistoryCallSmTextarea());
			}
			return parklotExtentSettingService.saveOrUpdate(parklotExtentSettingnew);
		} else {
			if (parklotSmsSettingVO.getTimeIntervalType().equals(IntervalTypeConstant.MOVING_CARS)) {
				parklotExtentSetting.setMobileSmsTemplate(parklotSmsSettingVO.getSmTextarea());
			} else {
				parklotExtentSetting.setSmsPaymentTemplate(parklotSmsSettingVO.getHistoryCallSmTextarea());
			}
			parklotExtentSetting.setUpdateTime(new Date());
			parklotExtentSetting.setUpdateUser(SecureUtil.getUserId());
			return parklotExtentSettingService.saveOrUpdate(parklotExtentSetting);
		}
	}


	@Override
	public void isSendSms(NoticeTempOrderUnPayEvent event) {
		ITempParkingUnpaidOrderService tempParkingUnpaidOrderService = SpringUtil.getBean(ITempParkingUnpaidOrderService.class);
		TempParkingUnpaidOrder tempParkingOrder = tempParkingUnpaidOrderService.getOne(Wrappers.<TempParkingUnpaidOrder>lambdaQuery()
			.eq(TempParkingUnpaidOrder::getId, event.getOrderNumber())
			.eq(TempParkingUnpaidOrder::getIsDeleted, 0)
			.eq(TempParkingUnpaidOrder::getStatus, 1)
			.ne(TempParkingUnpaidOrder::getAuditStatus, 1));
		List<String> phoneList = userPlateService.getPhoneByPlate(tempParkingOrder.getPlate());
		if (tempParkingOrder != null && CollectionUtil.isNotEmpty(phoneList)) {
			log.info("开始发送催缴短信....");
			//调用下发短信接口(参数：车牌 开始结束时间 未缴金额)
			Map<String, String> contentMap = new HashMap<>(1);
			String content = "[\"".
				concat(tempParkingOrder.getPlate())
				.concat("\",")
				.concat("\"".concat(event.getStartTime().split(" ", -1)[0]).concat("\""))
				.concat(",")
				.concat("\"".concat(event.getStartTime().split(" ", -1)[1]).concat("\""))
				.concat(",")
				.concat("\"".concat(event.getEndTime().split(" ", -1)[0]).concat("\""))
				.concat(",")
				.concat("\"".concat(event.getEndTime().split(" ", -1)[1]).concat("\""))
				.concat(",")
				.concat("\"".concat(event.getUnPayAmount().toString()).concat("\""))
				/*.concat(",")
				.concat("\"".concat("url").concat("\""))*/
				.concat("]");
			contentMap.put("content", content);
			phoneList.forEach(phone -> {
				smsClient.sendTenantMessage(tempParkingOrder.getTenantId(), deviceConfig.getCallReminderTemplate(), JsonUtil.toJson(contentMap), "+86".concat(phone));
			});
		}else{
			log.info("未查到订单信息，event={}",event);

		}
	}
}
