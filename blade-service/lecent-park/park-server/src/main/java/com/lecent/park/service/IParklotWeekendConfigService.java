package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParklotWeekendConfig;
import com.lecent.park.vo.ParklotWeekendConfigVO;
import org.springblade.core.mp.base.BaseService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
public interface IParklotWeekendConfigService extends BaseService<ParklotWeekendConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotWeekendConfig
	 * @return
	 */
	IPage<ParklotWeekendConfigVO> selectParklotWeekendConfigPage(IPage<ParklotWeekendConfigVO> page,
																 ParklotWeekendConfigVO parklotWeekendConfig);

    boolean isConfigWeekend(Long parkLotId);

	void saveConfig(Integer weekendConfig);
}
