package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.MCoupon;
import com.lecent.park.service.IMCouponService;
import com.lecent.park.vo.MCouponVO;
import com.lecent.park.wrapper.MCouponWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 优惠券明细表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/mcoupon")
@Api(value = "优惠券明细表", tags = "优惠券明细表接口")
public class MCouponController extends BladeController {

	private IMCouponService mCouponService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入mCoupon")
	public R<MCouponVO> detail(MCoupon mCoupon) {
		MCoupon detail = mCouponService.getOne(Condition.getQueryWrapper(mCoupon));
		return R.data(MCouponWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 优惠券明细表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入mCoupon")
	public R<IPage<MCouponVO>> list(MCoupon mCoupon, Query query) {
		IPage<MCoupon> pages = mCouponService.page(Condition.getPage(query), Condition.getQueryWrapper(mCoupon));
		return R.data(MCouponWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 优惠券明细表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入mCoupon")
	public R<IPage<MCouponVO>> page(MCouponVO mCoupon, Query query) {
		IPage<MCouponVO> pages = mCouponService.selectMCouponPage(Condition.getPage(query), mCoupon);
		return R.data(pages);
	}

	/**
	 * 新增 优惠券明细表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入mCoupon")
	public R save(@Valid @RequestBody MCoupon mCoupon) {
		return R.status(mCouponService.save(mCoupon));
	}

	/**
	 * 修改 优惠券明细表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入mCoupon")
	public R update(@Valid @RequestBody MCoupon mCoupon) {
		return R.status(mCouponService.updateById(mCoupon));
	}

	/**
	 * 新增或修改 优惠券明细表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入mCoupon")
	public R submit(@Valid @RequestBody MCoupon mCoupon) {
		return R.status(mCouponService.saveOrUpdate(mCoupon));
	}


	/**
	 * 删除 优惠券明细表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(mCouponService.deleteLogic(Func.toLongList(ids)));
	}


}
