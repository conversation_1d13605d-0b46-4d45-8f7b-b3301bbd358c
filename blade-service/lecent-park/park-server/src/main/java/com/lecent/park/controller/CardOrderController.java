package com.lecent.park.controller;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.CardOrderDTO;
import com.lecent.park.entity.CardOrder;
import com.lecent.park.service.ICardOrderService;
import com.lecent.park.service.ICardTempUnpaidOrderService;
import com.lecent.park.vo.CardOrderVO;
import com.lecent.park.vo.CardTempUnpaidOrderVO;
import com.lecent.park.wrapper.CardOrderWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;

/**
 * 套餐订单表 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/cardorder")
@Api(value = "套餐订单表", tags = "套餐订单表接口")
public class CardOrderController extends BladeController {

	private ICardOrderService cardOrderService;

	private ICardTempUnpaidOrderService cardTempUnpaidOrderService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cardOrder")
	public R<CardOrderVO> detail(CardOrder cardOrder) {
		CardOrder detail = cardOrderService.getOne(Condition.getQueryWrapper(cardOrder));
		return R.data(CardOrderWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 套餐订单表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cardOrder")
	public R<IPage<CardOrderVO>> list(CardOrder cardOrder, Query query) {
		IPage<CardOrder> pages = cardOrderService.page(Condition.getPage(query), Condition.getQueryWrapper(cardOrder));
		return R.data(CardOrderWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 套餐订单表
	 */
	@Slave
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入cardOrder")
	public R<IPage<CardOrderVO>> page(CardOrderDTO cardOrderDTO, Query query) {
		IPage<CardOrderVO> pages = cardOrderService.selectCardOrderPage(Condition.getPage(query), cardOrderDTO);
		return R.data(pages);
	}

	/**
	 * 新增 套餐订单表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入cardOrder")
	public R save(@Valid @RequestBody CardOrder cardOrder) {
		return R.status(cardOrderService.save(cardOrder));
	}

	/**
	 * 修改 套餐订单表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入cardOrder")
	public R update(@Valid @RequestBody CardOrder cardOrder) {
		return R.status(cardOrderService.updateById(cardOrder));
	}

	/**
	 * 新增或修改 套餐订单表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入cardOrder")
	public R submit(@Valid @RequestBody CardOrder cardOrder) {
		return R.status(cardOrderService.saveOrUpdate(cardOrder));
	}

	/**
	 * 删除 套餐订单表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cardOrderService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 获取月卡订单合计
	 */
	@Slave
	@GetMapping("/getCardOrderTotal")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "获取月卡订单合计", notes = "获取月卡订单合计")
	public R getCardOrderTotal(CardOrderDTO cardOrderDTO) {
		return R.data(cardOrderService.getCardOrderTotal(cardOrderDTO));
	}

	/**
	 * 导出月卡订单
	 */
	@PostMapping("/exportCardOrder")
	@ApiOperation(value = "导出月卡订单", notes = "导出月卡订单")
	public void exportCardOrder(CardOrderDTO cardOrder, HttpServletResponse response) {
			cardOrderService.exportCardOrder(cardOrder,response);
	}

	@PostMapping("/remove/id")
	@ApiOperation(value = "逻辑删除", notes = "传入id")
	public R<Boolean> remove(@RequestBody CardOrderDTO order) {
		return R.status(cardOrderService.removeOrder(order));
	}
	@GetMapping("/findCardTempUnpaidOrder")
	@ApiOperation(value = "查询异常未缴", notes = "传入id")
	public R<CardTempUnpaidOrderVO> findCardTempUnpaidOrder(@ApiParam(value = "月卡id", required = true) @RequestParam Long cardId){
		CardTempUnpaidOrderVO cardTempUnpaidOrderVO = cardTempUnpaidOrderService.findCardTempUnpaidOrder(cardId);
		return R.data(cardTempUnpaidOrderVO);
	}

}
