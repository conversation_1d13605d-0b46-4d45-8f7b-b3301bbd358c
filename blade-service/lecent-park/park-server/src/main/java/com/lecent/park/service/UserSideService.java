package com.lecent.park.service;

import com.lecent.park.dto.UserSideDTO;
import com.lecent.park.vo.CardOrderVO;
import com.lecent.park.vo.CardTempUnpaidOrderVO;
import com.lecent.payment.vo.OrderQueryResultVO;
import com.lecent.payment.vo.UnifiedOrderResultVO;
import org.springblade.common.payment.PayResult;

import java.util.List;

/**
 * 用戶端服务
 */
public interface UserSideService {


	/**
	 * 用户端续费接口
	 *
	 * @param userSideDto
	 * @return
	 */
	UnifiedOrderResultVO renewalFee(UserSideDTO userSideDto);

	UnifiedOrderResultVO recovery(UserSideDTO userSideDto);


	/**
	 * 用户端支付回调
	 *
	 * @param tradeNo
	 * @return
	 */
	OrderQueryResultVO payCallback(String tradeNo);

	/**
	 * 用户端查询支付状态
	 *
	 * @param outTradeNo
	 * @return
	 */
	Integer orderStatus(String outTradeNo);

	Integer getPayStatus(String todoId);

	/**
	 * 创建续费月卡订单
	 *
	 * @param userSideDto
	 * @return
	 */
	CardOrderVO createRenewalFeeCardOrder(UserSideDTO userSideDto);

	/**
	 * 月卡支付
	 *
	 * @param cardOrderId card order Id
	 * @return
	 */
	UnifiedOrderResultVO cardPayByCardOrderId(Long cardOrderId);

	/**
	 * 月卡订单去支付
	 *
	 * @param cardOrder
	 * @return
	 */
	PayResult toPay(CardOrderVO cardOrder);
	/**
	 * 查询欠缴
	 * @param cardId
	 * @return
	 */

	List<CardTempUnpaidOrderVO> findCardTempUnpaidOrder(Long cardId);

	CardTempUnpaidOrderVO findCardTempUnpaidOrderByItem(Long cardId);
}
