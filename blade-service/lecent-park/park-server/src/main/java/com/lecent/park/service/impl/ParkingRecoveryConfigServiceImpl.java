package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ParkingRecoveryConfig;
import com.lecent.park.mapper.ParkingRecoveryConfigMapper;
import com.lecent.park.service.IParkingRecoveryConfigService;
import com.lecent.park.vo.ParkingRecoveryConfigVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import static com.lecent.park.cache.ParkCacheNames.PARKING_RECOVERY_CONFIG;

/**
 * 停车追缴配置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Service
public class ParkingRecoveryConfigServiceImpl extends BaseServiceImpl<ParkingRecoveryConfigMapper, ParkingRecoveryConfig> implements IParkingRecoveryConfigService {

	@Override
	public IPage<ParkingRecoveryConfigVO> selectParkingRecoveryConfigPage(IPage<ParkingRecoveryConfigVO> page, ParkingRecoveryConfigVO parkingRecoveryConfig) {
		return page.setRecords(baseMapper.selectParkingRecoveryConfigPage(page, parkingRecoveryConfig));
	}

	@Override
	@Cacheable(cacheNames = PARKING_RECOVERY_CONFIG, key = "#parklotId", condition = "#result != null")
	public ParkingRecoveryConfig getByParklotId(Long parklotId) {
		return lambdaQuery().eq(ParkingRecoveryConfig::getParklotId, parklotId).one();
	}

}
