package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.bizoptlog.AroundOpt;
import com.lecent.park.bizoptlog.AroundOptHeader;
import com.lecent.park.bizoptlog.OptTerminalEnum;
import com.lecent.park.bizoptlog.OptTypeEnum;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.dto.CardCategoryDTO;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IBizOptLogService;
import com.lecent.park.service.ICardCategoryService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.CardCategoryVO;
import com.lecent.park.vo.CardRuleVO;
import com.lecent.park.wrapper.CardCategoryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 套餐卡类型 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@Validated
@AllArgsConstructor
@RequestMapping("/cardcategory")
@Api(value = "套餐卡类型", tags = "套餐卡类型接口")
public class CardCategoryController extends BladeController {

	private ICardCategoryService cardCategoryService;

	private IBizOptLogService bizOptLogService;

	private IParklotService parklotService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cardCategory")
	public R<CardCategoryVO> detail(Long id) {
		return R.data(cardCategoryService.getDetail(id));
	}

	/**
	 * 根据车场id获取月卡套餐
	 */
	@GetMapping("/getByParklotId")
	@ApiOperation(value = "根据车场id获取月卡套餐", notes = "根据车场id获取月卡套餐")
	public R<List<CardRuleVO>> getByParklotId(@NotNull(message = "车场id不能为空") @RequestParam Long parklotId) {
		return R.data(cardCategoryService.getByParklotId(parklotId));
	}


	/**
	 * 根据车场id获取月卡套餐
	 */
	@GetMapping("/getByParklotIds")
	@ApiOperation(value = "根据车场id获取月卡套餐", notes = "根据车场id获取月卡套餐")
	public R<List<CardRuleVO>> getByParklotIds(@NotEmpty(message = "车场id不能为空") String parklotIds) {
		return R.data(cardCategoryService.getByParklotIds(Func.toLongList(parklotIds)));
	}


	/**
	 * 获取该车场可以用的收费规则（根据该车场的月卡类型获取对应的收费规则）
	 */
	@GetMapping("/getCardRuleByParklotId")
	@ApiOperation(value = "获取该车场可以用的收费规则", notes = "获取该车场可以用的收费规则")
	public R<List<CardCategory>> getCardRuleByParklotId(Long parklotId) {
		return R.data(cardCategoryService.getCardRuleByParklotId(parklotId));
	}


	/**
	 * 分页 套餐卡类型
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cardCategory")
	public R<IPage<CardCategoryVO>> list(CardCategory cardCategory, Query query) {
		IPage<CardCategory> pages = cardCategoryService.page(Condition.getPage(query), Condition.getQueryWrapper(cardCategory));
		return R.data(CardCategoryWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 套餐卡类型
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入cardCategory")
	public R<IPage<CardCategoryVO>> page(CardCategoryDTO cardCategoryDTO, Query query) {
		IPage<CardCategoryVO> pages = cardCategoryService.selectCardCategoryPage(Condition.getPage(query), cardCategoryDTO);
		return R.data(pages);
	}

	/**
	 * 新增 套餐卡类型
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入cardCategory")
	public R save(@Valid @RequestBody CardCategory cardCategory) {
		return R.status(cardCategoryService.unifySaveOrUpdate(cardCategory));
	}

	/**
	 * 修改 套餐卡类型
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入cardCategory")
	public R update(@Valid @RequestBody CardCategory cardCategory) {
		try {
			if (bizOptLogService.enableSaveLog()) {
				CardCategory category = cardCategoryService.getById(cardCategory.getId());
				Parklot parklot = ParkLotCaches.getParkLot(category.getParklotId());
				OptTypeEnum optTypeEnum = cardCategory.getStatus().equals(1) ? OptTypeEnum.ENABLE : OptTypeEnum.DISABLE;
				bizOptLogService.saveBizLog(SecureUtil.getUser(), "月卡收费规则",
					optTypeEnum.getName() + "<<" + (Func.notNull(parklot) ? parklot.getName() : "")
						+ ">>月卡收费规则," + category.getName(), optTypeEnum.getValue(), OptTerminalEnum.WEB.getValue());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return R.status(cardCategoryService.unifySaveOrUpdate(cardCategory));
	}

	/**
	 * 新增或修改 套餐卡类型
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入cardCategory")
	public R submit(@Valid @RequestBody CardCategoryDTO cardCategoryDTO) {
		return R.status(cardCategoryService.submit(cardCategoryDTO));
	}


	/**
	 * 删除 套餐卡类型
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@AroundOpt(serviceClass = ICardCategoryService.class, optType = OptTypeEnum.DEL,
		title = "月卡收费规则", idName = "ids", customMsgTemp = "删除月卡收费规则,{}", recordIdentification = "name",
		headers = {
			@AroundOptHeader(headerClass = IParklotService.class, headerId = "parklotId", headerName = "name")
		}
	)
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cardCategoryService.removeCardCategory(Func.toLongList(ids)));
	}

	/**
	 * dict
	 */
	@GetMapping("/CardCategoryDict")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cardCategory")
	public R<List<CardCategory>> CardCategoryDict() {
		List<CardCategory> list = cardCategoryService.CardCategoryDict(AuthUtil.getUserId());
		return R.data(list);
	}


}
