package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lecent.park.entity.WechatUserPlate;
import com.lecent.park.mapper.WechatUserPlateMapper;
import com.lecent.park.service.IWechatUserPlateService;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 车位预约配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class WechatUserPlateServiceImpl extends BaseServiceImpl<WechatUserPlateMapper, WechatUserPlate> implements IWechatUserPlateService {


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean bindPlace(WechatUserPlate wechatUserPlate) {
		List<WechatUserPlate> list = list(new QueryWrapper<WechatUserPlate>().lambda().eq(WechatUserPlate::getOpenId, wechatUserPlate.getOpenId()));
		int weChatBindNum = 3;
		if (list.size() >= weChatBindNum) {
			throw new ServiceException("该微信号绑定车位达到上限,每个微信号最多可绑定3个车位");
		}
		return save(wechatUserPlate);
	}

}
