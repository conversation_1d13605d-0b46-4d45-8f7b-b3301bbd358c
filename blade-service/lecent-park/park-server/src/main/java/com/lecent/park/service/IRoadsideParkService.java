package com.lecent.park.service;


import com.lecent.park.access.ParkTodoContext;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.TempParkingOrder;

/**
 * 路边停车
 */
public interface IRoadsideParkService {

	/**
	 * 场外缴费-自助缴费成功回调
	 *
	 * @param tempParkingOrder 临停订单
	 * @return boolean
	 */
	boolean absentPaymentSuccessfulCallback(TempParkingOrder tempParkingOrder);

	/**
	 * 更新临停订单
	 * @param tempParkingOrder 临停订单
	 * @param todo 待办事项
	 * @return 更新是否成功
	 */
	Boolean updateTempParkingOrder(TempParkingOrder tempParkingOrder, ChannelTodo todo);

	/**
	 * 获取路边停车待办上下文
	 * @param place 停车场
	 * @return 路边停车待办上下文
	 */
	ParkTodoContext getRoadSideTodoContext(ParkingPlace place);
}
