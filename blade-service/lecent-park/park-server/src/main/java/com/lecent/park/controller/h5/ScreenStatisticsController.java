package com.lecent.park.controller.h5;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.lecent.park.entity.ScreenParklot;
import com.lecent.park.service.IScreenStatisticsService;
import com.lecent.park.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description： 盘州大屏统计展示
 * <AUTHOR>
 * @Date: 2020/6/11 11:07
 */
@Slave
@RestController
@AllArgsConstructor
@RequestMapping("/screenStatistics")
@Api(value = "盘州大屏统计展示", tags = "盘州大屏统计展示")
public class ScreenStatisticsController {

	private IScreenStatisticsService screenStatisticsService;

	/**
	 * 近7天流量分析（每个车场近七天的出入场数量统计）
	 */
	@GetMapping("/lastWeekParkingOrder")
	@ApiOperation(value = "近7天流量分析", notes = "近7天流量分析")
	public R<ParkLastWeekVO> lastWeekParkingOrder(String parklotId) {
		return R.data(screenStatisticsService.lastWeekParkingOrder(parklotId));
	}

	/**
	 * 停车时段分析
	 */
	@GetMapping("/stopDuration")
	@ApiOperation(value = "停车时段分析", notes = "停车时段分析")
	public R<List<StopDurationVO>> stopDuration(String parklotId) {
		return R.data(screenStatisticsService.stopDuration(parklotId));
	}

	/**
	 * 大屏所有车场数据  包含经纬度
	 */
	@GetMapping("/screenParklots")
	@ApiOperation(value = "大屏所有车场数据", notes = "大屏所有车场数据")
	public R<List<ScreenParklot>> screenParklots() {
		return R.data(screenStatisticsService.screenParklots());
	}

	/**
	 * 大屏首页总统计数据
	 */
	@GetMapping("/totalStatistics")
	@ApiOperation(value = "大屏首页总统计数据", notes = "大屏首页总统计数据")
	public R<TotalStatisticsVO> totalStatistics(String parklotId) {
		return R.data(screenStatisticsService.totalStatistics(parklotId));
	}

	/**
	 * 剩余车位最少top5
	 */
	@GetMapping("/remainPlaceTopFive")
	@ApiOperation(value = "剩余车位最少top5", notes = "剩余车位最少top5")
	public R<List<PlaceRemainNumVO>> remainPlaceTopFive() {
		return R.data(screenStatisticsService.remainPlaceTopFive());
	}

	/**
	 * 财务分析
	 */
	@GetMapping("/financialAnalysis")
	@ApiOperation(value = "财务分析", notes = "财务分析")
	public R<List<List<String>>> financialAnalysis(String parklotId) {
		return R.data(screenStatisticsService.financialAnalysis(parklotId));
	}

	/**
	 * 收入环比
	 */
	@GetMapping("/financialRate")
	@ApiOperation(value = "收入环比", notes = "收入环比")
	public R<FinancialRate> financialRate(String parklotId) {
		return R.data(screenStatisticsService.financialRate(parklotId));
	}

	/**
	 * 泊位利用率分析
	 */
	@GetMapping("/parkUseRate")
	@ApiOperation(value = "parkUseRate", notes = "parkUseRate")
	public R<ParkLastWeekRateVO> parkUseRate(String parklotId) {
		return R.data(screenStatisticsService.parkUseRate(parklotId));
	}

	/**
	 * 泊位停车饱和度
	 */
	@GetMapping("/parkStopRate")
	@ApiOperation(value = "泊位停车饱和度", notes = "泊位停车饱和度")
	public R<ParkStopRateVO> parkStopRate(String parklotId) {
		return R.data(screenStatisticsService.parkStopRate(parklotId));
	}

}
