package com.lecent.park.service.impl;

import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.ParkingConstant;
import com.lecent.park.dto.BabyScanDTO;
import com.lecent.park.en.PassTemplate;
import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.entity.*;
import com.lecent.park.service.*;
import com.lecent.park.service.payment.PaymentDelayMessageHandler;
import com.lecent.pay.core.enums.PayScene;
import com.lecent.pay.core.enums.PayWay;
import com.lecent.payment.dto.UnifiedOrderDTO;
import com.lecent.payment.dto.UnifiedOrderInfoDTO;
import com.lecent.payment.feign.IPaymentClient;
import com.lecent.payment.vo.UnifiedOrderResultVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.en.payment.PayStatus;
import org.springblade.common.entity.ExRout;
import org.springblade.common.payment.PayResult;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Service;

import static com.lecent.park.core.mq.rabbitmq.MessageConstant.LECENT_PARK_PAY_PREFIX;
import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_PAY_EXCHANGE_NAME;

/**
 * 设置设置表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
@Service
@Slf4j
@AllArgsConstructor
public class ParkingBabyServiceImpl implements ParkingBabyService {

	private final IPaymentClient paymentClient;

	private final IParklotService parklotService;

	private final IChannelTodoService channelTodoService;

	private final ITempParkingOrderService tempParkingOrderService;

	private final IParklotPayWayService payWayService;

	private final PaymentDelayMessageHandler paymentDelayMessage;

	/**
	 * 停车宝宝被扫支付（用户出示付款码，商家用扫码枪扫描）
	 *
	 * @param babyScanDto 主扫请求参数
	 * @return 被扫支付结果
	 */
	@Override
	public boolean scanPayOrder(BabyScanDTO babyScanDto) {
		//下单
		R<UnifiedOrderResultVO> result = submitUnifiedOrder(babyScanDto, false);
		LecentAssert.isTrue(result.isSuccess(), "支付失败，失败原因：" + result.getMsg());

		return true;
	}

	@Override
	public PayResult queryPayResult(String tradeNo) {
		return tempParkingOrderService.queryParkingOrderPayResult(tradeNo);
	}

	@Override
	public PayResult createOrder(BabyScanDTO babyScanDto) {
		//下单
		R<UnifiedOrderResultVO> r = submitUnifiedOrder(babyScanDto, true);
		LecentAssert.isTrue(r.isSuccess(), "下单失败，失败原因：" + r.getMsg());

		UnifiedOrderResultVO resultVO = r.getData();
		return PayResult.builder().tradeNo(resultVO.getOutTradeNo())
			.totalFee(resultVO.getTotalFee())
			.payData(resultVO.getPayData())
			.payStatus(PayStatus.UNPAID.getKey())
			.build();
	}

	/**
	 * 下单
	 *
	 * @param async 是否异步下单
	 * @return 下单结果
	 */
	private R<UnifiedOrderResultVO> submitUnifiedOrder(BabyScanDTO babyScanDto, boolean async) {
		// 提前校验车场不为空
		Parklot parklot = parklotService.getParklotByNo(babyScanDto.getParklotNo());
		LecentAssert.notNull(parklot, "没有找到对应的停车场");

		Channel channel = ParkLotCaches.getChannelByNo(parklot.getId(), Integer.parseInt(babyScanDto.getChannelNo()));
		LecentAssert.notNull(channel, "没有找到对应的通道！");

		ChannelTodo lastExitTodo = channelTodoService.getLastExitTodo(parklot.getId(), channel.getId(), babyScanDto.getPlate());
		LecentAssert.isTrue(lastExitTodo != null, "暂无待支付订单，请稍后再试!");

		if (!TodoContext.isNoPlate(lastExitTodo.getPlate())) {
			LecentAssert.isFalse(PassTemplate.BLACK_LIST.name().equals(lastExitTodo.getType()), PassTemplate.BLACK_LIST.getMsg());
			LecentAssert.isFalse(PassTemplate.YELLOW_LIST.name().equals(lastExitTodo.getType()), PassTemplate.YELLOW_LIST.getMsg());
		}

		lastExitTodo.setCreateWay(CreateWay.CHANNEL.getValue());
		channelTodoService.saveOrUpdate(lastExitTodo);

		lastExitTodo.setRemark("停车宝宝被扫支付");
		ParklotPayWay payWay = payWayService.existPayWayByParkLotId(parklot.getId());
		lastExitTodo.setPayType(PayWay.getPayCode(payWay.getMerchantType(), PayScene.PAY_MICRO));
		TempParkingOrder tempParkingOrder = tempParkingOrderService.createOrder(lastExitTodo, parklot);
		R<UnifiedOrderResultVO> r = null;
		try {
			UnifiedOrderDTO unifiedOrder = new UnifiedOrderDTO();
			unifiedOrder.setAsync(async);
			unifiedOrder.setAttach("停车费用支付-停车宝宝");
			unifiedOrder.setAuthCode(babyScanDto.getAuthCode());
			unifiedOrder.setBody(parklot.getName() + "---" + tempParkingOrder.getPlate());
			unifiedOrder.setOutTradeNo(tempParkingOrder.getTradeNo());
			unifiedOrder.setTenantId(parklot.getTenantId());
			unifiedOrder.setTotalFee(tempParkingOrder.getReceiveAmount().toString());
			unifiedOrder.setExRout(new ExRout(LECENT_PARK_PAY_EXCHANGE_NAME, LECENT_PARK_PAY_PREFIX));

			unifiedOrder.setTradeType(PayScene.PAY_MICRO.name());
			unifiedOrder.setMerchantIds(payWay.getMerchantId());
			unifiedOrder.setTimeOut(ParkingConstant.PAY_TEMP_PARKING_TIME_OUT);

			// 同步提前添加主查
			if (!async) {
				paymentDelayMessage.addTempParkingPayQuery(tempParkingOrder.getTradeNo());
			}

			// 工行支付必须传递车牌
			UnifiedOrderInfoDTO extend = new UnifiedOrderInfoDTO();
			extend.setAuthNo(tempParkingOrder.getPlate());
			unifiedOrder.setExtend(extend);
			// 发起支付
			r = paymentClient.payUnifiedOrder(unifiedOrder);
			log.info("被扫支付下单调用结束，返回结果result={}", r);
			if (r.isSuccess()) {
				// 异步添加主查
				if (async) {
					paymentDelayMessage.addTempParkingPayQuery(tempParkingOrder.getTradeNo());
				}
				return r;
			}
			throw new ServiceException(r.getMsg());
		} catch (Exception e) {
			log.error(tempParkingOrder.getPlate() + "被扫支付下单失败", e);
			tempParkingOrderService.releasePayingLock(tempParkingOrder.getParkingId());
		}
		return r;
	}

}
