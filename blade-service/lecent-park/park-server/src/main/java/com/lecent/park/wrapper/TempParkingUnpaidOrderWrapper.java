package com.lecent.park.wrapper;

import com.lecent.park.entity.TempParkingUnpaidOrder;
import com.lecent.park.vo.TempParkingUnpaidOrderVO;
import com.lecent.process.park.abnormality.audit.entity.ParkAbnormalityAudit;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;

/**
 * 临停待缴费订单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class TempParkingUnpaidOrderWrapper extends BaseEntityWrapper<TempParkingUnpaidOrder, TempParkingUnpaidOrderVO>  {

	public static TempParkingUnpaidOrderWrapper build() {
		return new TempParkingUnpaidOrderWrapper();
 	}

	@Override
	public TempParkingUnpaidOrderVO entityVO(TempParkingUnpaidOrder parkingUnpaidOrder) {
		TempParkingUnpaidOrderVO parkingUnpaidOrderVO = BeanUtil.copy(parkingUnpaidOrder, TempParkingUnpaidOrderVO.class);

		//User createUser = UserCache.getUser(parkingUnpaidOrder.getCreateUser());
		//User updateUser = UserCache.getUser(parkingUnpaidOrder.getUpdateUser());
		//parkingUnpaidOrderVO.setCreateUserName(createUser.getName());
		//parkingUnpaidOrderVO.setUpdateUserName(updateUser.getName());

		return parkingUnpaidOrderVO;
	}

	public TempParkingUnpaidOrder entity(ParkAbnormalityAudit parkAbnormalityAudit) {

		TempParkingUnpaidOrder tempParkingUnpaidOrder = new TempParkingUnpaidOrder();

		if (Func.isEmpty(parkAbnormalityAudit)) {
			return tempParkingUnpaidOrder;
		}

		tempParkingUnpaidOrder.setId(parkAbnormalityAudit.getTempParkingUnpaidOrderId());
		tempParkingUnpaidOrder.setAuditRemake(parkAbnormalityAudit.getAuditRemark());
		tempParkingUnpaidOrder.setAuditStatus(parkAbnormalityAudit.getAuditStatus());
		tempParkingUnpaidOrder.setImageUrl(parkAbnormalityAudit.getImageUrl());
		tempParkingUnpaidOrder.setRealAmount(parkAbnormalityAudit.getRealAmount());

		return tempParkingUnpaidOrder;
	}

}
