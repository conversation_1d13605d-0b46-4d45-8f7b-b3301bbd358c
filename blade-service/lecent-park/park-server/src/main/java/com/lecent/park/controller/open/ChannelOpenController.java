package com.lecent.park.controller.open;


import com.alibaba.fastjson.JSON;
import com.lecent.park.common.constant.CustomResultCode;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.common.exception.RepeatSubmitException;
import com.lecent.park.service.IChannelTodoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 通道车辆进出场 控制器
 *
 * <AUTHOR>
 * @date 2021-12-07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/open-api/park/channel")
@Api(value = "第三方设备上传车辆信息")
@Slf4j
public class ChannelOpenController {

	@Autowired
	private IChannelTodoService channelTodoService;

	@PostMapping("/v1/cameraTrigger")
	@ApiOperation(value = "车牌识别上传", notes = "传入event")
	public R cameraTrigger(@RequestBody ParkChannelMessageEvent event) {
		try {
			log.info("开放接口车牌上传数据为：data={}", JSON.toJSONString(event));
			Map<String, Object> map = channelTodoService.cameraHttpTrigger(event);
			return R.data(map);

		} catch (RepeatSubmitException e) {
			log.error("ClientController-cameraTrigger", e);
			return R.fail(CustomResultCode.REPEAT_SUBMIT_ERROR, e.getMessage());

		} catch (Exception e) {
			log.error("ClientController-cameraTrigger", e);
			return R.fail(e.getMessage());
		}
	}


}
