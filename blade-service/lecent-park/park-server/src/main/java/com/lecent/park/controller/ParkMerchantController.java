package com.lecent.park.controller;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.ParkMerchantCarStatisticsDTO;
import com.lecent.park.dto.ParkMerchantDTO;
import com.lecent.park.entity.MerchantOrder;
import com.lecent.park.entity.ParkMerchant;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IParkMerchantService;
import com.lecent.park.vo.CarDetailStatisticsVO;
import com.lecent.park.vo.ParkMerchantVO;
import com.lecent.park.wrapper.ParkMerchantWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 酒店商户表 控制器
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@RestController
@Validated
@AllArgsConstructor
@RequestMapping("/parkmerchant")
@Api(value = "酒店商户表", tags = "酒店商户表接口")
public class ParkMerchantController extends BladeController {

	private IParkMerchantService parkMerchantService;
	private IUserClient userClient;


	@GetMapping("/getOptions")
	@ApiOperation(value = "根据套餐类型获取商家下拉框", notes = "根据套餐类型获取商家下拉框")
	public R<List<ParkMerchant>> getOptions(Integer type) {
		List<ParkMerchant> list = parkMerchantService.getOptions(type);
		return R.data(list);
	}

	@GetMapping("/getParkListById")
	@ApiOperation(value = "根据商家获取车场", notes = "根据商家获取车场")
	public R<List<Parklot>> getParkListById(@NotNull(message = "id不能为空") Long id) {
		List<Parklot> list = parkMerchantService.getParkListById(id);
		return R.data(list);
	}

	@GetMapping("/getMerchantByParklotId")
	@ApiOperation(value = "根据车场id获取商家", notes = "根据车场id获取商家")
	public R<List<ParkMerchant>> getMerchantByParklotId(Long parklotId) {
		List<ParkMerchant> list = parkMerchantService.getMerchantByParklotId(parklotId);
		return R.data(list);
	}

	@GetMapping("/getParkOptions")
	@ApiOperation(value = "根据商家套餐类型获取车场", notes = "根据商家套餐类型获取车场")
	public R<List<Parklot>> getParkOptions(Integer type) {
		List<Parklot> list = parkMerchantService.getParkOptions(type);
		return R.data(list);
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入parkMerchant")
	public R<ParkMerchantVO> detail(ParkMerchant parkMerchant) {

		String businessId = SecureUtil.getBusinessId();
		//businessId为空则是物业端查询，
		if ("-1".equals(businessId)) {
			businessId = Func.toStr(parkMerchant.getId(), "-1");
		}

		ParkMerchant merchant = parkMerchantService.getById(businessId);
		if (null == merchant) {
			return R.data(null);
		}

		R<User> userR = userClient.userInfoById(merchant.getUserId());
		LecentAssert.isTrue(userR.isSuccess(), "调用服务失败");
		User user = userR.getData();
		ParkMerchantVO parkMerchantVO = ParkMerchantWrapper.build().entityVO(merchant);
		parkMerchantVO.setAccount(user.getAccount());
		parkMerchantVO.setMerName(merchant.getMerName());
		parkMerchantVO.setRealName(user.getRealName());
		parkMerchantVO.setManager(merchant.getManager());
		parkMerchantVO.setPhone(user.getPhone());
		parkMerchantVO.setCreateTime(user.getCreateTime());
		return R.data(parkMerchantVO);
	}

	/**
	 * 分页 酒店商户表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入parkMerchant")
	public R<IPage<ParkMerchantVO>> list(ParkMerchantDTO parkMerchantDto, Query query) {
		IPage<ParkMerchantVO> ret = parkMerchantService.selectParkMerchantPage(Condition.getPage(query),
			parkMerchantDto);
		return R.data(ret);
	}


	/**
	 * 自定义分页 酒店商户表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入parkMerchant")
	public R<IPage<ParkMerchantVO>> page(ParkMerchantDTO parkMerchantDto, Query query) {
		IPage<ParkMerchantVO> pages = parkMerchantService.selectParkMerchantPage(Condition.getPage(query), parkMerchantDto);
		return R.data(pages);
	}

	/**
	 * 新增 酒店商户表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入parkMerchant")
	public R save(@Valid @RequestBody ParkMerchantDTO parkMerchantDto) {
		return R.status(parkMerchantService.addParkMerchant(parkMerchantDto));
	}

	/**
	 * 修改 酒店商户表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入parkMerchant")
	public R update(@Valid @RequestBody ParkMerchantDTO parkMerchant) {
		return R.status(parkMerchantService.submit(parkMerchant));
	}

	/**
	 * 新增或修改 酒店商户表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入parkMerchant")
	public R submit(@Valid @RequestBody ParkMerchantDTO parkMerchant) {
		return R.status(parkMerchantService.submit(parkMerchant));
	}

	/**
	 * 新增或修改 酒店商户表
	 */
	@PostMapping("/updateMerchantUser")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "更换商户管理员", notes = "传入parkMerchant")
	public R<Boolean>  updateMerchantUser(@RequestBody ParkMerchantDTO parkMerchantDto){
		return R.status(parkMerchantService.updateMerchantUser(parkMerchantDto));
	}

	/**
	 * 删除 酒店商户表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(parkMerchantService.customDel(ids));
	}

	/**
	 * 获取当前用户创建的商户列表
	 */
	@GetMapping("/getCurrentMerchantList")
	@ApiOperation(value = "获取当前用户创建的商户列表", notes = "获取当前用户创建的商户列表")
	public R<List<ParkMerchant>> getCurrentMerchantList() {
		List<ParkMerchant> list = parkMerchantService.list(Wrappers.<ParkMerchant>lambdaQuery().eq(ParkMerchant::getTenantId, SecureUtil.getTenantId()));
		return R.data(list);
	}


	/**
	 * 商户报表
	 */
	@Slave
	@GetMapping("/merchantReport")
	@ApiOperation(value = "商户报表", notes = "商户报表")
	public R<IPage<MerchantOrder>> merchantReportPage(ParkMerchantCarStatisticsDTO statisticsDTO, Query query) {
		IPage<MerchantOrder> pages = parkMerchantService.merchantReportPage(Condition.getPage(query), statisticsDTO);
		return R.data(pages);
	}

	/**
	 * 商户报表导出
	 */
	@Slave
	@PostMapping("/merchantReportExport")
	@ApiOperation(value = "商户报表导出", notes = "商户报表导出")
	public void merchantReportExport(ParkMerchantCarStatisticsDTO statisticsDTO, HttpServletResponse response) {
		parkMerchantService.merchantReportExport(statisticsDTO, response);
	}

	/**
	 * 商户报表统计
	 */
	@Slave
	@GetMapping("/merchantReportStatistics")
	@ApiOperation(value = "商户报表统计", notes = "商户报表统计")
	public R merchantReportStatistics(ParkMerchantCarStatisticsDTO statisticsDTO) {
		CarDetailStatisticsVO carDetailStatisticsVO = parkMerchantService.merchantReportStatistics(statisticsDTO);
		return R.data(carDetailStatisticsVO);
	}

	/**
	 * 查询商户套餐类型
	 */
	@Slave
	@GetMapping("/getMerchantMealType")
	@ApiOperation(value = "查询商户套餐类型", notes = "查询商户套餐类型")
	public R getMerchantMealType() {
		List<Integer> list =  parkMerchantService.getMerchantMealType();
		return R.data(list);
	}


}
