package com.lecent.park.controller.test;

import com.lecent.park.core.mq.rabbitmq.MessageConstant;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.service.IChannelTodoService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.system.user.feign.ICUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021-04-02 10:14
 */
@RestController
@RequestMapping("/pay-test")
@Slf4j
public class PayTestController {


	@Autowired
	private IChannelTodoService channelTodoService;


	@Resource
	private MqSender mqSender;


	@Resource
	private ICUserClient userClient;


	@PostMapping("/in-parklot")
	public String inParklot(@RequestParam(value = "channelNo", defaultValue = "3") Integer channelNo,
							@RequestParam(value = "parkLotId", defaultValue = "12") Long parkLotId,
							@RequestParam(value = "plate") String plate) {
		ParkChannelMessageEvent event = new ParkChannelMessageEvent();
		event.setChannelNo(channelNo);
		event.setDateTime(System.currentTimeMillis());
		event.setParkLotId(parkLotId);
		event.setPlate(plate);
		event.setTriggerType(ChannelWay.WAY_4.getValue());
		event.setUrl("https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1591777256639&di=c923dcf05872d03f10301d2a575d8002&imgtype=0&src=http%3A%2F%2Fdpic.tiankong.com%2Fmp%2Fxa%2FQJ8167455339.jpg");
		try {
			ChannelTodo channelTodo = channelTodoService.triggerCreateTodo(event);
			return channelTodo.getId().toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error("模拟进场失败.通道:{},车场:{},车牌:{}", channelNo, parkLotId, plate);
			throw new ServiceException("模拟进场失败.通道:" + channelNo + ",车场:{" +
				parkLotId + "},车牌:{" + plate + "}");
		}
	}


	@PostMapping("/out-parklot")
	public boolean paySuccess(
		@RequestParam(value = "channelNo", defaultValue = "2") Integer channelNo,
		@RequestParam(value = "parkLotId", defaultValue = "12") Long parkLotId,
		@RequestParam(value = "plate") String plate
	) {
		ParkChannelMessageEvent event = new ParkChannelMessageEvent();
		event.setChannelNo(channelNo);
		event.setDateTime(System.currentTimeMillis());
		event.setParkLotId(parkLotId);
		event.setPlate(plate);
		event.setTriggerType(ChannelWay.WAY_4.getValue());
		event.setUrl("https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1591777256639&di=c923dcf05872d03f10301d2a575d8002&imgtype=0&src=http%3A%2F%2Fdpic.tiankong.com%2Fmp%2Fxa%2FQJ8167455339.jpg");
		try {
			ChannelTodo channelTodo = channelTodoService.triggerCreateTodo(event);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("模拟进场失败.通道:{},车场:{},车牌:{}", channelNo, parkLotId, plate);
			throw new ServiceException("模拟进场失败.通道:" + channelNo + ",车场:{" +
				parkLotId + "},车牌:{" + plate + "}");
		}
	}


	@GetMapping("/send-msg")
	public void sendMsg() {
		mqSender.sendMessage("测试", MessageConstant.LECENT_PARK_ROUTE_UP_STAFF_ACK, Exchanges.LECENT_PARK_EXCHANGE_NAME);
	}


//	@GetMapping("/test-plate")
//	public String testPlate(String url) {
//		return AliyunPlateRecognize.recognizePlate(url);
//	}


	@GetMapping("/openId-plate")
	public String openIdPlate() {
		return userClient.getOpenId("233048", "13985529082").getData();
	}


}
