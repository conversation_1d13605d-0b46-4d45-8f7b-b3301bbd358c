package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.CardParklot;
import com.lecent.park.mapper.CardParklotMapper;
import com.lecent.park.service.ICardParklotService;
import com.lecent.park.vo.CardParklotVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 套餐车场表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Service
public class CardParklotServiceImpl extends BaseServiceImpl<CardParklotMapper, CardParklot> implements ICardParklotService {

	@Override
	public IPage<CardParklotVO> selectCardParklotPage(IPage<CardParklotVO> page, CardParklotVO cardParklot) {
		return page.setRecords(baseMapper.selectCardParklotPage(page, cardParklot));
	}

}
