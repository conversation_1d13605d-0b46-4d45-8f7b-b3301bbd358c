package com.lecent.park.wrapper;

import com.lecent.park.entity.CarBrand;
import com.lecent.park.vo.CarBrandVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public class CarBrandWrapper extends BaseEntityWrapper<CarBrand, CarBrandVO>  {

	public static CarBrandWrapper build() {
		return new CarBrandWrapper();
 	}

	@Override
	public CarBrandVO entityVO(CarBrand carBrand) {
		CarBrandVO carBrandVO = BeanUtil.copy(carBrand, CarBrandVO.class);

		//User createUser = UserCache.getUser(carBrand.getCreateUser());
		//User updateUser = UserCache.getUser(carBrand.getUpdateUser());
		//carBrandVO.setCreateUserName(createUser.getName());
		//carBrandVO.setUpdateUserName(updateUser.getName());

		return carBrandVO;
	}

}
