package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.CacheConstant;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.UserParklot;
import com.lecent.park.mapper.UserParklotMapper;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IUserParklotService;
import com.lecent.park.vo.UserParklotVO;
import org.springblade.common.entity.KeyValueVo;
import org.springblade.common.utils.CacheUtils;
import org.springblade.common.utils.stream.StreamUtils;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公司员工车场资源授权表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Service
public class UserParklotServiceImpl extends
        BaseServiceImpl<UserParklotMapper, UserParklot> implements IUserParklotService {

    @Lazy
    @Resource
    private IParklotService parklotService;

    private static final String USER_PARK_IDS_KEY = CacheConstant.PARK_CACHE + "userBindParkLotIds:";

    @Override
    public IPage<UserParklotVO> selectUserParklotPage(IPage<UserParklotVO> page, UserParklotVO userParklot) {
        return page.setRecords(baseMapper.selectUserParklotPage(page, userParklot));
    }

    @Override
    public void customDeleteByUserId(Long userId) {
        // 删除缓存
        CacheUtil.evict(CacheConstant.PARK_CACHE, USER_PARK_IDS_KEY, userId);

        // 删除用户绑定车场
        remove(Wrappers.<UserParklot>lambdaQuery().eq(UserParklot::getUserId, userId));
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
    public void customSaveBatch(List<UserParklot> userParkLots) {
        if (Func.isEmpty(userParkLots)) {
            return;
        }

        saveBatch(userParkLots);
		// 删除缓存
		CacheUtils.delKeyAfterTxCommitted(
			USER_PARK_IDS_KEY,
			userParkLots.stream()
				.filter(Objects::nonNull)
				.map(t -> t.getUserId().toString())
				.distinct()
				.collect(Collectors.toList())
		);
    }

    @Override
    public List<Long> getParkLotIds(Long userId) {
        List<Long> parkLotIds = CacheUtils.getList(USER_PARK_IDS_KEY + userId,
                Long.class,
                () -> {
                    List<Long> lotIds = baseMapper.getParkLotIds(userId);
                    return lotIds.isEmpty() ? null : lotIds;
                },
                CacheUtils.DEFAULT_1_WEEK);

		if (Func.isEmpty(parkLotIds)) {
			return Lists.newArrayList();
		}
		return parkLotIds;
	}

	@Override
	public List<UserParklot> getByUserId(Long userId) {
		return this.list(Wrappers.<UserParklot>lambdaQuery().eq(UserParklot::getUserId, userId));
	}

	@Override
	public <V> V actionIfHasPermission(Function<List<Long>, V> action, V defaultValue) {
		List<Long> parkLotIds = this.getCurrentUserBindParkLotIds();
		if (parkLotIds.isEmpty() || Objects.isNull(action)) {
			return defaultValue;
		}
		return action.apply(parkLotIds);
	}

	@Override
	public List<Long> getCurrentUserBindParkLotIds(Long parkLotId) {
		return getBindParkLotIds(AuthUtil.getUserId(), parkLotId);
	}
	@Override
	public List<Long> getCurrentUserBindParkLotIdsBylist(List<Long> parkLotIds){
		return  getBindParkLotIdsByList(AuthUtil.getUserId(),parkLotIds);
	}

	@Override
	public List<Long> getCurrentUserBindParkLotIds() {
		return getBindParkLotIds(AuthUtil.getUserId());
	}

    @Override
    public Set<Long> getCurrentUserBindParkSetIds() {
        return new HashSet<>(getCurrentUserBindParkLotIds());
    }

    @Override
    public List<KeyValueVo> getCurrentUserBindParkLot() {
        List<Long> ids = getBindParkLotIds(AuthUtil.getUserId());
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }

        List<Parklot> list = parklotService.lambdaQuery().in(Parklot::getId, ids).list();
        return list.stream().map(parklot -> {
            KeyValueVo kv = new KeyValueVo();
            kv.setKey(Func.toStr(parklot.getId()));
            kv.setValue(parklot.getName());
            return kv;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Long> getBindParkLotIds(Long userId, Long parkLotId) {
        List<Long> parkLotIds = getParkLotIds(userId);
        if (CollectionUtil.isEmpty(parkLotIds)) {
            return Collections.emptyList();
        }

        if (null == parkLotId) {
            return parkLotIds;
        }

        if (!parkLotIds.contains(parkLotId)) {
            return Collections.emptyList();
        }
        return Collections.singletonList(parkLotId);
    }
	@Override
	public List<Long> getBindParkLotIdsByList(Long userId, List<Long> parkLotIds) {
		List<Long> ids = getParkLotIds(userId);
		if (CollectionUtil.isEmpty(ids)) {
			return Collections.emptyList();
		}
		if(Func.isEmpty(parkLotIds)){
			return ids;
		}
		List<Long> list = new ArrayList<>();
		for(Long parkLotId : parkLotIds){
			if(ids.contains(parkLotId)){
				list.add(parkLotId);
			}
		}
		return list;
	}

    @Override
    public List<Long> getBindParkLotIds(Long userId) {
        return getBindParkLotIds(userId, null);
    }

    @Override
    public String getParkLotUser(Long parkLotId) {
        return baseMapper.getParkLotUser(parkLotId);
    }

    @Override
    public List<Long> queryUserByParklot(String parklotIds, Integer userType) {
        return baseMapper.queryUserByParklot(Func.toLongList(parklotIds), userType);
    }

	@Override
	public List<Long> getRoadSideParkLotIds(Long userId) {
		List<Long> parkLotIds = getParkLotIds(userId);
		// 过滤掉非路边支付车场
		parkLotIds = StreamUtils.filter(parkLotIds, parkLotId -> {
			Parklot parkLot = ParkLotCaches.getParkLot(parkLotId);
			return null != parkLot && parkLot.isRoadSide();
		});
		return parkLotIds;
	}

    public static void main(String[] args) {
        List<Long> s = new ArrayList<>();


    }
}
