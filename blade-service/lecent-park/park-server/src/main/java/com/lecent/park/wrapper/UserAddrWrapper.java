package com.lecent.park.wrapper;

import com.lecent.park.entity.UserAddr;
import com.lecent.park.vo.UserAddrVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 发票抬头表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
public class UserAddrWrapper extends BaseEntityWrapper<UserAddr, UserAddrVO>  {

	public static UserAddrWrapper build() {
		return new UserAddrWrapper();
 	}

	@Override
	public UserAddrVO entityVO(UserAddr userAddr) {
		UserAddrVO userAddrVO = BeanUtil.copy(userAddr, UserAddrVO.class);

		//User createUser = UserCache.getUser(userAddr.getCreateUser());
		//User updateUser = UserCache.getUser(userAddr.getUpdateUser());
		//userAddrVO.setCreateUserName(createUser.getName());
		//userAddrVO.setUpdateUserName(updateUser.getName());

		return userAddrVO;
	}

}
