package com.lecent.park.controller.open.openh5;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.access.ParkAccessUtils;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.PayStatusEnum;
import com.lecent.park.controller.open.openh5.req.ReqPayOrder;
import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.entity.*;
import com.lecent.park.open.OpenTemOrderDto;
import com.lecent.park.open.OpenTempOrderDetailVO;
import com.lecent.park.service.*;
import com.lecent.pay.core.enums.PayWay;
import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 扫码端临停查询
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = {
	OpenApiConstant.OPEN_API + "temp",
	OpenApiConstant.OPEN_API + "open-h5/temp",
	// 测试环境免授权接口
	"test-open-api/open-h5/temp"})
@Api(value = "扫码端", tags = "扫码端临停信息openApi接口")
public class OpenH5TemParkController extends BladeController {

	@Autowired
	private IChannelTodoService todoService;

	@Autowired
	private IBaseAppConfigService baseAppConfigService;

	@Autowired
	private IUserParklotService userParklotService;

	@Autowired
	private ITempParkingOrderService tempParkingOrderService;

	@Autowired
	private ClientService clientService;


	/**
	 * 根据车牌查询临停信息
	 */
	@PostMapping(value = {"/v1/tempCarOrder", "/v1/query"})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据车牌查询临停信息", notes = "ChannelTodoVO")
	public R<OpenTemOrderDto> queryTempCarOrder(@RequestBody OpenTemOrderDto orderDto) {
		LecentAssert.notBlank(orderDto.getPlate(), "车牌不能为空");
		// 查询绑定车场信息
		List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIds();
		LecentAssert.notEmpty(parkLotIds, "无绑定车场信息");

		ChannelTodo todo = todoService.lastOnSiteCarTempOrder(orderDto.getPlate(), parkLotIds);
		Parklot parklot = ParkLotCaches.getParkLot(todo.getParklotId());

		OpenTemOrderDto build = OpenTemOrderDto.builder()
			.todoId(todo.getId())
			.enterImageUrl(todo.getEnterImageUrl())
			.plate(todo.getPlate())
			.parklotId(todo.getParklotId())
			.enterTime(todo.getEnterTime())
			.parklotName(parklot != null ? parklot.getName() : "")
			.duration(todo.getDuration())
			.totalAmount(todo.getTotalAmount())
			.paidAmount(todo.getPaidAmount())
			.receiveAmount(todo.getReceiveAmount())
			.discountAmount(todo.getDiscountAmount())
			.build();
		return R.data(build);
	}


	/**
	 * 根据待办ID查询临停信息
	 */
	@GetMapping("/v1/payDetail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据待办ID查询临停信息", notes = "OpenTempOrderDetailVO")
	public R<OpenTempOrderDetailVO> getTempOrderDetail(@RequestParam("todoId") String todoId) {
		ChannelTodo todo = todoService.getById(todoId);
		if (null == todo) {
			return R.fail(CommonConstant.REST_API_ERROR_CODE, "订单信息不存在");
		}
		OpenTempOrderDetailVO ret = OpenTempOrderDetailVO.builder()
			.todoId(todo.getId())
			.enterImageUrl(todo.getEnterImageUrl())
			.plate(todo.getPlate())
			.parklotId(todo.getParklotId())
			.enterTime(todo.getEnterTime())
			.exitTime(DateUtil.now())
			.duration(todo.getDuration())
			.totalAmount(todo.getTotalAmount())
			.paidAmount(todo.getPaidAmount())
			.receiveAmount(todo.getReceiveAmount())
			.discountAmount(todo.getDiscountAmount())
			.build();

		// get parkLot
		Parklot parklot = ParkLotCaches.getParkLot(todo.getParklotId());
		if (null == parklot) {
			return R.data(ret);
		}
		ret.setParklotName(parklot.getName());

		// 获取微信支付小程序配置
		BaseAppConfig wxScanMiniConfig = baseAppConfigService.getConfigById(parklot.getScanMimiId());
		if (null != wxScanMiniConfig) {
			ret.setScanMimiId(wxScanMiniConfig.getJumpAppId());
			ret.setScanMimiOriginalId(wxScanMiniConfig.getAppOriginalId());
		}

		// 获取支付宝小程序配置
		BaseAppConfig aliScanMiniConfig = baseAppConfigService.getConfigById(parklot.getScanAliMimiId());
		if (null != aliScanMiniConfig) {
			ret.setScanAliMimiId(aliScanMiniConfig.getAppId());
		}
		return R.data(ret);
	}


	/**
	 * 根据待办ID查询临停是否已支付
	 */
	@GetMapping("/v1/paySuccess")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "根据待办ID查询临停是否已支付", notes = "OpenTempOrderDetailVO")
	public R<Boolean> paySuccess(@RequestParam("todoId") String todoId) {
		ChannelTodo todo = todoService.getById(todoId);
		LecentAssert.notNull(todo, "订单信息不存在");
		return R.data(ParkAccessUtils.isTodoPaid(todo.getStatus()));
	}


	/**
	 * 下单接口
	 */
	@PostMapping("/v1/order/create")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "下单接口", notes = "req")
	public R<Map<String, Object>> createOrder(@RequestBody ReqPayOrder req) {
		ChannelTodo todo = todoService.getById(req.getTodoId());
		LecentAssert.notNull(todo, "待缴订单不存在!");
		LecentAssert.isFalse(DateUtil.between(todo.getCreateTime(), new Date()).toMinutes() > 5, "待缴订单超时!");
		LecentAssert.isFalse(req.getDiscountAmount().compareTo(todo.getReceiveAmount()) > 0, "优惠金额不能大于停车金额!");

		//初始化订单数据
		todo.setDiscountAmount(todo.getDiscountAmount().add(req.getDiscountAmount()));
		todo.setCouponDiscountAmount(req.getDiscountAmount());
		todo.setReceiveAmount(todo.getReceiveAmount().subtract(req.getDiscountAmount()));

		TempParkingOrder order = TempParkingOrder.create(todo);
		order.setPayStatus(PayStatusEnum.UN_PAY.getValue());
		order.setCreateWay(CreateWay.INSIDE.getValue());
		order.setPayType(PayWay.BLX.getKey());
		tempParkingOrderService.save(order);

		Map<String, Object> map = new HashMap<String, Object>(8) {{
			put("orderNo", order.getTradeNo());
			put("parklotId", order.getParklotId());
			put("plate", order.getPlate());
			put("totalAmount", order.getTotalAmount());
			put("paidAmount", order.getPaidAmount());
			put("receiveAmount", order.getReceiveAmount());
			put("discountAmount", order.getDiscountAmount());
			put("createTime", order.getCreateTime());
		}};

		return R.data(map);
	}


	/**
	 * 支付成功回调
	 */
	@PostMapping("/v1/order/callback")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "支付成功回调", notes = "req")
	public R<String> orderCallback(@RequestBody ReqPayOrder req) {
		LecentAssert.isTrue(Func.isNotBlank(req.getOrderNo()), "参数[orderNo]不能为空");
		LecentAssert.isTrue(Func.isNotBlank(req.getThirdTradeNo()), "参数[thirdTradeNo]不能为空");

		try {
			PaymentSuccessPayload payload = PaymentSuccessPayload.builder()
				.tradeNo(req.getOrderNo())
				.thirdTradeNo(req.getThirdTradeNo())
				.build();
			clientService.mqPaySuccessCallback(req.getOrderNo(), payload);
		} catch (Exception e) {
			log.error("比邻星支付回调失败", e);
			return R.fail("支付回调失败");
		}

		return R.data(req.getOrderNo());
	}


}
