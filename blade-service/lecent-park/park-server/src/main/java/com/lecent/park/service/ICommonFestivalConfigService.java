package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.CommonFestivalConfigDTO;
import com.lecent.park.entity.CommonFestivalConfig;
import com.lecent.park.vo.CommonFestivalConfigVO;
import com.lecent.park.vo.FestivalConfigVO;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;
import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
public interface ICommonFestivalConfigService extends BaseService<CommonFestivalConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotFestivalConfig
	 * @return
	 */
	IPage<CommonFestivalConfigVO> selectParklotFestivalConfigPage(IPage<CommonFestivalConfigVO> page,
																  CommonFestivalConfig parklotFestivalConfig);

	boolean submitConfig(CommonFestivalConfigDTO festivalConfigDTO);

	FestivalConfigVO getConfigInfo();

	void saveConfig(List<CommonFestivalConfig> festivalDurations);

	List<CommonFestivalConfig> getListByVacationIds(String[] festivalIds);

	/**
	 * 判断传入的日期是否是节假日
	 *
	 * @param date 日期
	 * @return 是否节假日
	 */
	boolean isFestival(Date date);

	/**
	 * 根据日期获取节日
	 *
	 * @param now 日期
	 * @return 节日
	 */
	CommonFestivalConfig getFestivalByDate(Date now);

	/**
	 * 获取节假日
	 *
	 * @param startTime
	 * @param exitTime
	 * @return
	 */
	List<CommonFestivalConfigVO> periodHolidayList(Date startTime, Date exitTime);
}
