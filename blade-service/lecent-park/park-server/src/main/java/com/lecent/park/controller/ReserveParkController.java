package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.biz.IReserveParkBiz;
import com.lecent.park.dto.ReserveParkDTO;
import com.lecent.park.entity.ReservePark;
import com.lecent.park.service.IReserveParkService;
import com.lecent.park.vo.ReserveParkDetailVO;
import com.lecent.park.vo.ReserveParkVO;
import com.lecent.park.vo.ReserveParkingOrderVO;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * 预约车位表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/reservepark")
@Api(value = "预约车位表", tags = "预约车位表接口")
public class ReserveParkController extends BladeController {

	private IReserveParkService reserveParkService;

	private IReserveParkBiz reserveParkBiz;

	/**
	 * 详情
	 */
	@GetMapping("/parkingDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "停车详情", notes = "传入reservePark")
	public R<ReserveParkDetailVO> parkingDetail(Long parkOrderId) {
		return R.data(reserveParkBiz.getReserveParkDetail(parkOrderId));
	}

	/**
	 * 详情
	 */
	@GetMapping("/reserveDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "预约详情", notes = "传入reservePark")
	public R<ReserveParkVO> reserveDetail(Long id) {
		return R.data(reserveParkService.getVoById(id));
	}

	/**
	 * 退款
	 */
	@GetMapping("/refund")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "退款", notes = "传入reservePark")
	public R refund(String orderId) {
		return R.status(reserveParkService.refundReserveOrder(orderId));
	}

	/**
	 * 退款回调
	 */
	@GetMapping("/refundCallback")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "退款回调", notes = "")
	public R refundCallback(String refundOrderId, Integer status) {
		return R.status(reserveParkService.refundCallback(refundOrderId, status));
	}

	/**
	 * 取消预约
	 */
	@GetMapping("/cancel")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "取消预约", notes = "传入id")
	public R cancelReserve(Long id) {
		return R.status(reserveParkService.cancelReserve(id));
	}

	/**
	 * web端分页
	 */
	@GetMapping("/webList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "web端分页", notes = "web端分页")
	public R<IPage<ReserveParkVO>> webList(ReservePark reservePark, Query query) {
		return R.data(reserveParkBiz.selectReserveParkWebPage(query, reservePark));
	}

	/**
	 * 分页 预约车位表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "预约订单分页", notes = "传入reservePark")
	public R<IPage<ReserveParkVO>> list(ReserveParkDTO reservePark, Query query) {
		return R.data(reserveParkService.selectReserveParkPage(Condition.getPage(query), reservePark));
	}


	/**
	 * 自定义分页 停车记录分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "停车记录分页", notes = "传入reservePark")
	public R<IPage<ReserveParkingOrderVO>> page(ReservePark reservePark, Query query) {
		IPage<ReserveParkingOrderVO> pages = reserveParkBiz.selectReserveParkPage(Condition.getPage(query), reservePark);
		return R.data(pages);
	}

	/**
	 * 新增 预约车位表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入reservePark")
	public R save(@Valid @RequestBody ReservePark reservePark) {
		return R.status(reserveParkService.save(reservePark));
	}

	/**
	 * 预约车位
	 */
	@PostMapping("/reservePark")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "预约", notes = "传入reservePark")
	public R reservePark(@Valid @RequestBody ReserveParkDTO reservePark) {
		return R.data(reserveParkService.reserveList(reservePark));
	}

	/**
	 * 修改 预约车位表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入reservePark")
	public R update(@Valid @RequestBody ReservePark reservePark) {
		return R.status(reserveParkService.updateById(reservePark));
	}

	/**
	 * 新增或修改 预约车位表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入reservePark")
	public R submit(@Valid @RequestBody ReservePark reservePark) {
		return R.status(reserveParkService.saveOrUpdate(reservePark));
	}


	/**
	 * 删除 预约车位表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(reserveParkService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 生成预约支付订单
	 */
	@PostMapping("/payBefore")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "生成预约支付订单", notes = "传入reservePark")
	public R reservePayBefore(@Valid @RequestBody ReserveParkDTO reservePark) {
		return R.data(reserveParkService.reservePayBefore(reservePark));
	}


	/**
	 * 付款后回调
	 */
	@GetMapping("/payAfter")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "付款后回调", notes = "")
	public R<ReserveParkVO> payAfter(String orderId) {
		return R.data(reserveParkService.reserveMoneyPayAfter(orderId));
	}

	/**
	 * 补缴预约费用
	 */
	@PostMapping("/repairMoney")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "补缴预约费用", notes = "补缴预约费用")
	public R repairPayReserveMoney(@Valid @RequestBody ReserveParkDTO reservePark) {
		return R.data(reserveParkService.repairPayReserveMoney(reservePark));
	}

	/**
	 * 计算停车费用
	 */
	@PostMapping("/parkingPayHandle")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "计算停车费用", notes = "parkingOrderId")
	public R parkingPayBefore(@RequestBody ReserveParkDTO reservePark) {
		return reserveParkBiz.payParkingMoneyHandle(reservePark.getParkingOrderId(), reservePark.getReturnUrl());
	}


	/**
	 * 付款后回调
	 */
	@GetMapping("/parkingPayAfter")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "付款后回调", notes = "传入订单号")
	public R parkingPayAfter(String outTradeNo) {
		return reserveParkBiz.parkingPayAfter(outTradeNo);
	}

	/**
	 * 获取收费规则
	 */
	@GetMapping("/chargeRuler")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取收费规则", notes = "获取收费规则")
	public R<TempParkingChargeRuleVO> getChargeRuler(Long parkId) {
		return R.data(reserveParkService.getParkChargeRuler(parkId));
	}


	/**
	 * 预约记录导出
	 */
	@PostMapping("/reserve-export")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "预约记录导出", notes = "预约记录导出")
	public void cardExport(ReserveParkDTO reservePark, HttpServletResponse response) {
		reserveParkBiz.reserveExport(reservePark, response);
	}
}
