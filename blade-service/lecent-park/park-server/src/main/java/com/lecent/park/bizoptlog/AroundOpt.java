package com.lecent.park.bizoptlog;


import org.springblade.core.mp.base.BaseService;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AroundOpt {
	/**
	 * 操作对象的service
	 *
	 * @return
	 */
	Class<? extends BaseService> serviceClass() default BaseService.class;

	/**
	 * 操作日志的标题
	 *
	 * @return
	 */
	String title() default "";

	/**
	 * 操作的id名称
	 *
	 * @return
	 */
	String idName() default "";

	/**
	 * 操作终端
	 *
	 * @return
	 */
	OptTerminalEnum optTerminal() default OptTerminalEnum.WEB;

	/**
	 * 操作类型
	 *
	 * @return
	 */
	OptTypeEnum optType() default OptTypeEnum.EDIT;

	/**
	 * 待记录的日志内容,在新增时配合customMsgTemp使用
	 *
	 * @return
	 */
	String customMsg() default "";

	/**
	 * 日志记录模板
	 *
	 * @return
	 */
	String customMsgTemp() default "";

	/**
	 * 记录标识  在删除时配合customMsgTemp使用
	 *
	 * @return
	 */
	String recordIdentification() default "";


	/**
	 * 是否从参数中去optTerminal
	 *
	 * @return
	 */
	boolean getTerminalFromParam() default false;

	/**
	 * 变更头部信息
	 *
	 * @return
	 */
	AroundOptHeader[] headers() default {};


	/**
	 * 头部信息后缀
	 *
	 * @return
	 */
	String headerSuffix() default "";


}
