package com.lecent.park.controller.merchant.web;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.common.constant.ParkLotConstants;
import com.lecent.park.service.IMerchantUserRltService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.mp.support.BladePage;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

import static org.springblade.core.cache.constant.CacheConstant.USER_CACHE;

/**
 * 商户员工
 *
 * <AUTHOR> zxr
 * @date : 2021/9/7
 */
@RestController
@AllArgsConstructor
@RequestMapping("/merchant/user")
@Api(value = "商户员工信息表", tags = "商户员工信息表接口")
public class MerchantUserController {

	private IUserClient userClient;

	private IMerchantUserRltService merchantUserRltService;

	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询员工数据 分页", notes = "传入user")
	public R<BladePage<User>> page(@ApiIgnore User user, Query query) {
		// 设置商户ID进行查询
		return R.data(merchantUserRltService.empyInfo(Condition.getPage(query),user));
	}

	@PostMapping("/save")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "新增", notes = "传入user")
	@PreAuth(ParkLotConstants.HAS_ROLE_HOTEL_ADMINISTRATOR)
	public R<Boolean> save(@Valid @RequestBody User user) {
		boolean success = merchantUserRltService.addUser(user);
		return R.status(success);
	}

	@PostMapping("/update")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "更新用户信息", notes = "传入user")
	@PreAuth(ParkLotConstants.HAS_ROLE_HOTEL_ADMINISTRATOR)
	public R<Boolean> update(@Valid @RequestBody User user) {
		return R.data(userClient.updateInfo(user));
	}

	@PostMapping("/reset-password")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "初始化密码", notes = "传入userId集合")
	@PreAuth(ParkLotConstants.HAS_ROLE_HOTEL_ADMINISTRATOR)
	public R<Boolean> resetPassword(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds) {
		return userClient.resetPassword(userIds);
	}

	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除", notes = "传入id集合")
	@PreAuth(ParkLotConstants.HAS_ROLE_HOTEL_ADMINISTRATOR)
	public R<Boolean> remove(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds) {
		CacheUtil.clear(USER_CACHE);
		return userClient.removeUserById(userIds);
	}
}
