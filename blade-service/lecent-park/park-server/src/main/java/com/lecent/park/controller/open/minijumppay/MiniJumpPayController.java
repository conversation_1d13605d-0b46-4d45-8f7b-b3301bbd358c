package com.lecent.park.controller.open.minijumppay;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.controller.open.minijumppay.req.ReqMiniToPay;
import com.lecent.park.service.ClientService;
import com.lecent.park.service.UserSideService;
import com.lecent.park.vo.CardOrderVO;
import com.lecent.park.vo.MiniScanPayDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.OpenApiConstant;
import org.springblade.common.payment.PayResult;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 月卡开放接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@AllArgsConstructor
@RequestMapping({
	OpenApiConstant.OPEN_API + "/mini-pay",
	// 测试环境免授权接口
	"test-open-api/mini-pay"})
@Api(value = "月卡开放接口", tags = "月卡开放接口")
public class MiniJumpPayController extends BladeController {

	@Autowired
	private UserSideService userSideService;

	@Autowired
	private ClientService clientService;

	/**
	 * 根据订单号发起支付
	 */
	@PostMapping("/v1/toPay")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据车牌查询月卡", notes = "根据车牌查询月卡")
	public R<PayResult> cardByPlate(@RequestBody ReqMiniToPay toPayBean) {
		Long orderId = toPayBean.getOrderId();
		PayResult payResult = null;
		if ("temp".equals(toPayBean.getType())) {
			MiniScanPayDTO payDTO = new MiniScanPayDTO();
			payDTO.setTodoId(orderId);
			payDTO.setSource(toPayBean.getSource());
			payDTO.setOpenId(toPayBean.getOpenId());
			payDTO.setUserId(toPayBean.getUserId());
			payResult = clientService.miniScanPay(payDTO);
		} else if ("card".equals(toPayBean.getType())) {
			CardOrderVO cardOrder = new CardOrderVO();
			cardOrder.setId(toPayBean.getOrderId());
			cardOrder.setOpenId(toPayBean.getOpenId());
			cardOrder.setSource(toPayBean.getSource());
			cardOrder.setOrderUserId(toPayBean.getUserId());
			payResult = userSideService.toPay(cardOrder);
		}
		return R.data(Optional.ofNullable(payResult).orElseGet(PayResult::new));
	}
}
