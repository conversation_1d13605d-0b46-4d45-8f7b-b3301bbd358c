package com.lecent.park.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.controller.ebo.mini.resp.EboParkLotPlace;
import com.lecent.park.controller.ebo.mini.resp.PlaceDevice;
import com.lecent.park.dto.space.ParkingPlaceDTO;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.vo.ParkingPlaceStatisticsVO;
import com.lecent.park.vo.ParkingPlaceVO;
import com.lecent.park.vo.RemainPlaceNumVO;
import com.lecent.park.vo.place.PlaceIteratorVO;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 车位信息表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public interface IParkingPlaceService extends BaseService<ParkingPlace> {

	/**
	 * @param page            分页参数
	 * @param parkingPlaceDTO 查询条件
	 * @return 分页的停车位信息
	 */
	IPage<ParkingPlaceVO> selectParkingPlacePage(IPage<ParkingPlaceVO> page, ParkingPlaceDTO parkingPlaceDTO);

	/**
	 * 获取车位状况统计信息
	 *
	 * @param parkingPlaceDTO 搜索条件
	 * @return 车位状况统计信息
	 */
	ParkingPlaceStatisticsVO getParkSpaceStateStatistics(ParkingPlaceDTO parkingPlaceDTO);

	/**
	 * 根据车场id获取车位数量
	 *
	 * @param parklotId 车场id
	 * @return 车位数量
	 */
	int getListByParklotId(Long parklotId);
	int getListByParklotId(Long parklotId,Integer status);

	/**
	 * 通过车场id查询车位列表
	 *
	 * @param parklotId 车场id
	 * @return {@link List}<{@link ParkingPlace}>
	 */
	List<ParkingPlace> listByParklotId(Long parklotId);



	List<ParkingPlace> listByParklotId(List<Long> parklotIds);

	/**
	 * 批量保存停车位信息
	 *
	 * @param parkingPlaceDTO 停车位信息
	 * @return 保存结果
	 */
	boolean batchSave(ParkingPlaceDTO parkingPlaceDTO);

	/**
	 * 保存停车位信息
	 *
	 * @param parkingPlace 停车位信息
	 * @return 保存结果
	 */
	boolean save(ParkingPlaceDTO parkingPlace);

	/**
	 * 更新停车位信息
	 *
	 * @param parkingPlace 停车位信息
	 * @return 更新结果
	 */
	boolean update(ParkingPlaceDTO parkingPlace);

	/**
	 * 忽略租户查询车位数量
	 *
	 * @param queryWrapper 查询条件
	 * @return 车位数量
	 */
	@TenantIgnore
	int countTenantIgnore(Wrapper<ParkingPlace> queryWrapper);

	/**
	 * 根据区域ID获取车位列表
	 *
	 * @param regionId 区域ID
	 * @return 车位列表
	 */
	List<ParkingPlace> dropListByRegionId(Long regionId);

	/**
	 * 根据区域ID列表获取车位信息
	 *
	 * @param regionIdList 区域ID列表
	 * @return 车位信息列表
	 */
	List<ParkingPlace> getByRegionIds(List<Long> regionIdList);

	/**
	 * 预约车位
	 *
	 * @param count     预约数量
	 * @param interest  利率
	 * @param parklotId 车场ID
	 * @param placeType 车位类型
	 * @return 预约的车位列表
	 */
	List<ParkingPlace> reservePlace(Integer count, Integer interest, Long parklotId, Integer placeType);

	/**
	 * 释放预约车位
	 *
	 * @param placeIds 车位ID列表
	 * @return 释放结果
	 */
	boolean releasePlace(List<Long> placeIds);

	/**
	 * 根据车位编码获取车位信息
	 *
	 * @param placeCode 车位编码
	 * @return 车位信息
	 */
	ParkingPlace getByPlaceCode(String placeCode);

	/**
	 * 根据区域ID和规则ID获取车位信息
	 *
	 * @param toLongList 区域ID列表
	 * @param ruleId     规则ID
	 * @return 车位信息列表
	 */
	List<ParkingPlace> getByRuleIdRegionIds(List<Long> toLongList, Long ruleId);

	/**
	 * 根据区域ID获取剩余车位数量
	 *
	 * @param regionId 区域ID
	 * @return 剩余车位数量
	 */
	RemainPlaceNumVO getRemainPlaceNumByRegionId(Long regionId);

	/**
	 * 更新车位空闲状态
	 *
	 * @param id     车位ID
	 * @param isFree 是否空闲（1 空闲   0 占用）
	 * @return 更新结果
	 */
	boolean updateIsFree(Long id, int isFree);

	/**
	 * 判断车位是否空闲
	 *
	 * @param parkLotPlaceId 车位ID
	 * @return 车位设备信息
	 */
	PlaceDevice isPlaceFree(Long parkLotPlaceId);

	/**
	 * 统计使用的vip服务数量
	 *
	 * @param parkLotId 车场ID
	 * @return 使用的vip服务数量
	 */
	int countReservedVipNumber(Long parkLotId);

	/**
	 * 根据区域ID获取车位ID
	 *
	 * @param regionId 区域ID
	 * @return 车位ID
	 */
	String getPlaceIds(String regionId);

	/**
	 * 根据车位ID获取车位信息
	 *
	 * @param id 车位ID
	 * @return 车位信息
	 */
	ParkingPlaceVO detailById(Long id);

	/**
	 * 根据短链接获取停车位详情
	 *
	 * @param shortUrl 短链接
	 * @return 停车位详情
	 */
	ParkingPlaceVO detailByShortUrl(String shortUrl);

	/**
	 * 根据支付码获取停车位详情
	 *
	 * @param payCode 支付码
	 * @return 停车位详情
	 */
	ParkingPlaceVO detailByPayCode(String payCode);

	/**
	 * 导出车位信息
	 *
	 * @param parkingPlace 查询条件
	 * @return 车位信息列表
	 */
	List<ParkingPlaceVO> exprPlace(ParkingPlaceDTO parkingPlace);

	/**
	 * 根据区域ID获取车位列表
	 *
	 * @param regionId 区域ID
	 * @return 车位列表
	 */
	List<ParkingPlace> placeListByRegionId(Long regionId);

	/**
	 * 根据车位ID检查车位信息
	 *
	 * @param placeId 车位ID
	 * @return 车位信息
	 */
	ParkingPlace checkById(Long placeId);

	/**
	 * 根据设备序列号查询车位信息
	 *
	 * @param sn 设备序列号
	 * @return 车位信息
	 */
	@Nullable
	ParkingPlace getByDeviceSn(String sn);


	/**
	 * 根据设备ID查询车位信息
	 *
	 * @param sn 设备序列号
	 * @return ParkingPlace
	 */
	@Nullable
	ParkingPlace getByDeviceId(Long deviceId);

	/**
	 * 根据设备序列号检查车位是否存在
	 *
	 * @param sn 设备序列号
	 * @return 车位信息
	 */
	ParkingPlace existByDeviceSn(String sn);

	/**
	 * 获取二维码连接
	 *
	 * @param placeId 车位ID
	 * @param url     路径
	 * @return 二维码链接
	 */
	String shortQrUrl(Long placeId, String url);

	/**
	 * 获取短链接二维码
	 *
	 * @param parklotId 车场ID
	 * @param url       链接
	 * @return 短链接二维码
	 */
	String getShortQrUrl(Long parklotId, String url);

	/**
	 * 更新车位当前状态和车牌信息
	 *
	 * @param id         车位id
	 * @param freeStatus 使用状态
	 * @param plate      车牌
	 */
	void updateCurStatus(Long id, ParkingPlaceIdleState freeStatus, String plate);

	/**
	 * 获取ebo车位列表
	 *
	 * @param parklotId 车场id
	 * @return ebo车位列表
	 */
	List<EboParkLotPlace> eboParkLotPlaceList(Long parklotId);

	/**
	 * 获取车位占用数量
	 *
	 * @param parklotId 车场id
	 * @return 车位占用数量
	 */
	int usedNumber(Long parklotId);

	/**
	 * 启用车位
	 *
	 * @param id 车位ID
	 * @param memo 备注
	 * @return 启用结果
	 */
	boolean enableSpace(Long id, String memo);

	/**
	 * 停用车位
	 *
	 * @param id   车位ID
	 * @param memo 备注
	 * @return 停用结果
	 */
	boolean disableSpace(Long id, String memo);

	/**
	 * 车位状态变更业务
	 *
	 * @param ids     车位ID
	 * @param status 车位状态
	 * @param memo   备注
	 * @return boolean
	 */
	boolean updateStatusProcess(List<Long> ids,Integer status,String memo);
	/**
	 * 更新车位状态
	 *
	 * @param id     车位ID
	 * @param status 车位状态
	 * @param memo   备注
	 * @return 更新结果
	 */
	boolean updateStatusProcess(@Valid @NotNull(message = "参数[id]不能为空") Long id,
								@NotNull(message = "参数[status]不能为空") Integer status,
								@NotBlank(message = "参数[memo]不能为空") String memo);

	/**
	 * 根据车场id获取停用车位数量
	 *
	 * @param parklotId 车场id
	 * @return 停用车位数量
	 */
	int getDisableNum(Long parklotId);

	/**
	 * 更新车位位置信息
	 *
	 * @param dto 车位DTO
	 * @return 更新结果
	 */
	boolean updateSpaceLocation(ParkingPlaceDTO dto);

	/**
	 * 更新车位位置信息
	 *
	 * @param id       车位id
	 * @param lng      经度
	 * @param lat      纬度
	 * @param location 位置信息
	 * @return 更新结果
	 */
	boolean updateSpaceLocation(Long id, Double lng, Double lat, String location);

	/**
	 * 车位信息登记列表 (pda 端使用)
	 * @param page
	 * @param parkingPlaceDTO
	 * @return
	 */
	IPage<ParkingPlaceVO> informationRegistrationPage(IPage<ParkingPlaceVO> page, ParkingPlaceDTO parkingPlaceDTO);

	/**
	 * 车位迭代器
	 * @param placeId 车位id
	 * @return PlaceIteratorVO
	 */
	PlaceIteratorVO placeIterator(Long placeId);
}
