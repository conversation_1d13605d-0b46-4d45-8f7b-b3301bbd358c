package com.lecent.park.scheduled;


import com.lecent.park.discount.cashreduce.service.IPlatform170CouponService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 170平台立减金定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RefreshScope
@AllArgsConstructor
public class Platform170CouponScheduled {

	private IPlatform170CouponService platform170CouponService;

	/**
	 * 重发发送失败的优惠劵
	 */
	@Scheduled(cron = "${lecent.park.coupon.platform170.send-cron:0 0/10 * * * ?}")
	@RedisLock(value = "lecent:park::timedTask:lock:platform170:send:coupon")
	public void retrySend() {
		log.info("---------定时任务重发发放失败的立减金-----------------");
		platform170CouponService.retrySend();
	}

	/**
	 * 查询优惠劵是否使用以及是否过期
	 */
	@Scheduled(cron = "${lecent.park.coupon.platform170.query-cron:0 45 23 * * ?}")
	@RedisLock(value = "lecent:park::timedTask:lock:platform170:query:coupon")
	public void updateCouponStatus() {
		log.info("---------定时任务查询170平台立减金状态-----------------");
		platform170CouponService.updateCouponStatusFromPlatform170();
	}

	/**
	 * 更新过期的立减金
	 */
	@Scheduled(cron = "${lecent.park.coupon.platform170.query-cron:0 55 23 * * ?}")
	@RedisLock(value = "lecent:park::timedTask:lock:platform170:expire:coupon")
	public void updateExpireCoupon() {
		log.info("---------定时任务查询170平台立减金状态-----------------");
		platform170CouponService.updateExpireCoupon();
	}

}
