package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.EmployeeCarDTO;
import com.lecent.park.entity.EmployeeCar;
import com.lecent.park.service.IEmployeeCarService;
import com.lecent.park.vo.EmployeeCarVO;
import com.lecent.park.wrapper.EmployeeCarWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 员工车辆 控制器
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@RestController
@AllArgsConstructor
@RequestMapping("/employee-car")
@Api(value = "员工车辆", tags = "员工车辆接口")
public class EmployeeCarController extends BladeController {

	private IEmployeeCarService employeeCarService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入employeeCar")
	public R<EmployeeCarVO> detail(String id) {
		return R.data(employeeCarService.detail(id));
	}

	/**
	 * 分页 员工车辆
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入employeeCar")
	public R<IPage<EmployeeCarVO>> list(EmployeeCar employeeCar, Query query) {
		IPage<EmployeeCar> pages = employeeCarService.page(Condition.getPage(query), Condition.getQueryWrapper(employeeCar));
		return R.data(EmployeeCarWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 员工车辆
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入employeeCar")
	public R<IPage<EmployeeCarVO>> page(EmployeeCarDTO employeeCarDto, Query query) {
		IPage<EmployeeCarVO> pages = employeeCarService.selectEmployeeCarPage(Condition.getPage(query), employeeCarDto);
		return R.data(pages);
	}

	/**
	 * 新增 员工车辆
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入employeeCar")
	public R save(@Valid @RequestBody EmployeeCar employeeCar) {
		return R.status(employeeCarService.save(employeeCar));
	}

	/**
	 * 修改 员工车辆
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入employeeCar")
	public R update(@Valid @RequestBody EmployeeCar employeeCar) {
		return R.status(employeeCarService.updateById(employeeCar));
	}

	/**
	 * 新增或修改 员工车辆
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入employeeCar")
	public R submit(@Valid @RequestBody EmployeeCarDTO employeeCarDto) {
		return R.status(employeeCarService.submit(employeeCarDto));
	}


	/**
	 * 删除 员工车辆
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入id")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String id) {
		return R.status(employeeCarService.customRemove(id));
	}


	/**
	 * 删除 关联车场
	 */
	@GetMapping("/remove-parklot")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除 关联车场", notes = "")
	public R removeParklot(String contactId) {
		return R.status(employeeCarService.removeParklot(contactId));
	}

}
