package com.lecent.park.service;


import com.lecent.park.entity.Card;
import com.lecent.park.entity.FreeCardAuth;
import com.lecent.park.entity.TempParkingUnpaidOrder;

/**
 *  流程申请服务类
 *
 * <AUTHOR>
 * @since 2021-08-18
 */

public interface IParkProcessApplyService {

	/**
	 * 发起稽核审批
	 * @param unpaidOrder    临停未付订单
	 * @param parklotId    车场id
	 * @param plate
	 */
	void applyAbnormalityAudit(TempParkingUnpaidOrder unpaidOrder, Long parklotId, String plate);

	/**
	 * 提交月卡申请
	 * @param card 月卡信息
	 */
	void applyMonthCard(Card card);

	/**
	 * 发起免费车申请
	 * @param cardAuth cardAuth
	 * @param plate 车牌号
	 */
	void applyFreeCar(FreeCardAuth cardAuth, String plate);
}
