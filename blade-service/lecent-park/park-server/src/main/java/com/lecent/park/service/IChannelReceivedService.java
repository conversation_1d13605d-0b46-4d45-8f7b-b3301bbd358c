package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ChannelReceived;
import com.lecent.park.vo.ChannelReceivedVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 通道设备识别结果 服务类
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public interface IChannelReceivedService extends BaseService<ChannelReceived> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param channelReceived
	 * @return
	 */
	IPage<ChannelReceivedVO> selectChannelReceivedPage(IPage<ChannelReceivedVO> page, ChannelReceivedVO channelReceived);

}
