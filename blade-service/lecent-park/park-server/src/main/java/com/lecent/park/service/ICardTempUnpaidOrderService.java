package com.lecent.park.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.CardOrder;
import com.lecent.park.entity.CardTempUnpaidOrder;
import com.lecent.park.vo.CardTempUnpaidOrderVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

public interface ICardTempUnpaidOrderService  extends BaseService<CardTempUnpaidOrder> {

	/**
	 * 分页查询列表
	 * @param page
	 * @param cardTempUnpaidOrderVO
	 * @return
	 */
	IPage<CardTempUnpaidOrderVO> findCardTempUnpaidOrderPage(IPage<CardTempUnpaidOrderVO> page, CardTempUnpaidOrderVO cardTempUnpaidOrderVO);
	List<CardTempUnpaidOrderVO> findCardTempUnpaidOrder(List<Long> parklotIds,Long cardId);
	CardTempUnpaidOrderVO findCardTempUnpaidOrder(Long cardId);

	/**
	 * 支付成功回调更新状态
	 * @param orders
	 * @return
	 */
	public boolean paySucceedCallBback(List<CardOrder> orders);

}
