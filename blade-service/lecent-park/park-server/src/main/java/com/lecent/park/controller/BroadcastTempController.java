package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.BroadcastTempDTO;
import com.lecent.park.entity.BroadcastTemp;
import com.lecent.park.service.IBroadcastTempService;
import com.lecent.park.vo.BroadcastTempVO;
import com.lecent.park.wrapper.BroadcastTempWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 设置设置表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("/broadcast-temp")
@Api(value = "设置设置表", tags = "设置设置表接口")
public class BroadcastTempController extends BladeController {

	private IBroadcastTempService broadcastTempService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入BroadcastTemp")
	public R<BroadcastTempVO> detail(BroadcastTemp broadcastTemp) {
		BroadcastTemp detail = broadcastTempService.getOne(Condition.getQueryWrapper(broadcastTemp));
		return R.data(BroadcastTempWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 设置设置表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入BroadcastTemp")
	public R<IPage<BroadcastTempVO>> list(BroadcastTemp broadcastTemp, Query query) {
		IPage<BroadcastTemp> pages = broadcastTempService.page(Condition.getPage(query),
															   Condition.getQueryWrapper(broadcastTemp));
		return R.data(BroadcastTempWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 设置设置表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入BroadcastTemp")
	public R<IPage<BroadcastTempVO>> page(BroadcastTempVO broadcastTemp, Query query) {
		IPage<BroadcastTempVO> pages = broadcastTempService.selectBroadcastTempPage(Condition.getPage(query),
			broadcastTemp);
		return R.data(pages);
	}

	/**
	 * 新增 设置设置表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入BroadcastTemp")
	public R save(@Valid @RequestBody BroadcastTemp broadcastTemp) {
		return R.status(broadcastTempService.save(broadcastTemp));
	}

	/**
	 * 修改 设置设置表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入BroadcastTemp")
	public R update(@Valid @RequestBody BroadcastTemp broadcastTemp) {
		return R.status(broadcastTempService.updateById(broadcastTemp));
	}

	/**
	 * 保存设置内容
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入BroadcastTemp")
	public R submit(@Valid @RequestBody BroadcastTempDTO broadcastTempDTO) {
		if (Func.isNull(broadcastTempDTO.getId())) {
			int count = broadcastTempService.count(Wrappers.<BroadcastTemp>lambdaQuery().eq(BroadcastTemp::getName,
				broadcastTempDTO.getName()));
			LecentAssert.isTrue(count == 0, "模板名称重复");
		}
		return R.status(broadcastTempService.saveOrUpdate(broadcastTempDTO));
	}

	/**
	 * 删除 设置设置表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(broadcastTempService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 获取租户下所有的有效设置模板
	 */
	@GetMapping("/getAll")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取所有模板", notes = "获取所有模板")
	public R<List<BroadcastTempVO>> getAll() {
		List<BroadcastTemp> list = broadcastTempService.list(Wrappers.<BroadcastTemp>lambdaQuery()
																 .eq(BroadcastTemp::getStatus, 1));
		return R.data(BroadcastTempWrapper.build().listVO(list));
	}

}
