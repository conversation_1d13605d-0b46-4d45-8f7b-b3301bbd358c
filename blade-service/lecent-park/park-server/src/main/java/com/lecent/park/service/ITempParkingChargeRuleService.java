package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.TempParkingChargeRuleDTO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.TempParkingChargeRule;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 车场计费规则 服务类
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public interface ITempParkingChargeRuleService extends BaseService<TempParkingChargeRule> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param tempParkingChargeRuleDTO
	 * @return
	 */
	IPage<TempParkingChargeRuleVO> selectTempParkingChargeRulePage(IPage<TempParkingChargeRuleVO> page,
																   TempParkingChargeRuleDTO tempParkingChargeRuleDTO);

	TempParkingChargeRuleVO getDetail(String id, String ownerId);

	boolean submitCharge(TempParkingChargeRuleDTO tempParkingChargeRuleDTO);

	boolean delCharge(String id);

	List<TempParkingChargeRule> getTempRuleList(Long parklotId);

	TempParkingChargeRuleVO selectStandardOneByParkLotId(Long parklotId);


	/**
	 * 查询计费规则
	 *
	 * @param parkLotId
	 * @param carTypes
	 * @return
	 */
	TempParkingChargeRuleVO selectOneCarTypeByParkLotId(Long parkLotId, Integer... carTypes);


	/**
	 * 查询规则List
	 *
	 * @param parkLotId id
	 * @param carTypes  types
	 * @return list
	 *
	 * @param parkLotId
	 * @return
	 */
	List<TempParkingChargeRuleVO> selectListByParkLotId(Long parkLotId, Integer... carTypes);

	/**
	 * 查询计费规则
	 *
	 * @param chargeRuleId
	 * @return
	 */
	TempParkingChargeRuleVO selectOneById(Long chargeRuleId);

	List<TempParkingChargeRule> selectSupplementCardList(Long parklotId);

	/**
	 * 判断开始结束时间是否超过提前缴费的免费离场时间
	 *
	 * @param startDate    开始时间
	 * @param endDate      结束时间
	 * @param chargeRuleId 临停规则
	 * @return 是否
	 */
	boolean isMoreThanPayLeaveTime(Date startDate, Date endDate, Long chargeRuleId);

	/**
	 * 获取车场支付完成临停免费时长
	 *
	 * @param parklot 车场
	 * @return 免费时长
	 */
	Integer getPayLeaveTimeByParkLotId(Parklot parklot);

	/**
	 * 车场临停规则文字描述
	 * @param ruleIds 规则ids
	 * @return String
	 */
	String chargeRuleDesc(String ruleIds);

	String chargeRuleDesc(Long ruleId);

	List<String> chargeRuleDesc(List<Long> ruleIds);

	Map<String, List<String>> chargeRuleList(Long parklotId);
}
