package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.UserChannelDuty;
import com.lecent.park.vo.UserChannelDutyVO;
import com.lecent.park.vo.UserChannelInfoVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 通道值班表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
public interface IUserChannelDutyService extends BaseService<UserChannelDuty> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userChannelDuty
	 * @return
	 */
	IPage<UserChannelDutyVO> selectUserChannelDutyPage(IPage<UserChannelDutyVO> page, UserChannelDutyVO userChannelDuty);

	/**
	 * 保存用户值班日志
	 *
	 * @param channelIds
	 * @return
	 */
	boolean saveLoginChannel(String channelIds);

	/**
	 * 更新用户值班日志
	 *
	 * @param userId
	 * @return
	 */
	boolean updateLogoutChannel(Long userId);

	/**
	 * 判断当前用户是否正在值班
	 *
	 * @param userId
	 * @return
	 */
	boolean getCurUserDutyStatus(Long userId);

	IPage<UserChannelDutyVO> userChannelDuty(IPage<UserChannelDutyVO> page, long userId);

	/**
	 * 获取当前值班用户的信息
	 *
	 * @param channels
	 * @return
	 */
	UserChannelInfoVO info(String channels) throws Exception;

	/**
	 * 值班中用户
	 *
	 * @param userId
	 * @return
	 */
	UserChannelDutyVO userDutying(long userId);

	UserChannelDuty getByUserId(Long userId);


	/**
	 * 岗亭端在綫更新值班日志
	 *
	 * @param channelIds
	 * @return
	 */
	boolean updateDutyLogOnLine(String channelIds);

	/**
	 * 获取当前用户值班信息
	 *
	 * @return 值班信息
	 */
	UserChannelDuty getCurrentUserDutyInfo();

}
