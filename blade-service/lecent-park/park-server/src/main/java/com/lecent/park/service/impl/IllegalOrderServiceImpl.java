package com.lecent.park.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.constant.IllegalType;
import com.lecent.park.common.constant.IsUploadEnum;
import com.lecent.park.dto.IllegalOrderDTO;
import com.lecent.park.en.parklot.ParkLotType;
import com.lecent.park.entity.IllegalOrder;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.mapper.IllegalOrderMapper;
import com.lecent.park.service.IIllegalOrderService;
import com.lecent.park.service.IParkingPlaceService;
import com.lecent.park.vo.IllegalOrderVO;
import com.lecent.park.vo.IllegalStopPeriod;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 车辆违法记录 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
@Service
public class IllegalOrderServiceImpl extends BaseServiceImpl<IllegalOrderMapper, IllegalOrder> implements IIllegalOrderService {

    @Autowired
	IParkingPlaceService placeService;

	@Override
	public IPage<IllegalOrderVO> selectIllegalOrderPage(IPage<IllegalOrderVO> page, IllegalOrderDTO illegalOrder) {
		List<IllegalOrderVO> illegalOrderVOS = baseMapper.selectIllegalOrderPage(page, illegalOrder);
		illegalOrderVOS.stream()
			.filter(i -> Objects.nonNull(i.getParklotType()))
			.forEach(i -> {
				final String parkLotTypeName = ParkLotType.getNameForValue(Integer.parseInt(i.getParklotType()));
				i.setParklotType(parkLotTypeName);
			});
		return page.setRecords(illegalOrderVOS);
	}

	@Override
	public IllegalOrderVO getDetail(Long id) {
		IllegalOrderVO detail = baseMapper.getDetail(id);
		LecentAssert.notNull(detail, "没有此违法管理信息");

		if (StrUtil.isNotBlank(detail.getParklotType())) {
			final String parkLotTypeName = ParkLotType.getNameForValue(Integer.parseInt(detail.getParklotType()));
			detail.setParklotType(parkLotTypeName);
		}

		if (detail.getIllegalType().equals(IllegalType.DISABLE_DURATION.getValue())) {
			detail.setIllegalTypeName(IllegalType.DISABLE_DURATION.getName());
		}
		if (detail.getIllegalType().equals(IllegalType.OVER_ALLOW_DURATION.getValue())) {
			detail.setIllegalTypeName(IllegalType.OVER_ALLOW_DURATION.getName());
		}
		if (detail.getIsUpload().equals(IsUploadEnum.UPLOADED.getValue())) {
			detail.setIsUploadName(IsUploadEnum.UPLOADED.getName());
		}
		if (detail.getIsUpload().equals(IsUploadEnum.NOT_UPLOAD.getValue())) {
			detail.setIsUploadName(IsUploadEnum.NOT_UPLOAD.getName());
		}
		return detail;
	}

	@Override
	public IllegalOrder insertIllegalOrderInfo(ParkingOrder parking, List<IllegalStopPeriod> periodList, Integer illegalType) {
		ParkingPlace parkingPlace = placeService.getById(parking.getPlaceId());
		IllegalOrder illegalStop = new IllegalOrderDTO();
		illegalStop.setEnterTime(parking.getEnterTime());
		illegalStop.setExitTime(Func.isNotEmpty(parking.getExitTime())?parking.getExitTime():new Date());
		illegalStop.setPlate(parking.getPlate());
		illegalStop.setImgAddr(parking.getEnterImageUrl());
		illegalStop.setIllegalPeriod(JSON.toJSONString(periodList));
		illegalStop.setParklotId(parking.getParklotId());
		illegalStop.setIllegalType(illegalType);
		if (ObjectUtil.isNotEmpty(parkingPlace)){
			illegalStop.setPlaceId(parkingPlace.getId());
			illegalStop.setRegionId(parkingPlace.getRegionId());
		}

		this.save(illegalStop);

		return illegalStop;
	}

}
