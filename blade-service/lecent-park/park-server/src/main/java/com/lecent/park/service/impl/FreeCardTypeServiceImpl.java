package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.dto.FreeCardTypeDTO;
import com.lecent.park.entity.FreeCard;
import com.lecent.park.entity.FreeCardType;
import com.lecent.park.mapper.FreeCardTypeMapper;
import com.lecent.park.service.IFreeCardService;
import com.lecent.park.service.IFreeCardTypeService;
import com.lecent.park.service.IUserParklotService;
import com.lecent.park.vo.FreeCardTypeVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 公司角色表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class FreeCardTypeServiceImpl extends BaseServiceImpl<FreeCardTypeMapper, FreeCardType> implements IFreeCardTypeService {

	@Autowired
	private IUserParklotService userParkLotService;
	@Lazy
	@Autowired
	private IFreeCardService freeCardService;

	@Override
	public IPage<FreeCardTypeVO> selectFreeCardTypePage(IPage<FreeCardTypeVO> page, FreeCardTypeVO freeCardType) {
		return page.setRecords(baseMapper.selectFreeCardTypePage(page, freeCardType));
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public boolean saveTypes(FreeCardTypeDTO freeCardTypeDTO) {
		List<FreeCardType> typeList = listAll().stream().filter(t -> t.getIsSystem() != 1).collect(Collectors.toList());

		List<FreeCardType> names = freeCardTypeDTO.getNames();
		List<String> namesList  = names.stream().map(FreeCardType::getName).collect(Collectors.toList());

		for (FreeCardType type:typeList) {
			if (!namesList.contains(type.getName())){
				int count = freeCardService.count(Wrappers.<FreeCard>lambdaQuery().eq(FreeCard::getCarType, type.getId()));
				LecentAssert.isFalse(count>0,type.getName()+"类型已有授权车辆,请先删除授权车辆!");
			}
		}
		checkName(names);
		names.forEach(freeCardType -> {
			freeCardType.setTenantId(SecureUtil.getTenantId());
		});
		remove(new QueryWrapper<FreeCardType>().lambda().eq(FreeCardType::getTenantId, SecureUtil.getTenantId()));
		return saveBatch(names);
	}

	@Override
	public FreeCardType getByName(String name) {
		return this.getOne(Wrappers.<FreeCardType>lambdaQuery().eq(FreeCardType::getName, name));
	}

	@Override
	public List<FreeCardType> listAll() {
		String tenantId = SecureUtil.getTenantId();
		List<Long> parkLotIds = userParkLotService.getParkLotIds(SecureUtil.getUserId());
		if (parkLotIds == null) {
			parkLotIds = new ArrayList<>();
		}
		parkLotIds.add(0L);
		return baseMapper.selfList(tenantId, parkLotIds);
	}

	private void checkName(List<FreeCardType> names) {
		List<String> list = names.stream().map(FreeCardType::getName).collect(Collectors.toList());
		Set set = new HashSet<>(list);
		if (set.size() < list.size()) {
			throw new ServiceException("存在重复的免费车类型，请检查后重试！");
		}
		list.forEach(name -> LecentAssert.isFalse(name.equals("公务车") ||
			name.equals("领导车辆") ||
			name.equals("供应商") ||
			name.equals("兄弟单位") ||
			name.equals("VIP"), "存在重复的免费车类型，请检查后重试！"));
	}

}
