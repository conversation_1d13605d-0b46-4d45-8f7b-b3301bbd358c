package com.lecent.park.wrapper;

import com.lecent.park.entity.UserCoupon;
import com.lecent.park.vo.UserCouponVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 用户优惠劵包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
public class UserCouponWrapper extends BaseEntityWrapper<UserCoupon, UserCouponVO> {

	public static UserCouponWrapper build() {
		return new UserCouponWrapper();
	}

	@Override
	public UserCouponVO entityVO(UserCoupon userCoupon) {
		UserCouponVO userCouponVO = BeanUtil.copy(userCoupon, UserCouponVO.class);

		//User createUser = UserCache.getUser(userCoupon.getCreateUser());
		//User updateUser = UserCache.getUser(userCoupon.getUpdateUser());
		//userCouponVO.setCreateUserName(createUser.getName());
		//userCouponVO.setUpdateUserName(updateUser.getName());

		return userCouponVO;
	}

}
