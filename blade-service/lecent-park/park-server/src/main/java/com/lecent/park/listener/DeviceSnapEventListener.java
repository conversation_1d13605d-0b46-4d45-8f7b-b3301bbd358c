package com.lecent.park.listener;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.lecent.device.dto.DeviceSnapCacheDTO;
import com.lecent.park.common.cache.DeviceSnapCaches;
import com.lecent.park.dto.VehicleDTO;
import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.service.IParkingOrderService;
import com.lecent.park.vo.RoadSideParkingDTO;
import com.leliven.park.application.parking.IRoadSideParkingBiz;
import com.leliven.park.domain.basic.place.event.ParkingPlaceCameraCaptureDomainEvent;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCapture;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCaptureRecord;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.park.common.model.ParkingDevice;
import com.leliven.vehicle.enums.PlateColor;
import com.leliven.vehicle.model.Vehicle;
import com.leliven.vehicle.util.VehicleHelper;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CacheUtils;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;


/**
 * 停车抓拍事件处理
 * <p>
 * 视屏杆没有识别到车牌信息，或网络异常导致没有接收到上报的信息，采用抓拍的方式进行入场
 *
 * <AUTHOR>
 * @date 2023/7/12
 */
@Slf4j
@Service
public class DeviceSnapEventListener {

	@Autowired
	private IRoadSideParkingBiz roadSideParkingBiz;
	@Autowired
	private IParkingOrderService parkingOrderService;
	@Autowired
	private RedisLockClient redisLockClient;

    @EventListener(condition = "#event.isTimedCapture()")
	public void deviceSnapEventHandle(ParkingPlaceCameraCaptureDomainEvent event) {
		String sn = event.getPayload().getDevice().getSn();
		redisLockClient.lockFair(DeviceSnapCaches.DEVICE_SNAP_EVENT_LISTENER + sn,
			30L,
			100L,
			() -> {
				handleParams(event);
				return true;
			}
		);
	}

	public void handleParams(ParkingPlaceCameraCaptureDomainEvent event) {
		ParkingPlaceCameraCapture cameraCapture = event.getPayload();

		Optional<ParkingPlaceCameraCaptureRecord> cameraCaptureRecord = cameraCapture.getFirstRecord();
		Date captureTime = cameraCaptureRecord.map(ParkingPlaceCameraCaptureRecord::getCaptureTime).orElse(event.getDatetime());
		String imageUrl = cameraCaptureRecord.map(ParkingPlaceCameraCaptureRecord::getImageUrl).orElse("");
		String plate = cameraCaptureRecord.map(ParkingPlaceCameraCaptureRecord::getVehicle).map(Vehicle::getPlate).orElse("");
		PlateColor plateColor = cameraCaptureRecord.map(ParkingPlaceCameraCaptureRecord::getVehicle).map(Vehicle::getPlateColor).orElse(PlateColor.UNKNOWN);

		ParkingDevice device = cameraCapture.getDevice();
		String sn = device.getSn();

		// 必须有图片
		if(Func.isBlank(imageUrl)){
			return;
		}
		// 触发时间 + 15分钟 < 当前时间
		DateTime triggerTimeNew = DateUtil.offset(captureTime, DateField.MINUTE, 15);
		if (DateUtil.compare(triggerTimeNew, DateUtil.date()) < 0) {
			log.warn("触发时间[{}]已超过15分钟, 丢弃", triggerTimeNew);
			return;
		}

		// 无牌车直接不做处理
		if (!PlateValidator.isPlate(plate)) {
			// todo 判断是否需要生成工单
			return;
		}

		ParkingPlaceIdleState placeIdleState = cameraCaptureRecord.map(ParkingPlaceCameraCaptureRecord::getParkingState).orElse(ParkingPlaceIdleState.UNKNOWN);
		if (ParkingPlaceIdleState.isUnknown(placeIdleState.getValue())) {
			log.warn("车位[{}]状态[{}]异常，丢弃", placeIdleState.getName(), placeIdleState.getValue());
			return;
		}

		ParkingSpace place = cameraCapture.getParkingSpace();

		// 查询车牌订单
		ParkingOrder parkingOrderOfPlate = this.parkingOrderService.selectPresentParking(place.getParklotId(), plate);
		// 存在当前抓拍车牌的在场停车订单
		if (Objects.nonNull(parkingOrderOfPlate)) {
			if (!place.getId().equals(parkingOrderOfPlate.getPlaceId())) {
				log.warn("车位[{}]实时抓拍车牌[{}]在其他车位处于在停，告警", place.getPayCode(), plate);
			}
			// 不做处理
			return;
		}
		// 查询车位订单
		ParkingOrder parkingOrderOfPlace = this.parkingOrderService.selectPresentByPlaceId(place.getId());
		// 车位存在在场停车订单
		if (Objects.nonNull(parkingOrderOfPlace)) {
			// 不做处理
			return;
		}

		// 抓拍三次车牌都相同，再做入场处理
		String key = DeviceSnapCaches.DEVICE_SNAP + place.getId();
		if (!CacheUtils.exists(key)) {
			CacheUtils.setEx(key, DeviceSnapCacheDTO.builder()
					.plate(plate)
					.triggerTime(captureTime)
					.imageUrl(imageUrl)
					.snapCount(1).build(),
				Duration.ofMinutes(15));
			return;
		}
		DeviceSnapCacheDTO cacheDTO = CacheUtils.get(key, DeviceSnapCacheDTO.class);
		if (!StrUtil.equals(cacheDTO.getPlate(), plate)) {
			CacheUtils.setEx(key, DeviceSnapCacheDTO.builder()
					.plate(plate)
					.triggerTime(captureTime)
					.imageUrl(imageUrl)
					.snapCount(1).build(),
				Duration.ofMinutes(15));
			return;
		}
		if (CompareUtil.compare(cacheDTO.getSnapCount(), 2) < 0) {
			cacheDTO.setSnapCount(cacheDTO.getSnapCount() + 1);
			CacheUtils.setEx(key, cacheDTO, Duration.ofMinutes(15));
			return;
		}

		if (log.isDebugEnabled()) {
			log.warn("deviceSnapEventDTO = {}", event);
		}

		if (!plateColor.equals(PlateColor.GREEN) && PlateColor.isGreen(plate)) {
			plateColor = PlateColor.GREEN;
		}

		RoadSideParkingDTO build = RoadSideParkingDTO.builder()
			.plate(cacheDTO.getPlate())
			.date(cacheDTO.getTriggerTime())
			.imageUrl(cacheDTO.getImageUrl())
			.parkingStatus(ParkingPlaceIdleState.OCCUPIED.equals(placeIdleState) ? ParkingStatusEnum.PARK_IN.getValue()
				: ParkingStatusEnum.PARK_OUT.getValue())
			.triggerType(ChannelWay.WAY_7.getValue())
			.deviceType(device.getType().getValue())
			.sn(sn)
			.vehicleDTO(
				VehicleDTO.builder()
					.plate(plate)
					.plateColorCode(VehicleHelper.plateColor2PlateColorCode(plateColor))
					.build()
			).build();
		roadSideParkingBiz.roadSideDeviceTrigger(build);
		CacheUtils.delKey(key);
	}
}
