package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.ChannelReceived;
import com.lecent.park.mapper.ChannelReceivedMapper;
import com.lecent.park.service.IChannelReceivedService;
import com.lecent.park.vo.ChannelReceivedVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 通道设备识别结果 服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@Service
public class ChannelReceivedServiceImpl extends BaseServiceImpl<ChannelReceivedMapper, ChannelReceived> implements IChannelReceivedService {

	@Override
	public IPage<ChannelReceivedVO> selectChannelReceivedPage(IPage<ChannelReceivedVO> page, ChannelReceivedVO channelReceived) {
		return page.setRecords(baseMapper.selectChannelReceivedPage(page, channelReceived));
	}

}
