package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.dto.ViolationRecordDTO;
import com.lecent.park.entity.ViolationRecord;
import com.lecent.park.mapper.ViolationRecordMapper;
import com.lecent.park.service.IViolationRecordService;
import com.lecent.park.vo.ViolationRecordVO;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 停车违章记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-13
 */
@Slf4j
@Service
public class ViolationRecordServiceImpl extends BaseServiceImpl<ViolationRecordMapper, ViolationRecord> implements IViolationRecordService {

    @Override
    public IPage<ViolationRecordVO> selectViolationRecordPage(IPage<ViolationRecordVO> page, ViolationRecordVO violationRecord) {
        List<ViolationRecordVO> list = baseMapper.selectViolationRecordPage(page, violationRecord);
        list.forEach(vo -> vo.setMinuteTime(DateUtils.getDiffMinutes(vo.getEnterTime(), vo.getExitTime())));
        return page.setRecords(list);
    }

    @Override
    public ViolationRecordVO detail(Long id) {
        ViolationRecordVO violationRecord = new ViolationRecordVO();
        violationRecord.setId(id);
        List<ViolationRecordVO> list = baseMapper.selectViolationRecordPage(new Page<>(), violationRecord);
        if (Func.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public void createViolationRecord(ViolationRecordDTO violationRecordDTO) {
        if (PlateValidator.isNoPlate(violationRecordDTO.getPlate())) {
            return;
        }

        ViolationRecord presentRecord = lambdaQuery()
                .eq(ViolationRecord::getPlate, violationRecordDTO.getPlate())
                .eq(ViolationRecord::getStatus, 1)
                .orderByDesc(ViolationRecord::getId)
                .last("limit 1")
                .one();

        if (Func.notNull(presentRecord)) {
            // 已存在记录
            presentRecord.setExitTime(violationRecordDTO.getDate());
            presentRecord.setExitImageUrl(violationRecordDTO.getImageUrl());
            presentRecord.setExitWay(violationRecordDTO.getTriggerType());
            presentRecord.setDurationTime(DateUtils.getDuration(presentRecord.getEnterTime(), presentRecord.getExitTime()));
            presentRecord.setStatus(violationRecordDTO.getIsEnter() ? 1 : 2);
            updateById(presentRecord);
        } else if (violationRecordDTO.getIsEnter()) {
            // 不存在记录，创建进场记录
            ViolationRecord violationRecord = new ViolationRecord();
            violationRecord.setPlate(violationRecordDTO.getPlate());
            violationRecord.setParklotId(violationRecordDTO.getParklotId());
            violationRecord.setPlaceId(violationRecordDTO.getPlaceId());
            violationRecord.setEnterTime(violationRecordDTO.getDate());
            violationRecord.setEnterImageUrl(violationRecordDTO.getImageUrl());
            violationRecord.setEnterWay(violationRecordDTO.getTriggerType());
            violationRecord.setExitTime(violationRecordDTO.getDate());
            violationRecord.setExitImageUrl(violationRecordDTO.getImageUrl());
            violationRecord.setExitWay(violationRecordDTO.getTriggerType());
            violationRecord.setDurationTime(DateUtils.getDuration(violationRecord.getEnterTime(), violationRecord.getExitTime()));
            violationRecord.setPayStatus(0);
            violationRecord.setStatus(1);
            save(violationRecord);
        } else {
            // 未找到违规记录，可以进行相应的错误处理或者日志记录
            log.info("未找到相应的违规记录，车牌号：" + violationRecordDTO.getPlate());
        }
    }
}
