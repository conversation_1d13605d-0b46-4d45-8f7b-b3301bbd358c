package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.BaseAppConfig;
import com.lecent.park.service.IBaseAppConfigService;
import com.lecent.park.vo.BaseAppConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 基础信息-第三方应用配置  控制器
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/base/app-config")
@Api(value = "基础信息-第三方应用配置 ", tags = "基础信息-第三方应用配置 接口")
public class BaseAppConfigController extends BladeController {

	private final IBaseAppConfigService baseAppConfigService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入baseAppConfig")
	public R<BaseAppConfig> detail(BaseAppConfig baseAppConfig) {
		BaseAppConfig detail = baseAppConfigService.getOne(Condition.getQueryWrapper(baseAppConfig));
		return R.data(detail);
	}

	/**
	 * 分页 基础信息-第三方应用配置
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入baseAppConfig")
	public R<IPage<BaseAppConfig>> list(BaseAppConfig baseAppConfig, Query query) {
		IPage<BaseAppConfig> pages = baseAppConfigService.page(Condition.getPage(query), Condition.getQueryWrapper(baseAppConfig));
		return R.data(pages);
	}

	/**
	 * 自定义分页 基础信息-第三方应用配置
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入baseAppConfig")
	public R<IPage<BaseAppConfigVO>> page(BaseAppConfigVO baseAppConfig, Query query) {
		IPage<BaseAppConfigVO> pages = baseAppConfigService.selectBaseAppConfigPage(Condition.getPage(query), baseAppConfig);
		return R.data(pages);
	}

	/**
	 * 新增 基础信息-第三方应用配置
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入baseAppConfig")
	public R save(@Valid @RequestBody BaseAppConfig baseAppConfig) {
		return R.status(baseAppConfigService.customSaveOrUpdate(baseAppConfig));
	}

	/**
	 * 修改 基础信息-第三方应用配置
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入baseAppConfig")
	public R update(@Valid @RequestBody BaseAppConfig baseAppConfig) {
		return R.status(baseAppConfigService.customSaveOrUpdate(baseAppConfig));
	}

	/**
	 * 删除 基础信息-第三方应用配置
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(baseAppConfigService.deleteLogic(Func.toLongList(ids)));
	}


}
