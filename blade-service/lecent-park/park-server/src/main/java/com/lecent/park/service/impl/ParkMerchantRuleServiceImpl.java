package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.en.merchant.MerchantRuleTypeEnum;
import com.lecent.park.entity.ParkMerchantParklot;
import com.lecent.park.entity.ParkMerchantRule;
import com.lecent.park.entity.Parklot;
import com.lecent.park.mapper.ParkMerchantRuleMapper;
import com.lecent.park.service.IParkMerchantRuleService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.ParkMerchantRuleOptionVO;
import com.lecent.park.vo.ParkMerchantRuleVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
@Service
public class ParkMerchantRuleServiceImpl extends BaseServiceImpl<ParkMerchantRuleMapper, ParkMerchantRule> implements IParkMerchantRuleService {

	@Lazy
	@Autowired
	private ParkMerchantParklotServiceImpl merchantParkLotService;
	@Autowired
	private IParklotService parkLotService;
	@Autowired
	private ISysClient sysClient;

	@Override
	public IPage<ParkMerchantRuleVO> selectParkMerchantRulePage(IPage<ParkMerchantRuleVO> page, ParkMerchantRuleVO parkMerchantRule) {

		fillDept(parkMerchantRule);

		List<ParkMerchantRuleVO> parkMerchantRuleVOS = baseMapper.selectParkMerchantRulePage(page, parkMerchantRule);
		if (CollectionUtil.isEmpty(parkMerchantRuleVOS)) {
			return page.setRecords(Collections.EMPTY_LIST);
		}
		for (ParkMerchantRuleVO parkMerchantRuleVO : parkMerchantRuleVOS) {
			String deptNames = "";
			if (StringUtil.isEmpty(parkMerchantRuleVO.getDeptIds())) {
				continue;
			}
			R<List<String>> result = sysClient.getDeptNames(parkMerchantRuleVO.getDeptIds());
			if (result.isSuccess()) {
				deptNames = Func.toStr(result.getData());
			}
			parkMerchantRuleVO.setDeptNames(deptNames);

		}
		return page.setRecords(parkMerchantRuleVOS);
	}

	/**
	 * 设置部门权限（查询本部门以及子部门）
	 *
	 * @param parkMerchantRule param
	 */
	private void fillDept(ParkMerchantRuleVO parkMerchantRule) {
		String deptIds = SecureUtil.getDeptId();
		if (StringUtil.isEmpty(deptIds)) {
			return;
		}

		List<Long> deptIdList = new ArrayList<>();
		deptIdList.addAll(Func.toLongList(deptIds));
		List<Long> childIdList = new ArrayList<>();

		for (Long deptId : deptIdList) {
			R<List<Dept>> deptResult = sysClient.getDeptChild(deptId);

			if (deptResult.isSuccess()) {
				childIdList.addAll(deptResult.getData().stream().map(Dept::getId).collect(Collectors.toList()));
			}
		}
		deptIdList.addAll(childIdList);
		parkMerchantRule.setDeptIdList(deptIdList);

	}

	@Override
	public Boolean removeLogic(List<Long> ruleIdList) {

		if (Func.isEmpty(ruleIdList)) {
			return false;
		}
		ruleIdList.forEach(id -> {
			ParkMerchantParklot merchantParkLot = merchantParkLotService.getOne(Wrappers.<ParkMerchantParklot>lambdaQuery()
				.eq(ParkMerchantParklot::getMerchantRuleId, id)
				.eq(ParkMerchantParklot::getIsDeleted, 0).last("limit 1"));
			if (Func.notNull(merchantParkLot)) {
				Parklot parklot = ParkLotCaches.getParkLot(merchantParkLot.getParklotId());
				LecentAssert.isNull(parklot, "该套餐已被[" + parklot.getName() + "]使用！");
			}
		});

		return this.deleteLogic(ruleIdList);
	}

	@Override
	public Boolean submit(ParkMerchantRule parkMerchantRule) {

		if (isRepeatRuleName(parkMerchantRule)) {
			throw new ServiceException("选择部门商户套餐名称已存在");
		}

		return this.saveOrUpdate(parkMerchantRule);
	}

	private boolean isRepeatRuleName(ParkMerchantRule parkMerchantRule) {
		return this.baseMapper.countMerchantName(parkMerchantRule) > 0;
	}

	@Override
	public List<ParkMerchantRuleOptionVO> ruleOptions() {

		List<ParkMerchantRuleOptionVO> result = Lists.newArrayList();
		for (MerchantRuleTypeEnum anEnum : MerchantRuleTypeEnum.values()) {
			List<ParkMerchantRule> ruleList = this.getByRuleType(anEnum.getValue());
			if (CollectionUtil.isEmpty(ruleList)) {
				continue;
			}
			ParkMerchantRuleOptionVO rule = new ParkMerchantRuleOptionVO();
			rule.setMerchantType(anEnum.getName());
			rule.setMerchantList(ruleList);
			result.add(rule);
		}
		return result;
	}


	@Override
	public List<ParkMerchantRule> getByRuleType(int ruleType) {

		List<Long> deptIdList = Func.toLongList(SecureUtil.getDeptId());
		List<Long> deptIds = new ArrayList<>();

		for (Long deptId : deptIdList) {
			R<List<Long>> deptResult = sysClient.getPidAndChildId(deptId);
			if (deptResult.isSuccess()) {
				deptIds = deptResult.getData();
			}
		}
		return baseMapper.getByRuleTypeDeptIds(deptIds, ruleType);

	}
}
