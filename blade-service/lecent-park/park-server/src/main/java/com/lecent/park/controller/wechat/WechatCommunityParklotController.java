package com.lecent.park.controller.wechat;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.CommunityParklotDTO;
import com.lecent.park.service.ICommunityParklotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 社区与车场关联表 控制器
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wechat/community-parklot")
@Api(value = "社区与车场关联表", tags = "社区与车场关联表接口")
public class WechatCommunityParklotController extends BladeController {

	private ICommunityParklotService communityParklotService;


	/**
	 * 获取社区的车场
	 */
	@GetMapping("/list-by-communityId")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取社区的车场", notes = "获取社区的车场")
	public R<List<String>> listByCommunityId(
		@ApiParam(value = "社区主键", required = true) @RequestParam @NotBlank String communityId) {
		return R.data(communityParklotService.listByCommunityId(Long.valueOf(communityId)));
	}


	/**
	 * 保存社区与车场关联关系
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "保存社区与车场关联关系", notes = "保存社区与车场关联关系")
	public R save(
		@RequestBody CommunityParklotDTO communityParklotDto) {
		return R.status(communityParklotService.saveRelation(communityParklotDto));
	}

}
