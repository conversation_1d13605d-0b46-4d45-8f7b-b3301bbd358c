package com.lecent.park.controller;

import javax.servlet.http.HttpServletResponse;

import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.CorpseCleanLogDTO;
import com.lecent.park.service.ICorpseCleanLogService;
import com.lecent.park.vo.CorpseCleanLogVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

/**
 * 僵尸车
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/corpse")
@Api(value = "僵尸车", tags = "僵尸车")
public class CorpseController extends BladeController {

    private ICorpseCleanLogService corpseCleanLogService;

    /**
     * 自定义分页 僵尸车清理记录
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页", notes = "传入corpseCleanLog")
    public R<IPage<CorpseCleanLogVO>> page(CorpseCleanLogDTO corpseCleanLogDto, Query query) {
        corpseCleanLogDto.setParkLotIdList(Func.toLongList(corpseCleanLogDto.getParklotIds()));
        IPage<CorpseCleanLogVO> pages = corpseCleanLogService.selectCorpsePage(Condition.getPage(query), corpseCleanLogDto);
        return R.data(pages);
    }

    @PostMapping(value = "/corpse-export")
    @ApiOperation(value = "僵尸车导出", notes = "传入corpseCleanLog")
    public void corpseExport(CorpseCleanLogDTO corpseCleanLogDto, HttpServletResponse response) {
        corpseCleanLogDto.setParkLotIdList(Func.toLongList(corpseCleanLogDto.getParklotIds()));
        corpseCleanLogService.corpseExport(corpseCleanLogDto, response);
    }

}
