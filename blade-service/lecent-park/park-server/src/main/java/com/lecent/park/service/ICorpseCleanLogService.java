package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.CorpseCleanLogDTO;
import com.lecent.park.entity.CorpseCleanLog;
import com.lecent.park.vo.CorpseCleanLogVO;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 僵尸车清理记录 服务类
 *
 * <AUTHOR>
 * @since 2020-06-20
 */
public interface ICorpseCleanLogService extends BaseService<CorpseCleanLog> {

    /**
     * 自定义分页
     *
     * @param page              分页对象
     * @param corpseCleanLogDto 尸体清理日志DTO对象
     * @return 分页后的尸体清理日志VO对象
     */
    IPage<CorpseCleanLogVO> selectCorpseCleanLogPage(IPage<CorpseCleanLogVO> page, CorpseCleanLogDTO corpseCleanLogDto);

    /**
     * 清理尸体车辆
     *
     * @param parkingOrderIds 停车订单ID列表
     * @param reason          清理原因
     * @return 清理结果
     */
    boolean cleanCorpseCar(List<Long> parkingOrderIds, String reason);

    /**
     * 导出尸体清理日志
     *
     * @param corpseCleanLogDTO 尸体清理日志DTO对象
     * @param response          HTTP响应对象
     */
    void corpseCleanLogExport(CorpseCleanLogDTO corpseCleanLogDTO, HttpServletResponse response);

    /**
     * 导出尸体
     *
     * @param corpseCleanLogDTO 尸体清理日志DTO对象
     * @param response          HTTP响应对象
     */
    void corpseExport(CorpseCleanLogDTO corpseCleanLogDTO, HttpServletResponse response);

    /**
     * 分页查询尸体清理日志
     *
     * @param page              分页对象
     * @param corpseCleanLogDto 尸体清理日志DTO对象
     * @return 分页后的尸体清理日志对象
     */
    IPage<CorpseCleanLogVO> selectCorpsePage(IPage page, CorpseCleanLogDTO corpseCleanLogDto);

    /**
     * 查询尸体清理日志列表
     *
     * @param page              分页对象
     * @param corpseCleanLogDto 尸体清理日志DTO对象
     * @return 尸体清理日志列表
     */
    List<CorpseCleanLogVO> selectCorpses(IPage page, CorpseCleanLogDTO corpseCleanLogDto);

    /**
     * 恢复记录
     *
     * @param id 记录ID
     * @return 恢复结果
     */
    String restoreRecords(Long id);

    /**
     * 批量恢复记录
     *
     * @param ids 记录ID列表
     * @return 恢复结果
     */
    String restoreRecordsIds(List<Long> ids);
}
