package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.CommunityParklotDTO;
import com.lecent.park.entity.CommunityParklot;
import com.lecent.park.vo.CommunityParklotVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 社区与车场关联表 服务类
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
public interface ICommunityParklotService extends BaseService<CommunityParklot> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param communityParklot
	 * @return
	 */
	IPage<CommunityParklotVO> selectCommunityParklotPage(IPage<CommunityParklotVO> page, CommunityParklotVO communityParklot);

	/**
	 * 获取社区的车场
	 *
	 * @param communityId 社区主键
	 * @return
	 */
	List<String> listByCommunityId(Long communityId);

	/**
	 * 保存社区与车场关联关系
	 * @param communityParklotDto
	 * @return
	 */
	boolean saveRelation(CommunityParklotDTO communityParklotDto);
}
