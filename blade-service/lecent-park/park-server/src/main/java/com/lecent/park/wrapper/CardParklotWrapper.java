package com.lecent.park.wrapper;

import com.lecent.park.entity.CardParklot;
import com.lecent.park.vo.CardParklotVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 套餐车场表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class CardParklotWrapper extends BaseEntityWrapper<CardParklot, CardParklotVO>  {

	public static CardParklotWrapper build() {
		return new CardParklotWrapper();
 	}

	@Override
	public CardParklotVO entityVO(CardParklot cardParklot) {
		CardParklotVO cardParklotVO = BeanUtil.copy(cardParklot, CardParklotVO.class);

		//User createUser = UserCache.getUser(cardParklot.getCreateUser());
		//User updateUser = UserCache.getUser(cardParklot.getUpdateUser());
		//cardParklotVO.setCreateUserName(createUser.getName());
		//cardParklotVO.setUpdateUserName(updateUser.getName());

		return cardParklotVO;
	}

}
