package com.lecent.park.service;

import com.lecent.park.entity.ReceiptPrintLog;
import com.lecent.park.vo.ReceiptPrintLogVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 小票打印记录 服务类
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface IReceiptPrintLogService extends BaseService<ReceiptPrintLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param receiptPrintLog
	 * @return
	 */
	IPage<ReceiptPrintLogVO> selectReceiptPrintLogPage(IPage<ReceiptPrintLogVO> page, ReceiptPrintLogVO receiptPrintLog);

	List<ReceiptPrintLogVO> selectReceiptPrintLogByParkingId(Long parkingId);
}
