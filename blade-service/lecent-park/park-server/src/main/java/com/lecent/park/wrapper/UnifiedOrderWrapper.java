package com.lecent.park.wrapper;

import com.lecent.park.common.constant.PayWayEnum;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.entity.PayWayEntity;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.payment.dto.UnifiedOrderDTO;
import com.lecent.payment.enums.PayType;

/**
 * 调用支付的请求参数组装
 *
 * <AUTHOR>
 * @since 2022-03-14
 */
public class UnifiedOrderWrapper {
	/**
	 * 小程序主扫支付请求参数
	 *
	 * @param channelTodo      待办
	 * @param tempParkingOrder 临停订单
	 * @param payWay           支付方式
	 * @param body             body
	 * @param returnUrl        returnUrl
	 * @param openId           openId
	 * @return UnifiedOrderDTO
	 */
	public static UnifiedOrderDTO miniScanEntity(ChannelTodo channelTodo, TempParkingOrder tempParkingOrder, PayWayEntity payWay,
												 String body, String returnUrl, String openId, String appId) {
		UnifiedOrderDTO unifiedOrder = new UnifiedOrderDTO();
		unifiedOrder.setOpenid(openId);
		unifiedOrder.setTenantId(channelTodo.getTenantId());
		unifiedOrder.setAttach("停车费用支付-小程序-主扫二维码");
		unifiedOrder.setBody(body);
		unifiedOrder.setTradeType(PayWayEnum.WX.getValue().equals(payWay.getPayTypeVal()) ? PayType.CCB_JSAPI.name() : PayType.CCB_MINIAPP.name());
		unifiedOrder.setMerchantIds(payWay.getPayId());
		unifiedOrder.setTotalFee(String.valueOf(tempParkingOrder.getReceiveAmount()));
		unifiedOrder.setReturnUrl(returnUrl + "?orderId=" + tempParkingOrder.getTradeNo());
		//小程序或者公众号需要appId
		unifiedOrder.setAppId(appId);
		unifiedOrder.setOutTradeNo(tempParkingOrder.getTradeNo());
		return unifiedOrder;
	}
}
