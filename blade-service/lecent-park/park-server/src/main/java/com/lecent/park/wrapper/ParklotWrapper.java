package com.lecent.park.wrapper;

import com.lecent.park.entity.Parklot;
import com.lecent.park.vo.ParklotVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class ParklotWrapper extends BaseEntityWrapper<Parklot, ParklotVO>  {

	public static ParklotWrapper build() {
		return new ParklotWrapper();
 	}

	@Override
	public ParklotVO entityVO(Parklot parklot) {
		ParklotVO parklotVO = BeanUtil.copy(parklot, ParklotVO.class);

		//User createUser = UserCache.getUser(parklot.getCreateUser());
		//User updateUser = UserCache.getUser(parklot.getUpdateUser());
		//parklotVO.setCreateUserName(createUser.getName());
		//parklotVO.setUpdateUserName(updateUser.getName());

		return parklotVO;
	}

}
