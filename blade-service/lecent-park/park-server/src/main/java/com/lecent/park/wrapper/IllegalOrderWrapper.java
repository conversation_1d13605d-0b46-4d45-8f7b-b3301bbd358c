package com.lecent.park.wrapper;

import com.lecent.park.entity.IllegalOrder;
import com.lecent.park.vo.IllegalOrderVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车辆违法记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
public class IllegalOrderWrapper extends BaseEntityWrapper<IllegalOrder, IllegalOrderVO>  {

	public static IllegalOrderWrapper build() {
		return new IllegalOrderWrapper();
 	}

	@Override
	public IllegalOrderVO entityVO(IllegalOrder illegalOrder) {
		IllegalOrderVO illegalOrderVO = BeanUtil.copy(illegalOrder, IllegalOrderVO.class);

		//User createUser = UserCache.getUser(illegalOrder.getCreateUser());
		//User updateUser = UserCache.getUser(illegalOrder.getUpdateUser());
		//illegalOrderVO.setCreateUserName(createUser.getName());
		//illegalOrderVO.setUpdateUserName(updateUser.getName());

		return illegalOrderVO;
	}

}
