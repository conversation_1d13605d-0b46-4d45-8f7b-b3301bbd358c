package com.lecent.park.wrapper;

import com.lecent.park.entity.CommunityParklot;
import com.lecent.park.vo.CommunityParklotVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 社区与车场关联表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
public class CommunityParklotWrapper extends BaseEntityWrapper<CommunityParklot, CommunityParklotVO>  {

	public static CommunityParklotWrapper build() {
		return new CommunityParklotWrapper();
 	}

	@Override
	public CommunityParklotVO entityVO(CommunityParklot communityParklot) {
		CommunityParklotVO communityParklotVO = BeanUtil.copy(communityParklot, CommunityParklotVO.class);

		//User createUser = UserCache.getUser(communityParklot.getCreateUser());
		//User updateUser = UserCache.getUser(communityParklot.getUpdateUser());
		//communityParklotVO.setCreateUserName(createUser.getName());
		//communityParklotVO.setUpdateUserName(updateUser.getName());

		return communityParklotVO;
	}

}
