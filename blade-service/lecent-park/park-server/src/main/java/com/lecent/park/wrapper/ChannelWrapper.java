package com.lecent.park.wrapper;

import com.lecent.park.entity.Channel;
import com.lecent.park.vo.ChannelVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 车场通道包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public class ChannelWrapper extends BaseEntityWrapper<Channel, ChannelVO>  {

	public static ChannelWrapper build() {
		return new ChannelWrapper();
 	}

	@Override
	public ChannelVO entityVO(Channel channel) {
		ChannelVO channelVO = BeanUtil.copy(channel, ChannelVO.class);

		//User createUser = UserCache.getUser(channel.getCreateUser());
		//User updateUser = UserCache.getUser(channel.getUpdateUser());
		//channelVO.setCreateUserName(createUser.getName());
		//channelVO.setUpdateUserName(updateUser.getName());

		return channelVO;
	}

}
