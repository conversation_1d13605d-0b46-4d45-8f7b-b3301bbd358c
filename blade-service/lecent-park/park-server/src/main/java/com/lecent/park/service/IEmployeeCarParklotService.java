package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.EmployeeCarParklot;
import com.lecent.park.vo.EmployeeCarParklotVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 员工车辆关联车场表 服务类
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
public interface IEmployeeCarParklotService extends BaseService<EmployeeCarParklot> {

	/**
	 * 自定义分页
	 *
	 * @param page               分页对象
	 * @param employeeCarParklot 员工车辆停车场对象
	 * @return 分页结果对象
	 */
	IPage<EmployeeCarParklotVO> selectEmployeeCarParklotPage(IPage<EmployeeCarParklotVO> page, EmployeeCarParklotVO employeeCarParklot);

	/**
	 * 获取员工车辆联系人信息
	 *
	 * @param id 员工ID
	 * @return 员工车辆联系人列表
	 */
	List<EmployeeCarParklotVO> employeeCarContact(Long id);
}
