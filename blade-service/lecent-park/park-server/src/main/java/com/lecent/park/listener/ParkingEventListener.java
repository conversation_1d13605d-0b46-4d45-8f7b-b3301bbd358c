package com.lecent.park.listener;

import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.park.core.notify.base.BaseMsgReceiver;
import com.lecent.park.dto.VehicleDTO;
import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.vo.RoadSideParkingDTO;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.application.parking.IRoadSideParkingBiz;
import com.leliven.park.common.model.valueobject.BasicParkingScene;
import com.leliven.park.domain.parking.core.model.objectvalue.RoadsideParkingLockCtrlSceneParam;
import com.leliven.park.domain.parking.core.support.RoadsideParkingLockCtrlProcessor;
import com.leliven.vehicle.enums.PlateColor;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.LecentAppConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE;
import static com.lecent.park.core.mq.rabbitmq.queue.Queues.LECENT_PARK_MAGNETIC_ENTER_DELAY_QUEUE;
import static com.lecent.park.core.mq.rabbitmq.queue.Queues.LECENT_PARK_UNLOCK_DELAY_QUEUE;
import static com.lecent.park.core.mq.rabbitmq.routing.RoutingKeys.LECENT_PARK_MAGNETIC_ENTER;
import static com.lecent.park.core.mq.rabbitmq.routing.RoutingKeys.LECENT_PARK_UNLOCK;


/**
 * 停车事件监听
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParkingEventListener extends BaseMsgReceiver<DeviceParkingEventDTO> {

	@Resource
	private IRoadSideParkingBiz roadSideParkingBiz;
	@Resource
	private RoadsideParkingLockCtrlProcessor lockCtrlProcessor;

	protected ParkingEventListener() {
		super(2000L, 3, LecentAppConstant.APPLICATION_DEVICE_NAME, DeviceMQConstant.ENTER_EXIT_EVENT_CODE);
	}

	@Override
	public void accept(DeviceParkingEventDTO req) {
		String plateColorCode = PlateColor.resolve(req.getPlateColor()).code();

		if (!plateColorCode.equals(PlateColor.GREEN.code()) && PlateColor.isGreen(req.getPlate())) {
			plateColorCode = PlateColor.GREEN.code();
		}

		log.info("deviceTriggerDTO = {}", req);
		VehicleDTO vehicleDTO = VehicleDTO.builder()
			.plate(req.getPlate())
			.plateColorCode(plateColorCode)
			.build();

		// 过滤掉进出场时间和系统时间相差30分钟的事件
		if (!DeviceType.PATROL_VEHICLE.equals(req.getDeviceType())
			&& Math.abs(req.getTriggerTime().getTime() - System.currentTimeMillis()) > 30 * 60 * 1000) {
			log.info("进出场时间和系统时间相差30分钟的事件，不处理");
			return;
		}

		RoadSideParkingDTO build = RoadSideParkingDTO.builder()
			.plate(req.getPlate())
			.plateColor(plateColorCode)
			.date(req.getTriggerTime())
			.imageUrl(req.getDetectionPicUrl())
			.imageUrlMap(req.getImageUrlMap())
			.parkingStatus(req.getType())
			.triggerType(toChannelWay(req.getDeviceType()).getValue())
			.deviceType(req.getDeviceType().getValue())
			.sn(req.getDeviceSn())
			.payCode(req.getPayCode())
			.vehicleDTO(vehicleDTO)
			.build();

		if (!resetDate(build)) {
			return;
		}
		
		roadSideParkingBiz.roadSideDeviceTrigger(build);
	}

	private boolean resetDate(RoadSideParkingDTO req) {
		try {
			if (ChannelWay.WAY_12.getValue().equals(req.getTriggerType())) {
				if (req.getDate().before(DateUtil.toDate(LocalDate.now().withDayOfYear(1)))) {
					if (req.isEnter()) {
						Date now = new Date();
						log.info("进场日期[{}]小于本年，重置为当前[{}]", DateUtil.formatDateTime(req.getDate()),
								DateUtil.formatDateTime(now));
						req.setDate(now);
					} else {
						log.info("出场日期[{}]小于本年，不处理", DateUtil.formatDateTime(req.getDate()));
						return false;
					}
				}
			}
		} catch (Exception e) {
			log.error("reset enter date failed! ", e);
		}
		return true;
	}

	public ChannelWay toChannelWay(DeviceType deviceType) {
		switch (deviceType) {
			case PARKING_LOCK:
				return ChannelWay.WAY_5;
			case MAGNETIC:
				return ChannelWay.WAY_6;
			case PATROL_VEHICLE:
				return ChannelWay.WAY_12;
			default:
				return ChannelWay.WAY_1;
		}
	}

    @RabbitListener(bindings = {
            @QueueBinding(
                    value = @Queue(value = LECENT_PARK_MAGNETIC_ENTER_DELAY_QUEUE, durable = StringPool.TRUE),
                    exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
                    key = LECENT_PARK_MAGNETIC_ENTER
            )
    })
    public void magneticTriggerDelayHandler(Message message) {
        try {
            RoadSideParkingDTO parkingDTO = Func.readJson(message.getBody(), RoadSideParkingDTO.class);
            log.info("magneticTriggerDelayHandler parkingDTO={}", parkingDTO);
            if (Objects.isNull(parkingDTO)) {
                log.error("magneticTriggerDelayHandler 参数值有误");
                return;
            }

            roadSideParkingBiz.roadSideDeviceTrigger(parkingDTO);
        } catch (Exception e) {
            log.error("magneticTriggerDelayHandler error:", e);
        }
    }

	@RabbitListener(bindings = {
			@QueueBinding(
					value = @Queue(value = LECENT_PARK_UNLOCK_DELAY_QUEUE, durable = StringPool.TRUE),
					exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
					key = LECENT_PARK_UNLOCK
			)
	})
	public void delayUnlockHandler(Message message) {
        try {
            RoadSideParkingDTO parkingDTO = Func.readJson(message.getBody(), RoadSideParkingDTO.class);
            log.info("delayUnlockHandler parkingDTO={}", parkingDTO);
            if (Objects.isNull(parkingDTO)) {
                log.error("delayUnlockHandler 参数值有误");
                return;
            }
            lockCtrlProcessor.asyncUnlock(
                new RoadsideParkingLockCtrlSceneParam(parkingDTO.getPlaceId(), BasicParkingScene.DELAYED_UNLOCK));
        } catch (Exception e) {
            log.error("delayUnlockHandler error:", e);
        }
    }

}
