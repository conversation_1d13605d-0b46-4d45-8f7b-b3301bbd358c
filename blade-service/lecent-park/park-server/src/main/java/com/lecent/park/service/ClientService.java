package com.lecent.park.service;

import com.lecent.park.charge.ProjectCost;
import com.lecent.park.dto.ClientTodo;
import com.lecent.park.dto.MiniScanChannelTodoDTO;
import com.lecent.park.dto.ResParkingOrderInfo;
import com.lecent.park.dto.req.ReqParkingPay;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.entity.Parklot;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.MiniScanPayDTO;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import org.springblade.common.payment.PayResult;
import org.springblade.core.tool.api.R;

import java.util.Date;
import java.util.List;

/**
 * 微信扫码服务接口
 */
public interface ClientService {
	/**
	 * 停车计费
	 *
	 * @param parkLotId 停车场ID
	 * @param plate     车牌号
	 * @param openId    用户openid
	 * @return 停车计费结果
	 */
	ChannelTodoVO parkingCharge(Long parkLotId, String plate, String openId);

	/**
	 * 创建支付代办
	 * @param parkLotId 车场id
	 * @param plate 车牌
	 * @param openId openId
	 * @return ChannelTodoVO 代办
	 */
	ChannelTodoVO createPayTodo(Long parkLotId, String plate, String openId);

	/**
	 * 计算停车费用
	 *
	 * @param parkLotId 停车场ID
	 * @param plate     车牌号
	 * @param openId    用户openid
	 * @param date      停车时间
	 * @return 返回停车费用
	 */
	ChannelTodoVO parkingCost(Long parkLotId, String plate, String openId, Date date);

	/**
	 * 通用计费
	 *
	 * @param parkLotId 停车场ID
	 * @param startDate 开始时间
	 * @param endDate   结束时间
	 * @return 通用计费结果
	 */
	ProjectCost commonCharge(Long parkLotId, Date startDate, Date endDate);

	/**
	 * 获取有效车牌
	 *
	 * @param channelId 通道ID
	 * @return 有效车牌号
	 */
	String getChannelValidPlate(Long channelId);

	/**
	 * 微信扫码进出场
	 *
	 * @param clientTodo 客户端请求数据
	 * @return 进出场结果
	 */
	ChannelTodoVO clientTriggerCreateTodo(ClientTodo clientTodo);

	/**
	 * 微信扫码出场数据封装
	 *
	 * @param channelTodoVO 通道数据对象
	 * @return 封装后的通道数据对象
	 */
	ChannelTodoVO resultDataPackage(ChannelTodoVO channelTodoVO);

	/**
	 * 获取收费规则
	 *
	 * @param clientTodo 客户端请求数据
	 * @return 收费规则对象
	 */
	TempParkingChargeRuleVO getChargingRules(ClientTodo clientTodo);

	/**
	 * 出场扫码付款
	 *
	 * @param reqParkingPay 支付请求数据
	 * @return 支付结果
	 */
	R payment(ReqParkingPay reqParkingPay);

	/**
	 * 场内扫码
	 *
	 * @param clientTodo 客户端请求数据
	 * @return 扫码结果
	 */
	ChannelTodoVO codeScanning(ClientTodo clientTodo);

	/**
	 * 通道付款是否成功
	 *
	 * @param outTradeNo 订单号
	 * @return 支付成功或失败的结果
	 */
	R payCallback(String outTradeNo);

	/**
	 * 场内付款是否成功
	 *
	 * @param outTradeNo 订单号
	 * @return 支付成功或失败的结果
	 */
	R siteSuccessOrFail(String outTradeNo);

	/**
	 * 处理etc支付后的逻辑
	 *
	 * @param todoId todoID
	 */
	void resolveETCPay(String todoId);

	/**
	 * Mq付款成功回调
	 *
	 * @param tradeNo 订单号
	 */
	void mqPaySuccessCallback(String tradeNo);

	/**
	 * Mq付款成功回调
	 *
	 * @param orderNo      订单号
	 * @param payload  	   支付消息体
	 */
	void mqPaySuccessCallback(String orderNo, PaymentSuccessPayload payload);

	/**
	 * 小程序端发起支付
	 *
	 * @param payDTO 支付参数
	 * @return 支付结果
	 */
	PayResult miniScanPay(MiniScanPayDTO payDTO);

	/**
	 * 微信小程序扫码端触发
	 *
	 * @param channelTodoDTO todoDto
	 * @return todoVo
	 */
	ResParkingOrderInfo miniScanClientTrigger(MiniScanChannelTodoDTO channelTodoDTO);

	/**
	 * 合并支付
	 *
	 * @param payDTO 支付参数
	 * @return 支付结果
	 */
	PayResult miniMergePay(MiniScanPayDTO payDTO);

	/**
	 * 获取当前openId的所有未支付放单
	 *
	 * @param clientTodo 客户端请求数据
	 * @param parklot    停车场对象
	 * @return 未支付的停车订单列表
	 */
	List<ParkingOrder> getPlateByOpenId(ClientTodo clientTodo, Parklot parklot);
}
