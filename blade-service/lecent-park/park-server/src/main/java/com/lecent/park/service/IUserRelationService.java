package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.UserRelation;
import com.lecent.park.vo.UserRelationVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 微信用户亲友信息 服务类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
public interface IUserRelationService extends BaseService<UserRelation> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userRelation
	 * @return
	 */
	IPage<UserRelationVO> selectUserRelationPage(IPage<UserRelationVO> page, UserRelationVO userRelation);

	List<UserRelationVO> listMineRelation();

}
