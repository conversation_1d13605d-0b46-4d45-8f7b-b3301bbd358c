package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.MerchantParklotAddPlateDTO;
import com.lecent.park.dto.ParkMerchantParklotPlateDTO;
import com.lecent.park.entity.ParkMerchantParklot;
import com.lecent.park.entity.ParkMerchantParklotPlate;
import com.lecent.park.vo.ParkMerchantParklotPlateVO;
import com.lecent.park.vo.ParklotPlatePageVO;
import org.springblade.core.mp.base.BaseService;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家车场授权车牌表 服务类
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
public interface IParkMerchantParklotPlateService extends BaseService<ParkMerchantParklotPlate> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parkMerchantParklotPlate
	 * @return
	 */
	IPage<ParklotPlatePageVO> selectParkMerchantParklotPlatePage(IPage<ParklotPlatePageVO> page, ParkMerchantParklotPlateDTO parkMerchantParklotPlate);

	/**
	 * 根据车牌查询授权信息
	 * @param plate
	 * @param parkLotId
	 * @param enterDate
	 * @return
	 */
	ParkMerchantParklotPlateVO selectByPlateOrOpenId(String plate, String openId, Long parkLotId, Date enterDate);


	/**
	 * 校验车牌号是否需要场内授权
	 *
	 * @param plate     车牌号
	 * @param parklotId 车场id
	 * @return 入场时间
	 */
	String validatePlateNeedAuthInPark(String plate, Long parklotId);


	/**
	 * 商户授权车辆过期时若还在场则释放车位
	 * @param authPlate
	 */
	void releaseParkingSpace(ParkMerchantParklotPlate authPlate);

	/**
	 * 保存授权车牌
	 * @param merchantPlate 授权信息
	 * @return 授权结果
	 */
	ParkMerchantParklotPlate saveAuthPlate(MerchantParklotAddPlateDTO merchantPlate);

	/**
	 * 发送授权截止日期延时队列(授权过期时处理)
	 *
	 * @param authPlate 车辆授权信息
	 */
	void sendExpireDelayMessage(ParkMerchantParklotPlate authPlate);

	/**
	 * @param id
	 * @param authEndTime 授权结束时间
	 * @return 操作结果
	 */
	Boolean updateAuthEndTime(Long id, String authEndTime);

	/**
	 * 车场授权车辆详情
	 *
	 * @param id 主键id
	 * @return 车辆详情
	 */
	ParklotPlatePageVO parkCarDetail(Long id);

	/**
	 * 无牌车授权
	 * @param id  车牌授权ID
	 * @param openId 扫码微信ID
	 * @return
	 */
	Boolean noPlateAuth(Long id, String openId);

	/**
	 * 根据授权截止时间查询
	 * @param plate
	 * @param parkLotId
	 * @param date
	 * @return
	 */
	ParkMerchantParklotPlate selectByEndAuthDate(String plate, Long parkLotId, Date date);



	/**
	 * 是否可授权不在场车辆
	 * @param parkLotId
	 * @return
	 */
	Boolean isCanAuthLeaveCar(Long parkLotId);

	/**
	 * @param merchantPlate
	 * @return
	 */
	BigDecimal getAuthTimeoutCost(MerchantParklotAddPlateDTO merchantPlate);

	/**
	 * 取消授权
	 *
	 * @param id
	 * @return
	 */
	Boolean cancelAuth(Long id);

	/**
	 * 获取当日授权数量
	 *
	 * @param parklotId 车场ID
	 * @return 授权数量
	 */
	Integer getTodayAuthNum(Long parklotId, Long merchantId);

	/**
	 * 获取车场已经授权的车位数量
	 *
	 * @param merchantId
	 * @param parklotId  车场ID
	 * @return 授权数量
	 */
	Integer getAuthedPlaceByParklotId(Long parklotId, Long merchantId);

	/**
	 * 每天扫描授权车辆（结算）
	 */
	void everyDayCheckSettlement();


	/**
	 * 结算周期总时长
	 * @param placeNum
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	Long cycleTotalTimeLength(Integer placeNum, Date startDate, Date endDate);

	/**
	 * 结算周期剩余时长
	 *
	 * @param merchantParkLot
	 * @return
	 */
	Long cycleSurplusTimeLength(ParkMerchantParklot merchantParkLot);

	/**
	 * 生成商户授权无牌车二维码
	 *
	 * @param id 授权ID
	 * @return base64二维码
	 */
	String createNoPlateQr(Long id);

	/**
	 * 用户主动授权
	 * @param merchantPlate 授权信息
	 * @return 授权结果
	 */
	ParklotPlatePageVO activeAuth(MerchantParklotAddPlateDTO merchantPlate);
}
