package com.lecent.park.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.bizoptlog.AroundOpt;
import com.lecent.park.bizoptlog.AroundOptHeader;
import com.lecent.park.bizoptlog.OptTypeEnum;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.common.utils.PlateCheckUtils;
import com.lecent.park.common.enums.card.CardStatusConstants;
import com.lecent.park.common.constant.ParkingStatus;
import com.lecent.park.common.constant.VisitorAuthEnum;
import com.lecent.park.dto.ReqVisitorAuth;
import com.lecent.park.dto.VisitorAuthDTO;
import com.lecent.park.entity.*;
import com.lecent.park.mapper.VisitorAuthMapper;
import com.lecent.park.service.*;
import com.lecent.park.third.CarTypeEnum;
import com.lecent.park.third.ThirdHttpCar;
import com.lecent.park.third.ThirdHttpRes;
import com.lecent.park.third.ThirdHttpVisitorInfo;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.VisitorAuthVO;
import com.lecent.park.wrapper.VisitorAuthWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.UrlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 访客授权表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Slf4j
@Service
public class VisitorAuthServiceImpl extends BaseServiceImpl<VisitorAuthMapper, VisitorAuth> implements IVisitorAuthService {
	/**
	 * 域名模板
	 */
	private static final String LONG_URL_TEMPLATE = "{}/lecent-payment/wxAuth/getOpenId?tenantId={}&returnUrl={}";

	@Autowired
	private IParkingOrderService parkingOrderService;

	@Autowired
	private IParklotService parklotService;

	@Autowired
	IUserParklotService userParklotService;

	@Autowired
	private ICommunityParklotService communityParklotService;

	@Autowired
	private IOwnerDetailInfoService ownerDetailInfoService;

	@Autowired
	private ThirdCRHJService thirdCRHJService;

	@Autowired
	private IDomainService domainService;

	@Autowired
	private IPlatePropertyService platePropertyService;

	@Override
	public IPage<VisitorAuthVO> selectVisitorAuthPage(IPage<VisitorAuthVO> page, VisitorAuthDTO visitorAuthDto) {
		List<Long> bindParkLotIds = userParklotService.getCurrentUserBindParkLotIds(visitorAuthDto.getParklotId());
		if (bindParkLotIds.isEmpty()) {
			return page.setRecords(Collections.emptyList());
		}
		visitorAuthDto.setParklotIdList(bindParkLotIds);
		List<VisitorAuthVO> list = baseMapper.selectVisitorAuthPage(page, visitorAuthDto);
		if (Func.isNotEmpty(list)){
			for (VisitorAuthVO vo : list) {
				if (Func.isBlank(vo.getPlate())){
					continue;
				}
				int length = vo.getPlate().split(",").length;
				vo.setAuthCarNum(length);
			}
		}
		return page.setRecords(list);
	}

	/**
	 * 统一保存或更新
	 *
	 * @param saveBean 更新实体
	 * @return true 成功
	 */
	private boolean unifySaveOrUpdate(VisitorAuth saveBean) {
		super.saveOrUpdate(saveBean);
		//扫码授权时车牌为空
		if (Func.isBlank(saveBean.getPlate())) {
			return true;
		}
		VisitorAuth oldBean = getById(saveBean.getId());
		// 更新车辆属性关系
		return platePropertyService.update(oldBean.getTenantId(),
			oldBean.getParklotId(),
			saveBean.getPlate(),
			saveBean.getId(),
			PlatePropertyType.VISITOR_CARD,
			saveBean.getStartTime(),
			saveBean.getEndTime());
	}

	@Override
	public String isEnter(String plate, Long parkLotId) {
		Parklot parkLot = ParkLotCaches.getParkLot(parkLotId);
		LecentAssert.notNull(parkLot, "车场不存在");

		List<ParkingOrder> parkingOrders = getTodayParkingOrders(plate, parkLotId);
		LecentAssert.notEmpty(parkingOrders, "车牌plate=[" + plate + "]今天在[" + parkLot.getName() + "]车场没有入场记录");
		return DateUtil.formatDateTime(parkingOrders.get(0).getEnterTime());
	}

	private List<ParkingOrder> getTodayParkingOrders(String plate, Long parklotId) {
		//当天零点
		LocalDateTime today_start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
		//当天零点
		LocalDateTime today_end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
		LambdaQueryWrapper<ParkingOrder> wrapper = Wrappers.<ParkingOrder>lambdaQuery()
			.like(ParkingOrder::getPlate, plate)
			.eq(ParkingOrder::getParklotId, parklotId)
			.eq(ParkingOrder::getParkingStatus, ParkingStatus.SPRKING_PRESENT)
			.gt(ParkingOrder::getEnterTime, today_start)
			.lt(ParkingOrder::getEnterTime, today_end)
			.orderByDesc(ParkingOrder::getEnterTime);
		return parkingOrderService.list(wrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@AroundOpt(serviceClass = IVisitorAuthService.class, optType = OptTypeEnum.ADD, title = "访客授权",
		customMsgTemp = "新增访客授权,{}",
		headers = {
			@AroundOptHeader(headerClass = IParklotService.class, headerId = "parklotId", headerName = "name"),
			@AroundOptHeader(headerClass = IVisitorAuthService.class, headerId = "id", headerName = "plate"),
		}
	)
	public VisitorAuth addVisitorAuth(VisitorAuth visitorAuth) {
//		if (VisitorAuthEnum.SINGLETON.getValue().equals(visitorAuth.getIsSingleAuth())){
//			int length = visitorAuth.getPlate().split(",").length;
//			LecentAssert.isFalse(length>1,"不能授权多个车牌!");
//		}
		checkAuthPermission(visitorAuth, false);
		fillDefaultValue(visitorAuth);

		unifySaveOrUpdate(visitorAuth);
		ParkingOrder parkingOrder = parkingOrderService.selectPresentParking(visitorAuth.getParklotId(), visitorAuth.getPlate());
		if (parkingOrder != null) {
			parkingOrder.setCardId(visitorAuth.getId());
			parkingOrder.setRelationType(PlatePropertyType.VISITOR_CARD.getValue());
			parkingOrder.setRelationId(visitorAuth.getId());
			parkingOrderService.updateById(parkingOrder);
		}

		// 初始化短域名
		if (VisitorAuthEnum.MULTIPART.getValue().equals(visitorAuth.getIsSingleAuth())) {
			initShortUrl(visitorAuth);

			unifySaveOrUpdate(visitorAuth);
		}

		sendDataToPlatform(visitorAuth, ThirdHttpCar.OpTypeEnum.ADD);
		return visitorAuth;
	}

	/**
	 * 初始化短域名
	 *
	 * @param visitorAuth bean
	 */
	private void initShortUrl(VisitorAuth visitorAuth) {
		// 获取网关地址
		String gateWayApiPath = domainService.getGateWayApiPath();

		// 获取用户端地址
		String mpDomain = domainService.getUserH5Path();

		String returnUrl = StrUtil.format("{}/visitor?id={}", mpDomain, visitorAuth.getId());

		String longUrl = StrUtil.format(LONG_URL_TEMPLATE, gateWayApiPath, AuthUtil.getTenantId(), UrlUtil.encode(returnUrl));

		// 获取短域名
		String shortUrl = domainService.createShortDomain(longUrl);
		visitorAuth.setQrUrl(shortUrl);
	}


	private void sendDataToPlatform(VisitorAuth visitorAuth, ThirdHttpCar.OpTypeEnum opTypeEnum) {
		Long parklotId = visitorAuth.getParklotId();
		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		if (Func.isNull(parklot)) {
			return;
		}
		List<CommunityParklot> communityParklotList = communityParklotService.lambdaQuery()
			.eq(CommunityParklot::getParklotId, parklotId)
			.list();
		if (Func.isEmpty(communityParklotList)) {
			return;
		}
		communityParklotList.forEach(communityParklot -> {
			Long communityId = communityParklot.getCommunityId();
			OwnerDetailInfo ownerDetailInfo = ownerDetailInfoService.getById(communityId);
			if (Func.notNull(ownerDetailInfo)) {
				if (ownerDetailInfo.getPush().equals(1)) {
					ThirdHttpCar thirdHttpCar = ThirdHttpCar.builder()
						.companyId(ownerDetailInfo.getId().toString())
						.companyName(ownerDetailInfo.getName())
						.carParkId(parklotId.toString())
						.carParkName(parklot.getName())
						.carNum(visitorAuth.getPlate())
						.carType(CarTypeEnum.VISITOR.getValue().toString())
						.updateTime(new Date())
						.personName("")
						.personMobile("")
						.personId("")
						.parkingSpace("1")
						.opType(opTypeEnum.getValue().toString())
						.build();
					thirdCRHJService.pushCar(Collections.singletonList(thirdHttpCar));
				}
			}
		});
	}

	@Override
	public boolean setVisitorAuthLimit(Integer parklotlimit, Integer userlimit, Long parklotId) {
		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		LecentAssert.notNull(parklot, "车场不存在");
		parklot.setVisitorParklotLimit(parklotlimit);
		parklot.setVisitorUserLimit(userlimit);
		return parklotService.unifySaveOrUpdate(parklot);
	}

	@Override
	public VisitorAuthVO getDetail(Long id) {

		VisitorAuth visitorAuth = getById(id);
		LecentAssert.notNull(visitorAuth, "id=[" + id + "]的访客授权不存在");

		String plate = visitorAuth.getPlate();
		if (StrUtil.isNotBlank(plate)) {
			List<String> plateList = Func.toStrList(plate);
			Integer authCarNum = visitorAuth.getAuthCarNum();
			int usedAuthNum = plateList.size();
			int availableAuthNum = authCarNum > usedAuthNum ? authCarNum - usedAuthNum : 0;
			visitorAuth.setAuthCarNum(availableAuthNum);
		}

		Parklot parklot = ParkLotCaches.getParkLot(visitorAuth.getParklotId());
		VisitorAuthVO visitorAuthVO = VisitorAuthWrapper.build().entityVO(visitorAuth);
		visitorAuthVO.setParklotName(parklot.getName());
		return visitorAuthVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addUserFreePermission(VisitorAuth auth) {

		LecentAssert.notNull(auth.getId(), "ID不能为空");

		VisitorAuth visitorAuth = getById(auth.getId());
		LecentAssert.notNull(visitorAuth, "访客授权不存在");

		//判断二维码是否过期
		boolean between = isBetween(visitorAuth.getStartTime(), visitorAuth.getEndTime());
		LecentAssert.isTrue(between, "二维码过期或无效");

		//判断二维码是否已满车辆数
		List<String> plateList = new ArrayList<>(Func.toStrList(visitorAuth.getPlate()));

		Integer authCarNum = visitorAuth.getAuthCarNum();

		List<String> authCarNumSize = new ArrayList<>();
		authCarNumSize.addAll(Func.toStrList(visitorAuth.getPlate()));
		authCarNumSize.addAll(Func.toStrList(visitorAuth.getOpenId()));
		LecentAssert.isTrue(authCarNum > authCarNumSize.size(), "二维码授权车辆数已满");

		if (!PlateCheckUtils.isNoPlate(auth.getPlate())) {
			LecentAssert.isFalse(plateList.contains(auth.getPlate()), auth.getPlate() + "已授权，请勿重复授权!");

			plateList.add(auth.getPlate());
			visitorAuth.setPlate(Func.join(plateList));

		} else if (Func.isNotBlank(auth.getOpenId())) {
			List<String> openIdList = new ArrayList<>(Func.toStrList(visitorAuth.getOpenId()));

			LecentAssert.isFalse(openIdList.contains(auth.getOpenId()), "请勿重复授权!");

			openIdList.add(auth.getOpenId());
			visitorAuth.setOpenId(Func.join(openIdList));
		}
		return unifySaveOrUpdate(visitorAuth);
	}


	private boolean isBetween(Date startTime, Date endTime) {
		long curr = System.currentTimeMillis();
		long start = startTime.getTime();
		long end = endTime.getTime();
		return curr <= end && curr >= start;
	}

	private void fillDefaultValue(VisitorAuth visitorAuth) {
		visitorAuth.setOptUserName(SecureUtil.getUserName());
		visitorAuth.setOptUserId(SecureUtil.getUserId());
		String plate = visitorAuth.getPlate();

		if (Func.isNull(visitorAuth.getAuthCarNum())) {
			visitorAuth.setAuthCarNum(0);
		}

		if (Func.isNotBlank(plate)) {
			int length = Func.toStrArray(plate).length;
			visitorAuth.setAuthCarNum(length);
		}
	}

	private void checkAuthPermission(VisitorAuth visitorAuth, boolean isEdit) {
		Parklot parklot = ParkLotCaches.getParkLot(visitorAuth.getParklotId());
		LecentAssert.notNull(parklot, "车场不存在");

		Date endTime = visitorAuth.getEndTime();
		if (Func.isNotEmpty(endTime) && endTime.before(DateUtil.now())) {
			throw new ServiceException("授权结束时间不能小于当前时间！");
		}

		Date startDateOfCurrMonth = DateUtils.startDateOfCurrMonth();
		Date endDateOfCurrMonth = DateUtils.endDateOfCurrMonth();

		LambdaQueryWrapper<VisitorAuth> wrapper = Wrappers.<VisitorAuth>lambdaQuery().eq(VisitorAuth::getParklotId,
			visitorAuth.getParklotId())
			.gt(VisitorAuth::getCreateTime,
				startDateOfCurrMonth)
			.lt(VisitorAuth::getCreateTime,
				endDateOfCurrMonth);
		if (isEdit) {
			wrapper.ne(VisitorAuth::getId, visitorAuth.getId());
		}

		List<VisitorAuth> visitorAuths = list(wrapper);

		log.debug("visitorAuths 的值是{}", visitorAuths);

		if (!visitorAuths.isEmpty()) {
			//访客授权表已经存在的车辆数量
			int parklotSum = visitorAuths.stream().mapToInt(VisitorAuth::getAuthCarNum).sum();
			log.debug("parklotSum 的值{}", parklotSum);
			Integer visitorParklotLimit = parklot.getVisitorParklotLimit();
			//parklotSum + visitorAuth.getAuthCarNum() 已经存在车辆数 + 本次需要授权数
			LecentAssert.isTrue(visitorParklotLimit >= parklotSum + visitorAuth.getAuthCarNum(), "车场访客授权本月已达限制");
			wrapper.eq(VisitorAuth::getOptUserId, SecureUtil.getUserId());
			visitorAuths = list(wrapper);
			if (!visitorAuths.isEmpty()) {
				int userSum = visitorAuths.stream().mapToInt(VisitorAuth::getAuthCarNum).sum();
				Integer visitorUserLimit = parklot.getVisitorUserLimit();
				LecentAssert.isTrue(visitorUserLimit >= userSum, "当前用户授权已达限制");
			}
		}
	}

	private void checkTime(String plate, Long parklotId, long limitTime) {
		List<ParkingOrder> todayParkingOrders = getTodayParkingOrders(plate, parklotId);
		LecentAssert.notEmpty(todayParkingOrders, "车牌为plate=[" + plate + "]的车辆在parklotId=[" +
			parklotId + "]车场今天没有入场记录");
		LecentAssert.isTrue(todayParkingOrders.get(0).getEnterTime().getTime() <
			limitTime, "授权结束时间不能小于车辆入场时间");
	}

	@Override
	public VisitorAuth selectOneByPlate(Long parkLotId, String plate, String openId, Date enterTime) {
		if (Func.isBlank(plate) && Func.isBlank(openId)) {
			return null;
		}
		if (TodoContext.isNoPlate(plate)) {
			plate = null;
		} else {
			openId = null;
		}
		return baseMapper.selectOneByPlate(parkLotId, plate, openId, enterTime);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@AroundOpt(serviceClass = IVisitorAuthService.class, optType = OptTypeEnum.EDIT, title = "访客授权",
		headers = {
			@AroundOptHeader(headerClass = IParklotService.class, headerId = "parklotId", headerName = "name"),
			@AroundOptHeader(headerClass = IVisitorAuthService.class, headerId = "id", headerName = "plate"),
		}
	)
	public VisitorAuth updateCustom(VisitorAuth visitorAuth) {
		VisitorAuth oldVisitorAuth = getById(visitorAuth.getId());
		LecentAssert.notNull(oldVisitorAuth, "id[" + visitorAuth.getId() + "]的访客授权不存在");
		LecentAssert.isTrue(SecureUtil.getUserId().equals(oldVisitorAuth.getOptUserId()),
			"此访客授权不是id=[" + visitorAuth.getOptUserId() + "]的用户创建的，没有修改的权限");
		LecentAssert.isTrue(visitorAuth.getIsSingleAuth().equals(oldVisitorAuth.getIsSingleAuth()),
			"修改是单车授权的与多车授权不能修改");
		LecentAssert.isTrue(visitorAuth.getEndTime().getTime() > System.currentTimeMillis(),
			"访客授权时间不能小于当前时间");
		checkAuthPermission(visitorAuth, true);

//		if (VisitorAuthEnum.SINGLETON.getValue().equals(visitorAuth.getIsSingleAuth())){
//			int length = visitorAuth.getPlate().split(",").length;
//			LecentAssert.isFalse(length>1,"不能授权多个车牌!");
//		}

		unifySaveOrUpdate(visitorAuth);
		return visitorAuth;
	}

	@Override
	public ThirdHttpRes addThirdVisitorAuth(ThirdHttpVisitorInfo visitorInfo) {
		try {
			List<VisitorAuth> visitorAuthList = getVisitorAuthList(visitorInfo);
			visitorAuthList.forEach(visitorAuth -> addVisitorAuth(visitorAuth));
		} catch (Exception e) {
			log.info("建行访客授权推送失败,{}", e);
			return ThirdHttpRes.builder()
				.code(ThirdHttpRes.CodeEnum.ERROR.getValue())
				.message(ThirdHttpRes.CodeEnum.ERROR.getName())
				.build();
		}
		return ThirdHttpRes.builder()
			.code(ThirdHttpRes.CodeEnum.SUCCESS.getValue())
			.message(ThirdHttpRes.CodeEnum.SUCCESS.getName())
			.build();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean customDeleteLogic(List<Long> ids) {
		ids.forEach(id -> {
			VisitorAuth auth = getById(id);
			LecentAssert.notNull(auth, "此访客授权不存在");

			sendDataToPlatform(auth, ThirdHttpCar.OpTypeEnum.DEL);
		});

		platePropertyService.removeByCardIds(PlatePropertyType.VISITOR_CARD, ids);
		return deleteLogic(ids);
	}

	@Override
	public List<VisitorAuth> getExpireList() {
		return this.list(Wrappers.<VisitorAuth>lambdaQuery()
			.eq(VisitorAuth::getStatus, CardStatusConstants.EFFECTIVE)
			.apply("UNIX_TIMESTAMP(end_time) <= UNIX_TIMESTAMP('" + DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATETIME) + "')")
		);
	}

	@Override
	public boolean miniVisitorAuthBind(ReqVisitorAuth req) {
		VisitorAuth auth = new VisitorAuth();

		auth.setId(req.getId());
		auth.setOpenId(req.getUnionId());
		auth.setPlate(req.getPlate());
		return addUserFreePermission(auth);
	}

	@Override
	public VisitorAuth getVisitorAuthById(Long id) {
		return getById(id);
	}

	private List<VisitorAuth> getVisitorAuthList(ThirdHttpVisitorInfo visitorInfo) {
		String companyId = visitorInfo.getCompanyId();
		List<String> parklotIds = communityParklotService.listByCommunityId(Long.valueOf(companyId));
		List<VisitorAuth> visitorAuthList = parklotIds.stream().map(id -> {
			VisitorAuth visitorAuth = new VisitorAuth();
			visitorAuth.setStartTime(visitorInfo.getStartTime());
			visitorAuth.setEndTime(visitorInfo.getEndTime());
			visitorAuth.setIsSingleAuth(VisitorAuthEnum.SINGLETON.getValue());
			visitorAuth.setIsMultipart(VisitorAuthEnum.SINGLETON.getValue());
			visitorAuth.setPlate(visitorInfo.getCarNum());
			visitorAuth.setParklotId(Long.valueOf(id));
			visitorAuth.setMemo("建行推送的访客授权");
			visitorAuth.setOptUserId(SecureUtil.getUserId());
			visitorAuth.setOptUserName(SecureUtil.getUserName());
			visitorAuth.setAuthCarNum(1);
			return visitorAuth;
		}).collect(Collectors.toList());
		return visitorAuthList;
	}
}
