package com.lecent.park.service.impl;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.charge.ProjectCalculate;
import com.lecent.park.common.constant.*;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.common.utils.PlateCheckUtils;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.dto.MerchantParklotAddPlateDTO;
import com.lecent.park.dto.ParkMerchantParklotPlateDTO;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.en.merchant.CompareRuleEnum;
import com.lecent.park.en.merchant.DiscountPeriodEnum;
import com.lecent.park.en.merchant.MerchantParingStatusEnum;
import com.lecent.park.en.merchant.MerchantRuleTypeEnum;
import com.lecent.park.en.unpaidorder.UnpaidOrderType;
import com.lecent.park.entity.*;
import com.lecent.park.mapper.ParkMerchantParklotPlateMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.ParkMerchantParklotPlateVO;
import com.lecent.park.vo.ParklotPlatePageVO;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import com.lecent.park.wrapper.ParkMerchantParklotPlateWrapper;
import com.lecent.pay.core.enums.PayWay;
import com.leliven.park.domain.order.parking.support.ParkingOrderDomainService;
import com.leliven.park.infrastructure.gateway.persistence.order.converter.ParkingOrderConverter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.DateUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.common.utils.SequenceNoUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.*;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.entity.UserInfo;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE;
import static com.lecent.park.core.mq.rabbitmq.routing.SendRoutingKeys.MERCHANT_AUTH_PLATE_EXPIRE;

/**
 * 商家车场授权车牌表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Service
@AllArgsConstructor
@Slf4j
public class ParkMerchantParklotPlateServiceImpl extends BaseServiceImpl<ParkMerchantParklotPlateMapper, ParkMerchantParklotPlate> implements IParkMerchantParklotPlateService {

	private ParkingOrderConverter parkingOrderConverter;
	private ParkingOrderDomainService parkingOrderDomainService;
	private IParkingOrderService parkingOrderService;
	private IParkMerchantParklotService parkMerchantParklotService;
	private IParkMerchantService merchantService;
	private ITempParkingChargeRuleService chargeRuleService;
	private ITempParkingUnpaidOrderService unpaidOrderService;
	private ICardLogService cardLogService;
	private IUserClient userClient;
	private MqSender mqSender;
	private ClientService clientService;
	private ITempParkingOrderService orderService;
	private IMessageLogService messageService;
	private IUserParklotService userParklotService;
	private ICardService cardService;
	private IFreeCardAuthService freeCardAuthService;

	private IParkMerchantRuleService parkMerchantRuleService;

	private IPlatePropertyService platePropertyService;

	@Autowired
	private IParklotAuxiliaryService auxiliaryService;

	@Autowired
	private IDomainService domainService;

	@Autowired
	private BladeRedis bladeRedis;

	/**
	 * 统一保存或更新
	 *
	 * @param saveBean 更新实体
	 */
	private boolean unifySaveOrUpdate(ParkMerchantParklotPlate saveBean) {
		boolean b = super.saveOrUpdate(saveBean);

		if (PlateCheckUtils.isNoPlate(saveBean.getPlate()) && Func.isBlank(saveBean.getOpenId())) {
			return b;
		}

		ParkMerchantParklotPlate olderCar = getById(saveBean.getId());
		if (null != olderCar) {
			if (null == saveBean.getParklotId()) {
				saveBean.setParklotId(olderCar.getParklotId());
			}
			if (Func.isNotBlank(saveBean.getPlate())) {
				saveBean.setPlate(olderCar.getPlate());
			}
			if (Func.isNotBlank(saveBean.getOpenId())) {
				saveBean.setOpenId(olderCar.getOpenId());
			}
			if (null == saveBean.getAuthStartTime()) {
				saveBean.setAuthStartTime(olderCar.getAuthStartTime());
			}
			if (null == saveBean.getAuthEndTime()) {
				saveBean.setAuthEndTime(olderCar.getAuthEndTime());
			}
			if (null == saveBean.getStatus()) {
				saveBean.setStatus(olderCar.getStatus());
			}
		}

		if (!EnableStatus.DELETE.getCode().equals(saveBean.getStatus())) {
			// 获取确切车牌还是OpenId进行更新
			String plate = PlateCheckUtils.isNoPlate(saveBean.getPlate()) ? saveBean.getOpenId() : saveBean.getPlate();

			Date authStartTime = saveBean.getAuthStartTime() == null ? PlatePropertyServiceImpl.DEFAULT_START_TIME : saveBean.getAuthStartTime();
			Date authEndTime = saveBean.getAuthEndTime() == null ? PlatePropertyServiceImpl.DEFAULT_END_TIME : saveBean.getAuthEndTime();
			return platePropertyService.update(olderCar.getTenantId(), saveBean.getParklotId(),
				plate,
				saveBean.getId(),
				PlatePropertyType.MERCHANT_CARD,
				authStartTime,
				authEndTime,
				saveBean.getStatus());
		} else {
			platePropertyService.removeByCardIds(PlatePropertyType.MERCHANT_CARD, saveBean.getId());
			return true;
		}
	}

	private void fillQueryType(ParkMerchantParklotPlateDTO parkMerchantParklotPlate) {

		//默认为商户端
		if (parkMerchantParklotPlate.getQueryType() == null) {
			parkMerchantParklotPlate.setQueryType(UserTypeConstants.MERCHANT);
		}

		// 商家端查询
		if (UserTypeConstants.MERCHANT == parkMerchantParklotPlate.getQueryType()) {
			parkMerchantParklotPlate.setQueryType(UserTypeConstants.MERCHANT);

			// 设置商户ID进行查询
			String businessId = AuthUtil.getBusinessId();
			if (StringUtil.isNotBlank(businessId)) {
				parkMerchantParklotPlate.setMerchantId(Long.valueOf(businessId));
			} else {
				ParkMerchant merchant = merchantService.getByUserId(SecureUtil.getUserId());
				if (null != merchant) {
					parkMerchantParklotPlate.setMerchantId(merchant.getId());
				}
			}
		} else {
			List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIds(parkMerchantParklotPlate.getParklotId());
			parkMerchantParklotPlate.setParkLotIds(parkLotIds);
			parkMerchantParklotPlate.setTenantId(SecureUtil.getTenantId());
		}
	}

	/**
	 * queryType 1 商家端查询 2 物业端查询
	 *
	 * @param page
	 * @param queryBean
	 * @return
	 */
	@Override
	public IPage<ParklotPlatePageVO> selectParkMerchantParklotPlatePage(IPage<ParklotPlatePageVO> page, ParkMerchantParklotPlateDTO queryBean) {
		//设置查询类型
		fillQueryType(queryBean);

		// 非商户必须带车场ID查询
		if (UserTypeConstants.MERCHANT != queryBean.getQueryType()
			&& CollectionUtil.isEmpty(queryBean.getParkLotIds())) {
			return page.setRecords(Collections.emptyList());
		}

		// 默认最近一个月数据,由于授权时间在车辆未进场时可能为空，按照创建时间查询
//		if (null == queryBean.getAuthStartTimeBegin() && null == queryBean.getAuthStartTimeEnd()
//			&& null == queryBean.getAuthEndTimeBegin() && null == queryBean.getAuthEndTimeEnd()
//			&& null == queryBean.getCreateTimeStart() && null == queryBean.getCreateTimeEnd()
//			&& Func.isBlank(queryBean.getPlate())) {
//			queryBean.setCreateTime(DateUtil.toDate(LocalDateTime.now().minusMonths(6)));
//		}

		List<ParklotPlatePageVO> list = baseMapper.selectParkMerchantParklotPlatePage(page, queryBean);
		if (Func.isNotEmpty(list)) {
			for (ParklotPlatePageVO parklotPlate : list) {
				Date enterTime = this.selectEnterTime(parklotPlate.getPlate(), parklotPlate.getId());
				parklotPlate.setEnterTime(enterTime);
			}
		}
		return page.setRecords(list);
	}

	private Date selectEnterTime(String plate, Long id) {
		ParkingOrder parkingOrder = parkingOrderService.getOne(
			Wrappers.<ParkingOrder>lambdaQuery().eq(ParkingOrder::getPlate, plate)
				.eq(ParkingOrder::getRelationId, id)
				.orderByDesc(ParkingOrder::getEnterTime)
				.last("limit 1")
		);
		if (Func.isNotEmpty(parkingOrder)) {
			return parkingOrder.getEnterTime();
		}
		return null;
	}

	@Override
	public String validatePlateNeedAuthInPark(String plate, Long parklotId) {
		if (PlateCheckUtils.isNoPlate(plate)) {
			return String.valueOf(AuthConstants.ALLOW_AUTH);
		}

		ParkingOrder parkingOrder = parkingOrderService.selectPresentParking(SecureUtil.getTenantId(), parklotId, plate, StringPool.EMPTY);
		if (Func.isNotEmpty(parkingOrder)) {
			return DateUtils.format(parkingOrder.getEnterTime());
		}

		Boolean canAuthLeaveCar = this.isCanAuthLeaveCar(parklotId);
		if (canAuthLeaveCar) {
			return String.valueOf(AuthConstants.ALLOW_AUTH);
		}

		return String.valueOf(AuthConstants.NOT_ALLOW_AUTH);
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public ParkMerchantParklotPlate saveAuthPlate(MerchantParklotAddPlateDTO merchantPlate) {
		// 获取商户ID，如果当前用户没有商户ID则使用传入的商户ID
		String merchantId = SecureUtil.getBusinessId();
		if (Func.isBlank(merchantId)) {
			merchantId = merchantPlate.getMerchantId();
		}
		LecentAssert.notBlank(merchantId, "商户ID不能为空");

		ParkMerchant merchant = merchantService.getById(merchantId);
		LecentAssert.notNull(merchant, "该商家不存在！");

		ParkMerchantParklot merchantParklot = parkMerchantParklotService.getByMerchantIdParklotId(merchant.getId(), merchantPlate.getParklotId());
		LecentAssert.notNull(merchantParklot, "该商家未授权选择的车场！");
		LecentAssert.isFalse(merchantParklot.getStatus() != 1, "该商家授权车场无效！");
		if(merchantParklot.getAuthEndTime() ==null ||merchantParklot.getAuthEndTime().compareTo(new Date())<0){
			throw  new ServiceException("商户套餐已失效！!");
		}

		LecentAssert.isFalse(overstep(merchantParklot), String.format("授权已超%s次,请联系车场管理员增加授权总次数!", merchantParklot.getAuthTotalNumber()));

		ParkMerchantRule parkMerchantRule = parkMerchantRuleService.getById(merchantParklot.getMerchantRuleId());
		LecentAssert.notNull(parkMerchantRule, "商户的套餐不存在");
		LecentAssert.isTrue(parkMerchantRule.getStatus() == 1, "套餐已经被禁用，授权失败");

		LecentAssert.isTrue(this.isDoubleAuth(merchantParklot, merchantPlate.getPlate()), "重复授权");

		LecentAssert.isTrue(amountEnough(merchantParklot, parkMerchantRule), "商户余额已用完,请充值后授权");

		Card card = cardService.selectCard(merchantParklot.getParklotId(), merchantPlate.getPlate(), new Date());
		LecentAssert.isNull(card, "月卡车无需授权!");
		FreeCardAuth freeCardAuth = freeCardAuthService.selectByPlate(merchantPlate.getPlate(), merchantParklot.getParklotId(), new Date(), new Date());
		LecentAssert.isNull(freeCardAuth, "免费车无需授权!");

		R<User> userR = userClient.userInfoById(SecureUtil.getUserId());
		LecentAssert.isTrue(userR.isSuccess() && null != userR.getData(), "查询商户用户信息失败");

		ParkingOrder parkingOrder = parkingOrderService.selectPresentParking(merchantPlate.getParklotId(), merchantPlate.getPlate());
		Integer notIntAllowAuth = merchantParklot.getNotIntAllowAuth();

		if (0 == notIntAllowAuth && null == parkingOrder) {
			if (!TodoContext.isNoPlate(merchantPlate.getPlate())) {
				throw new ServiceException("车辆未在场");
			}
		}
		//设置固定优惠时长
		resolveFixedPeriod(merchantParklot, merchantPlate, parkingOrder);

		LecentAssert.isTrue(verifyAuthTime(merchantPlate.getAuthStartTime(), merchantPlate.getAuthEndTime(), merchantParklot),
			"商户套餐已失效！!");

		ParkMerchantParklotPlate parkLotPlate = ParkMerchantParklotPlateWrapper.build().addDTO2entity(merchantPlate, merchant, merchantParklot, userR.getData());

		//处理停车记录更换商家的情况
		ParkingOrder newParkingOrder = resolveChangeMerchant(parkingOrder, parkLotPlate);

		//校验授权规则
		this.verifyAuthRule(parkLotPlate, merchantParklot, parkingOrder);

		//如果授权时间在车辆进场时间6天内将授权开始时间强制设置为进场时间
		if (newParkingOrder != null) {
			log.info("newParkingOrder={}", Func.toJson(newParkingOrder));
			//设置固定优惠时长
			resolveFixedPeriod(merchantParklot, merchantPlate, newParkingOrder);
			parkLotPlate = ParkMerchantParklotPlateWrapper.build().addDTO2entity(merchantPlate, merchant, merchantParklot, userR.getData());
		}
		saveOrUpdate(parkLotPlate);
		//将授权车辆记录更新到授权汇总表中
		unifySaveOrUpdate(parkLotPlate);

		//更新停车记录获得授权免费停车
		this.updateParkingOrder(parkLotPlate, newParkingOrder);

		//发送延时队列
		sendExpireDelayMessage(parkLotPlate);

		return parkLotPlate;
	}

	private boolean amountEnough(ParkMerchantParklot merchantParklot, ParkMerchantRule rule) {
		if (!MerchantRuleTypeEnum.AMOUNT_MONEY.getValue().equals(rule.getRuleType())) {
			return true;
		}
		BigDecimal totalAmount = merchantParklot.getTotalPrice();
		BigDecimal usedAmount = merchantParklot.getUsedAmount();
		if (totalAmount.compareTo(usedAmount) <= 0) {
			return false;
		}
		return true;
	}

	/**
	 * 授权次数校验
	 *
	 * @param merchantParkLot
	 * @return
	 */
	private boolean overstep(ParkMerchantParklot merchantParkLot) {
		if (!merchantParkLot.getEnableAuthTotalNumber()) {
			return false;
		}

		int count = this.count(Wrappers.<ParkMerchantParklotPlate>lambdaQuery()
			.eq(ParkMerchantParklotPlate::getMerchantParklotId, merchantParkLot.getId())
			.ne(ParkMerchantParklotPlate::getStatus, -1)
		);

		if (count >= merchantParkLot.getAuthTotalNumber()) {
			return true;
		}
		return false;
	}

	/**
	 * 处理停车记录更换商家的情况
	 *
	 * @param parkingOrder 停车记录
	 * @param parkLotPlate 授权记录
	 */
	private ParkingOrder resolveChangeMerchant(ParkingOrder parkingOrder, ParkMerchantParklotPlate parkLotPlate) {
		if (parkingOrder == null) {
			return null;
		}
		if (Func.isEmpty(parkingOrder.getRelationId())) {
			return parkingOrder;
		}
		ParkMerchantParklotPlate authPlate = this.getById(parkingOrder.getRelationId());
		if (authPlate == null) {
			return parkingOrder;
		}
		if (authPlate.getMerchantId().equals(parkLotPlate.getMerchantId())) {
			return parkingOrder;
		}
		log.info("车主更换商家，将停车记录做出场后在进场的操作");
		return this.frontMerchantCost(parkingOrder, authPlate, parkLotPlate.getAuthStartTime());
	}

	/**
	 * 设置授权的固定时长
	 *
	 * @param merchantParklot 商户车场
	 * @param plateDTO        授权参数
	 * @param parkingOrder    停车记录
	 */
	private void resolveFixedPeriod(ParkMerchantParklot merchantParklot, MerchantParklotAddPlateDTO plateDTO, ParkingOrder parkingOrder) {
		// 如果是用户自主授权，优惠套餐必须为固定时长
		if (plateDTO.getIsUserAuth() != null && plateDTO.getIsUserAuth()) {
			LecentAssert.isTrue(merchantParklot.getDiscountPeriodType() == DiscountPeriodEnum.FIXED_PERIOD.getValue(),
				"用户自主授权时，优惠套餐必须为固定时长");
		}

		// 优惠固定时长设置授权时间
		if (DiscountPeriodEnum.FIXED_PERIOD.getValue() == merchantParklot.getDiscountPeriodType()) {
			if (null != parkingOrder) {
				plateDTO.setAuthStartTime(parkingOrder.getEnterTime());
				plateDTO.setAuthEndTime(DateUtil.plusMinutes(parkingOrder.getEnterTime(), merchantParklot.getDiscountMinutes()));
				plateDTO.setAuthDuration(com.lecent.park.common.utils.DateUtils.getDuration(plateDTO.getAuthStartTime(), plateDTO.getAuthEndTime()));
			} else {
				plateDTO.setStatus(MerchantParingStatusEnum.WAIT_ENTER.getValue());
			}
		}

		if (DiscountPeriodEnum.NO_RULE.getValue() == merchantParklot.getDiscountPeriodType()) {
			LecentAssert.isTrue(null != plateDTO.getAuthStartTime() && null != plateDTO.getAuthEndTime(),
				"优惠类型为不限制时，授权开始结束时间不能为空");
		}
	}

	/**
	 * @param authPlate       请求参数
	 * @param merchantParkLot 商户授权的车场
	 * @param parkingOrder
	 */
	private void verifyAuthRule(ParkMerchantParklotPlate authPlate, ParkMerchantParklot merchantParkLot, ParkingOrder parkingOrder) {

		//授权车辆数上限校验
		if (merchantParkLot.getAuthCarNumEnabled()) {
			Integer authNum = this.getTodayAuthNum(merchantParkLot.getParklotId(), merchantParkLot.getParkMerchantId());
			LecentAssert.isTrue(merchantParkLot.getAuthCarNumLimit() > authNum, "授权车辆数已达到配置上限 " + merchantParkLot.getAuthCarNumLimit() + "辆/天，授权失败");
		}

		//车辆入场时长校验
		if (merchantParkLot.getEnterTimeEnabled() && null != parkingOrder) {
			long limitTopMinutes = merchantParkLot.getEnterMinutes();
			long enterMinutes = DateUtil.between(parkingOrder.getEnterTime(), new Date()).toMinutes();

			Integer enterComparator = merchantParkLot.getEnterComparator();
			if (CompareRuleEnum.ge.getValue() == enterComparator) {
				String tips = String.format("车辆已入场%s分钟，车场限制 入场%s分钟后禁止授权! ", enterMinutes, limitTopMinutes);
				LecentAssert.isFalse(enterMinutes >= limitTopMinutes, tips);
			}
			if (CompareRuleEnum.le.getValue() == enterComparator) {
				String tips = String.format("车辆入场%s分钟，车场限制 入场%s分钟后才可授权! ", enterMinutes, limitTopMinutes);
				LecentAssert.isFalse(enterMinutes <= limitTopMinutes, tips);
			}

		}

		//优惠时长校验
		if (merchantParkLot.getValidPeriodEnabled()) {
			this.discountTimeLengthVerify(merchantParkLot, authPlate);
		}

		//授权车辆数校验
		if (merchantParkLot.getValidPlaceEnabled()) {
			Integer placeNum = merchantParkLot.getPlaceNum();
			//已授权车位数
			Integer authedNum = this.getAuthedPlaceByParklotId(merchantParkLot.getParklotId(), merchantParkLot.getParkMerchantId());
			LecentAssert.isTrue(placeNum > authedNum, "授权车辆数超过商家可用车位数" + placeNum + "个，授权失败！");
		}


		//有效授权数量限制
		if (merchantParkLot.getEnableAuthNumTop()) {

			Integer topNum = merchantParkLot.getAuthNumTop();
			int validNum = this.count(Wrappers.<ParkMerchantParklotPlate>lambdaQuery()
				.eq(ParkMerchantParklotPlate::getStatus, 1)
				.eq(ParkMerchantParklotPlate::getParklotId, merchantParkLot.getParklotId())
				.eq(ParkMerchantParklotPlate::getMerchantId, merchantParkLot.getParkMerchantId())
			);

			String tips = String.format("授权限制,最多可授权%s辆车,当前已授权%s辆车，授权失败！", topNum, validNum);
			LecentAssert.isFalse(validNum >= topNum, tips);
		}

	}


	/**
	 * 优惠时长校验
	 *
	 * @param merchantParkLot
	 * @param authPlate
	 */
	private void discountTimeLengthVerify(ParkMerchantParklot merchantParkLot, ParkMerchantParklotPlate authPlate) {
		Date preSettlementTime = parkMerchantParklotService.preSettlementTime(merchantParkLot);
		Date settlementStartTime = parkMerchantParklotService.settlementStartTime(merchantParkLot.getSettleFrequency(), preSettlementTime, merchantParkLot.getSettlementCycle());
		Long cycleTotalTimeLength = this.cycleTotalTimeLength(merchantParkLot.getPlaceNum(), preSettlementTime, settlementStartTime);
		Long usedTimeLength = parkingOrderService.getUsedTimeLength(Collections.singletonList(merchantParkLot.getId()), authPlate.getParklotId(), preSettlementTime, settlementStartTime, null);
		String preSettlementDate = DateUtil.formatDate(preSettlementTime);
		String settlementStartDate = DateUtil.formatDate(settlementStartTime);
		LecentAssert.isTrue(cycleTotalTimeLength > usedTimeLength, String.format("%s至%s结算周期内授权时长已使用完 ，授权失败！", preSettlementDate, settlementStartDate));
	}


	/**
	 * 验证授权时间是否合法
	 */
	private boolean verifyAuthTime(Date startTime, Date endTime, ParkMerchantParklot merchantParklot) {
		//固定时段时，开始结束时间可能为空
		if (null == startTime || null == endTime) {
			return true;
		}
		Date authStartTime = merchantParklot.getAuthStartTime();
		Date authEndTime = merchantParklot.getAuthEndTime();

		LecentAssert.isTrue(startTime.getTime() < endTime.getTime(), "授权开始时间不能小于结束时间");

		return endTime.getTime() <= authEndTime.getTime();
	}

	private ParkingOrder frontMerchantCost(ParkingOrder parkingOrder, ParkMerchantParklotPlate authPlate, Date authStartTime) {

		Date costEndDate = authPlate.getAuthEndTime();
		if (costEndDate.after(authStartTime)) {
			if (costEndDate.after(new Date())) {
				costEndDate = new Date();
			}
		}
		ParkingOrder newParkingOrder = JSON.parseObject(JSON.toJSONString(parkingOrder), ParkingOrder.class);

		ChannelTodoVO todo = clientService.parkingCost(parkingOrder.getParklotId(), parkingOrder.getPlate(), parkingOrder.getOpenId(), costEndDate);
		if(todo.getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0){
			//未产生优惠金额则不用拆单
			return parkingOrder;
		}

		todo.setPayType(PayWay.CASH.getKey());
		todo.setRemark("车主更换换酒店");
		TempParkingOrder tempOrder = TempParkingOrder.create(todo);
		tempOrder.setTradeNo(SequenceNoUtils.generateNo());
		tempOrder.setMerchantIds(authPlate.getMerchantId() + "");
		tempOrder.setPayTime(authPlate.getAuthEndTime());
		tempOrder.setPayStatus(1);
		orderService.save(tempOrder);

		parkingOrder.setParkingStatus(4);
		parkingOrder.setExitTime(costEndDate);
		parkingOrder.setTotalAmount(todo.getTotalAmount());
		parkingOrder.setPaidAmount(todo.getPaidAmount());
		parkingOrder.setDiscountAmount(todo.getDiscountAmount());
		parkingOrder.setMerchantAmount(todo.getMerchantAmount());
		parkingOrder.setCouponDiscountAmount(todo.getCouponDiscountAmount());
		parkingOrder.setUnusualAmount(todo.getErrorAmount());
		parkingOrderDomainService.update(parkingOrderConverter.fromDO(parkingOrder));


		newParkingOrder.setId(null);
		newParkingOrder.setOccupied(false);
		newParkingOrder.setEnterTime(costEndDate);
		newParkingOrder.setBillingStartTime(costEndDate);
		return newParkingOrder;

	}

	/**
	 * 车辆在场是是否多次授权
	 *
	 * @param merchantParkLot
	 * @param plate
	 * @return
	 */
	private boolean isDoubleAuth(ParkMerchantParklot merchantParkLot, String plate) {
		if (TodoContext.isNoPlate(plate)) {
			return true;
		}
		ParkingOrder parkingOrder = parkingOrderService.selectPresentParkingByCardId(merchantParkLot.getId(), merchantParkLot.getParklotId(),plate);
		if (Func.isEmpty(parkingOrder)) {
			return true;
		}

		ParkMerchantParklotPlate merchantPlate = this.getOne(Wrappers.<ParkMerchantParklotPlate>lambdaQuery()
			.eq(ParkMerchantParklotPlate::getParklotId, merchantParkLot.getParklotId())
			.eq(ParkMerchantParklotPlate::getMerchantId, merchantParkLot.getParkMerchantId())
			.eq(ParkMerchantParklotPlate::getPlate, plate)
			.eq(ParkMerchantParklotPlate::getStatus, 1)
			.orderByDesc(ParkMerchantParklotPlate::getAuthEndTime)
			.last("limit 1")
		);
		if (Func.isEmpty(merchantPlate)) {
			return true;
		}

		if (merchantPlate.getAuthEndTime().getTime() > parkingOrder.getEnterTime().getTime()) {
			return false;
		}

		return true;
	}

	/**
	 * 更新停车记录获得授权免费停车
	 *
	 * @param parklotPlate
	 * @param parkingOrder
	 */
	private void updateParkingOrder(ParkMerchantParklotPlate parklotPlate, ParkingOrder parkingOrder) {
		if (Func.isEmpty(parkingOrder)) {
			return;
		}
		log.info("updateParkingOrder parklotPlate={}", JSON.toJSON(parklotPlate));
		log.info("updateParkingOrder parkingOrder={}", JSON.toJSON(parkingOrder));
		parkingOrder.setRelationId(parklotPlate.getId());

		if (parkingOrder.getEnterTime().getTime() == parklotPlate.getAuthStartTime().getTime()) {
			parkingOrder.setParkingTimeNode(null);
		}
		ParkMerchantParklotPlateVO parkLotPlate = this.selectByPlateOrOpenId(parklotPlate.getPlate(), parklotPlate.getOpenId(), parklotPlate.getParklotId(), parkingOrder.getEnterTime());
		if (Func.isEmpty(parkLotPlate)) {
			return;
		}

		parkingOrder.setCardId(parkLotPlate.getMerchantParklotId());
		parkingOrder.setTempCardIds("," + parkLotPlate.getMerchantParklotId() + ",");

		List<TimeNode> timeNodeList = getParkingTimeNodeList(parkingOrder.getParkingTimeNode());
		boolean notExist = true;
		if (Func.isNotEmpty(timeNodeList)) {
			for (TimeNode tn : timeNodeList) {
				Date parse = DateUtils.parse(tn.getStartDate(), null);
				if (parse.compareTo(parklotPlate.getAuthStartTime()) == 0) {
					notExist = false;
				}
			}
		}


		if (MerchantRuleTypeEnum.PLACE_TIME.getValue().equals(parkLotPlate.getMerchantRule())) {
			ParkMerchantParklot merchantParkLot = parkMerchantParklotService.getById(parkLotPlate.getMerchantParklotId());
			Long cycleSurplusTimeLength = this.cycleSurplusTimeLength(merchantParkLot);
			if (cycleSurplusTimeLength > 0) {
				timeNodeList.add(new TimeNode(DateUtils.format(parklotPlate.getAuthStartTime()), "开始授权，停止计费", "车辆进场后添加的授权", false));
			} else {
				timeNodeList.add(new TimeNode(DateUtils.format(parklotPlate.getAuthStartTime()), "开始授权，时长已用完，按临停计费", "车辆进场后添加的授权", true));
			}

		} else if(MerchantRuleTypeEnum.AMOUNT_MONEY.getValue().equals(parkLotPlate.getMerchantRule())){
			//查询余额
			BigDecimal merchantSurplusAmount = parkLotPlate.getTotalPrice().subtract(parkLotPlate.getUsedAmount());
			if (merchantSurplusAmount.compareTo(BigDecimal.ZERO) <= 0) {
				timeNodeList.add(new TimeNode(DateUtils.format(parklotPlate.getAuthStartTime()), "开始授权，余额已用完，按临停计费", "车辆进场后添加的授权", true));
			}else{
				timeNodeList.add(new TimeNode(DateUtils.format(parklotPlate.getAuthStartTime()), "开始授权，停止计费", "车辆进场后添加的授权", false));
			}
		}else{

			int presentCount = parkingOrderService.countParkingSpaceOccupied(parkingOrder.getParklotId(), parkLotPlate.getMerchantParklotId(), parklotPlate.getAuthStartTime(), parkLotPlate.getAuthEndTime());
			if (parkLotPlate.getPlaceNum() > presentCount) {
				parkingOrder.setOccupied(true);
				if (notExist) {
					timeNodeList.add(new TimeNode(DateUtils.format(parklotPlate.getAuthStartTime()), "开始授权，停止计费", "车辆进场后添加的授权", false));
				}
			} else {
				if (notExist) {
					timeNodeList.add(new TimeNode(DateUtils.format(parklotPlate.getAuthStartTime()), "开始授权，车位已满，按临停计费", "车辆进场后添加的授权", true));
				}
			}
		}

		parkingOrder.setParkingTimeNode(JSON.toJSONString(timeNodeList));
		parkingOrderService.saveOrUpdate(parkingOrder);
	}


	/**
	 * 获取操作时间节点
	 *
	 * @param parkingTimeNode
	 * @return
	 */
	private List<TimeNode> getParkingTimeNodeList(String parkingTimeNode) {
		List<TimeNode> timeNodeList;
		if (Func.isBlank(parkingTimeNode)) {
			timeNodeList = new ArrayList<>();
		} else {
			timeNodeList = JSON.parseArray(parkingTimeNode, TimeNode.class);
			if (Func.isEmpty(timeNodeList)) {
				timeNodeList = new ArrayList<>();
			}
		}
		return timeNodeList;
	}


	/**
	 * 发送授权截止日期延时队列(授权过期时处理)
	 *
	 * @param authPlate 车辆授权信息
	 */
	@Override
	public void sendExpireDelayMessage(ParkMerchantParklotPlate authPlate) {
		Date authEndTime = authPlate.getAuthEndTime();
		if (Func.isEmpty(authEndTime)) {
			return;
		}
		long expireMillis = authEndTime.getTime() - System.currentTimeMillis();
		if (authEndTime.getTime() < System.currentTimeMillis()) {
			expireMillis = 5000;
		}
		mqSender.sendDelayMessage(JSON.toJSONString(authPlate), expireMillis, LECENT_PARK_DELAY_QUEUE_EXCHANGE, MERCHANT_AUTH_PLATE_EXPIRE);
		log.info("发送车辆授权过期延时队列，msg={},millis={},Exchange={}, Routing={}", authPlate, expireMillis, LECENT_PARK_DELAY_QUEUE_EXCHANGE, MERCHANT_AUTH_PLATE_EXPIRE);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateAuthEndTime(Long id, String authEndTime) {
		ParkMerchantParklotPlate plate = this.getById(id);
		LecentAssert.notNull(plate, "找不到该车辆授权信息");

		Date oldAuthEndTime = plate.getAuthEndTime();

		ParkMerchantParklot merchantParklot = parkMerchantParklotService.getById(plate.getMerchantParklotId());
		LecentAssert.notNull(merchantParklot, "找不到该商家授权的车场！");

		boolean isFixTimeLength = merchantParklot.getDiscountPeriodType() == DiscountPeriodEnum.FIXED_PERIOD.getValue();
		LecentAssert.isFalse(isFixTimeLength, "固定时长授权类型不允许修改截至时间！");


		plate.setAuthEndTime(DateUtil.parse(authEndTime, DateUtil.PATTERN_DATETIME));

		ParkingOrder exitParkingOrder = this.selectAlreadyExitCar(id);
		if (exitParkingOrder != null) {
			boolean before = plate.getAuthEndTime().before(exitParkingOrder.getExitTime());
			LecentAssert.isFalse(before, "截至时间不能小于授权车辆的出场时间！");
		}

		if (Func.isNull(plate.getAuthStartTime())) {
			return this.cancelAuth(id);
		}

		LecentAssert.isTrue(plate.getAuthEndTime().getTime() > plate.getAuthStartTime().getTime(), "授权结束时间不能小于授权开始时间");

		plate.setAuthDuration(DateUtils.getDuration(plate.getAuthStartTime(), plate.getAuthEndTime()));
		plate.setStatus(1);

		//修车车辆授权信息
		unifySaveOrUpdate(plate);

		ParkingOrder presentParkingOrder = parkingOrderService.selectPresentParking(plate.getParklotId(), plate.getPlate());
		if (Func.notNull(presentParkingOrder)) {
			presentParkingOrder.setCardId(plate.getMerchantParklotId());
			presentParkingOrder.setRelationId(plate.getId());
			if (!presentParkingOrder.getOccupied()) {
				int i = parkingOrderService.countParkingSpaceOccupied(presentParkingOrder.getParklotId(), plate.getMerchantParklotId(), plate.getAuthStartTime(), plate.getAuthEndTime());
				if (merchantParklot.getPlaceNum() > i) {
					presentParkingOrder.setOccupied(true);
				}
			}
			parkingOrderService.updateById(presentParkingOrder);
		}

		//发送延时队列
		sendExpireDelayMessage(plate);

		saveAuthPlateLog(plate, oldAuthEndTime);
		return true;

	}

	private void saveAuthPlateLog(ParkMerchantParklotPlate plate, Date oldAuthEndTime) {
		CardLog authPlateLog = new CardLog();
		authPlateLog.setTenantId(plate.getTenantId());
		authPlateLog.setCardId(plate.getId());
		authPlateLog.setTitle("授权结束时间变更");
		authPlateLog.setName(SecureUtil.getUserName());
		authPlateLog.setCreateTime(DateUtil.now());
		authPlateLog.setCreateUser(SecureUtil.getUserId());
		authPlateLog.setOldContent(DateUtil.format(oldAuthEndTime, DateUtil.PATTERN_DATETIME));
		authPlateLog.setNewContent(DateUtil.format(plate.getAuthEndTime(), DateUtil.PATTERN_DATETIME));
		cardLogService.save(authPlateLog);

	}

	@Override
	public ParklotPlatePageVO parkCarDetail(Long id) {
		ParklotPlatePageVO parklotPlatePageVO = baseMapper.parkCarDetail(id);
		if (Func.isNotEmpty(parklotPlatePageVO)) {
			R<UserInfo> userInfo = userClient.userInfo(AuthUtil.getTenantId(), parklotPlatePageVO.getMerAccount());
			if (userInfo.isSuccess() && Func.isNotEmpty(userInfo.getData())) {
				parklotPlatePageVO.setRealName(userInfo.getData().getUser().getRealName());
			}
			if (Objects.nonNull(parklotPlatePageVO.getAuthEndTime()) && DateUtil.now().after(parklotPlatePageVO.getAuthEndTime())) {
				if (parklotPlatePageVO.getStatus() != -1) {
					parklotPlatePageVO.setStatus(CommonStatusConstants.INVALID);
				}
			}
		}

		return parklotPlatePageVO;
	}

	@Override
	public ParkMerchantParklotPlateVO selectByPlateOrOpenId(String plate, String openId, Long parkLotId, Date enterDate) {
		ParkMerchantParklotPlateVO vo = null;

		if (Func.isNotBlank(openId)) {
			vo = baseMapper.selectByOpenId(openId, parkLotId, enterDate);
		}

		if (!TodoContext.isNoPlate(plate)) {
			vo = baseMapper.selectByPlate(plate, parkLotId, enterDate);
		}

		if (vo != null && vo.getAuthEndTime() == null) {
			ParkMerchantParklot merchantParkLot = parkMerchantParklotService.getById(vo.getMerchantParklotId());
			//固定时长设置授权开始结束时间
			if (null != merchantParkLot && DiscountPeriodEnum.FIXED_PERIOD.getValue() == merchantParkLot.getDiscountPeriodType()) {
				//如果授权开始时间早于入场时间，以入场时间为准
				if (MerchantParingStatusEnum.WAIT_ENTER.getValue() == vo.getStatus()) {
					vo.setAuthStartTime(enterDate);
					vo.setAuthEndTime(DateUtil.plusMinutes(enterDate, merchantParkLot.getDiscountMinutes()));
					vo.setAuthDuration(DateUtils.getDuration(vo.getAuthStartTime(), vo.getAuthEndTime()));
					vo.setStatus(MerchantParingStatusEnum.VALID.getValue());
					this.updateById(vo);
					//发送延时队列
					this.sendExpireDelayMessage(vo);

					platePropertyService.update(vo.getTenantId(),
						vo.getParklotId(),
						vo.getPlate(), vo.getId(),
						PlatePropertyType.MERCHANT_CARD,
						vo.getAuthStartTime(),
						vo.getAuthEndTime());
				}
			}
		}
		return vo;
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public void releaseParkingSpace(ParkMerchantParklotPlate authPlate) {
		ParkMerchantParklotPlateVO selectAuthPlate = baseMapper.selectAuthPlateById(authPlate.getId());
		if (isReturn(authPlate, selectAuthPlate)) {
			return;
		}
		selectAuthPlate.setStatus(EnableStatus.INVALID.getCode());
		unifySaveOrUpdate(selectAuthPlate);
		messageService.merchantAuthOverdueMsgCall(authPlate);

		if (!MerchantRuleTypeEnum.PLACE_NUM.getValue().equals(selectAuthPlate.getMerchantRule())) {
			return;
		}

		ParkingOrder parkingOrder = parkingOrderService.selectOccupied(selectAuthPlate.getMerchantParklotId(), selectAuthPlate.getParklotId(), selectAuthPlate.getPlate());
		if (Func.isNotEmpty(parkingOrder)) {
			ParkingOrder afterCar = parkingOrderService.selectAfterCarByCardId(parkingOrder.getTenantId(), selectAuthPlate.getParklotId(), selectAuthPlate.getMerchantParklotId());
			if (Func.isNotEmpty(afterCar)) {
				afterCar.setOccupied(true);
				TempParkingChargeRuleVO chargeRule = chargeRuleService.selectStandardOneByParkLotId(afterCar.getParklotId());
				if (Func.isNotEmpty(chargeRule)) {
					ProjectCost cost = new ProjectCost();
					ProjectCalculate.calculate(cost, "循环车(后车缴费)临停计费时段", chargeRule, afterCar.getBillingStartTime(), selectAuthPlate.getAuthEndTime());
					TempParkingUnpaidOrder unpaidOrder = TempParkingUnpaidOrder.createUnpaidOrder(afterCar);
					unpaidOrder.setType(UnpaidOrderType.TYPE03.getValue());
					unpaidOrder.setMemo("循环车-后车缴费,前车【" + selectAuthPlate.getPlate() + "】授权过期时生成");
					unpaidOrder.setBillingStartTime(afterCar.getBillingStartTime());
					unpaidOrder.setBillingEndTime(selectAuthPlate.getAuthEndTime());
					unpaidOrderService.saveUnpaidOrder(unpaidOrder, cost);
				}
				afterCar.setBillingStartTime(selectAuthPlate.getAuthEndTime());
				parkingOrderService.updateById(afterCar);
			}
		}

	}

	private boolean isReturn(ParkMerchantParklotPlate authPlate, ParkMerchantParklotPlate selectAuthPlate) {
		if (Func.isEmpty(selectAuthPlate)) {
			return true;
		}
		if (Func.isEmpty(authPlate.getAuthEndTime())) {
			return true;
		}
		if (Func.isEmpty(selectAuthPlate.getAuthEndTime())) {
			return true;
		}
		//接收到的消息截止日期与数据库里的截止日期一样时处理流程
		if (authPlate.getAuthEndTime().getTime() == selectAuthPlate.getAuthEndTime().getTime()) {
			return false;
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public Boolean noPlateAuth(Long id, String openId) {
		ParkMerchantParklotPlate authPlate = this.getById(id);
		if (Func.isNull(authPlate)) {
			return false;
		}

		ParkMerchantParklot merchantParklot = parkMerchantParklotService.getByMerchantIdParklotId(authPlate.getMerchantId(), authPlate.getParklotId());
		LecentAssert.notNull(merchantParklot, "该商家未授权选择的车场！");

		ParkingOrder parkingOrder = parkingOrderService.selectPresentParking(null, authPlate.getParklotId(), authPlate.getPlate(), openId);
		log.info("无牌车授权停车记录查询结果parkingOrder={}", parkingOrder);
		if (Func.notNull(parkingOrder)) {
			//优惠固定时长设置授权时间
			if (DiscountPeriodEnum.FIXED_PERIOD.getValue() == merchantParklot.getDiscountPeriodType()) {
				authPlate.setAuthStartTime(parkingOrder.getEnterTime());
				authPlate.setAuthEndTime(DateUtil.plusMinutes(parkingOrder.getEnterTime(), merchantParklot.getDiscountMinutes()));
				authPlate.setAuthDuration(com.lecent.park.common.utils.DateUtils.getDuration(authPlate.getAuthStartTime(), authPlate.getAuthEndTime()));
			}

			Date addDate = DateUtil.plusDays(parkingOrder.getEnterTime(), 6);
			if (addDate.after(authPlate.getAuthStartTime())) {
				authPlate.setAuthStartTime(parkingOrder.getEnterTime());
			}
		}
		authPlate.setOpenId(openId);
		unifySaveOrUpdate(authPlate);

		updateParkingOrder(authPlate, parkingOrder);
		return true;
	}

	@Override
	public ParkMerchantParklotPlate selectByEndAuthDate(String plate, Long parkLotId, Date date) {
		return this.getOne(Wrappers.<ParkMerchantParklotPlate>lambdaQuery()
			.eq(ParkMerchantParklotPlate::getPlate, plate)
			.eq(ParkMerchantParklotPlate::getParklotId, parkLotId)
			.le(ParkMerchantParklotPlate::getAuthStartTime, date)
			.ne(ParkMerchantParklotPlate::getStatus, -1)
			.ge(ParkMerchantParklotPlate::getAuthEndTime, date)
			.ne(ParkMerchantParklotPlate::getStatus, -1)
			.orderByDesc(ParkMerchantParklotPlate::getCreateTime)
			.last("limit 1")
		);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void everyDayCheckSettlement() {
		List<ParkMerchantParklot> merchantParkLotList = parkMerchantParklotService.allSettlementCycle();
		if (Func.isEmpty(merchantParkLotList)) {
			return;
		}

		for (ParkMerchantParklot mp : merchantParkLotList) {
			try {
				Date settlementStartTime = parkMerchantParklotService.settlementStartTime(mp.getSettleFrequency(), parkMerchantParklotService.preSettlementTime(mp), mp.getSettlementCycle());
				Date currentTime = DateUtils.getDateMinTime(new Date());

				if (DateUtil.between(currentTime, settlementStartTime).toHours() > 0) {
					continue;
				}
				Date preSettlementTime = parkMerchantParklotService.preSettlementTime(mp);
				List<ParkingOrder> parkingOrderList = this.selectPresentByCardId(mp.getId(), mp.getParklotId(), settlementStartTime);

				if (Func.isNotEmpty(parkingOrderList)) {
					Long cycleTotalTimeLength = this.cycleTotalTimeLength(mp.getPlaceNum(), preSettlementTime, settlementStartTime);
					this.cycleUpdateParkOrder(parkingOrderList, cycleTotalTimeLength, mp, preSettlementTime, settlementStartTime);
				}
				mp.setPreSettlementTime(settlementStartTime);
				parkMerchantParklotService.updateById(mp);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}


	@Override
	public Long cycleTotalTimeLength(Integer placeNum, Date startDate, Date endDate) {
		long days = DateUtil.between(startDate, DateUtil.plusSeconds(endDate, 1)).toDays();
		return (long) placeNum * days * 24 * 60 * 60;
	}


	@Override
	public Long cycleSurplusTimeLength(ParkMerchantParklot merchantParkLot) {
		Date preSettlementTime = parkMerchantParklotService.preSettlementTime(merchantParkLot);
		Date settlementStartTime = parkMerchantParklotService.settlementStartTime(merchantParkLot.getSettleFrequency(), preSettlementTime, merchantParkLot.getSettlementCycle());
		Long cycleTotalTimeLength = this.cycleTotalTimeLength(merchantParkLot.getPlaceNum(), preSettlementTime, settlementStartTime);
		Long usedTimeLength = parkingOrderService.merchantUseTimeLength(merchantParkLot, 0);
		log.info("cycleSurplusTimeLength parkMerchantId={} cycleTotalTimeLength={},usedTimeLength={}", merchantParkLot.getParkMerchantId(), cycleTotalTimeLength, usedTimeLength);
		//车位剩余总时长
		return cycleTotalTimeLength - usedTimeLength;
	}

	@Override
	public String createNoPlateQr(Long id) {
		ParkMerchantParklotPlate authPlate = this.getById(id);
		if (null == authPlate) {
			return StringUtils.EMPTY;
		}

		ParklotAuxiliary auxiliary = auxiliaryService.getByParklotId(authPlate.getParklotId());
		//小程序二维码
		if (ScanCodeTypeEnum.isMiniProgram(auxiliary.getScanCodeType())) {
			Object redisQr = bladeRedis.get(CacheNames.MERCHANT_AUTH_QR + id);
			if (null == redisQr) {
				String path = StringUtil.format(MiniProgramRoutesConstants.MERCHANT_AUTH_ROUTE, authPlate.getParklotId(), id);
				String qr = domainService.generateMiniProgramQr(auxiliary, path);
				bladeRedis.set(CacheNames.MERCHANT_AUTH_QR + id, qr);
				return qr;
			}
			return bladeRedis.get(CacheNames.MERCHANT_AUTH_QR + id);
		}
		//公众号二维码
		String url = domainService.createAuthNoPlateQr(authPlate);
		return QrCodeUtil.generateAsBase64(url, new QrConfig(), "png");
	}


	/**
	 * 周期结算更新处理停车和订单信息
	 *
	 * @param parkOrderList
	 * @param totalTimeLength
	 * @param merchantParkLot
	 * @param startDate
	 * @param endDate
	 */
	private void cycleUpdateParkOrder(List<ParkingOrder> parkOrderList, long totalTimeLength, ParkMerchantParklot merchantParkLot, Date startDate, final Date endDate) {
		for (ParkingOrder p : parkOrderList) {
			ParkMerchantParklotPlate authPlate = this.getById(p.getRelationId());
			if (authPlate == null) {
				continue;
			}
			if (authPlate.getAuthEndTime() == null) {
				continue;
			}
			if (!p.getEnterTime().before(authPlate.getAuthEndTime())) {
				continue;
			}

			Date enterTime = endDate;
			if (authPlate.getAuthEndTime().before(endDate)) {
				enterTime = authPlate.getAuthEndTime();
			}

			ParkingOrder parkingOrder = JSON.parseObject(JSON.toJSONString(p), ParkingOrder.class);
			p.setExitTime(enterTime);

			this.parkOrderExit(p, DateUtil.minusSeconds(enterTime, 1), merchantParkLot);

			parkingOrder.setId(null);
			parkingOrder.setParkingStatus(2);
			parkingOrder.setEnterTime(enterTime);
			parkingOrder.setBillingStartTime(enterTime);
			parkingOrder.setExitTime(null);
			parkingOrderService.save(parkingOrder);
		}
	}

	/**
	 * 手动生成一条出场数据
	 *
	 * @param p
	 */
	private void parkOrderExit(ParkingOrder p, Date endDate, ParkMerchantParklot merchantParkLot) {
		parkingOrderService.updateById(p);
		ChannelTodoVO todo = clientService.parkingCost(p.getParklotId(), p.getPlate(), p.getOpenId(), endDate);
		if (todo.getMerchantAmount().compareTo(BigDecimal.ZERO) > 0) {
			todo.setPayType(PayWay.CASH.getKey());
			todo.setRemark("结算时自动生成");
			TempParkingOrder tempOrder = TempParkingOrder.create(todo);
			tempOrder.setTradeNo(SequenceNoUtils.generateNo());
			tempOrder.setMerchantIds(merchantParkLot.getParkMerchantId() + "");
			tempOrder.setPayTime(endDate);
			tempOrder.setPayStatus(1);
			tempOrder.setTotalAmount(todo.getMerchantAmount());
			tempOrder.setReceiveAmount(BigDecimal.ZERO);
			orderService.save(tempOrder);
		}
		parkingOrderService.updateParkingInfoWithExitedEvent(p, todo);
	}

	@Override
	public Boolean isCanAuthLeaveCar(Long parkLotId) {
		ParkMerchant merchant = merchantService.getById(AuthUtil.getBusinessId());
		LecentAssert.notNull(merchant, "当前登录商家不存在！");
		ParkMerchantParklot parkMerchantParklot = parkMerchantParklotService.getByMerchantIdParklotId(merchant.getId(), parkLotId);
		LecentAssert.notNull(merchant, "该商家未授权选择的车场！");

		LecentAssert.isFalse(parkMerchantParklot != null && parkMerchantParklot.getStatus() != 1, "该商家授权车场无效！");

		if (Objects.nonNull(parkMerchantParklot)) {
			Integer allowAuth = parkMerchantParklot.getNotIntAllowAuth();
			if (Func.notNull(allowAuth) && allowAuth.equals(AuthConstants.ALLOW_AUTH)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public BigDecimal getAuthTimeoutCost(MerchantParklotAddPlateDTO merchantPlate) {

		String plate = merchantPlate.getPlate();
		Date authStartTime = merchantPlate.getAuthStartTime();

		ParkingOrder parkingOrder = parkingOrderService.
			selectPresentParking(null, merchantPlate.getParklotId(), plate, StringPool.EMPTY);

		//车辆不在场，返回0元
		if (null == parkingOrder) {
			return BigDecimal.ZERO;
		}
		//入场前授权，返回0元
		if (authStartTime.getTime() <= parkingOrder.getEnterTime().getTime()) {
			return BigDecimal.ZERO;
		}

		Date enterTime = parkingOrder.getEnterTime();


		TempParkingChargeRuleVO chargeRule = chargeRuleService.selectStandardOneByParkLotId(merchantPlate.getParklotId());
		if (null == chargeRule) {
			return BigDecimal.ZERO;
		}

		ProjectCost cost = new ProjectCost();
		cost.setTotalAmount(BigDecimal.ZERO);
		cost = ProjectCalculate.calculate(cost, "计费时段", chargeRule, enterTime, authStartTime);

		return cost.getTotalAmount();
	}

	@Override
	public Boolean cancelAuth(Long id) {

		ParkMerchantParklotPlate plate = this.getById(id);
		LecentAssert.notNull(plate, "找不到该车辆授权信息");

		LecentAssert.isNull(selectAlreadyExitCar(id), "车辆已结算，不允许取消授权");

		//同步车辆信息
		platePropertyService.removeByCardIds(PlatePropertyType.MERCHANT_CARD, id);

		plate.setStatus(MerchantParingStatusEnum.DELETED.getValue());
		return unifySaveOrUpdate(plate);
	}

	@Override
	public Integer getTodayAuthNum(Long parklotId, Long merchantId) {
		return baseMapper.getTodayAuthNum(parklotId, merchantId);
	}

	@Override
	public Integer getAuthedPlaceByParklotId(Long parklotId, Long merchantId) {
		return baseMapper.getAuthedPlaceByParklotId(parklotId, merchantId);
	}


	private ParkingOrder selectAlreadyExitCar(Long businessId) {
		return parkingOrderService.getOne(Wrappers.<ParkingOrder>lambdaQuery()
			.eq(ParkingOrder::getRelationId, businessId)
			.eq(ParkingOrder::getParkingStatus, ParkingStatus.SPRKING_APPEARED)
			.orderByDesc(ParkingOrder::getExitTime)
			.last("limit 1"));
	}


	/**
	 * 结算时查询在场车辆
	 *
	 * @param cardId
	 * @param parklotId
	 * @param settlementStartTime
	 * @return
	 */
	private List<ParkingOrder> selectPresentByCardId(Long cardId, Long parklotId, Date settlementStartTime) {
		return parkingOrderService.list(Wrappers.<ParkingOrder>lambdaQuery()
			.eq(ParkingOrder::getParklotId, parklotId)
			.eq(ParkingOrder::getCardId, cardId)
			.eq(ParkingOrder::getParkingStatus, 2)
			.lt(ParkingOrder::getEnterTime, settlementStartTime)
			.orderByAsc(ParkingOrder::getEnterTime)
		);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ParklotPlatePageVO activeAuth(MerchantParklotAddPlateDTO merchantPlate) {
		// 用户自主授权
		merchantPlate.setIsUserAuth(true);

		// 验证requestId是否过期
		if (merchantPlate.getRequestId() != null) {
			String cacheKey = CacheConstant.MERCHANT_QRCODE_CACHE + merchantPlate.getRequestId();
			if (!ParkLotCaches.exists(cacheKey)) {
				throw new ServiceException("二维码已过期，请扫码重试");
			}
		}

		ParkMerchantParklotPlate merchantPlateOld = baseMapper.selectParkingAuthPlateById(merchantPlate.getPlate(),
			merchantPlate.getParklotId(), merchantPlate.getMerchantId());
		LecentAssert.isNull(merchantPlateOld, "重复授权");

		ParklotPlatePageVO parklotPlate = BeanUtil.copyProperties(this.saveAuthPlate(merchantPlate), ParklotPlatePageVO.class);

		if (Func.notNull(parklotPlate)) {
			parklotPlate.setParklotName(Optional.ofNullable(ParkLotCaches.getParkLot(parklotPlate.getParklotId()))
				.map(Parklot::getName)
				.orElse(""));
		}

		// 调用原有的授权逻辑
		return parklotPlate;
	}

}
