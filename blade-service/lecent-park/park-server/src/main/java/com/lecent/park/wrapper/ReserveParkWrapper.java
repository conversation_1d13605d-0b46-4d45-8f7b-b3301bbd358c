package com.lecent.park.wrapper;

import com.lecent.park.entity.ReservePark;
import com.lecent.park.vo.ReserveParkVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 预约车位表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
public class ReserveParkWrapper extends BaseEntityWrapper<ReservePark, ReserveParkVO>  {

	public static ReserveParkWrapper build() {
		return new ReserveParkWrapper();
 	}

	@Override
	public ReserveParkVO entityVO(ReservePark reservePark) {
		return BeanUtil.copy(reservePark, ReserveParkVO.class);
	}

}
