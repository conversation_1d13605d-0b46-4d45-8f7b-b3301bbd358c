package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.FestivalConfigDTO;
import com.lecent.park.entity.ParklotExtentSetting;
import com.lecent.park.vo.ParklotExtentSettingVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 车场扩展配置表 服务类
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
public interface IParklotExtentSettingService extends BaseService<ParklotExtentSetting> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotExtentSetting
	 * @return
	 */
	IPage<ParklotExtentSettingVO> selectParklotExtentSettingPage(IPage<ParklotExtentSettingVO> page,
																 ParklotExtentSettingVO parklotExtentSetting);

	boolean submit(ParklotExtentSetting parklotExtentSetting);

	ParklotExtentSetting getByParklotId(Long parklotId);

	/**
	 * 更新周末禁用时段
	 * @param parklotId 车场id
	 * @param isSettingWeekend 是否设置周末禁用时段
	 */
	void updateSettingWeekend(Long parklotId, Integer isSettingWeekend);

	boolean submitFestivalInfo(FestivalConfigDTO parklotExtentSetting);
	/**
	 * 根据车场查询
	 * @param parklotId
	 * @return
	 */
	ParklotExtentSetting selectExtentSettingByParklotId(Long parklotId);

	/**
	 * 判断 当前时间是否节假日
	 * @param parklot
	 * @return
	 */
	boolean isFestival(long parklot, String dateStr);
}
