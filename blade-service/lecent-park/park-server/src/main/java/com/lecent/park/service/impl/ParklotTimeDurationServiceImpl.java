package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.dto.ParklotTimeDurationAddBatch;
import com.lecent.park.dto.TimeDTO;
import com.lecent.park.en.parklot.DurationType;
import com.lecent.park.entity.ParklotTimeDuration;
import com.lecent.park.mapper.ParklotTimeDurationMapper;
import com.lecent.park.service.IParklotTimeDurationService;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.vo.ParklotTimeDurationDetailVO;
import com.lecent.park.vo.ParklotTimeDurationVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 停车场停车时段配置 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
@Service
public class ParklotTimeDurationServiceImpl extends BaseServiceImpl<ParklotTimeDurationMapper, ParklotTimeDuration> implements IParklotTimeDurationService {

	@Override
	public IPage<ParklotTimeDurationVO> selectParklotTimeDurationPage(IPage<ParklotTimeDurationVO> page, ParklotTimeDurationVO parklotTimeDuration) {
		return page.setRecords(baseMapper.selectParklotTimeDurationPage(page, parklotTimeDuration));
	}

	@Override
	public List<ParklotTimeDuration> getListByParkLotId(Long parkLotId, Integer type,Date currentDate) {
		List<ParklotTimeDuration> ptdList = baseMapper.getListByParkLotId(parkLotId,type);

		if (ObjectUtil.isEmpty(ptdList) || ptdList.size()<1){
			 ptdList = new ArrayList<>();
			 return ptdList;
		}

		for (ParklotTimeDuration p:ptdList) {
			p.setStartMinute(DateUtils.getTotalMinute(p.getStartTime()));
			p.setEndMinute(DateUtils.getTotalMinute(p.getEndTime()));
			p.setStartDateTime(DateUtils.getDateTime(currentDate,p.getStartTime()));
			p.setEndDateTime(DateUtils.getDateTime(currentDate,p.getEndTime()));
		}

		return ptdList.stream().sorted(Comparator.comparing(ParklotTimeDuration::getStartMinute)).collect(Collectors.toList());

	}


    @Override
	@Transactional(rollbackFor = Exception.class)
    public Boolean saveBatchDuration(ParklotTimeDurationAddBatch parklotTimeDuration) {

		this.remove(Wrappers.<ParklotTimeDuration>lambdaQuery().eq(ParklotTimeDuration::getParklotId,parklotTimeDuration.getParklotId()));

		List<TimeDTO> workDurations = parklotTimeDuration.getWorkDurations();
		List<TimeDTO> festivalDurations = parklotTimeDuration.getFestivalDurations();

		workDurations.forEach(t->{
			ParklotTimeDuration duration = new ParklotTimeDuration();
			duration.setDurationType(DurationType.WORK.getValue());
			duration.setSettingType(parklotTimeDuration.getWorkType());
			duration.setParklotId(parklotTimeDuration.getParklotId());
			duration.setStartTime(t.getStartTime());
			duration.setEndTime(t.getEndTime());

			this.save(duration);

		});
		festivalDurations.forEach(t->{
			ParklotTimeDuration duration = new ParklotTimeDuration();
			duration.setDurationType(DurationType.FESTIVAL.getValue());
			duration.setSettingType(parklotTimeDuration.getFestivalType());
			duration.setParklotId(parklotTimeDuration.getParklotId());
			duration.setStartTime(t.getStartTime());
			duration.setEndTime(t.getEndTime());
			this.save(duration);

		});
		return true;
    }

	@Override
	public ParklotTimeDurationDetailVO getTimeDurationByParklotId(Long parklotId) {
		List<ParklotTimeDuration> workList = this.list(Wrappers.<ParklotTimeDuration>lambdaQuery()
			.eq(ParklotTimeDuration::getParklotId, parklotId)
			.eq(ParklotTimeDuration::getDurationType,DurationType.WORK.getValue()));

		List<ParklotTimeDuration> festivalList = this.list(Wrappers.<ParklotTimeDuration>lambdaQuery()
			.eq(ParklotTimeDuration::getParklotId, parklotId)
			.eq(ParklotTimeDuration::getDurationType,DurationType.FESTIVAL.getValue()));

		ParklotTimeDurationDetailVO vo = new ParklotTimeDurationDetailVO();
		vo.setParklotId(parklotId);
		if (Func.isNotEmpty(workList)) {
			vo.setWorkType(workList.get(0).getSettingType());
			vo.setWorkDurations(workList);
		}

		if (Func.isNotEmpty(festivalList)) {
			vo.setFestivalType(festivalList.get(0).getSettingType());
			vo.setFestivalDurations(festivalList);
		}
		return vo;
	}

	@Override
	public boolean isNoParking(int durationType,String nowTime) {
		return baseMapper.isNoParking(durationType,nowTime);
	}

}
