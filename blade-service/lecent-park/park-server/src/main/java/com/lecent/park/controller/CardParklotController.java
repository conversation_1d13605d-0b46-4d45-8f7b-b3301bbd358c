package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.CardParklot;
import com.lecent.park.service.ICardParklotService;
import com.lecent.park.vo.CardParklotVO;
import com.lecent.park.wrapper.CardParklotWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 套餐车场表 控制器
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/cardparklot")
@Api(value = "套餐车场表", tags = "套餐车场表接口")
public class CardParklotController extends BladeController {

	private ICardParklotService cardParklotService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cardParklot")
	public R<CardParklotVO> detail(CardParklot cardParklot) {
		CardParklot detail = cardParklotService.getOne(Condition.getQueryWrapper(cardParklot));
		return R.data(CardParklotWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 套餐车场表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cardParklot")
	public R<IPage<CardParklotVO>> list(CardParklot cardParklot, Query query) {
		IPage<CardParklot> pages = cardParklotService.page(Condition.getPage(query), Condition.getQueryWrapper(cardParklot));
		return R.data(CardParklotWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 套餐车场表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入cardParklot")
	public R<IPage<CardParklotVO>> page(CardParklotVO cardParklot, Query query) {
		IPage<CardParklotVO> pages = cardParklotService.selectCardParklotPage(Condition.getPage(query), cardParklot);
		return R.data(pages);
	}

	/**
	 * 新增 套餐车场表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入cardParklot")
	public R save(@Valid @RequestBody CardParklot cardParklot) {
		return R.status(cardParklotService.save(cardParklot));
	}

	/**
	 * 修改 套餐车场表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入cardParklot")
	public R update(@Valid @RequestBody CardParklot cardParklot) {
		return R.status(cardParklotService.updateById(cardParklot));
	}

	/**
	 * 新增或修改 套餐车场表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入cardParklot")
	public R submit(@Valid @RequestBody CardParklot cardParklot) {
		return R.status(cardParklotService.saveOrUpdate(cardParklot));
	}


	/**
	 * 删除 套餐车场表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cardParklotService.deleteLogic(Func.toLongList(ids)));
	}


}
