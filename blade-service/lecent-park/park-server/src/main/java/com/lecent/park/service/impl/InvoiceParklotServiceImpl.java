package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.InvoiceParklot;
import com.lecent.park.mapper.InvoiceParklotMapper;
import com.lecent.park.service.IInvoiceParklotService;
import com.lecent.park.vo.InvoiceParklotVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车场和开票信息关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Service
public class InvoiceParklotServiceImpl extends BaseServiceImpl<InvoiceParklotMapper, InvoiceParklot> implements IInvoiceParklotService {

	@Override
	public IPage<InvoiceParklotVO> selectInvoiceParklotPage(IPage<InvoiceParklotVO> page, InvoiceParklotVO invoiceParklot) {
		return page.setRecords(baseMapper.selectInvoiceParklotPage(page, invoiceParklot));
	}

	@Override
	public InvoiceParklot queryByParkLotId(String parkLotId) {
		List<InvoiceParklot> invoiceParklots = baseMapper.selectList(Wrappers.<InvoiceParklot>lambdaQuery().eq(InvoiceParklot::getParklotId, parkLotId));
		return invoiceParklots.isEmpty() ? null : invoiceParklots.get(0);
	}

}
