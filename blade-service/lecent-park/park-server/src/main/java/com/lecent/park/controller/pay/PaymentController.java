package com.lecent.park.controller.pay;


import com.lecent.park.dto.BabyScanDTO;
import com.lecent.park.service.ParkingBabyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.payment.PayResult;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 支付控制器
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/client/payment")
@Validated
@Api(value = "停车系统支付控制层")
@Slf4j
public class PaymentController {

	private final ParkingBabyService parkingBabyService;

	/**
	 * 被扫支付结果查询
	 */
	@GetMapping("/passive-scan/order/result")
	@ApiOperation(value = "支付结果查询", notes = "传入交易流水号")
	public R<PayResult> queryPayResult(@NotEmpty(message = "流水号不能为空") @RequestParam String tradeNo) {
		return R.data(parkingBabyService.queryPayResult(tradeNo));
	}

	/**
	 * 被扫下单
	 */
	@PostMapping("/passive-scan/order/create")
	@ApiOperation(value = "被扫下单", notes = "被扫下单")
	public R<PayResult> createOrder(@Valid @RequestBody BabyScanDTO babyScanDto) {
		return R.data(parkingBabyService.createOrder(babyScanDto));
	}
}
