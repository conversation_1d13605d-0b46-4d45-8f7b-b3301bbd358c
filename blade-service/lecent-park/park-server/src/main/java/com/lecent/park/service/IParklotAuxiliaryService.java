package com.lecent.park.service;

import com.lecent.park.entity.ParklotAuxiliary;
import com.lecent.park.vo.ParklotAuxiliaryVO;
import org.springblade.common.entity.QrDownload;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 车场信息扩展表 服务类
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
public interface IParklotAuxiliaryService extends BaseService<ParklotAuxiliary> {

	/**
	 * 修改
	 *
	 * @param parklotAuxiliary bean
	 * @return t
	 */
	Boolean customSaveOrUpdate(ParklotAuxiliary parklotAuxiliary);

	/**
	 * 根据车场id获取车场扩展信息
	 *
	 * @param parklotId 车场id
	 * @return 车场扩展信息
	 */
	ParklotAuxiliary getByParklotId(Long parklotId);

	/**
	 * 判断停车场是否开启月卡审核功能
	 *
	 * @param parklotId 车场id
	 * @return boolean
	 */
	Boolean monthCardEnabled(Long parklotId);

	/**
	 * 判断停车场是否开启免费车审核功能
	 *
	 * @param parklotId 车场id
	 * @return boolean
	 */
	Boolean freeCarEnabled(Long parklotId);

	/**
	 * 判断停车场是否开启稽核审核功能
	 *
	 * @param parklotId 车场id
	 * @return boolean
	 */
	Boolean abnormalityAuditEnabled(Long parklotId);

	/**
	 * 创建短链接
	 *
	 * @param parklotId 车场id
	 * @return 短链接
	 */
	String createShortUrl(Long parklotId);

	/**
	 * 车场二维码下载
	 *
	 * @param parkLotIds
	 * @param placeIds
	 * @param templateIds
	 * @return
	 */
	List<QrDownload> qrCodeDownloadZip(List<Long> parkLotIds, List<Long> placeIds, List<Long> templateIds);

	/**
	 * 扫码是否需要关注公众号
	 *
	 * @param parkLotId
	 * @return
	 */
	Boolean checkParkLotOpenScanSub(Long parkLotId);


	/**
	 * 查询小程序配置信息
	 *
	 * @param parklotId 车场ID
	 * @return
	 */
	ParklotAuxiliary getMiniInfoByParkLotId(Long parklotId);

	/**
	 * 月卡到期短信提醒开关
	 *
	 * @param parklotId 车场ID
	 * @return true开启、false未开启
	 */
	boolean monthCardExpireSmsEnable(Long parklotId);

	/**
	 * 绑定标签
	 *
	 * @param parklotAuxiliary 车场
	 * @return {@link Boolean}
	 */
	Boolean bindLabel(ParklotAuxiliaryVO parklotAuxiliary);

	/**
	 * 批量绑定标签
	 *
	 * @param parklotAuxiliary parklot辅助
	 * @return {@link Boolean}
	 */
	Boolean batchBindLabel(ParklotAuxiliaryVO parklotAuxiliary);

	/**
	 * 删除标签
	 *
	 * @param parklotAuxiliary parklot辅助
	 * @return {@link Boolean}
	 */
	Boolean batchRemoveLabel(ParklotAuxiliaryVO parklotAuxiliary);

	/**
	 * 查询 月卡失效通知开关
	 * @return
	 */
	List<ParklotAuxiliaryVO> findMonthCardExpireEnable(Long parkLotId);
}
