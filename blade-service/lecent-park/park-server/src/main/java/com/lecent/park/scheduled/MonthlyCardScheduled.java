package com.lecent.park.scheduled;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.utils.RegularUtils;
import com.lecent.park.dto.DeductParklotDto;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.service.*;
import com.lecent.park.vo.CardVO;
import com.lecent.park.vo.ParklotAuxiliaryVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.sms.model.SmsResponse;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.resource.feign.ISmsClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RefreshScope
@Component
@Slf4j
public class MonthlyCardScheduled {
	private static final String CARD_EXPIRE_SHORT_MESSAGE_NOTICE = "lecent:park:lock:MonthlyCardScheduled.cardExpireShortMessageNotice";
	/**
	 * 月卡服务
	 */
	@Autowired
	private ICardService cardService;

	@Autowired
	private ICardOrderService cardOrderService;
	/**
	 * 套餐服务
	 */
	@Autowired
	private ICardCategoryService cardCategoryService;
	/**
	 * 车场服务
	 */
	@Autowired
	private IParklotAuxiliaryService parklotAuxiliaryService;
	/**
	 * 车场服务
	 */
	@Autowired
	private IParklotService parklotService;
	/**
	 * 短信客户端
	 */
	@Resource
	private ISmsClient smsClient;

	@Value("${card.sms.template-code:card_expire-notify}")
	private String smsNoticeTemplate;

	@Autowired
	private BladeRedis bladeRedis;
	@Autowired
	private MessageWxPushService messageWxPushService;

	/**
	 * 月卡即将过期短信通知车主
	 * 每天17:00点执行
	 */
	@Scheduled(cron = "${lecent.park.card.expire-cron}")
	@RedisLock(value = "lecent:park::timedTask:lock:cardExpireShortMessageNotice")
	public void cardExpireShortMessageNotice() {
		try {
			// 非生产环境不发生
			String active = System.getProperties().getProperty("spring.profiles.active", "dev");
			if (!"prod".equals(active)) {
				return;
			}

			// 查看是否有任务已经执行
			if (Boolean.TRUE.equals(bladeRedis.exists(CARD_EXPIRE_SHORT_MESSAGE_NOTICE))) {
				return;
			}
			bladeRedis.setEx(CARD_EXPIRE_SHORT_MESSAGE_NOTICE, Boolean.TRUE, 100L);

			log.info("月卡过期提醒>>>>Exec...");
			List<ParklotAuxiliaryVO> parklotAuxiliaryVOS = parklotAuxiliaryService.findMonthCardExpireEnable(null);
//		List<Parklot> parkLots = parklotService.getParkLotList();
			if (Func.isEmpty(parklotAuxiliaryVOS)) {
				return;
			}

			log.info("月卡过期提醒>>>>共轮询{}个车场", parklotAuxiliaryVOS.size());
			Set<Long> smsCardIds = new ConcurrentHashSet<>();
			Set<Long> miniCardIds = new ConcurrentHashSet<>();
			int count = 0;
			for (ParklotAuxiliaryVO parklotAuxiliaryVO : parklotAuxiliaryVOS) {
				String monthCardExpireEnable = parklotAuxiliaryVO.getMonthCardExpireEnable();
				if (Func.isEmpty(monthCardExpireEnable)) {
					continue;
				}
				JSONObject json = JSON.parseObject(monthCardExpireEnable);
				if (json == null) {
					continue;
				}
				// 查询即将过期月卡
				List<CardVO> expireCards =
					cardService.aboutToExpireCard(parklotAuxiliaryVO.getParklotId(), parklotAuxiliaryVO.getMonthCardExpireSmsDays(), null);
				if (Func.isEmpty(expireCards)) {
					continue;
				}
				if (json.containsKey("sms") && 1 == json.getInteger("sms")) {
					cardExpireShortMessageBysms(expireCards, smsCardIds);
				}
				if (json.containsKey("mini") && 1 == json.getInteger("mini")) {
					cardExpireShortMessageByMini(expireCards, miniCardIds, parklotAuxiliaryVO.getParklotId());
				}
				smsCardIds.addAll(miniCardIds);
				if (!smsCardIds.isEmpty()) {
					cardService.update(Wrappers.<Card>lambdaUpdate()
						.set(Card::getExpireNoticeTime, new Date())
						.in(Card::getId, smsCardIds));
				}
				count = count + smsCardIds.size();
				smsCardIds.clear();
				miniCardIds.clear();
				Thread.sleep(100L);
			}
			log.info("成功通知{}条月卡用户。", count);
		}catch (Exception e){
			log.error("",e);
		}


		log.debug("月卡过期提醒通知任务结束>>>>");
	}

	/**
	 * 发送短信
	 *
	 * @param expireCards
	 * @param cardIds
	 */
	private void cardExpireShortMessageByMini(List<CardVO> expireCards, Set<Long> cardIds, Long parklotId) {
		expireCards.parallelStream().forEach(cardVO -> {
			if (Func.isNotBlank(cardVO.getPlates())) {
				String[] plates = cardVO.getPlates().split(",");
				Card card = new Card();
				for (String plate : plates) {
					if (Func.isNotBlank(plate)) {
						card.setPlates(plate);
						card.setParklotIds(parklotId.toString());
						card.setEndDate(cardVO.getEndDate());
						messageWxPushService.monthCardOverMessage(card);
					}
				}
				cardIds.add(cardVO.getId());
			}

		});

	}

	/**
	 * 发送短信
	 *
	 * @param expireCards
	 * @param cardIds
	 */
	private void cardExpireShortMessageBysms(List<CardVO> expireCards, Set<Long> cardIds) {
		expireCards.parallelStream().forEach(card -> {
			if (!RegularUtils.isPhone(card.getPhone())
				|| cardIds.contains(card.getId())) {
				return;
			}
			JSONObject smsJs = new JSONObject();
			smsJs.put("plate", card.getPlates());
			smsJs.put("parklotName", card.getParklotNames());
			smsJs.put("days", DateUtil.between(new Date(), card.getEndDate(), DateUnit.DAY));

			String params = smsJs.toJSONString();
			log.info("月卡提醒短信发送，params={}", params);
			R<SmsResponse> res = smsClient.sendMessage(smsNoticeTemplate, params, card.getPhone());
			if (res.getCode() != HttpStatus.OK.value()) {
				log.error("月卡提醒短信发送失败，params={},errorMsg:{}", params, res.getMsg());
			} else {
				cardIds.add(card.getId());
				log.debug("月卡提醒短信发送成功，params={}", params);
			}
		});

	}

//	/**
//	 * 扫描支付成功后未更新的订单(5分钟执行一次)
//	 */
//	@Scheduled(cron = "0 0/5 * * * ?")
//	@RedisLock(value = "lecent:park::timedTask:lock:cardPaySuccess", waitTime = 60, leaseTime = 200)
//	public void queryPaySuccessOrderScheduled() {
//		log.info("开始执行扫描月卡支付成功后未更新的订单==============");
//
//		cardOrderService.updateCardEndDate();
//
//		List<String> tradeNoList = cardOrderService.getTradeNoList();
//		if (Func.isEmpty(tradeNoList)) {
//			return;
//		}
//		for (String tradeNo : tradeNoList) {
//			log.info("MonthlyCardScheduled-queryPaySuccessOrderScheduled:{}", tradeNo);
//			cardOrderService.paySuccess(tradeNo);
//		}
//	}


	/**
	 * 每天凌晨扫描即将过期的月卡
	 */
	@Scheduled(cron = "0 0 0 * * ? ")
	@RedisLock(value = "lecent:park::timedTask:lock:updateCardToInvalid", waitTime = 60, leaseTime = 300)
	public void monthlyCardExpiredScheduled() {
		log.info("开始执行扫描即将过期的月卡==============");
		cardService.updateCardToInvalid();
	}


	/**
	 * 定时处理过期并超过缓冲时间的月卡
	 * 释放也开卡的数量
	 */
	@Scheduled(cron = "${lecent.park.card.expire-release-cron}")
	public void cardExpireRelease() {
		log.info("开始扫描过期并超过缓冲时间的月卡>>>>");
		List<CardVO> cards = cardService.getReleaseCards();
		cards.forEach(cardVO -> {
			Long parklotId = cardService.getFirstParkLotId(cardVO);
			CardCategory cardCategory = cardCategoryService.getById(cardVO.getCategoryId());
			if (Func.notNull(cardCategory)) {
				DeductParklotDto deductParklotDto = DeductParklotDto.builder()
					.parklotId(parklotId)
					.parkingPlaceClassify(Integer.valueOf(cardCategory.getClassify()))
					.placeNum(cardCategory.getMaxPlaceNum()).build();
				parklotService.releaseCard(deductParklotDto);
				cardVO.setIsRelease(1);
				cardService.updateById(cardVO);
			}
		});
		log.debug("结束对过期并超过缓冲时间月卡的处理>>>>");
	}
}
