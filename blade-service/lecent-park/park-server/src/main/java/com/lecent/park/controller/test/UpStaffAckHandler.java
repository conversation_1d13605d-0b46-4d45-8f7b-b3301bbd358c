package com.lecent.park.controller.test;

import com.alibaba.fastjson.JSONObject;
import com.lecent.park.core.mq.rabbitmq.MessageConstant;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.HashMap;


/**
 * 抬杆回复监听
 *
 * <AUTHOR>
 */

@Component
@Slf4j
@Order
public class UpStaffAckHandler {


	private static Logger optLog = LoggerFactory.getLogger("opt_log");

	/**
	 * 抬杆回复监听
	 *
	 * @param message
	 */
	@RabbitListener(bindings = {@QueueBinding(value = @Queue(value = MessageConstant.LECENT_PARK_UP_STAFF_MESSAGE_ACK_QUEUE, durable = "true")
		, exchange = @Exchange(value = Exchanges.LECENT_PARK_EXCHANGE_NAME
		, type = ExchangeTypes.TOPIC), key = MessageConstant.LECENT_PARK_ROUTE_UP_STAFF_ACK)})
	public void upAckMessageHandler(Message message) {
		try {
			String msg = new String(message.getBody(), Charset.forName("utf-8"));
			if(msg.contains("passPlate")){
				HashMap map = JsonUtil.parse(msg, HashMap.class);
				String passPlate = (String)map.get("passPlate");
				Long timestamp = (Long)map.get("timestamp");
				optLog.error("收到抬杆回复||{}||{}", timestamp, passPlate);
			}
		} catch (Exception e) {
			log.error("本次收到抬杆回复转码失败：messageId={},body={}", message.getMessageProperties().getMessageId(),
				message.getBody(), e);
			return;
		}
	}

	public static void main(String[] args) {

		String url = "http://api.telaidian.com.cn:9501/evcs/v20161110/query_token";
		JSONObject json = new JSONObject();
		json.put("OperatorID", "395815801");
		json.put("OperatorSecret","");

	}
}

