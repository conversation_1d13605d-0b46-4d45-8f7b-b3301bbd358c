package com.lecent.park.controller.ebo.mini.resp;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 车位设备信息
 * <AUTHOR>
 */
@Data
@Builder
public class PlaceDevice {


	/**
	 * 设备名称
	 */
	private String deviceName;


	/**
	 * 设备序列号
	 */
	private String sn;


	/**
	 * 设备序列号
	 */
	private String mac;


	/**
	 * 蓝牙名称
	 */
	private String bluetoothName;


	/**
	 * 是否已降锁
	 */
	private Boolean isDown;

}
