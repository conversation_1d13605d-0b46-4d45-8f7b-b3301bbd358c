package com.lecent.park.scheduled;

import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.device.config.DeviceConfig;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IParklotSpaceNumberPushService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import static com.lecent.park.core.mq.rabbitmq.MessageConstant.LECENT_PARKING_DEVICE_DOWN_FEEDBACK_TOPIC_PREFIX;
import static com.lecent.park.core.mq.rabbitmq.MessageConstant.RABBITMQ_MQTT_DEFAULT_EXCHANGE;

/**
 * 车位视屏桩心跳
 */
@Component
@Slf4j
public class ParkDeviceScheduled {
    /**
	 * 消息服务
	 */
	private final MqSender mqSender;


	private final DeviceConfig deviceConfig;

	private final IParklotSpaceNumberPushService parklotSpaceNumberPushService;

	private final IParklotService parkLotService;


	public ParkDeviceScheduled(MqSender mqSender,
							   DeviceConfig deviceConfig,
							   IParklotSpaceNumberPushService parklotSpaceNumberPushService,
							   IParklotService parkLotService) {
        this.mqSender = mqSender;
		this.deviceConfig = deviceConfig;
		this.parklotSpaceNumberPushService = parklotSpaceNumberPushService;
		this.parkLotService = parkLotService;
	}

	/**
	 * 定时处理过期并超过缓冲时间的月卡
	 * 释放也开卡的数量
	 */
	@Scheduled(cron = "${lecent.park.device.info-screen-cron}")
	@RedisLock(value = "lecent:park::timedTask:lock:infoScreenHeart")
	public void infoScreenHeart() {
		String commandStr = "{\"command\":\"heartbeat\"}";
		if (CollectionUtil.isNotEmpty(deviceConfig.getInfoScreenMac())) {
			deviceConfig.getInfoScreenMac().forEach(mac -> {
				String[] macInfo = mac.split("::", -1);
				if (macInfo.length == 2) {
					mqSender.sendMessage(commandStr,
						LECENT_PARKING_DEVICE_DOWN_FEEDBACK_TOPIC_PREFIX.concat(macInfo[1])
						, RABBITMQ_MQTT_DEFAULT_EXCHANGE);
				}
			});

		}
	}


	/**
	 * 停车场信息屏(15分钟执行一次)
	 */
	@Scheduled(cron = "0 0/15 * * * ?")
	@RedisLock(value = "lecent:park::timedTask:lock:placeNumber", waitTime = 1L, leaseTime = 30L)
	public void placeNumberPushTask() throws InterruptedException {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		parkLotService.list().forEach(this.parklotSpaceNumberPushService::asyncPush);
		Thread.sleep(2000);
		stopWatch.stop();
		if (log.isDebugEnabled()) {
			log.debug("停车场信息屏推送消耗总时长: {}s：", stopWatch.getTotalTimeSeconds());
		}
	}

}
