package com.lecent.park.wrapper;

import com.lecent.park.entity.ParklotTimeDuration;
import com.lecent.park.vo.ParklotTimeDurationVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 停车场停车时段配置包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-07-31
 */
public class ParklotTimeDurationWrapper extends BaseEntityWrapper<ParklotTimeDuration, ParklotTimeDurationVO>  {

	public static ParklotTimeDurationWrapper build() {
		return new ParklotTimeDurationWrapper();
 	}

	@Override
	public ParklotTimeDurationVO entityVO(ParklotTimeDuration parklotTimeDuration) {
		ParklotTimeDurationVO parklotTimeDurationVO = BeanUtil.copy(parklotTimeDuration, ParklotTimeDurationVO.class);

		//User createUser = UserCache.getUser(parklotTimeDuration.getCreateUser());
		//User updateUser = UserCache.getUser(parklotTimeDuration.getUpdateUser());
		//parklotTimeDurationVO.setCreateUserName(createUser.getName());
		//parklotTimeDurationVO.setUpdateUserName(updateUser.getName());

		return parklotTimeDurationVO;
	}

}
