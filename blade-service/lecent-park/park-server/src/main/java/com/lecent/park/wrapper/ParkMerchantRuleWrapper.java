package com.lecent.park.wrapper;

import com.lecent.park.entity.ParkMerchantRule;
import com.lecent.park.vo.ParkMerchantRuleVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
public class ParkMerchantRuleWrapper extends BaseEntityWrapper<ParkMerchantRule, ParkMerchantRuleVO> {

	public static ParkMerchantRuleWrapper build() {
		return new ParkMerchantRuleWrapper();
	}

	@Override
	public ParkMerchantRuleVO entityVO(ParkMerchantRule parkMerchantRule) {
		ParkMerchantRuleVO parkMerchantRuleVO = BeanUtil.copy(parkMerchantRule, ParkMerchantRuleVO.class);

		//User createUser = UserCache.getUser(parkMerchantRule.getCreateUser());
		//User updateUser = UserCache.getUser(parkMerchantRule.getUpdateUser());
		//parkMerchantRuleVO.setCreateUserName(createUser.getName());
		//parkMerchantRuleVO.setUpdateUserName(updateUser.getName());

		return parkMerchantRuleVO;
	}

}
