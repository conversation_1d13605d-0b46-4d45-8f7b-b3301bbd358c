package com.lecent.park.controller.openapi;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.entity.InvoiceParklot;
import com.lecent.park.entity.UserInvoiceConfig;
import com.lecent.park.service.IInvoiceParklotService;
import com.lecent.park.service.IUserInvoiceConfigService;
import com.lecent.park.vo.UserInvoiceConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 开发票抬头配置接口
 * @author: cy
 * @date: 2021年10月25日 17:23
 */

@RestController
@AllArgsConstructor
@RequestMapping("/park/invoice/config")
@Api(value = "车场发票开票配置", tags = "车场发票开票配置")
public class InvoiceConfigController {

	private final IUserInvoiceConfigService userInvoiceConfigService;


	private final IInvoiceParklotService invoiceParklotService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入userInvoiceConfig")
	public R<UserInvoiceConfig> detail(UserInvoiceConfig userInvoiceConfig) {
		UserInvoiceConfig detail = userInvoiceConfigService.getOne(Condition.getQueryWrapper(userInvoiceConfig));
		return R.data(detail);
	}

	/**
	 * 列表 开发票抬头配置
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入userInvoiceConfig")
	public R<List<UserInvoiceConfig>> list(UserInvoiceConfig userInvoiceConfig) {
		userInvoiceConfig.setPhone(AuthUtil.getPhone());
		List<UserInvoiceConfig> list = userInvoiceConfigService.list(Condition.getQueryWrapper(userInvoiceConfig));
		return R.data(list);
	}


	/**
	 * 自定义分页 开发票抬头配置
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入userInvoiceConfig")
	public R<IPage<UserInvoiceConfigVO>> page(UserInvoiceConfigVO userInvoiceConfig, Query query) {
		IPage<UserInvoiceConfigVO> pages = userInvoiceConfigService.selectUserInvoiceConfigPage(Condition.getPage(query), userInvoiceConfig);
		return R.data(pages);
	}

	/**
	 * 新增 开发票抬头配置
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入userInvoiceConfig")
	public R<Boolean> save(@Validated({UserInvoiceConfig.AddUserInvoiceConfig.class}) @RequestBody
							   UserInvoiceConfig userInvoiceConfig) {
		return R.status(userInvoiceConfigService.saveConfig(userInvoiceConfig));
	}

	/**
	 * 修改 开发票抬头配置
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入userInvoiceConfig")
	public R<Boolean> update(@Validated({UserInvoiceConfig.AddUserInvoiceConfig.class})
							 @RequestBody UserInvoiceConfig userInvoiceConfig) {
		return R.status(userInvoiceConfigService.updateInvoiceConfig(userInvoiceConfig));
	}


	/**
	 * 删除 开发票抬头配置
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userInvoiceConfigService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 车场是否支持开票
	 *
	 * @param parkLotId
	 * @return
	 */
	@GetMapping("/support/invoice")
	@ApiOperation(value = "车场是否支持开票", notes = "传入ids")
	public R<Boolean> supportInvoice(String parkLotId) {
		InvoiceParklot invoiceParklot = invoiceParklotService.queryByParkLotId(parkLotId);
		return R.data(invoiceParklot == null);
	}

}
