package com.lecent.park.controller.open.openh5.res;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResOnlineCard {

	/**
	 * 订单ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("订单ID")
	private Long orderId;

	/**
	 * 扫码端小程序ID
	 */
	@ApiModelProperty(value = "扫码端小程序ID")
	private String scanMimiId;

	/**
	 * 扫码端小程序应用原始ID
	 */
	@ApiModelProperty(value = "扫码端小程序应用原始ID")
	private String scanMimiOriginalId;

	/**
	 * 扫码端支付宝小程序ID
	 */
	@ApiModelProperty(value = "扫码端支付宝小程序ID")
	private String scanAliMimiId;
}
