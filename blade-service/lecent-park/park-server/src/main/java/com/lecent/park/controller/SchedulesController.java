package com.lecent.park.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.service.ISchedulesService;
import com.lecent.park.vo.SchedulesCalendar;
import com.lecent.park.vo.SchedulesVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 排班表 控制器
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@RestController
@AllArgsConstructor
@RequestMapping("/schedules")
@Api(value = "排班表", tags = "排班表接口")
public class SchedulesController extends BladeController {

    private final ISchedulesService schedulesService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入schedules")
    public R<SchedulesVO> detail(SchedulesVO schedules) {
        return R.data(schedulesService.detail(schedules.getId()));
    }

    /**
     * 分页 排班表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入schedules")
    public R<List<SchedulesCalendar>> list(SchedulesVO schedules) {
        return R.data(schedulesService.getSchedulesCalendar(schedules));
    }

    /**
     * 新增或修改 排班表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "新增或修改", notes = "传入schedules")
    public R<Boolean> submit(@Valid @RequestBody SchedulesCalendar calendar) {
        return R.status(schedulesService.submitSchedules(calendar));
    }

    /**
     * 删除 排班表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(schedulesService.delSchedules(Func.toLongList(ids)));
    }

    /**
     * 导出排班表
     */
    @PostMapping("/export")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "导出排班表", notes = "传入月份yyyy-MM")
    public void exportSchedules(HttpServletResponse response, @RequestParam("month") String monthStr) {
        schedulesService.exportSchedules(response, monthStr);
    }

    /**
     * 清空排班表
     */
    @PostMapping("/clean")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "清空排班表", notes = "传入schedules")
    public R<Boolean> clean(@RequestBody SchedulesVO schedules) {
        return R.status(schedulesService.clean(schedules));
    }
}
