package com.lecent.park.wrapper;

import com.lecent.park.entity.CardCategory;
import com.lecent.park.vo.CardCategoryVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 套餐卡类型包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class CardCategoryWrapper extends BaseEntityWrapper<CardCategory, CardCategoryVO>  {

	public static CardCategoryWrapper build() {
		return new CardCategoryWrapper();
 	}

	@Override
	public CardCategoryVO entityVO(CardCategory cardCategory) {
		CardCategoryVO cardCategoryVO = BeanUtil.copy(cardCategory, CardCategoryVO.class);

		//User createUser = UserCache.getUser(cardCategory.getCreateUser());
		//User updateUser = UserCache.getUser(cardCategory.getUpdateUser());
		//cardCategoryVO.setCreateUserName(createUser.getName());
		//cardCategoryVO.setUpdateUserName(updateUser.getName());

		return cardCategoryVO;
	}

}
