package com.lecent.park.service.impl;

/**
 * @Description： 首页统计服务类
 * <AUTHOR>
 * @Date: 2020/6/11 11:34
 */

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.entity.Parklot;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.ParkingOrderMapper;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParklotMapper;
import com.lecent.park.service.*;
import com.lecent.park.vo.*;
import com.lecent.pay.core.enums.PayWay;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.lecent.park.cache.ParkCacheNames.LECENT_PARK_REMAIN_NUMBER;

@Slf4j
@Service
@AllArgsConstructor
public class IndexStatisticsServiceImpl implements IIndexStatisticsService {

	private ITempParkingOrderService tempParkingOrderService;
	private IParkingOrderService parkingOrderService;
	private IUserChannelDutyItemService userChannelDutyItemService;
	private ParklotMapper parklotMapper;
	private ParkingOrderMapper parkingOrderMapper;
	private BladeRedis redis;
	private ICardOrderService cardOrderService;

	@Override
	public List<MoneyTrendVO> lastWeekMoney() {

		Long parklotId = userChannelDutyItemService.getCurrentUserParklotId(SecureUtil.getUserId());
		return tempParkingOrderService.lastWeekMoney(parklotId);
	}

	@Override
	public InAndOutCarVO inAndOutCarAmount() {

		Long parklotId = userChannelDutyItemService.getCurrentUserParklotId(SecureUtil.getUserId());

		//停车场停车数量
		Integer orderNum = parkingOrderService.getCurrentAmount(parklotId);

		//累计收费总额

		BigDecimal receiveAmount = tempParkingOrderService.getTodayAmount(parklotId);

		//今日进出车辆
		Integer inOutAmount = parkingOrderService.getTodayInAndOutAmount(parklotId);

		//今日开关闸数 todo

		return InAndOutCarVO.builder()
			.parkingCarAmount(orderNum)
			.totalReceiveAmount(receiveAmount)
			.inOutAmount(inOutAmount)
			.openCloseAmount(0)
			.build();
	}

	@Override
	public ReceivedMoneyDistributeVO receivedMoney() {
		Long parklotId = userChannelDutyItemService.getCurrentUserParklotId(SecureUtil.getUserId());
		return tempParkingOrderService.receivedMoney(parklotId);
	}

	@Override
	public List<TotalInAndOutCarVO> totalInAndOutCar() {
		Long parklotId = userChannelDutyItemService.getCurrentUserParklotId(SecureUtil.getUserId());
		return parkingOrderService.totalInAndOutCar(parklotId);
	}

	@Override
	public ParkingCarDuringVO parkingCarDuring() {
		Long parklotId = userChannelDutyItemService.getCurrentUserParklotId(SecureUtil.getUserId());
		return parkingOrderService.parkingCarDuring(parklotId);
	}

	@Override
	public List<Map> parkingEnterTimeDistribute() {
		Long parklotId = userChannelDutyItemService.getCurrentUserParklotId(SecureUtil.getUserId());
		return parkingOrderService.parkingEnterTimeDistribute(parklotId);
	}

	private List<Long> getParklotIds(String tenantId, Long parklotId) {
		List<Long> parklotIds = new LinkedList<>();
		if (tenantId != null && !"".equals(tenantId)) {
			List<Parklot> parklots = parklotMapper.selectList(Wrappers.<Parklot>lambdaQuery().eq(Parklot::getTenantId, tenantId));
			for (Parklot parklot : parklots) {
				parklotIds.add(parklot.getId());
			}
		}
		if (parklotId != null && !parklotIds.contains(parklotId)) {
			parklotIds.add(parklotId);
		}
		return parklotIds;
	}

	@Override
	public CarSpaceVO getCarSpaceInfo(String tenantId, Long parklotId) {
		List<Long> parklotIds = getParklotIds(tenantId, parklotId);
		CarSpaceVO carSpaceInfo = parklotMapper.getCarSpaceInfo(tenantId, parklotId);
		if (carSpaceInfo == null) {
			return null;
		}
		//获取车场剩余车位数量
		int count = 0;
		for (Long id : parklotIds) {
			int number = Func.toInt(redis.get(LECENT_PARK_REMAIN_NUMBER.concat("::").concat(String.valueOf(id))));
			count += number;
		}
		carSpaceInfo.setSurplusSpaceAmount(count);
		return carSpaceInfo;
	}

	@Override
	public Map<String, Integer> getEnterAndExitTotal(String tenantId, Long parklotId) {
		TotalInAndOutCarVO enterAndExitTotal = parkingOrderMapper.getEnterAndExitTotal(tenantId, parklotId);
		if (enterAndExitTotal == null) {
			return null;
		}
		Map<String, Integer> result = new HashMap<>();
		result.put("enterCount", enterAndExitTotal.getEnterCount() == null ? 0 : enterAndExitTotal.getEnterCount());
		result.put("exitCount", enterAndExitTotal.getExitCount() == null ? 0 : enterAndExitTotal.getExitCount());
		result.put("totalCount", result.get("enterCount") + result.get("exitCount"));
		return result;
	}

	/**
	 * @param tenantId
	 * @param parklotId
	 * @param type      月-month 日 - day
	 * @return
	 */
	@Override
	public Map<String, long[]> getCarFlow(String tenantId, Long parklotId, String type) {
		Map<String, long[]> map = new HashMap<>();
		//按月统计
		String month = "month";
		//按日统计
		String day = "day";
		if (month.equals(type)) {
			//按月统计 12个月
			List<Map<String, Object>> monthCarFlowEnters = parkingOrderMapper.getMonthCarFlowEnter(tenantId, parklotId);
			List<Map<String, Object>> monthCarFlowExits = parkingOrderMapper.getMonthCarFlowExit(tenantId, parklotId);
			long[] enter = getCarFlowInfo(monthCarFlowEnters, 12);
			long[] exit = getCarFlowInfo(monthCarFlowExits, 12);
			map.put("enter", enter);
			map.put("exit", exit);
		} else if (day.equals(type)) {
			//按日统计 31天
			List<Map<String, Object>> dayCarFlowEnters = parkingOrderMapper.getDayCarFlowEnter(tenantId, parklotId);
			List<Map<String, Object>> dayCarFlowExits = parkingOrderMapper.getDayCarFlowExit(tenantId, parklotId);
			long[] enter = getCarFlowInfo(dayCarFlowEnters, 31);
			long[] exit = getCarFlowInfo(dayCarFlowExits, 31);
			map.put("enter", enter);
			map.put("exit", exit);
		}
		return map;
	}

	@Override
	public List<GuardPayWayStatisticsVO> payWayRatio(Long parklotId) {
		List<GuardPayWayStatisticsVO> tempList = tempParkingOrderService.payWayRatio(parklotId);
		List<GuardPayWayStatisticsVO> cardList = cardOrderService.payWayRatio(parklotId);
		tempList.forEach(temp -> cardList.stream().filter(temp::equals).forEach(card -> plusRatio(temp, card)));
		return translatePayWay(tempList);
	}

	private List<GuardPayWayStatisticsVO> translatePayWay(List<GuardPayWayStatisticsVO> tempList) {
		Optional<GuardPayWayStatisticsVO> first = tempList.stream().filter(guardPayWayStatisticsVO -> Func.isNull(guardPayWayStatisticsVO.getPayWay()))
			.findFirst();
		GuardPayWayStatisticsVO total = first.isPresent() ? first.get()
			: GuardPayWayStatisticsVO.builder().payWay(null).ratio(String.valueOf(0)).build();
		BigDecimal totalRatio = new BigDecimal(total.getRatio());
		List<GuardPayWayStatisticsVO> collect = tempList.stream().filter(guardPayWayStatisticsVO -> Func.notNull(guardPayWayStatisticsVO.getPayWay())).map(guardPayWayStatisticsVO -> {
			String payWay = guardPayWayStatisticsVO.getPayWay();
			String name = PayWay.getNameByKey(Integer.valueOf(payWay));
			guardPayWayStatisticsVO.setPayWay(name);
			BigDecimal ratio = new BigDecimal(guardPayWayStatisticsVO.getRatio());
			guardPayWayStatisticsVO.setRatio(ratio.divide(totalRatio, 4, RoundingMode.HALF_UP).toString());
			return guardPayWayStatisticsVO;
		}).collect(Collectors.toList());
		return collect;
	}

	private void plusRatio(GuardPayWayStatisticsVO temp, GuardPayWayStatisticsVO card) {
		String tempRatio = temp.getRatio();
		int tempVal = Func.isNull(tempRatio) ? 0 : Integer.valueOf(tempRatio);
		String cardRatio = card.getRatio();
		int cardVal = Func.isNull(cardRatio) ? 0 : Integer.valueOf(cardRatio);
		temp.setRatio(String.valueOf(tempVal + cardVal));
	}

	private long[] getCarFlowInfo(List<Map<String, Object>> listMaps, int size) {
		long[] allDayCarFlow = new long[size];
		for (Map<String, Object> map : listMaps) {
			int dateIndex = 1;
			long count = 1;
			for (Map.Entry<String, Object> entry : map.entrySet()) {
				if ("dayDate".equals(entry.getKey())) {
					dateIndex = (Integer) entry.getValue();
				} else if ("monthDate".equals(entry.getKey())) {
					dateIndex = (Integer) entry.getValue();
				} else if ("carCount".equals(entry.getKey())) {
					count = (Long) entry.getValue();
				}
			}
			//对应的天 所对应的数量
			allDayCarFlow[dateIndex - 1] = count;
		}
		return allDayCarFlow;
	}
}
