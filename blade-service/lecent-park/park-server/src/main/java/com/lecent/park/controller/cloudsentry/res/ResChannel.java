package com.lecent.park.controller.cloudsentry.res;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 通道
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
@Builder
public class ResChannel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 通道ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "通道ID")
	private Long id;

	/**
	 * 通道编号
	 */
	@ApiModelProperty(value = "通道编号")
	private String code;

	/**
	 * 通道名称
	 */
	@ApiModelProperty(value = "通道名称")
	private String name;


}
