package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotFloor;
import com.lecent.park.vo.ParklotFloorVO;
import com.lecent.park.vo.UsedAmountVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
public interface IParklotFloorService extends BaseService<ParklotFloor> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param parklotFloor
	 * @return
	 */
	IPage<ParklotFloorVO> selectParklotFloorPage(IPage<ParklotFloorVO> page, ParklotFloorVO parklotFloor);

	/**
	 * 根据车场id获取楼层列表
	 * @param parklotId 车场id
	 * @return
	 */
	List<ParklotFloor> getByParklotId(Long parklotId);

	/**
	 * 批量新增楼层
	 * @param parklotFloor
	 * @return
	 */
	boolean doSaveBatch(List<ParklotFloor> parklotFloor);

	List<ParklotFloor> getListByParklotId(Long parklotId);

	/**
	 * 根据楼层id获取该楼层剩余车位
	 * @param floorId 楼层id
	 * @return
	 */
	ParklotFloor getRemainAmountByFloorId(Long floorId);

	boolean submit(ParklotFloor parklotFloor);

	List<ParklotFloor> dropListByParklotId(Long parklotId);


	/**
	 * 获取其他楼层添加的独立产权车位的总数
	 * @param parklotId
	 * @param floorId
	 * @return
	 */
	Integer getOtherFloorTotalIndependent(Long parklotId, Long floorId);

	/**
	 * 获取其他楼层添加的子母车位的总数
	 * @param parklotId
	 * @param floorId
	 * @return
	 */
	Integer getOtherFloorTotalLetter(Long parklotId, Long floorId);

	/**
	 * 获取其他楼层添加的临停车位的总数
	 * @param parklotId
	 * @param floorId
	 * @return
	 */
	Integer getOtherFloorTotalTemp(Long parklotId, Long floorId);

	/**
	 * 获取该车场已经使用的车场数量
	 * @param parklotId 车场id
	 * @return
	 */
	UsedAmountVO getUsedAmountById(Long parklotId);

	Integer getOtherFloorTotalVip(Long parklotId, Long floorId);

	Integer getOtherFloorTotalAcc(Long parklotId, Long floorId);

	boolean removeFloor(List<Long> toLongList);

	/**
	 * 路内停车楼层
	 * @param parklot
	 * @return
	 */
	ParklotFloor addInnerRoad(Parklot parklot);
}
