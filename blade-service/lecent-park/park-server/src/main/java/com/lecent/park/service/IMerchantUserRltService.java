package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.MerchantUserRlt;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.BladePage;
import org.springblade.system.user.entity.User;

/**
 * 酒店商户与blade_user用户关联表 服务类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface IMerchantUserRltService extends BaseService<MerchantUserRlt> {

	/**
	 * 酒店新增用户
	 *
	 * @param user 用户
	 * @return true
	 */
	boolean addUser(User user);

	BladePage<User> empyInfo(IPage<User> page, User user);
}
