package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.vo.PointsChangeRecord;

public interface IMyPointsService {

	/**
	 * 页
	 *
	 * @param page   页
	 * @param record 记录
	 * @return {@link IPage }<{@link PointsChangeRecord }>
	 */
	IPage<PointsChangeRecord> page(IPage<PointsChangeRecord> page, PointsChangeRecord record);

	/**
	 * 总分
	 *
	 * @param record 记录
	 * @return {@link PointsChangeRecord }
	 */
	PointsChangeRecord totalPoints(PointsChangeRecord record);
}
