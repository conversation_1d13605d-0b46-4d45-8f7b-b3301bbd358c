package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.CorpseCleanLogDTO;
import com.lecent.park.entity.CorpseCleanLog;
import com.lecent.park.service.ICorpseCleanLogService;
import com.lecent.park.vo.CorpseCleanLogVO;
import com.lecent.park.wrapper.CorpseCleanLogWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 僵尸车清理记录 控制器
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/corpsecleanlog")
@Api(value = "僵尸车清理记录", tags = "僵尸车清理记录接口")
public class CorpseCleanLogController extends BladeController {

	private ICorpseCleanLogService corpseCleanLogService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入corpseCleanLog")
	public R<CorpseCleanLogVO> detail(CorpseCleanLog corpseCleanLog) {
		CorpseCleanLog detail = corpseCleanLogService.getOne(Condition.getQueryWrapper(corpseCleanLog));
		return R.data(CorpseCleanLogWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 僵尸车清理记录
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入corpseCleanLog")
	public R<IPage<CorpseCleanLogVO>> list(CorpseCleanLog corpseCleanLog, Query query) {
		IPage<CorpseCleanLog> pages = corpseCleanLogService.page(Condition.getPage(query),
																 Condition.getQueryWrapper(corpseCleanLog));
		return R.data(CorpseCleanLogWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 僵尸车清理记录
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入corpseCleanLog")
	public R<IPage<CorpseCleanLogVO>> page(CorpseCleanLogDTO corpseCleanLogDto, Query query) {
		IPage<CorpseCleanLogVO> pages = corpseCleanLogService.selectCorpseCleanLogPage(Condition.getPage(query),
																					   corpseCleanLogDto);
		return R.data(pages);
	}

	/**
	 * 新增 僵尸车清理记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入corpseCleanLog")
	public R save(@Valid @RequestBody CorpseCleanLog corpseCleanLog) {
		return R.status(corpseCleanLogService.save(corpseCleanLog));
	}

	/**
	 * 修改 僵尸车清理记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入corpseCleanLog")
	public R update(@Valid @RequestBody CorpseCleanLog corpseCleanLog) {
		return R.status(corpseCleanLogService.updateById(corpseCleanLog));
	}

	/**
	 * 新增或修改 僵尸车清理记录
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入corpseCleanLog")
	public R submit(@Valid @RequestBody CorpseCleanLog corpseCleanLog) {
		return R.status(corpseCleanLogService.saveOrUpdate(corpseCleanLog));
	}

	/**
	 * 删除 僵尸车清理记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(corpseCleanLogService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 清理僵尸车
	 */
	@PostMapping("/cleanCorpseCar")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "清理僵尸车", notes = "传入ids")
	public R cleanCorpseCar(@RequestParam String ids, @RequestParam(required = false) String reason) {
		return R.status(corpseCleanLogService.cleanCorpseCar(Func.toLongList(ids), reason));
	}



	/**
	 * 僵尸车清理记录导出
	 */
	@PostMapping("/corpse-export")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "僵尸车清理记录导出", notes = "传入cardDTO")
	public void corpseCleanLogExport(CorpseCleanLogDTO corpseCleanLogDTO, HttpServletResponse response) {
		corpseCleanLogService.corpseCleanLogExport(corpseCleanLogDTO, response);
	}


	/**
	 * 恢复停车记录
	 */
	@PostMapping("/restoreRecords")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "恢复停车记录", notes = "传入主键id")
	public R restoreRecords(@RequestParam @NotNull Long id) {
		String result= corpseCleanLogService.restoreRecords(id);
		return R.data(result);
	}

	/**
	 * 僵尸车批量恢复接口
	 * @param ids
	 * @return
	 */
	@PostMapping("/restore-records-ids")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "批量恢复停车记录", notes = "传入主键ids")
	public R restoreRecordsIds(@RequestParam("ids") String ids) {
		String[] split = ids.split(",");
		List<Long> idList = Arrays.asList(split).stream()
			.map(Long::parseLong)
			.collect(Collectors.toList());
		String result = corpseCleanLogService.restoreRecordsIds(idList);
		return R.data(result);
	}

}
