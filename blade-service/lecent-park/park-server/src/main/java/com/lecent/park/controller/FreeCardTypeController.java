package com.lecent.park.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.FreeCardTypeDTO;
import com.lecent.park.entity.FreeCardType;
import com.lecent.park.service.IFreeCardTypeService;
import com.lecent.park.vo.FreeCardTypeVO;
import com.lecent.park.wrapper.FreeCardTypeWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 免费车类型 控制器
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/freecardtype")
@Api(value = "免费车类型", tags = "免费车类型")
public class FreeCardTypeController extends BladeController {

	private IFreeCardTypeService freeCardTypeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入freeCardType")
	public R<FreeCardTypeVO> detail(FreeCardType freeCardType) {
		FreeCardType detail = freeCardTypeService.getOne(Condition.getQueryWrapper(freeCardType));
		return R.data(FreeCardTypeWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 公司角色表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入freeCardType")
	public R<IPage<FreeCardTypeVO>> list(FreeCardType freeCardType, Query query) {
		IPage<FreeCardType> pages = freeCardTypeService.page(Condition.getPage(query), Condition.getQueryWrapper(freeCardType));
		return R.data(FreeCardTypeWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 公司角色表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入freeCardType")
	public R<IPage<FreeCardTypeVO>> page(FreeCardTypeVO freeCardType, Query query) {
		IPage<FreeCardTypeVO> pages = freeCardTypeService.selectFreeCardTypePage(Condition.getPage(query), freeCardType);
		return R.data(pages);
	}

	/**
	 * 新增 公司角色表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入freeCardType")
	public R save(@Valid @RequestBody FreeCardTypeDTO freeCardTypeDTO) {
		return R.status(freeCardTypeService.saveTypes(freeCardTypeDTO));
	}

	/**
	 * 修改 公司角色表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入freeCardType")
	public R update(@Valid @RequestBody FreeCardType freeCardType) {
		return R.status(freeCardTypeService.updateById(freeCardType));
	}

	/**
	 * 新增或修改 公司角色表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入freeCardType")
	public R submit(@Valid @RequestBody FreeCardType freeCardType) {
		return R.status(freeCardTypeService.saveOrUpdate(freeCardType));
	}


	/**
	 * 删除 公司角色表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(freeCardTypeService.deleteLogic(Func.toLongList(ids)));
	}




	/**
	 * 获取当前租户最下所有
	 */
	@GetMapping("/listAll")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取当前租户最下所有", notes = "获取当前租户最下所有")
	public R<List<FreeCardType>> listAll() {
		return R.data(freeCardTypeService.listAll());
	}

}
