package com.lecent.park.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.constant.ParkPlaceConstant;
import com.lecent.park.device.config.DeviceConfig;
import com.lecent.park.device.utils.ParkPlaceActionUtils;
import com.lecent.park.dto.BootScreenInfoDTO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.event.channeltodo.ReloadParkPlaceEvent;
import com.lecent.park.event.channeltodo.ReturnParkPlaceEvent;
import com.lecent.park.service.ICalculateParkRemainNumberService;
import com.lecent.park.service.IParkingOrderService;
import com.lecent.park.service.IParkingPlaceService;
import com.lecent.park.service.IParklotService;
import com.leliven.park.infrastructure.gateway.persistence.order.ParkingOrderQueryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.lecent.park.cache.ParkCacheNames.LECENT_PARKPLACE_INIT_FLAG;
import static com.lecent.park.cache.ParkCacheNames.LECENT_PARK_REMAIN_NUMBER;
import static com.lecent.park.common.constant.ParkPlaceConstant.PARKPLACE_INIT_STATUS_CALCULATE;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableAsync
public class ParkRemainNumberListener {

	/**
	 * 车位计算服务
	 */
	@Resource
	private ICalculateParkRemainNumberService calculateParkRemainNumberService;
	/**
	 * redis工具类
	 */
	@Resource
	private BladeRedis bladeRedis;
	@Resource
	private ParkingOrderQueryRepository parkingOrderQueryRepository;
	/**
	 * 停车记录服务
	 */
	@Resource
	private IParkingOrderService parkingOrderService;
	/**
	 * 停车场服务
	 */
	@Resource
	private IParklotService parklotService;
	/**
	 * 车位服务
	 */
	@Resource
	private IParkingPlaceService parkingPlaceService;
	/**
	 * 设备配置
	 */
	@Resource
	private DeviceConfig deviceConfig;

	/**
	 * 判断是否在初始化脚本
	 */
	private static final String INIT_LOCK_SCRIPT =
		"    local remain  =tostring(redis.call('get',KEYS[1]))\n" +
			"    if(remain=='false') then\n" +
			"       redis.call('set',KEYS[1],tonumber(ARGV[1]))\n" +
			"       redis.call('expire',KEYS[1],60)\n" +
			"       return 0\n" +
			"    else\n" +
			"    end\n" +
			"    return 1\n";

	/**
	 * 归还车位
	 *
	 * @param event 事件
	 */
	@Async
	@TransactionalEventListener(fallbackExecution = true)
	void returnParkPlace(ReturnParkPlaceEvent event) {
		calculateParkRemainNumberService.returnParkPlaceBook(event);
	}

	/**
	 * 重新加载车场的车位
	 *
	 * @param event 事件
	 */
	@Async
	@TransactionalEventListener(fallbackExecution = true)
	void reloadParkPlace(ReloadParkPlaceEvent event) {
		//车场为空 加载所有
		if (Objects.isNull(event.getParkLotId())) {
			initParkRemainNumber();
			return;
		}
		this.calculateParkRemainNumber(event.getParkLotId());
	}

	/**
	 * 初始化车位数
	 *
	 */
	public void initParkRemainNumber() {
		log.info("开始初始化车场车位数>>>");
		//当前初始化车位状态未锁
		if (getInitLock()) {
			List<Parklot> parklotList = parklotService.list(Wrappers.<Parklot>query().lambda().eq(Parklot::getIsDeleted, BladeConstant.DB_NOT_DELETED));
			if (CollectionUtil.isNotEmpty(parklotList)) {
				for (Parklot parklot : parklotList) {
					try {
						calculateParkRemainNumber(parklot.getId());
					} catch (Exception e) {
						log.warn("车场[{}]初始化车位数量失败！", parklot.getFullName());
						//设置状态异常
						bladeRedis.set(LECENT_PARKPLACE_INIT_FLAG, ParkPlaceConstant.PARKPLACE_INIT_STATUS_ERROR);
						throw e;
					}
					log.debug("车场[{}]初始化车位数量成功！", parklot.getFullName());

				}
				bladeRedis.expire(LECENT_PARKPLACE_INIT_FLAG, 0);
			}
		}
		log.info("初始化车场车位数结束>>>");
	}

	/**
	 * 实时计算车位数
	 *
	 * @param parkLotId 车场ID
	 */
	public int calculateParkRemainNumber(Long parkLotId) {
		LecentAssert.notNull(parkLotId, "实时计算车位>>>请传入车场id!");
		Parklot parklot = ParkLotCaches.getParkLot(parkLotId);
		LecentAssert.notNull(parklot, "实时计算车位>>>车场为空！ ");

		int total = parklot.getTempLotAmount() + parklot.getIndependentOwnershipAmount() + parklot.getLetterLotAmount() * 2;


		// 预定未进场的车位数
		int reservedVipNumber = parkingPlaceService.countReservedVipNumber(parkLotId);
		//在场vip
		int parkingVipNumber = parkingOrderService.countParkingVipNumber(parkLotId);
		int usedVipNumber = reservedVipNumber + parkingVipNumber;

		//VIP车位数
		int vipNumber = Math.max(parklot.getVipLotAmount(), 0);
		int vipCanParking = Math.max(vipNumber - usedVipNumber, 0);

		//车场可用临停车位数
		int remainNumber = parklot.getTempLotAmount() - getEnterTempNumber(parklot);

		BootScreenInfoDTO bootScreenInfoDTO = new BootScreenInfoDTO();
		bootScreenInfoDTO.setParkingTotal(total);
		bootScreenInfoDTO.setParking(remainNumber);
		bootScreenInfoDTO.setVipParking(vipCanParking);
		bootScreenInfoDTO.setReservable(remainNumber);


		List<String> infoScreenMacList = deviceConfig.getInfoScreenMac();
		ParkPlaceActionUtils.pushBootScreenInfo(bootScreenInfoDTO, infoScreenMacList, parkLotId);

		bladeRedis.set(LECENT_PARK_REMAIN_NUMBER.concat("::").concat(String.valueOf(parkLotId)), remainNumber);

		return remainNumber;
	}

	private int getEnterTempNumber(Parklot parklot) {
		return parkingOrderQueryRepository.countPresent(parklot.getId());
	}


	/**
	 * 异步计算 显示屏
	 *
	 * @param parkLotId   车场ID
	 */
	@Async
	public void asyncCalculateBootScreenInfo(Long parkLotId, Long remainNumber) {
		Parklot parklot = ParkLotCaches.getParkLot(parkLotId);
		//总车位数 临停+独立产权+子母车位*2
		int total = parklot.getTempLotAmount() + parklot.getIndependentOwnershipAmount() + parklot.getLetterLotAmount() * 2;

		BootScreenInfoDTO bootScreenInfoDTO = new BootScreenInfoDTO();
		bootScreenInfoDTO.setParkingTotal(total);
		// 计算车场可用数 临停-租赁月卡
		if (remainNumber < 0L) {
			remainNumber = 0L;
			bootScreenInfoDTO.setVipParking(0);
		} else {
			// vip车位
			int vipNumber = Func.toInt(parklot.getVipLotAmount());
			vipNumber = Math.max(vipNumber, 0);
			// 预定未进场的车位数
			int reservedVipNumber = parkingPlaceService.countReservedVipNumber(parkLotId);
			//在场vip
			int parkingVipNumber = parkingOrderService.countParkingVipNumber(parkLotId);
			int usedVipNumber = reservedVipNumber + parkingVipNumber;
			//剩余vip车位
			int vipCanParking = vipNumber - usedVipNumber;
			vipCanParking = Math.max(vipCanParking, 0);
			bootScreenInfoDTO.setVipParking(vipCanParking);
		}
		bootScreenInfoDTO.setParking(remainNumber.intValue());
		bootScreenInfoDTO.setReservable(remainNumber.intValue());
		//循环查找信息屏的mac
		List<String> infoScreenMacList = deviceConfig.getInfoScreenMac();
		ParkPlaceActionUtils.pushBootScreenInfo(bootScreenInfoDTO, infoScreenMacList, parkLotId);
	}

	/**
	 * 获取初始化锁
	 *
	 * @return true:未锁 false:锁定
	 */
	private boolean getInitLock() {
		DefaultRedisScript<Long> judgeScript = new DefaultRedisScript<>(INIT_LOCK_SCRIPT, Long.class);
		//系列化方式
		GenericToStringSerializer genericToStringSerializer = new GenericToStringSerializer(Object.class);
		//集群时保证 key在一个 slot
		List<String> keys = Collections.singletonList(LECENT_PARKPLACE_INIT_FLAG);
		Long flag = bladeRedis.getRedisTemplate().execute(judgeScript, genericToStringSerializer, genericToStringSerializer, keys, PARKPLACE_INIT_STATUS_CALCULATE);
		return Func.toInt(flag) != PARKPLACE_INIT_STATUS_CALCULATE;
	}
}
