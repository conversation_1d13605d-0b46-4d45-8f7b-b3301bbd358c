package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotFloor;
import com.lecent.park.entity.ParklotRegion;
import com.lecent.park.mapper.ParklotFloorMapper;
import com.lecent.park.service.IParklotFloorService;
import com.lecent.park.service.IParklotRegionService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.vo.ParklotFloorVO;
import com.lecent.park.vo.UsedAmountVO;
import jodd.util.MathUtil;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Service
public class ParklotFloorServiceImpl extends BaseServiceImpl<ParklotFloorMapper, ParklotFloor> implements IParklotFloorService {

	@Lazy
	@Autowired
	private IParklotService parklotService;

	@Lazy
	@Autowired
	private IParklotRegionService parklotRegionService;

	@Override
	public IPage<ParklotFloorVO> selectParklotFloorPage(IPage<ParklotFloorVO> page, ParklotFloorVO parklotFloor) {
		return page.setRecords(baseMapper.selectParklotFloorPage(page, parklotFloor));
	}

	@Override
	public List<ParklotFloor> getByParklotId(Long parklotId) {
		if (Func.isEmpty(parklotId)) {
			return this.list();
		}
		return this.list(Wrappers.<ParklotFloor>query().lambda()
													   .eq(ParklotFloor::getParklotId, parklotId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean doSaveBatch(List<ParklotFloor> parklotFloor) {

		Long parklotId = parklotFloor.get(0).getParklotId();
		LecentAssert.notNull(parklotId, "请选择车场");

		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		LecentAssert.notNull(parklot, "车场不存在或者被删除！");

		//校验楼层代号（同一个停车场内不能重复）
		checkFloorName(parklotFloor);
		//校验楼层层号（同一个停车场内不能重复）
		checkFloorNumber(parklotFloor);
		//校验所有楼层添加的车位是否超过车场的车位
		checkAmount(parklotFloor, parklot);

		parklotFloor.forEach(f -> {
			String floorNo;
			do {
				floorNo = String.valueOf(MathUtil.randomInt(1000, 9999));
			} while (validateNo(f.getParklotId(), floorNo));
			f.setFloorNo(floorNo);

		});

		return this.saveBatch(parklotFloor);
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public ParklotFloor addInnerRoad(Parklot parklot) {
		ParklotFloor floor = new ParklotFloor();
		String floorNo;
		do {
			floorNo = String.valueOf(MathUtil.randomInt(1000, 9999));
		} while (validateNo(floor.getParklotId(), floorNo));
		floor.setParklotId(parklot.getId());
		floor.setFloorNo(floorNo);
		floor.setNumber(1);
		floor.setFloorName("一楼");
		floor.setAccessibilityLotAmount(parklot.getAccessibilityLotAmount());
		floor.setVipLotAmount(parklot.getVipLotAmount());
		floor.setLetterLotAmount(parklot.getLetterLotAmount());
		floor.setTempLotAmount(parklot.getTempLotAmount());
		floor.setIndependentOwnershipAmount(parklot.getIndependentOwnershipAmount());
		this.save(floor);
		return floor;
	}

	@Override
	public List<ParklotFloor> getListByParklotId(Long parklotId) {
		return this.list(Wrappers.<ParklotFloor>query().lambda().eq(ParklotFloor::getParklotId, parklotId));
	}

	@Override
	public ParklotFloor getRemainAmountByFloorId(Long floorId) {
		ParklotFloor floor = getById(floorId);
		LecentAssert.notNull(floor, "没有此楼层");
		List<ParklotRegion> regions = parklotRegionService.list(Wrappers.<ParklotRegion>lambdaQuery()
																	.eq(ParklotRegion::getFloorId, floorId));
		regions.forEach(region -> {
			int IndependentOwnershipAmount =
				floor.getIndependentOwnershipAmount() - region.getIndependentOwnershipAmount();
			floor.setIndependentOwnershipAmount(IndependentOwnershipAmount < 0 ? 0 : IndependentOwnershipAmount);
			int letterLotAmount = floor.getLetterLotAmount() - region.getLetterLotAmount();
			floor.setLetterLotAmount(letterLotAmount < 0 ? 0 : letterLotAmount);
			int tempLotAmount = floor.getTempLotAmount() - region.getTempLotAmount();
			floor.setTempLotAmount(tempLotAmount < 0 ? 0 : tempLotAmount);
			int vipLotAmount = floor.getVipLotAmount() - region.getVipLotAmount();
			floor.setVipLotAmount(vipLotAmount < 0 ? 0 : vipLotAmount);
			int accessibilityLotAmount = floor.getAccessibilityLotAmount() - region.getAccessibilityLotAmount();
			floor.setAccessibilityLotAmount(accessibilityLotAmount < 0 ? 0 : accessibilityLotAmount);
		});
		return floor;
	}

	@Override
	public boolean submit(ParklotFloor parklotFloor) {
		//校验剩余车位
		checkAmount(parklotFloor);
		//校验楼层代号是否重复
		checkFloorName(parklotFloor);
		//校验楼层层号是否重复
		checkFloorNumber(parklotFloor);

		if (Func.isEmpty(parklotFloor.getId())) {
			parklotFloor.setFloorNo(generateFloorNo(parklotFloor.getParklotId()));
		}
		return this.saveOrUpdate(parklotFloor);
	}

	private String generateFloorNo(Long parklotId) {
		String floorNo;
		do {
			floorNo = String.valueOf(MathUtil.randomInt(1000, 9999));
		} while (validateNo(parklotId, floorNo));
		return floorNo;
	}

	@Override
	public List<ParklotFloor> dropListByParklotId(Long parklotId) {
		return this.list(Wrappers.<ParklotFloor>query()
							 .lambda()
							 .eq(ParklotFloor::getParklotId, parklotId)
							 .eq(ParklotFloor::getStatus, Constants.ONE));
	}

	@Override
	public Integer getOtherFloorTotalIndependent(Long parklotId, Long floorId) {
		return baseMapper.getOtherFloorTotalIndependent(parklotId, floorId);
	}

	@Override
	public Integer getOtherFloorTotalLetter(Long parklotId, Long floorId) {
		return baseMapper.getOtherFloorTotalLetter(parklotId, floorId);
	}

	@Override
	public Integer getOtherFloorTotalTemp(Long parklotId, Long floorId) {
		return baseMapper.getOtherFloorTotalTemp(parklotId, floorId);
	}

	@Override
	public UsedAmountVO getUsedAmountById(Long parklotId) {
		return baseMapper.getUsedAmountById(parklotId);
	}

	@Override
	public Integer getOtherFloorTotalVip(Long parklotId, Long floorId) {
		return baseMapper.getOtherFloorTotalVip(parklotId, floorId);
	}

	@Override
	public Integer getOtherFloorTotalAcc(Long parklotId, Long floorId) {
		return baseMapper.getOtherFloorTotalAcc(parklotId, floorId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeFloor(List<Long> idList) {
		for (Long id : idList) {
			int count = this.baseMapper.countRegionByFloorId(id);
			if (count > 0) {
				throw new ServiceException("楼层下已经存在区域，楼层删除失败");
			}
		}
		return this.deleteLogic(idList);
	}

	private boolean validateNo(Long parklotId, String floorNo) {
		int count = count(new QueryWrapper<ParklotFloor>().lambda().eq(ParklotFloor::getParklotId, parklotId)
														  .eq(ParklotFloor::getFloorNo, floorNo));
		return count > 0;
	}

	private void checkFloorNumber(ParklotFloor parklotFloor) {
		if (Func.notNull(parklotFloor.getId())) {
			ParklotFloor floor = getById(parklotFloor.getId());
			LecentAssert.notNull(floor, "不存在此楼层");
			return;
		}
		Integer number = parklotFloor.getNumber();
		int count = this.count(Wrappers.<ParklotFloor>query()
								   .lambda()
								   .eq(ParklotFloor::getNumber, number)
								   .eq(ParklotFloor::getParklotId, parklotFloor.getParklotId())
								   .ne(ParklotFloor::getId, parklotFloor.getId()));
		if (count > 0) {
			throw new ServiceException("该楼层层号已经存在，请重新填写！");
		}
	}

	private void checkFloorName(ParklotFloor parklotFloor) {
		if (Func.notNull(parklotFloor.getId())) {
			ParklotFloor floor = getById(parklotFloor.getId());
			LecentAssert.notNull(floor, "不存在此楼层");
			return;
		}
		int count = this.count(Wrappers.<ParklotFloor>query()
								   .lambda()
								   .eq(ParklotFloor::getParklotId, parklotFloor.getParklotId())
								   .eq(ParklotFloor::getFloorName, parklotFloor.getFloorName())
								   .ne(ParklotFloor::getId, parklotFloor.getId()
									  ));

		if (count > 0) {
			throw new ServiceException("该楼层代号在停车场中已经存在，请重新填写！");
		}
	}

	private void checkAmount(ParklotFloor parklotFloor) {
		Long floorId = parklotFloor.getId();
		Long parklotId = parklotFloor.getParklotId();
		Parklot parklot = ParkLotCaches.getParkLot(parklotId);
		LecentAssert.notNull(parklot, "车场被删除");

		if (Func.isNotEmpty(parklotFloor.getId())) {
			this.validateIndependent(parklotFloor);
			this.validateTemp(parklotFloor);
			this.validateLetter(parklotFloor);
			this.validateVip(parklotFloor);
			this.validateAcc(parklotFloor);
		}

		//获取当前车场其他楼层添加的车位
		int independent = this.getOtherFloorTotalIndependent(parklotId, floorId);
		int letter = this.getOtherFloorTotalLetter(parklotId, floorId);
		int temp = this.getOtherFloorTotalTemp(parklotId, floorId);
		int vip = this.getOtherFloorTotalVip(parklotId, floorId);
		int acc = this.getOtherFloorTotalAcc(parklotId, floorId);
		if (parklotFloor.getAccessibilityLotAmount() + parklotFloor.getVipLotAmount() > parklotFloor.getTempLotAmount()) {
			throw new ServiceException("vip车位和无障碍车位不能大于临停车位");
		}
		if (parklotFloor.getIndependentOwnershipAmount() + independent > parklot.getIndependentOwnershipAmount()) {
			throw new ServiceException("停车场剩余独立产权车位不足！请重新填写");
		}
		if (parklotFloor.getLetterLotAmount() + letter > parklot.getLetterLotAmount()) {
			throw new ServiceException("停车场剩余子母车位不足！请重新填写");
		}
		if (parklotFloor.getTempLotAmount() + temp > parklot.getTempLotAmount()) {
			throw new ServiceException("停车场剩余临停车位不足！请重新填写");
		}
		if (parklotFloor.getVipLotAmount() + vip > parklot.getVipLotAmount()) {
			throw new ServiceException("停车场剩余VIP车位不足！请重新填写");
		}
		if (parklotFloor.getAccessibilityLotAmount() + acc > parklot.getAccessibilityLotAmount()) {
			throw new ServiceException("停车场剩余无障碍车位不足！请重新填写");
		}
	}

	private void validateAcc(ParklotFloor parklotFloor) {
		Integer count = this.baseMapper.getAlreadyAcc(parklotFloor.getId());
		if (parklotFloor.getAccessibilityLotAmount() < count) {
			throw new ServiceException("修改的无障碍车位不能小于区域中已经设置的无障碍车位，请重新填写或者到区域管理中减少无障碍车位数量");
		}
	}

	private void validateVip(ParklotFloor parklotFloor) {
		Integer count = this.baseMapper.getAlreadyVip(parklotFloor.getId());
		if (parklotFloor.getVipLotAmount() < count) {
			throw new ServiceException("修改的VIP车位不能小于区域中已经设置的VIP车位，请重新填写或者到区域管理中减少VIP车位数量");
		}
	}

	private void validateLetter(ParklotFloor parklotFloor) {
		Integer count = this.baseMapper.getAlreadyLetter(parklotFloor.getId());
		if (parklotFloor.getLetterLotAmount() < count) {
			throw new ServiceException("修改的子母车位不能小于区域中已经设置的子母车位，请重新填写或者到区域管理中减少子母车位数量");
		}
	}

	private void validateTemp(ParklotFloor parklotFloor) {
		Integer count = this.baseMapper.getAlreadyTemp(parklotFloor.getId());
		if (parklotFloor.getTempLotAmount() < count) {
			throw new ServiceException("修改的临停车位不能小于区域中已经设置的临停车位，请重新填写或者到区域管理中减少临停车位数量");
		}
	}

	private void validateIndependent(ParklotFloor parklotFloor) {
		Integer count = this.baseMapper.getAlreadyInd(parklotFloor.getId());
		if (parklotFloor.getIndependentOwnershipAmount() < count) {
			throw new ServiceException("修改的独立产权车位不能小于区域中已经设置的独立产权车位，请重新填写或者到区域管理中减少独立产权车位数量");
		}
	}

	private void checkAmount(List<ParklotFloor> parklotFloorList, Parklot parklot) {
		parklotFloorList.forEach(f -> {
			if (f.getVipLotAmount() + f.getAccessibilityLotAmount() > f.getTempLotAmount()) {
				throw new ServiceException("vip车位和无障碍车位不能大于临停车位！");
			}
		});

		int remainIndependent = parklotService.getRemainIndependentOwnershipAmount(parklot.getId());
		int remainLetter = parklotService.getRemainLetterLotAmount(parklot.getId());
		int remainTemp = parklotService.getRemainTotalTempLotAmount(parklot.getId());
		int remainVip = parklotService.getRemainTotalVipLotAmount(parklot.getId());
		int remainAcc = parklotService.getRemainTotalAccLotAmount(parklot.getId());

		int independent = parklotFloorList.stream().mapToInt(ParklotFloor::getIndependentOwnershipAmount).sum();
		int letter = parklotFloorList.stream().mapToInt(ParklotFloor::getLetterLotAmount).sum();
		int temp = parklotFloorList.stream().mapToInt(ParklotFloor::getTempLotAmount).sum();
		int vip = parklotFloorList.stream().mapToInt(ParklotFloor::getVipLotAmount).sum();
		int acc = parklotFloorList.stream().mapToInt(ParklotFloor::getAccessibilityLotAmount).sum();

		if (independent > remainIndependent) {
			throw new ServiceException("独立产权车位数超过车场的独立产权车位数");
		}
		if (letter > remainLetter) {
			throw new ServiceException("子母车位数超过车场的子母车位数");
		}
		if (temp > remainTemp) {
			throw new ServiceException("临停车位数超过车场的临停车位数");
		}
		if (vip > remainVip) {
			throw new ServiceException("楼层vip车位数量不能大于车场vip车位数量");
		}
		if (acc > remainAcc) {
			throw new ServiceException("楼层无障碍车位数量不能大于车场无障碍车位数量");
		}

	}

	private void checkFloorNumber(List<ParklotFloor> parklotFloor) {
		List<Integer> list = parklotFloor.stream().map(ParklotFloor::getNumber).collect(Collectors.toList());
		HashSet<Integer> set = new HashSet<>(list);
		if (!(set.size() == list.size())) {
			throw new ServiceException("楼层层号填写有重复，请检测后重新填写");
		}

		parklotFloor.forEach(f -> {
			int count = getCountByParklotIdFloorNumber(f.getParklotId(), f.getNumber());
			if (count > 0) {
				throw new ServiceException(String.format("该车场的楼层层号:%s层已经存在，请重新填写！,", f.getNumber()));
			}
		});
	}

	private int getCountByParklotIdFloorNumber(Long parklotId, Integer number) {
		return this.count(Wrappers.<ParklotFloor>query()
							  .lambda()
							  .eq(ParklotFloor::getParklotId, parklotId)
							  .eq(ParklotFloor::getNumber, number));
	}

	private void checkFloorName(List<ParklotFloor> parklotFloor) {
		List<String> list = parklotFloor.stream().map(ParklotFloor::getFloorName).collect(Collectors.toList());
		list.forEach(name -> {
			if (Func.isEmpty(name)) {
				throw new ServiceException("填写的楼层中存在楼层代号为空的，请检查后重试！");
			}
		});

		HashSet<String> set = new HashSet<>(list);
		if (!(set.size() == list.size())) {
			throw new ServiceException("楼层代号填写有重复，请检测后重新填写");
		}

		parklotFloor.forEach(f -> {
			LecentAssert.notNull(f.getFloorName(), "楼层代号不能为空，请输入后重试！");

			int count = getCountByParklotIdFloorName(f.getParklotId(), f.getFloorName());
			if (count > 0) {
				throw new ServiceException(String.format("该车场的楼层代号:%s已经存在，请重新填写！,", f.getFloorName()));
			}
		});
	}

	private int getCountByParklotIdFloorName(Long parklotId, String floorName) {
		return this.count(Wrappers.<ParklotFloor>query()
							  .lambda()
							  .eq(ParklotFloor::getParklotId, parklotId)
							  .eq(ParklotFloor::getFloorName, floorName));

	}

}
