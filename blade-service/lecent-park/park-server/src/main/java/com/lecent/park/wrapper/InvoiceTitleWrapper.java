package com.lecent.park.wrapper;

import com.lecent.park.entity.InvoiceTitle;
import com.lecent.park.vo.InvoiceTitleVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 发票抬头表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
public class InvoiceTitleWrapper extends BaseEntityWrapper<InvoiceTitle, InvoiceTitleVO>  {

	public static InvoiceTitleWrapper build() {
		return new InvoiceTitleWrapper();
 	}

	@Override
	public InvoiceTitleVO entityVO(InvoiceTitle invoiceTitle) {
		InvoiceTitleVO invoiceTitleVO = BeanUtil.copy(invoiceTitle, InvoiceTitleVO.class);

		//User createUser = UserCache.getUser(invoiceTitle.getCreateUser());
		//User updateUser = UserCache.getUser(invoiceTitle.getUpdateUser());
		//invoiceTitleVO.setCreateUserName(createUser.getName());
		//invoiceTitleVO.setUpdateUserName(updateUser.getName());

		return invoiceTitleVO;
	}

}
