package com.lecent.park.wrapper;

import com.lecent.park.entity.GuardBtn;
import com.lecent.park.vo.GuardBtnVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

/**
 * 岗亭端按钮表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
public class GuardBtnWrapper extends BaseEntityWrapper<GuardBtn, GuardBtnVO>  {

	public static GuardBtnWrapper build() {
		return new GuardBtnWrapper();
 	}

	@Override
	public GuardBtnVO entityVO(GuardBtn guardBtn) {
		GuardBtnVO guardBtnVO = BeanUtil.copy(guardBtn, GuardBtnVO.class);

		//User createUser = UserCache.getUser(guardBtn.getCreateUser());
		//User updateUser = UserCache.getUser(guardBtn.getUpdateUser());
		//guardBtnVO.setCreateUserName(createUser.getName());
		//guardBtnVO.setUpdateUserName(updateUser.getName());

		return guardBtnVO;
	}

}
