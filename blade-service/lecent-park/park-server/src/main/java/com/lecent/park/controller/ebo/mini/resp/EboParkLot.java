package com.lecent.park.controller.ebo.mini.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class EboParkLot {

	/**
	 * 车场ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 车场名称
	 */
	private String parklotName;

	/**
	 * 车场地址
	 */
	private String address;


	/**
	 * 剩余车位数
	 */
	private Integer remainingParkingNum = 0;

	/**
	 * 总车位数
	 */
	private Integer totalParkingNum = 0;

	/**
	 * 暂停使用车位数
	 */
	private Integer pauseParkingNum = 0;

	/**
	 * 设备异常数
	 */
	private Integer deviceAbnormalNum = 0;

	/**
	 * 剩余状态[0-充足,1-紧张，2-车位已满]
	 */
	private int remainingStatus = 0;

	/**
	 *车场类型[1-非路测2-路测]
	 */
	private Integer parklotType = 1;

	/**
	 * 车场图片
	 */
	private String parklotImgUrl;

	/**
	 * 收费规则
	 */
	private List<String> chargeRule;

	/**
	 * 收费规则（根据不同的车型）
	 */
	private Map<String, List<String>> chargeRuleMap;

	/**
	 * 经度
	 */
	private BigDecimal lat;

	/**
	 * 纬度
	 */
	private BigDecimal lng;

	/**
	 * 车位
	 */
	private List<EboParkLotPlace> parkLotPlaceList;

	/**
	 * 未缴订单数
	 */
	private Integer unpaidOrderNum;

	/**
	 * 有锁
	 */
	private Boolean haveLock;

	/**
	 * 追缴金额
	 */
	private BigDecimal recoveredAmount;

	/**
	 * 收益
	 */
	private BigDecimal earnings;

}
