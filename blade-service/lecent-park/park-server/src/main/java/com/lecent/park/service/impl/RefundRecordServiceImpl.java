package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.RefundRecord;
import com.lecent.park.mapper.RefundRecordMapper;
import com.lecent.park.service.IRefundRecordService;
import com.lecent.park.vo.RefundRecordVO;
import com.lecent.payment.feign.IPaymentClient;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退款记录 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Service
public class RefundRecordServiceImpl extends BaseServiceImpl<RefundRecordMapper, RefundRecord> implements IRefundRecordService {

	@Lazy
	@Resource
	private IPaymentClient paymentClient;

	@Override
	public IPage<RefundRecordVO> selectRefundRecordPage(IPage<RefundRecordVO> page, RefundRecordVO refundRecord) {
		return page.setRecords(baseMapper.selectRefundRecordPage(page, refundRecord));
	}

	@Override
	public RefundRecord getByOutId(String outsideId) {
		List<RefundRecord> refundRecords = list(new QueryWrapper<RefundRecord>().lambda()
			.eq(RefundRecord::getOutsideId, outsideId).orderByDesc(RefundRecord::getUpdateTime));
		if (refundRecords.isEmpty()) {
			return null;
		}
		return refundRecords.get(0);
	}

	@Override
	public RefundRecord getOneByOrderId(String refundOrderId) {
		return getOne(new QueryWrapper<RefundRecord>().lambda().eq(RefundRecord::getRefundOrderId, refundOrderId));
	}

}
