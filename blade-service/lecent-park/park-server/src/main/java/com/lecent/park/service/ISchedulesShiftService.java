package com.lecent.park.service;

import com.lecent.park.entity.SchedulesShift;
import com.lecent.park.vo.SchedulesShiftVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 排班班次表 服务类
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
public interface ISchedulesShiftService extends BaseService<SchedulesShift> {

	/**
	 * 自定义分页
	 *
	 * @param page           页
	 * @param schedulesShift 排班班次
	 * @return {@link IPage}<{@link SchedulesShiftVO}>
	 */
	IPage<SchedulesShiftVO> selectSchedulesShiftPage(IPage<SchedulesShiftVO> page, SchedulesShiftVO schedulesShift);

}
