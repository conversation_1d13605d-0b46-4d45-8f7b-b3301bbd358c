package com.lecent.park.scheduled;

import com.lecent.park.entity.MergeOrder;
import com.lecent.park.service.IMergeOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.payment.PayStatus;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 定时清理失效合并订单
 *
 * <AUTHOR>
 * @date 2023/08/17
 */
@Slf4j
@Component
public class MergeOrderExpireScheduled {

    @Resource
    private IMergeOrderService mergeOrderService;

    @Scheduled(cron = "0 0 4 * * ?")
    @RedisLock(value = "lecent:park::timedTask:lock:MergeOrderExpireScheduled", waitTime = -1)
    public void handler() {
        try {
            log.info("==========定时清理失效合并订单==========");
            List<MergeOrder> list = mergeOrderService.lambdaQuery()
                    .eq(MergeOrder::getPayStatus, PayStatus.UNPAID.getKey())
                    .lt(MergeOrder::getCreateTime, DateUtil.minusMonths(new Date(), 1))
                    .list();
            if (Func.isNotEmpty(list)) {
                for (MergeOrder mergeOrder : list) {
                    mergeOrderService.removeExpireOrder(mergeOrder.getTradeNo());
                }
            }
        } catch (Exception e) {
            log.info("删除过期合并订单失败:", e);
        }
    }
}
