package com.lecent.park.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.entity.UserRelation;
import com.lecent.park.mapper.UserRelationMapper;
import com.lecent.park.service.IUserRelationService;
import com.lecent.park.vo.UserRelationVO;
import com.lecent.park.wrapper.UserRelationWrapper;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.CUser;
import org.springblade.system.user.feign.ICUserClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信用户亲友信息 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@AllArgsConstructor
@Service
public class UserRelationServiceImpl extends BaseServiceImpl<UserRelationMapper, UserRelation> implements IUserRelationService {

	private ICUserClient icUserClient;

	@Override
	public IPage<UserRelationVO> selectUserRelationPage(IPage<UserRelationVO> page, UserRelationVO userRelation) {
		return page.setRecords(baseMapper.selectUserRelationPage(page, userRelation));
	}

	@Override
	public List<UserRelationVO> listMineRelation() {

		List<UserRelationVO> result = new ArrayList<>();

		List<UserRelation> list = this.lambdaQuery()
			.eq(UserRelation::getUserId, SecureUtil.getUserId())
			.list();

		if (Func.isEmpty(list)) {
			return result;
		}

		for (UserRelation userRelation : list) {

			UserRelationVO vo = UserRelationWrapper.build().entityVO(userRelation);
			String phone = userRelation.getPhone();
			R<CUser> cUserR = icUserClient.getByPhone(phone);
			if (cUserR.isSuccess() && Func.isNotEmpty(cUserR.getData().getId())) {
				vo.setFriendUserId(cUserR.getData().getId());
			} else {
				//好友没有注册时，设置-1；
				vo.setFriendUserId(-1L);
			}

			result.add(vo);
		}

		return result;
	}

}
