package com.lecent.park.strategy.cardperiod.impl;

import com.lecent.park.dto.CardDurationDTO;
import com.lecent.park.entity.Card;
import com.lecent.park.entity.CardCategory;
import com.lecent.park.strategy.cardperiod.CardPeriodHelper;
import com.lecent.park.strategy.cardperiod.CardPeriodPeriodStrategy;
import com.lecent.park.strategy.cardperiod.abstracts.AbstractCardPeriodService;
import com.lecent.park.vo.CardDurationVO;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * 月卡天对天续费（提前一天结束）
 */
@Component
@AllArgsConstructor
public class CardPeriodDay2DayEarlyEndStrategy extends AbstractCardPeriodService implements CardPeriodPeriodStrategy {


	@Override
	public CardDurationVO getCardPeriod(CardDurationDTO cardDurationDTO, Card card, CardCategory cardCategory) {

		Date startDate = super.getStartDate(cardDurationDTO.getStartDate(), card, cardCategory);

		Integer monthNum = cardDurationDTO.getMonthNum();
		LecentAssert.notNull(monthNum, "请选择缴费月数！");
		LecentAssert.isTrue(monthNum > 0, "缴费月数必须大于零！");

		Date endDate = CardPeriodHelper.calculateEndDate(startDate, monthNum, true);

		BigDecimal unitPrice = super.getUnitPrice(monthNum, cardCategory);

		return super.buildCardDuration(startDate, endDate, monthNum, unitPrice);

	}





}
