FROM harbor.leuplus.com/library/anapsix/alpine-java:8u201b09_jdk

MAINTAINER <EMAIL>

RUN mkdir -p /blade/device-server

WORKDIR /blade/device-server

EXPOSE 8102

ADD ./target/device-server.jar ./device-server.jar

#ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]

#CMD ["--spring.profiles.active=test"]

CMD java ${JVM_OPTS} -Djava.security.egd=file:/dev/./urandom -jar device-server.jar  ${JAVA_OPTS}
