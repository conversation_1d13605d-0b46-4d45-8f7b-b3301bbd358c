package com.leliven.device.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springblade.core.tool.utils.DateUtil;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Slf4j
class LockCmdEventRetryFilterTest {

    @Test
    void doFilter() {
        Date unlockDate = DateUtil.parse("2024-05-30 12:48:07", DateUtil.PATTERN_DATETIME);
        long lockTimestamp = 1717044477069L;
        log.info("lockDate {}", new Date(lockTimestamp));
        // 1717044487000
        long unlockTimestamp = unlockDate.getTime();
        System.out.println("unlockTimestamp: " + unlockTimestamp);
        assertTrue(lockTimestamp < unlockTimestamp);
    }
}
