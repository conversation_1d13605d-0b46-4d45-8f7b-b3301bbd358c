package com.leliven.device.dto.command;

import com.lecent.device.dto.command.DeviceLockCommand;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springblade.core.tool.utils.Func;

/**
 * <AUTHOR>
 * @since 2024/9/4
 */
@Slf4j
class DeviceLockCommandTest {


    @Test
    void testSerializable_whenFieldIsNull_returnJsonExcludeField() {
        DeviceLockCommand command = new DeviceLockCommand("up", 0L);
        String json = Func.toJson(command);
        debugLog(json);
        Assertions.assertFalse(json.contains("turnback"));
    }

    @Test
    void testSerializable_whenFieldNonNull_returnIncludeField() {
        DeviceLockCommand command = new DeviceLockCommand("up", 0L, 30);
        String json = Func.toJson(command);
        debugLog(json);
        Assertions.assertTrue(json.contains("turnback"));
    }

    private static void debugLog(String json) {
        if (log.isDebugEnabled()) {
            log.debug("json: {}", json);
        }
    }
}
