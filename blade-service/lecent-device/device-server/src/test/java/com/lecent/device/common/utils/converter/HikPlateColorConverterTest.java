package com.lecent.device.common.utils.converter;

import com.lecent.device.enums.HikPlateColor;
import com.leliven.vehicle.enums.PlateColor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 海康车牌颜色转换器测试
 *
 * <AUTHOR>
 */
class HikPlateColorConverterTest {

    private final HikPlateColorConverter converter = HikPlateColorConverter.INSTANCE;

    @Test
    @DisplayName("测试海康车牌颜色代码转换为系统车牌颜色")
    void testToInternalPlateColorByCode() {
        // 测试基本颜色代码转换
        assertEquals(PlateColor.BLUE, converter.toInternalPlateColor("blue"));
        assertEquals(PlateColor.YELLOW, converter.toInternalPlateColor("yellow"));
        assertEquals(PlateColor.BLACK, converter.toInternalPlateColor("black"));
        assertEquals(PlateColor.WHITE, converter.toInternalPlateColor("white"));
        assertEquals(PlateColor.RED, converter.toInternalPlateColor("red"));
        assertEquals(PlateColor.GOLDEN, converter.toInternalPlateColor("golden"));
        assertEquals(PlateColor.ORANGE, converter.toInternalPlateColor("orange"));
        
        // 测试绿色相关代码转换
        assertEquals(PlateColor.GREEN, converter.toInternalPlateColor("green"));
        assertEquals(PlateColor.GREEN, converter.toInternalPlateColor("newEnergyGreen"));
        assertEquals(PlateColor.GREEN, converter.toInternalPlateColor("newEnergyYellowGreen"));
        
        // 测试特殊情况
        assertEquals(PlateColor.UNKNOWN, converter.toInternalPlateColor("unknown"));
        assertEquals(PlateColor.UNKNOWN, converter.toInternalPlateColor(null));
        assertEquals(PlateColor.UNKNOWN, converter.toInternalPlateColor("invalidCode"));
    }

    @Test
    @DisplayName("测试海康普通颜色转换为系统颜色")
    void testConvertNormalColorsToInternalPlateColor() {
        // 测试基本颜色转换
        assertEquals(PlateColor.BLUE, converter.convertToInternalPlateColor(HikPlateColor.BLUE));
        assertEquals(PlateColor.YELLOW, converter.convertToInternalPlateColor(HikPlateColor.YELLOW));
        assertEquals(PlateColor.BLACK, converter.convertToInternalPlateColor(HikPlateColor.BLACK));
        assertEquals(PlateColor.WHITE, converter.convertToInternalPlateColor(HikPlateColor.WHITE));
        assertEquals(PlateColor.RED, converter.convertToInternalPlateColor(HikPlateColor.RED));
        assertEquals(PlateColor.GOLDEN, converter.convertToInternalPlateColor(HikPlateColor.GOLDEN));
        assertEquals(PlateColor.ORANGE, converter.convertToInternalPlateColor(HikPlateColor.ORANGE));
    }

    @Test
    @DisplayName("测试海康绿色相关颜色都转换为系统GREEN")
    void testConvertGreenColorsToInternalPlateColor() {
        // 测试所有绿色类型都转为系统GREEN
        assertEquals(PlateColor.GREEN, converter.convertToInternalPlateColor(HikPlateColor.GREEN));
        assertEquals(PlateColor.GREEN, converter.convertToInternalPlateColor(HikPlateColor.NEW_ENERGY_GREEN));
        assertEquals(PlateColor.GREEN, converter.convertToInternalPlateColor(HikPlateColor.NEW_ENERGY_YELLOW_GREEN));
    }

    @Test
    @DisplayName("测试海康特殊类型转换为系统颜色")
    void testConvertSpecialColorsToInternalPlateColor() {
        // 测试特殊类型
        assertEquals(PlateColor.UNKNOWN, converter.convertToInternalPlateColor(HikPlateColor.UNKNOWN));
        assertEquals(PlateColor.UNKNOWN, converter.convertToInternalPlateColor(null));
    }

    @Test
    @DisplayName("测试系统普通颜色转换为海康颜色")
    void testConvertNormalColorsFromInternalPlateColor() {
        // 测试基本颜色转换
        assertEquals(HikPlateColor.BLUE, converter.convertFromInternalPlateColor(PlateColor.BLUE));
        assertEquals(HikPlateColor.YELLOW, converter.convertFromInternalPlateColor(PlateColor.YELLOW));
        assertEquals(HikPlateColor.BLACK, converter.convertFromInternalPlateColor(PlateColor.BLACK));
        assertEquals(HikPlateColor.WHITE, converter.convertFromInternalPlateColor(PlateColor.WHITE));
        assertEquals(HikPlateColor.RED, converter.convertFromInternalPlateColor(PlateColor.RED));
        assertEquals(HikPlateColor.GOLDEN, converter.convertFromInternalPlateColor(PlateColor.GOLDEN));
        assertEquals(HikPlateColor.ORANGE, converter.convertFromInternalPlateColor(PlateColor.ORANGE));
    }

    @Test
    @DisplayName("测试系统绿色相关颜色都转换为海康GREEN")
    void testConvertGreenColorsFromInternalPlateColor() {
        // 测试所有绿色类型都转为海康GREEN
        assertEquals(HikPlateColor.GREEN, converter.convertFromInternalPlateColor(PlateColor.GREEN));
        assertEquals(HikPlateColor.GREEN, converter.convertFromInternalPlateColor(PlateColor.NEW_ENERGY_GREEN));
        assertEquals(HikPlateColor.GREEN, converter.convertFromInternalPlateColor(PlateColor.NEW_ENERGY_YELLOW_GREEN));
    }

    @Test
    @DisplayName("测试系统特殊类型转换为海康颜色")
    void testConvertSpecialColorsFromInternalPlateColor() {
        // 测试特殊类型
        assertEquals(HikPlateColor.UNKNOWN, converter.convertFromInternalPlateColor(PlateColor.UNKNOWN));
        assertEquals(HikPlateColor.UNKNOWN, converter.convertFromInternalPlateColor(null));
    }
}
