package com.lecent.device.domain.device.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 遮挡告警实体类
 * <AUTHOR>
 * @since 2024/7/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShelterAlarm {

    @DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
    @JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
    private Date dateTime;

    private String imageUrls;
}
