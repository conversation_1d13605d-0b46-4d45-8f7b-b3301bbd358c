package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.DownLogDTO;
import com.lecent.device.entity.DownLog;
import com.lecent.device.enums.DownCmd;
import com.lecent.device.vo.DownLogVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 设备下发日志 服务类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface IDownLogService extends BaseService<DownLog> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页
	 * @param downLog 参数条件
	 * @return IPage<DownLogVO>
	 */
	IPage<DownLogVO> selectDownLogPage(IPage<DownLogVO> page, DownLogDTO downLog);

	/**
	 * 根据请求id更新重试次数
	 *
	 * @param requestId 请求id
	 * @param retryNum 重试次数
	 */
	void updateRetryNumByRequestId(String requestId, Integer retryNum);

	/**
	 * 根据设备id和命令查询最新的日志
	 *
	 * @param deviceId 设备id
	 * @param cmd 命令
	 * @param limit 限制条数
	 * @return List<DownLog>
	 */
	List<DownLog> listLatestLogsByDeviceIdAndCmd(Long deviceId, DownCmd cmd, Long limit);

	/**
	 * 查询需要删除的日志id
	 *
	 * @return List<Long>
	 */
	List<Long> findRemoveDownLogId();

}
