package com.lecent.device.domain.vz.event;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lecent.device.domain.common.service.AlarmEventType;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 臻识巡检车事件类型
 */
@Getter
@AllArgsConstructor
public enum VZEventType implements AlarmEventType {

    /**
     * 未知
     */
    UNKNOWN(-1, "未知", 0),
    /**
     * 设备状态
     */
    DEVICE_STATUS(0, "设备状态", 0),
    /**
     * 停车事件-入场
     */
    PARKING_EVENT_ENTRY(1, "停车事件-入场", ParkingStatusEnum.PARK_IN.getValue()),
    /**
     * 停车事件-在停
     */
    PARKING_EVENT_STOP(3, "停车事件-在停", ParkingStatusEnum.PARK_IN.getValue()),
    /**
     * 停车事件-出场
     */
    PARKING_EVENT_EXIT(4, "停车事件-出场", ParkingStatusEnum.PARK_OUT.getValue()),
    /**
     * 停车事件-空闲
     */
    PARKING_EVENT_IDLE(8, "停车事件-空闲", 0),
    /**
     * 停车事件-未知泊位
     */
    PARKING_EVENT_UNKNOWN_SPACE(10, "停车事件-未知泊位", 0),
    /**
     * 巡检车告警
     */
    PATROL_ALARM(20, "巡检车告警", 0),
    /**
     * 设备心跳
     */
    DEVICE_HEARTBEAT(13, "设备心跳", 0);

    @JsonValue
    @EnumValue
    private final int value;
    private final String desc;
    private final int parkingStatus;

    public static VZEventType resolve(Integer type) {
        for (VZEventType eventType : VZEventType.values()) {
            if (eventType.value == type) {
                return eventType;
            }
        }

        return VZEventType.UNKNOWN;
    }

    @Override
    public VZEventType getValue() {
        return this;
    }

}
