<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.DownLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="downLogResultMap" type="com.lecent.device.entity.DownLog">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="request_id" property="requestId"/>
        <result column="device_id" property="deviceId"/>
        <result column="name" property="name"/>
        <result column="cmd" property="cmd"/>
        <result column="content" property="content"/>
        <result column="success" property="success"/>
        <result column="ack_time" property="ackTime"/>
        <result column="retry_time" property="retryTime"/>
        <result column="retry_num" property="retryNum"/>
        <result column="retry_next_time" property="retryNextTime"/>
        <result column="group_id" property="groupId"/>
        <result column="exception" property="exception"/>
    </resultMap>


    <select id="selectDownLogPage" resultType="com.lecent.device.vo.DownLogVO">
        SELECT *
        FROM DEV_DOWN_LOG
        WHERE IS_DELETED = 0
        <if test="downLog.deviceId != null">
            AND DEVICE_ID = #{downLog.deviceId}
        </if>
        <if test="downLog.startTime != null">
            AND CREATE_TIME &gt;= #{downLog.startTime}
        </if>
        <if test="downLog.endTime != null">
            AND CREATE_TIME &lt;= #{downLog.endTime}
        </if>
        <if test="downLog.cmd != null and downLog.cmd != '' ">
            AND CMD = #{downLog.cmd}
        </if>
        <if test="downLog.requestId != null and downLog.requestId != '' ">
            AND REQUEST_ID LIKE CONCAT('%', #{downLog.requestId}, '%')
        </if>
        <if test="downLog.success != null ">
            AND SUCCESS = #{downLog.success}
        </if>
        ORDER BY CREATE_TIME desc
    </select>

    <select id="selectRemoveDownLogId" resultType="java.lang.Long">
        SELECT id
        FROM dev_down_log
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL 3 MONTH)
        LIMIT 5000
    </select>

</mapper>
