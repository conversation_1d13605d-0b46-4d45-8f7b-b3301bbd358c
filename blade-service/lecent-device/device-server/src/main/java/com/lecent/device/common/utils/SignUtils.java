package com.lecent.device.common.utils;

import cn.hutool.crypto.SecureUtil;
import com.lecent.device.dto.MsgBody;
import com.lecent.device.dto.ReqDownMsg;

/**
 * 设备交互签名
 * MD5转hex(倒序排序(设备序列号+请求ID+命令+内容+车场ID))
 *
 * <AUTHOR> zxr
 * @date : 2022/9/19
 */
public class SignUtils {

	/**
	 * 签名key
	 */
	public static String DOWN_MSG_SALT = "ebo20221109";

	/**
	 * 下发消息获取签名串
	 *
	 * @param req  下发消息
	 * @param salt 盐值
	 * @return 签名串
	 */
	public static String getSign(ReqDownMsg req, String salt) {
		return getSign(req.getSn(), req.getRequestId(), req.getCmd(), req.getContent(), salt);
	}

	/**
	 * 接收消息签名
	 *
	 * @param req  接收消息
	 * @param salt 盐
	 * @return true验证通过
	 */
	public static boolean verifySign(MsgBody req, String salt) {
		return getSign(req.getSn(), req.getRequestId(), req.getCmd(), req.getContent(), salt).equals(req.getSign());
	}

	/**
	 * 获取签名串
	 *
	 * @param sn        设备序列号
	 * @param requestId 请求ID
	 * @param cmd       命令
	 * @param content   内容
	 * @param salt      盐值
	 * @return 签名串
	 */
	public static String getSign(String sn, String requestId, String cmd, String content, String salt) {
		return SecureUtil.md5((sn + requestId + cmd + content + SecureUtil.md5(sn + DOWN_MSG_SALT)));
	}
}
