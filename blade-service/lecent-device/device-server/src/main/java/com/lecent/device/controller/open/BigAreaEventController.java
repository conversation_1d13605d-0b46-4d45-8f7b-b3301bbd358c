package com.lecent.device.controller.open;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.chinatelecom.iot.api.IChinaTelecomIotAPI;
import com.lecent.chinatelecom.iot.model.DeviceDataInfoNotify;
import com.lecent.chinatelecom.iot.model.DeviceDataChangedNotify;
import com.lecent.chinatelecom.iot.model.NotifyType;
import com.lecent.device.service.IBigAreaDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

/**
 * 大域地磁事件回调 Controller
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = {"/big-area","da-yu"})
@Api(value = "大域地磁事件回调", tags = "大域地磁事件回调")
public class BigAreaEventController {

	private IBigAreaDeviceService bigAreaDeviceService;
	private IChinaTelecomIotAPI chinaTelecomIotAPI;

    @PostMapping(value = "/event/data-changed/callback")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "数据变化回调接口（地磁）")
    public R<Boolean> dataChangedCallback(@RequestBody DeviceDataChangedNotify deviceDataNotify) {
		try {
			this.bigAreaDeviceService.deviceDataChangedCallback(deviceDataNotify);
		} catch (Exception e) {
			log.warn("callback processing failure. ", e);
			return R.fail("callback processing failure.");
		}
		return R.status(true);
    }

	@PostMapping(value = "/event/info-changed/callback")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "信息变化回调接口")
	public R<Boolean> infoChangedCallback(@RequestBody DeviceDataInfoNotify deviceDataInfoNotify) {
		try {
			this.bigAreaDeviceService.deviceInfoChangedCallback(deviceDataInfoNotify);
		} catch (Exception e) {
			log.warn("callback processing failure. ", e);
			return R.fail("callback processing failure.");
		}
		return R.status(true);
	}

	@GetMapping(value = "/event/info-changed/subscribe")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "信息变化订阅接口")
	public R<Boolean> infoChangedSubscribe() {
		try {
			return R.data(this.bigAreaDeviceService.subscribe(NotifyType.DEVICE_INFO_CHANGED));
		} catch (Exception e) {
			log.warn("subscribe processing failure. ", e);
			return R.fail("subscribe failure");
		}
	}

	@GetMapping(value = "/event/data-changed/subscribe")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "数据变化订阅")
	public R<Boolean> dataChangedSubscribe() {
		try {
			return R.data(this.bigAreaDeviceService.subscribe(NotifyType.DEVICE_DATA_CHANGED));
		} catch (Exception e) {
			log.warn("subscribe processing failure. ", e);
			return R.fail("subscribe failure");
		}
	}

	@GetMapping(value = "/event/device-data-history")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询设备历史数据")
	public R<String> deviceDataHistory(String deviceId, String gatewayId) {
		try {
			return R.data(this.chinaTelecomIotAPI.deviceDataHistory(deviceId, gatewayId));
		} catch (Exception e) {
			log.warn("query deviceDataHistory failure. ", e);
			return R.fail("query deviceDataHistory failure.");
		}
	}








}

