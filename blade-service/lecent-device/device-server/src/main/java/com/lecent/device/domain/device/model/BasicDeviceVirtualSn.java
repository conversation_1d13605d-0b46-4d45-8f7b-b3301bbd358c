package com.lecent.device.domain.device.model;

import org.springblade.common.utils.ObjectValidator;

/**
 * 基础设备虚拟序列号实现类
 *
 * <AUTHOR>
 * @since 2024/6/12
 */
public class BasicDeviceVirtualSn implements DeviceVirtualSn {

    private final String virtualSn;

    public BasicDeviceVirtualSn(String originalSn, String channelNo) {
        ObjectValidator.requireNotBlank(originalSn, "Original sn must not be blank");
        ObjectValidator.requireNotBlank(channelNo, "Channel no must not be blank");
        this.virtualSn = generateVirtualSn(originalSn, channelNo);
    }

    @Override
    public String getCode() {
        return this.virtualSn;
    }

    /**
     * 生成虚拟序列号
     *
     * @param originalSn 原始序列号
     * @param channelNo  通道号/车位号
     * @return 虚拟序列号
     */
    private String generateVirtualSn(String originalSn, String channelNo) {
        return String.format("%s@%s", originalSn, channelNo);
    }
}
