<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.UpdatePackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="updatePackageResultMap" type="com.lecent.device.entity.UpdatePackage">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="product_code" property="productCode"/>
        <result column="start_hw_rev" property="startHwRev"/>
        <result column="end_hw_rev" property="endHwRev"/>
        <result column="revision" property="revision"/>
        <result column="sign_algorithm" property="signAlgorithm"/>
        <result column="package_url" property="packageUrl"/>
        <result column="platform_verify" property="platformVerify"/>
        <result column="description" property="description"/>
        <result column="push_message" property="pushMessage"/>
    </resultMap>


    <select id="selectUpdatePackagePage" resultType="com.lecent.device.vo.UpdatePackageVO">
        SELECT
            *
        FROM
            dev_update_package
        WHERE
            is_deleted = 0
        <if test="package.name != null and package.name != ''">
            AND name LIKE CONCAT('%', #{package.name}, '%')
        </if>
        <if test="package.productCode != null and package.productCode != ''">
            AND product_code = #{package.productCode}
        </if>
    </select>

</mapper>
