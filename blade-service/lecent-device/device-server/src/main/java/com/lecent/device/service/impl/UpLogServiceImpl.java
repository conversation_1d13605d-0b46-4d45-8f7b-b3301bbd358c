package com.lecent.device.service.impl;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.UpLogDTO;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.UpLog;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.DeviceEvent;
import com.lecent.device.mapper.UpLogMapper;
import com.lecent.device.service.IDeviceService;
import com.lecent.device.service.IUpLogService;
import com.lecent.device.vo.DeviceLogVO;
import com.lecent.device.vo.UpLogVO;
import com.lecent.park.entity.ParklotDeviceRet;
import com.lecent.park.feign.IParklotDeviceRetClient;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.dto.UserDTO;
import org.springblade.system.user.entity.CUser;
import org.springblade.system.user.feign.ICUserClient;
import org.springblade.system.user.feign.IUserClient;
import org.springblade.system.user.vo.UserVO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备下发日志 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Slf4j
@Service
public class UpLogServiceImpl extends BaseServiceImpl<UpLogMapper, UpLog> implements IUpLogService {

    @Resource
    private IParklotDeviceRetClient parklotDeviceRetClient;

    @Resource
    private IDeviceService deviceService;

    @Resource
    private IUserClient userClient;
	
    @Resource
    private ICUserClient cUserClient;

    @Slave
    @Override
    public IPage<UpLogVO> selectUpLogPage(IPage<UpLogVO> page, UpLogDTO upLogDTO) {
        return page.setRecords(baseMapper.selectUpLogPage(page, upLogDTO));
    }

    @Slave
    @Override
    public IPage<DeviceLogVO> selectDeviceLogPage(IPage<DeviceLogVO> page, UpLogDTO upLogDTO) {
        Long deviceId = upLogDTO.getDeviceId();
        if (upLogDTO.getParkingplaceId() != null) {
            R<List<ParklotDeviceRet>> listR = parklotDeviceRetClient.listByParkPlaceId(upLogDTO.getParkingplaceId());
            if (listR != null && Func.isNotEmpty(listR.getData())) {
                deviceId = listR.getData().stream()
                    .filter(ret -> DeviceType.PARKING_LOCK.getValue().equals(ret.getDeviceType()))
                    .findFirst()
                    .map(ParklotDeviceRet::getDeviceId)
                    .orElse(null);
            }
        } else if (Func.isNotBlank(upLogDTO.getSn())) {
            Device device = deviceService.getBySn(upLogDTO.getSn());
            if (device != null) {
                deviceId = device.getId();
            }
        }
        if (deviceId == null) {
            deviceId = -1L;
        }
        upLogDTO.setDeviceId(deviceId);
        List<DeviceLogVO> datas = baseMapper.selectDeviceLogPage(page, upLogDTO);
        // 查询用户信息
        if (Func.isNotEmpty(datas)) {
            List<Long> userIds = datas.stream().map(DeviceLogVO::getCreateUser).collect(Collectors.toList());
            if (Func.isNotEmpty(userIds)) {
                // 系统用户
                UserDTO userDTO = new UserDTO();
                String result = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                userDTO.setIdStr(result);
                R<List<UserVO>> listR = userClient.findUserList(userDTO);
                if (listR != null && Func.isNotEmpty(listR.getData())) {
                    for (UserVO user : listR.getData()) {
                        for (DeviceLogVO logVO : datas) {
                            if (user.getId().equals(logVO.getCreateUser())) {
                                logVO.setCreateUserName(user.getRealName());
                                userIds.remove(user.getId());
                                break;
                            }
                        }
                    }
                }

            }
            // c端用户
            if (Func.isNotEmpty(userIds)) {
                UserDTO userDTO = new UserDTO();
                String result = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                userDTO.setIdStr(result);
                R<List<CUser>> listR = cUserClient.ownerInfoByIds(result);
                if (listR != null && Func.isNotEmpty(listR.getData())) {
                    for (CUser user : listR.getData()) {
                        for (DeviceLogVO logVO : datas) {
                            if (user.getId().equals(logVO.getCreateUser())) {
                                logVO.setCreateUserName(user.getPhone());
                                userIds.remove(user.getId());
                                break;
                            }
                        }
                    }
                }
            }
        }
        return page.setRecords(datas);
    }

    @Async
    @Override
    public void asyncSave(UpLog upLog) {
        this.save(upLog);
    }

    @Async
    @Override
    public void asyncSave(DeviceEvent<?> event, UpCmd upCmd) {
        this.save(event.getSource(), upCmd, Func.toJson(event.getPayload()));
    }

    @Async
    @Override
    public void asyncSave(Device device, String content) {
        this.save(device, UpCmd.event, content);
    }

    public void save(Device device, UpCmd upCmd, String content) {
        UpLog upLog = new UpLog();
        upLog.setDeviceId(device.getId());
        upLog.setSn(device.getSn());
        upLog.setCmd(upCmd.name());
        upLog.setContent(content);
        upLog.setTimestamp(new Date().getTime());
        upLog.setSuccess(UpLog.STATUS_SUCCESS);
        this.save(upLog);
    }

    @Override
    public UpLogVO selectRecentlyUpLog(UpLogDTO upLogDTO, Integer orderBy) {
        return baseMapper.selectRecentlyUpLog(upLogDTO, orderBy);
    }

    @Override
    public List<Long> findRemoveUpLogId() {
        return baseMapper.findRemoveUpLogId();

    }
}
