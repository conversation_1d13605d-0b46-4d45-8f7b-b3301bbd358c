package com.lecent.device.controller.scheduled;

import com.lecent.device.dto.DeviceDTO;
import com.lecent.device.scheduled.DeviceScheduled;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

/**
 * 设备定时器Web接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/scheduled/device")
public class DeviceScheduledController {

	private final DeviceScheduled deviceScheduled;

	@GetMapping(value = "/info/sync")
	public R<Boolean> syncDeviceInfo() {
		try {
			this.deviceScheduled.syncDeviceInfo();
		} catch (Exception e) {
			log.error("同步设备信息失败", e);
			return R.fail("同步设备信息失败");
		}
		return R.success("同步设备信息成功");
	}
}
