package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.DeviceParam;
import com.lecent.device.vo.DeviceParamVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 设备管理参数 服务类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface IDeviceParamService extends BaseService<DeviceParam> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceParam
	 * @return
	 */
	IPage<DeviceParamVO> selectDeviceParamPage(IPage<DeviceParamVO> page, DeviceParamVO deviceParam);

	/**
	 * 根据设备ID查询设备扩展字段参数
	 *
	 * @param deviceId
	 * @return
	 */
	List<DeviceParam> listByDeviceId(Long deviceId);

	/**
	 * 保存或更新param
	 *
	 * @param params 设备params
	 * @return boolean
	 */
	boolean saveOrUpdateParams(List<DeviceParam> params);
}
