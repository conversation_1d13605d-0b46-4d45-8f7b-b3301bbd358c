package com.lecent.device.listener.spring.hik;

import com.lecent.device.cache.DeviceCache;
import com.lecent.device.common.utils.HikCheckUtils;
import com.lecent.device.common.utils.converter.HikPlateColorConverter;
import com.lecent.device.domain.hik.HikEventXmlFileResolver;
import com.lecent.device.domain.hik.HikMultipartFileWrapper;
import com.lecent.device.domain.hik.HikXStream;
import com.lecent.device.domain.hik.event.HikEvent;
import com.lecent.device.domain.hik.event.HikEventType;
import com.lecent.device.domain.image.MultiTypeImageUrlsWrapper;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.device.dto.DeviceSnapEventDTO;
import com.lecent.device.dto.TCLRcvDTO;
import com.lecent.device.entity.Device;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.enums.EnterExitType;
import com.lecent.device.publisher.ParkingPublisher;
import com.lecent.device.service.IUpLogService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 海康摄像头停车事件监听器
 * TFSEventListener
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TFSEventListener {

	@Resource
	private IUpLogService upLogService;
	@Resource
	private ParkingPublisher enterOrExitPublisher;

	/**
	 * 海康摄像头进出场事件
	 */
	@EventListener(
		classes = HikEvent.class,
		condition = "#event.eventType == T(com.lecent.device.domain.hik.event.HikEventType).TFS"
	)
	public void onEvent(HikEvent<HikMultipartFileWrapper> event) {
		try {
			handleEvent(event);
		} catch (Exception e) {
			log.error("TFSEventListener onEvent:", e);
		}
	}

	/**
	 * 处理事件
	 *
	 * @param event HikEvent
	 */
	private void handleEvent(HikEvent<HikMultipartFileWrapper> event) {
		log.info("HikEvent: {}", event.getEventType());
        // 解析TCLRcvDTO
		TCLRcvDTO tclRcvDTO = parseTCLRcvDTO(event);
		log.info("TFSEventHandler tclRcvDTO: {}", Func.toJson(tclRcvDTO));
		// 校验TCLRcvDTO
		HikCheckUtils.checkTclRcvDTO(tclRcvDTO);
		// 校验TriggerTime是否有效，无效则订正为当前时间
		HikCheckUtils.correctTriggerTimeIfInvalid(tclRcvDTO);
		// 获取设备
		Device device = getDevice(tclRcvDTO);
		// 上传图片
		MultiTypeImageUrlsWrapper multiTypeImageUrlsWrapper = HikCheckUtils.uploadImages(event.getPayload());
		// 构建事件
		DeviceParkingEventDTO deviceTriggerEvent = buildDeviceParkingEventDTO(tclRcvDTO, device, multiTypeImageUrlsWrapper);
		// 发布事件
		enterOrExitPublisher.publish(deviceTriggerEvent);
		// 保存上发日志
		saveUpLog(device, deviceTriggerEvent);
	}

	/**
	 * 解析TCLRcvDTO
	 *
	 * @param event HikEvent
	 * @return TCLRcvDTO
	 */
	private TCLRcvDTO parseTCLRcvDTO(HikEvent<HikMultipartFileWrapper> event) {
		HikMultipartFileWrapper fileWrapper = event.getPayload();
		return HikEventXmlFileResolver.build(HikXStream.of(event.getEventType())).resolve(fileWrapper.getXmlFile());
	}

	/**
	 * 获取设备
	 *
	 * @param tclRcvDTO TCLRcvDTO
	 * @return Device
	 */
	private Device getDevice(TCLRcvDTO tclRcvDTO) {
		String deviceSn = HikCheckUtils.deviceSn(tclRcvDTO.getChannelName(), tclRcvDTO.getTfs().getParkingSerialNo());
		Device device = DeviceCache.getBySn(deviceSn);

		if (device == null) {
			device = DeviceCache.existBySn(HikCheckUtils.deviceSn(tclRcvDTO.getChannelName(), tclRcvDTO.getChannelID()));
		}

		return device;
	}

	/**
	 * 构建事件
	 *
	 * @param tclRcvDTO                  TCLRcvDTO
	 * @param device                     设备
	 * @param multiTypeImageUrlsWrapper 图片地址
	 * @return DeviceParkingEventDTO
	 */
	private DeviceParkingEventDTO buildDeviceParkingEventDTO(TCLRcvDTO tclRcvDTO,
															 Device device,
															 MultiTypeImageUrlsWrapper multiTypeImageUrlsWrapper) {
		TCLRcvDTO.TFS tfs = tclRcvDTO.getTfs();
		TCLRcvDTO.PlateInfo plateInfo = tfs.getPlateInfo();
		TCLRcvDTO.VehicleInfo vehicleInfo = tfs.getVehicleInfo();

		return DeviceParkingEventDTO.builder()
			.deviceSn(device.getSn())
			.deviceType(DeviceType.VIDEO_PILE)
			.channelId(HikCheckUtils.channelId(tclRcvDTO.getChannelID()))
			.triggerTime(tclRcvDTO.getDateTime())
			.type(HikCheckUtils.typeOf(vehicleInfo.getVehicleEnterState()))
			.plate(HikCheckUtils.transformPlate(plateInfo.getPlate()))
			.plateType(plateInfo.getPlateType())
			.plateColor(HikPlateColorConverter.INSTANCE.toInternalPlateColor(plateInfo.getPlateColor()).code())
			.detectionPicUrl(multiTypeImageUrlsWrapper.getLastSceneImageUrl())
			.imageUrlMap(multiTypeImageUrlsWrapper.getMultiImageUrls())
			.build();
	}

	/**
	 * 保存上发日志
	 *
	 * @param device          设备
	 * @param parkingEventDTO 停车事件
	 */
	private void saveUpLog(Device device, DeviceParkingEventDTO parkingEventDTO) {
		try {
			List<String> imagesUrls = parkingEventDTO.getImageUrlMap().getOrDefault(2, Collections.singletonList(null));

			imagesUrls.stream()
				.map(imagesUrl -> createDeviceSnapEventDTO(device, parkingEventDTO, imagesUrl))
				.forEach(eventDTO -> upLogService.asyncSave(device, Func.toJson(eventDTO)));

		} catch (Exception e) {
			log.error("TFSEventListener saveUpLog", e);
		}
	}

	/**
	 * 构建上发日志
	 *
	 * @param device          设备
	 * @param parkingEventDTO 停车事件
	 * @param imagesUrl       图片地址
	 * @return DeviceSnapEventDTO
	 */
	private DeviceSnapEventDTO createDeviceSnapEventDTO(Device device, DeviceParkingEventDTO parkingEventDTO, String imagesUrl) {
		return DeviceSnapEventDTO.builder()
			.deviceSn(device.getSn())
			.deviceType(DeviceType.VIDEO_PILE)
			.channelId(parkingEventDTO.getChannelId())
			.triggerTime(parkingEventDTO.getTriggerTime())
			.type(EnterExitType.ENTER.getValue())
			.plate(parkingEventDTO.getPlate())
			.plateType(parkingEventDTO.getPlateType())
			.plateColor(HikPlateColorConverter.INSTANCE.toInternalPlateColor(parkingEventDTO.getPlateColor()))
			.imagesUrl(imagesUrl)
			.eventType(HikEventType.TFS.getType())
			.build();
	}

}
