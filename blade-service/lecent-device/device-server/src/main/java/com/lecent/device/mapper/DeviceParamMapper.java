package com.lecent.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.DeviceParam;
import com.lecent.device.vo.DeviceParamVO;

import java.util.List;

/**
 * 设备管理参数 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface DeviceParamMapper extends BaseMapper<DeviceParam> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceParam
	 * @return
	 */
	List<DeviceParamVO> selectDeviceParamPage(IPage page, DeviceParamVO deviceParam);

}
