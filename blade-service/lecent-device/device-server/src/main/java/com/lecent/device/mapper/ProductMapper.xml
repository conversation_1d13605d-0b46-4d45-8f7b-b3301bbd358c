<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.ProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="productResultMap" type="com.lecent.device.entity.Product">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="pic_url" property="picUrl"/>
        <result column="node_type" property="nodeType"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectProductPage" resultMap="productResultMap">
        select *
        from dev_product
        where is_deleted = 0
        <if test="product.name != null and product.name != ''">
            and name like CONCAT('%',#{product.name},'%')
        </if>
    </select>

</mapper>
