package com.lecent.device.config;

import com.lecent.device.constant.DeviceMQConstant;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMq配置
 *
 * <AUTHOR>
 */
@Configuration
public class DeviceRabbitMqConfig {

	/**
	 * 定义交换机
	 */
	@Bean
	public TopicExchange deviceTopicExchange() {
		return new TopicExchange(DeviceMQConstant.EXCHANGE);
	}
}
