package com.lecent.device.service.impl;

import com.lecent.device.cache.DeviceCache;
import com.lecent.device.dto.MsgBody;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.DownLog;
import com.lecent.device.enums.DownCmd;
import com.lecent.device.service.EventDownRetryFilter;
import com.lecent.device.service.IDownLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 锁命令事件重试过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LockCmdEventRetryFilter implements EventDownRetryFilter {

	private final IDownLogService downLogService;

	@Override
	public boolean doFilter(MsgBody msgBody) {
		return Optional.ofNullable(msgBody)
			.filter(this::support)
			.map(this::resolveRetryRule)
			.orElse(true);
	}

	/**
	 * 根据 msgBody 判断是否支持当前过滤器
	 *
	 * @param msgBody 消息体
	 * @return true-支持 false-不支持
	 */
	private boolean support(MsgBody msgBody) {
		return DownCmd.lock.name().equals(msgBody.getCmd()) && isContainsLockWordFrom(msgBody.getContent());
	}

	/**
	 * 解析重试规则
	 *
	 * @param msgBody 消息体
	 * @return true-重试 false-放弃重试
	 */
	private boolean resolveRetryRule(MsgBody msgBody) {
		Long retryTimestamp = msgBody.getTimestamp();
		DownLog latestLockData = getLatestLockLog(msgBody.getSn());
		// 如果获取到最新数据，且非同请求，且最新数据的时间戳在重试时间戳之后，说明有新的锁命令，则放弃重试
		return latestLockData == null || latestLockData.getRequestId().equals(msgBody.getRequestId())
			|| latestLockData.getCreateTime().getTime() <= retryTimestamp;
	}

	/**
	 * 根据sn获取设备最近一次 cmd 为 "lock" 的数据
	 *
	 * @param sn 设备 sn
	 * @return 设备最近一次 cmd 为 "lock" 的数据
	 */
	private DownLog getLatestLockLog(String sn) {
		Device device = DeviceCache.existBySn(sn);

		List<DownLog> downLogs = this.downLogService.listLatestLogsByDeviceIdAndCmd(device.getId(), DownCmd.lock, 50L);
		return downLogs.stream()
			.filter(log -> isContainsLockWordFrom(log.getContent()))
			.max(Comparator.comparing(BaseEntity::getCreateTime))
			.orElse(null);
	}

	/**
	 * 判断消息内容是否包含 lock 单词
	 * 由于前期设计问题，导致目前cmd为lock的消息不都是锁命令，
	 * 需要再根据消息内容进行过滤主要用于消息内容进行过滤判断，后续变更非锁命令的消息命令
	 *
	 * @param content 消息内容
	 * @return true 包含锁命令
	 */
	private boolean isContainsLockWordFrom(String content) {
		return Func.isNotBlank(content) && content.contains(DownCmd.lock.name());
	}
}
