package com.lecent.device.service.impl;

import com.lecent.device.common.enums.OssCode;
import com.lecent.device.common.utils.HikCheckUtils;
import com.lecent.device.service.IFileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.resource.feign.IOssClient;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 文件上传服务
 *
 * <AUTHOR>
 * @since 2024/6/12
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements IFileUploadService {

    @Resource
    private IOssClient ossClient;

    @Override
    public List<String> uploadImages(List<MultipartFile> imageFiles) {
        try {
            return Optional.ofNullable(imageFiles)
                .map(files -> files.stream().map(this::uploadImage).filter(Func::isNotBlank).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("uploadPic failed", e);
            return Collections.emptyList();
        }
    }

    @Override
    public String uploadImage(MultipartFile imageFile) {
        return Optional.ofNullable(imageFile)
            .map(file -> {
                final R<BladeFile> ossFile =
                    ossClient.putFileInputStreamByOssCode(file, OssCode.ROADSIDE_PARKING_CODE.getCode());
                if (!ossFile.isSuccess()) {
                    log.warn("upload file[filename={}] failed, ossFile = {}", imageFile.getOriginalFilename(), ossFile);
                    return StringPool.EMPTY;
                }
                return ossFile.getData().getLink();
            }).orElse(StringPool.EMPTY);
    }

    @Override
    public String uploadImage(String imageUrl) {
        if (Func.isNotBlank(imageUrl)) {
            R<BladeFile> r = ossClient.putFile(imageUrl, "leliven-park-roadside");
            if (r.isSuccess() && Func.isNotEmpty(r.getData())) {
                return r.getData().getLink();
            }
        }
        return imageUrl;
    }
}
