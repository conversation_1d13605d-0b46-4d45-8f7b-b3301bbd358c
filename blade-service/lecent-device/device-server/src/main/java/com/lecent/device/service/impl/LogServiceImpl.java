package com.lecent.device.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Log;
import com.lecent.device.mapper.LogMapper;
import com.lecent.device.service.ILogService;
import com.lecent.device.vo.LogVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 设备操作日志 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Service
public class LogServiceImpl extends BaseServiceImpl<LogMapper, Log> implements ILogService {

	@Override
	public IPage<LogVO> selectLogPage(IPage<LogVO> page, LogVO log) {
		return page.setRecords(baseMapper.selectLogPage(page, log));
	}

}
