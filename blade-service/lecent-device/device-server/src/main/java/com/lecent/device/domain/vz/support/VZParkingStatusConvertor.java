package com.lecent.device.domain.vz.support;

import com.lecent.device.domain.common.support.convertor.ParkingStatusConvertor;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;

/**
 * 臻识停车状态转换器
 *
 * <AUTHOR>
 * @since 2024/6/12
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class VZParkingStatusConvertor implements ParkingStatusConvertor<Integer> {

    private static final VZParkingStatusConvertor INSTANCE = new VZParkingStatusConvertor();

    public static VZParkingStatusConvertor getInstance() {
        return INSTANCE;
    }

    @Override
    @Nonnull
    public Integer get3rdEnterTypeValue() {
        return 1;
    }

    @Override
    @Nonnull
    public Integer get3rdExitTypeValue() {
        return 4;
    }

}
