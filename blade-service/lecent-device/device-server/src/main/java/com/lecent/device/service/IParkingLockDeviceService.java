package com.lecent.device.service;


import com.lecent.device.dto.ParkSpaceLockCtlDTO;
import com.lecent.device.dto.event.DeviceQueryCallbackEvent;
import com.lecent.device.dto.event.DeviceQueryEvent;
import com.lecent.device.entity.Device;

/**
 * 车位锁-设备管理  服务类
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface IParkingLockDeviceService {

    /**
     * 触发事件
     *
     * @param device  设备
     * @param content 事件内容
     */
    void triggerEvent(Device device, String content);

    /**
     * 手动同步升锁
     *
     * @param deviceSn 设备序列号
     * @return 是否成功
     */
    boolean manualSyncLock(String deviceSn);

    /**
     * 升锁指令
     *
     * @param dto 车位锁控制DTO
     * @return 请求ID
     */
    String lock(ParkSpaceLockCtlDTO dto);

    /**
     * 升锁指令
     *
     * @param dto 车位锁控制DTO
     * @return 请求ID
     */
    String lockWithCheckCondition(ParkSpaceLockCtlDTO dto);

    /**
     * 降锁指令
     *
     * @param dto 车位锁控制DTO
     * @return 请求ID
     */
    String unlock(ParkSpaceLockCtlDTO dto);

    /**
     * 手动同步降锁
     *
     * @param deviceSn 设备序列号
     * @return 是否成功
     */
    boolean manualSyncUnlock(String deviceSn);

    /**
     * 查询事件
     *
     * @param device 设备
     * @param deviceQueryEvent 查询事件
     * @param requestId 请求ID
     * @return 查询回调事件
     */
    DeviceQueryCallbackEvent<?> queryEvent(Device device, DeviceQueryEvent deviceQueryEvent, String requestId);
}
