package com.lecent.device.service;

import com.lecent.device.dto.inspectionvehicle.CloudParkingEventDTO;
import com.lecent.device.dto.vzentith.VZHighVideoCameraAlarmViolationResultDTO;
import com.lecent.device.dto.vzentith.VZHighVideoCameraHeartBeatDTO;
import com.lecent.device.dto.vzentith.VZHighVideoCameraParkingDTO;
import com.lecent.device.dto.vzentith.VZHighVideoCameraSnapDTO;

/**
 * 臻识高清视频摄像头服务
 *
 * <AUTHOR>
 * @since 2024/6/5
 */
public interface IVZHighVideoCameraServiceImpl {

    /**
     * 心跳
     *
     * @param dto 心跳信息
     */
    void heartbeat(VZHighVideoCameraHeartBeatDTO dto);

    /**
     * 停车事件
     *
     * @param dto 停车事件DTO
     */
    void parking(VZHighVideoCameraParkingDTO dto);

	/**
	 * 云平台停车事件
	 *
	 * @param cloudParkingEventDTO 云平台停车事件DTO
	 */
	void cloudParking(CloudParkingEventDTO cloudParkingEventDTO);

    /**
     * 臻识高位摄像头报警违停结果接收
     *
     * @param dto 臻识高位摄像头告警结果DTO
     */
    void alarmViolationResult(VZHighVideoCameraAlarmViolationResultDTO dto);

    /**
     * 抓拍事件
     *
     * @param dto 抓拍事件DTO
     */
    void snapEvent(VZHighVideoCameraSnapDTO dto);
}
