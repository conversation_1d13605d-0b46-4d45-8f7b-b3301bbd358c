package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.dto.UpLogDTO;
import com.lecent.device.entity.UpLog;
import com.lecent.device.service.IUpLogService;
import com.lecent.device.vo.DeviceLogVO;
import com.lecent.device.vo.DeviceSnapEventGroup;
import com.lecent.device.vo.UpLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.StrUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备上发日志 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/up-log")
@Api(value = "设备上发日志", tags = "设备上发日志接口")
public class UpLogController extends BladeController {

	private final IUpLogService upLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入downLog")
	public R<UpLog> detail(UpLog upLog) {
		UpLog detail = upLogService.getOne(Condition.getQueryWrapper(upLog));
		return R.data(detail);
	}

	/**
	 * 自定义分页 设备上发日志
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "upLogDTO")
	public R<IPage<UpLogVO>> page(UpLogDTO upLogDTO, Query query) {
		if(Func.isBlank(query.getAscs()) && Func.isBlank(query.getDescs())){
			//默认按照时间排序
			query.setDescs("create_time");
		}else{
			query.setAscs(StrUtil.camelToUnderlines(query.getAscs()));
			query.setDescs(StrUtil.camelToUnderlines(query.getDescs()));
		}
		IPage<UpLogVO> pages = upLogService.selectUpLogPage(Condition.getPage(query), upLogDTO);
		return R.data(pages);
	}

	@GetMapping("/findUpLogGroupByHour")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "按小时分组秒图", notes = "upLogDTO")
	public R<IPage<DeviceSnapEventGroup>> findUpLogGroupByHour(UpLogDTO upLogDTO, Query query){
		return R.fail("查询秒图失败，请升级最新APP版本");
	}

	/**
	 * 自定义分页 设备上发日志
	 */
	@GetMapping("/deviceLogPage")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "分页", notes = "upLogDTO")
	public R<IPage<DeviceLogVO>> selectDeviceLogPage(UpLogDTO upLogDTO, Query query) {
		if(Func.isBlank(query.getAscs()) && Func.isBlank(query.getDescs())){
			//默认按照时间排序
			query.setDescs("create_time");
		}else{
			query.setAscs(StrUtil.camelToUnderlines(query.getAscs()));
			query.setDescs(StrUtil.camelToUnderlines(query.getDescs()));
		}
		IPage<DeviceLogVO> pages = upLogService.selectDeviceLogPage(Condition.getPage(query), upLogDTO);
		return R.data(pages);
	}

}
