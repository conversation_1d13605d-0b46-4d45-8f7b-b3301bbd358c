<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.LogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="logResultMap" type="com.lecent.device.entity.Log">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="device_id" property="deviceId"/>
        <result column="resource_name" property="resourceName"/>
        <result column="resource_id" property="resourceId"/>
        <result column="resource_data" property="resourceData"/>
        <result column="group_id" property="groupId"/>
        <result column="status" property="status"/>
        <result column="create_user_name" property="createUserName"/>
    </resultMap>


    <select id="selectLogPage" resultMap="logResultMap">
        select *
        from dev_log
        where is_deleted = 0
    </select>

</mapper>
