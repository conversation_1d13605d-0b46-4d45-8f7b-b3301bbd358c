package com.lecent.device.service;

import com.lecent.device.dto.ReqDownMsg;

/**
 * 事件下发业务接口
 *
 * <AUTHOR>
 */
public interface IEventDownService {

	/**
	 * 下发消息
	 *
	 * @param sendMsg msg
	 * @return String
	 */
	String downMsgV1(ReqDownMsg sendMsg);

	/**
	 * 下发消息设备应答
	 *
	 * @param content json
	 */
	void ack(String content);

	/**
	 * 降锁状态
	 *
	 * @param requestId 请求ID
	 * @return Boolean
	 */
	boolean downLockStatus(String requestId);
}
