package com.lecent.device.config.props;

import com.leliven.device.domain.shared.valueobject.DeviceType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@Slf4j
@Component
@RefreshScope
@ConfigurationProperties(prefix = "device.network")
public class DeviceNetworkProperties {

	/**
	 * 心跳间隔，单位：秒
	 */
	private static final long DEFAULT_HEARTBEAT_INTERVAL = 10;

	/**
	 * 设备类型
	 * key - see {@link DeviceType}
	 */
	private Map<String, DeviceHeartbeat> heartbeats = new HashMap<>();

	public Long getIntervalSeconds(Integer type) {
		return getIntervalSeconds(DeviceType.get(type));
	}

	public Long getIntervalSeconds(DeviceType deviceType) {
		if (log.isDebugEnabled()) {
			log.debug("networkProperties :{}", Func.toJson(heartbeats));
		}

		if (Objects.isNull(deviceType)) {
			log.warn("deviceType is null");
			return DEFAULT_HEARTBEAT_INTERVAL;
		}

		DeviceHeartbeat deviceHeartbeat = heartbeats.get(deviceType.name().toLowerCase());
		if (log.isDebugEnabled()) {
			log.debug("heartbeats type={} , config = {}", deviceType.name(), Func.toJson(deviceHeartbeat));
		}

		if (Objects.isNull(deviceHeartbeat)) {
			return DEFAULT_HEARTBEAT_INTERVAL;
		}

		if (deviceHeartbeat.getIntervalSeconds() < 1) {
			log.warn("device[type={}] heartbeat config warn, intervalSeconds less than 0 ", deviceType.name());
			return DEFAULT_HEARTBEAT_INTERVAL;
		}

		return deviceHeartbeat.getIntervalSeconds();
	}
}
