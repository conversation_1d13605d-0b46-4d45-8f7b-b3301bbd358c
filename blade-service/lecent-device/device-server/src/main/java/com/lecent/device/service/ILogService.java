package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Log;
import com.lecent.device.vo.LogVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 设备操作日志 服务类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface ILogService extends BaseService<Log> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param log
	 * @return
	 */
	IPage<LogVO> selectLogPage(IPage<LogVO> page, LogVO log);

}
