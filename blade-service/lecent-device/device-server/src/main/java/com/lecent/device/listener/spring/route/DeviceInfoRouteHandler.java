package com.lecent.device.listener.spring.route;

import com.lecent.device.assembler.DeviceAssembler;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.constant.DeviceConstant;
import com.lecent.device.dto.MsgBody;
import com.lecent.device.entity.Device;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.DeviceLockStatusChangedEvent;
import com.lecent.device.event.UpEvent;
import com.lecent.device.event.payload.DeviceLockStatusPayload;
import com.lecent.device.publisher.DeviceEventPublisher;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import com.leliven.csc.alarm.dto.AlarmEvent;
import com.leliven.csc.alarm.enums.AlarmRestoreClassEnum;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 事件警告处理
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceInfoRouteHandler {

	public static final String CMD_INFO = UpCmd.info.name();

	@Resource
	private MqSender mqSender;
	@Resource
	private DeviceEventPublisher deviceEventPublisher;

	/**
	 * 接收事件
	 *
	 * @param event 事件
	 */
	@EventListener(classes = UpEvent.class,
		condition = "#event.cmd ==T(com.lecent.device.listener.spring.route.DeviceInfoRouteHandler).CMD_INFO")
	public void receive(UpEvent event) {
		try {
            if (log.isDebugEnabled()) {
				log.debug("info: {}", event);
			}
			handler(event);
		} catch (Exception e) {
			log.error("DeviceInfoRouteHandler.receive", e);
		}
	}

	private void handler(UpEvent event) {
		MsgBody payload = event.getPayload();
		Device device = event.getDevice();

		DeviceLockStatusPayload lockStatusPayload = Func.readJson(payload.getContent(), DeviceLockStatusPayload.class);
		lockStatusPayload.setTriggerTime(new Date());

		DeviceCache.setLockStatus(device.getSn(), lockStatusPayload, Duration.ofDays(7));

		Map<String, Object> runtimeStatus = Optional.ofNullable(DeviceCache.getRuntimeStatusMap(device.getSn())).orElseGet(HashMap::new);
		runtimeStatus.put("lock", lockStatusPayload.getLock());

		DeviceCache.setRuntimeStatus(device.getSn(), runtimeStatus);
		lockUpMessage(device);
		deviceEventPublisher.publishDeviceLockStatusChangeEvent(
			new DeviceLockStatusChangedEvent(lockStatusPayload, DeviceAssembler.toSimpleDeviceDTO(device)));
	}

	/**
	 * 升锁消息
	 *
	 * @param device
	 */
	private void lockUpMessage(Device device) {
		try {
			AlarmEvent alarmEvent = new AlarmEvent();
			alarmEvent.setAlarmType(1);
			alarmEvent.setAlarmClass(AlarmRestoreClassEnum.DEVICE_LOCK_HAVE_CAR_UP_CLEARING.getKey());
			alarmEvent.setAlarmTime(new Date());
			alarmEvent.setDeviceId(device.getId());
			alarmEvent.setDeviceSn(device.getDeviceSn());
			String routingKey = DeviceConstant.DEVICE_ALARM_UP_PREFIX + AlarmRestoreClassEnum.DEVICE_LOCK_HAVE_CAR_UP_CLEARING.getKey();
			mqSender.sendMessage(Func.toJson(alarmEvent), routingKey, Exchanges.AMQ_TOPIC);
		} catch (Exception e) {
			log.error("", e);
		}
	}


}
