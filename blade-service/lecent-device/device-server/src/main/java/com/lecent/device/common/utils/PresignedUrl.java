package com.lecent.device.common.utils;


import lombok.Builder;
import lombok.Data;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

@Data
@Builder
public class PresignedUrl {
	private String baseUrl;
	private String accessKeyId;
	private String accessKeySecret;
	private String expires;

	private String requestPath;
	private Map<String, String> params;
	private String body;

	private static final String NEW_LINE = "\n";

	private static String buildCanonicalizedResource(String resourcePath, Map<String, String> parameters) {
		if (!resourcePath.startsWith("/")) {
			throw new IllegalArgumentException("Resource path should start with slash character");
		}

		StringBuilder builder = new StringBuilder();
		builder.append(resourcePath);

		if (parameters != null) {
			String[] parameterNames = parameters.keySet().toArray(new String[parameters.size()]);
			Arrays.sort(parameterNames);

			char separater = '?';
			for (String paramName : parameterNames) {
				builder.append(separater);
				builder.append(paramName);
				String paramValue = parameters.get(paramName);
				if (paramValue != null) {
					builder.append("=").append(paramValue);
				}
				separater = '&';
			}
		}
		return builder.toString();
	}

	public String generateSignature() {
		int bodyLength = this.body.getBytes(StandardCharsets.UTF_8).length;

		String contentMD5 = "";
		String contentType = "";

		if (bodyLength > 0) {
			contentMD5 = BinaryUtil.toBase64String(BinaryUtil.calculateMd5(this.body.getBytes(StandardCharsets.UTF_8)));
		}

		String canonicalString = "GET" + NEW_LINE +
			contentMD5 + NEW_LINE +
			contentType + NEW_LINE +
			this.expires + NEW_LINE +
			buildCanonicalizedResource(this.requestPath, this.params);

		return ServiceSignature.create().computeSignature(this.accessKeySecret, canonicalString);
	}

	public String generatePresignedUrl() {
		String signature = generateSignature();
		Map<String, String> param = new LinkedHashMap<>();
		param.put("expires", this.expires);
		param.put("accesskey_id", this.accessKeyId);
		param.put("signature", signature);
		param.putAll(this.params);

		String queryString = paramToQueryString(param, "UTF8");
		String tempUrl = this.baseUrl + this.requestPath;
		int pos = tempUrl.indexOf("?");
		if (pos != -1) {
			tempUrl = tempUrl.substring(0, pos);
		}
		return tempUrl + "?" + queryString;

	}

	public static String urlEncode(String value, String encoding) {
		if (value == null) {
			return "";
		}

		try {
			String encoded = URLEncoder.encode(value, encoding);
			return encoded.replace("+", "%20")
				.replace("*", "%2A")
				.replace("~", "%7E")
				.replace("/", "%2F");
		} catch (UnsupportedEncodingException e) {
			throw new IllegalArgumentException("编码Url失败");
		}
	}

	/**
	 * Encode request parameters to URL segment.
	 */
	public static String paramToQueryString(Map<String, String> params, String charset) {

		if (params == null || params.isEmpty()) {
			return null;
		}

		StringBuilder paramString = new StringBuilder();
		boolean first = true;
		for (Map.Entry<String, String> p : params.entrySet()) {
			String key = p.getKey();
			String value = p.getValue();

			if (!first) {
				paramString.append("&");
			}

			// Urlencode each request parameter
			paramString.append(urlEncode(key, charset));
			if (value != null) {
				paramString.append("=").append(urlEncode(value, charset));
			}
			first = false;
		}

		return paramString.toString();
	}
}
