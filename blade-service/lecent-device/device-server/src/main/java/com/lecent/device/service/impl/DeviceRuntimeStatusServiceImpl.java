package com.lecent.device.service.impl;

import com.lecent.device.cache.DeviceCache;
import com.lecent.device.config.props.DeviceNetworkProperties;
import com.lecent.device.entity.Device;
import com.lecent.device.event.DeviceRuntimeStatusEvent;
import com.lecent.device.publisher.DeviceEventPublisher;
import com.lecent.device.service.IDeviceRuntimeStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;

/**
 * 设备运行状况服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceRuntimeStatusServiceImpl implements IDeviceRuntimeStatusService {

    private final DeviceEventPublisher deviceEventPublisher;
	private final DeviceNetworkProperties networkProperties;

	/**
	 * 运行状况处理
	 *
	 * @param device        设备信息
	 * @param runtimeStatus 运行状况
	 */
	@Override
	public void runtimeStatus(Device device, Map<String, Object> runtimeStatus) {
		DeviceCache.setRuntimeStatus(device.getSn(), runtimeStatus);
	}

	@Async
	@Override
	public void runtimeStatus(Device device, String runtimeStatusJson) {
		this.runtimeStatus(new DeviceRuntimeStatusEvent(runtimeStatusJson, device));
	}

	@Async
	@Override
	public void runtimeStatus(DeviceRuntimeStatusEvent event) {
		DeviceCache.setRuntimeStatus(
			event.getSource().getSn(),
			event.getPayload(),
			Duration.ofSeconds(networkProperties.getIntervalSeconds(event.getSource().getType())));
		deviceEventPublisher.publishDeviceRuntimeStatusEvent(event);
	}

}
