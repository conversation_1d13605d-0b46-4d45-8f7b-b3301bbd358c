package com.lecent.device.service.impl;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.device.dto.ProductDTO;
import com.lecent.device.entity.Product;
import com.lecent.device.entity.ProductParam;
import com.lecent.device.mapper.ProductMapper;
import com.lecent.device.service.IProductParamService;
import com.lecent.device.service.IProductService;
import com.lecent.device.vo.ProductVO;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备产品 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Service
public class ProductServiceImpl extends BaseServiceImpl<ProductMapper, Product> implements IProductService {
    @Resource
    private IProductParamService productParamService;

    @Slave
    @Override
    public IPage<ProductVO> selectProductPage(IPage<ProductVO> page, ProductVO product) {
        return page.setRecords(baseMapper.selectProductPage(page, product));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean customSaveOrUpdate(ProductDTO product) {
        LecentAssert.notBlank(product.getCode(), "产品编号不能为空");
        if (!checkCodeRepeat(product.getCode(), product.getId())) {
            throw new ServiceException("产品编号已存在");
        }
        if (null == product.getId()) {
            save(product);
        }

        List<ProductParam> params = Func.isNotEmpty(product.getParams()) ? product.getParams() : Collections.emptyList();

        // 删除参数
        Set<Long> paramIds = params.stream().map(ProductParam::getId).filter(Objects::nonNull).collect(Collectors.toSet());

        List<Long> deleteIds = productParamService.list(Wrappers.<ProductParam>lambdaQuery().select(ProductParam::getId).eq(ProductParam::getProductId, product.getId())).stream().map(ProductParam::getId).filter(id -> !paramIds.contains(id)).collect(Collectors.toList());
        if (!deleteIds.isEmpty()) {
            productParamService.deleteLogic(deleteIds);
        }
        // 构造参数
        if (Func.isNotEmpty(params)) {
            for (ProductParam param : params) {
                param.setProductId(product.getId());
            }
            // save
            productParamService.saveOrUpdateBatch(params);
        }

        return saveOrUpdate(product);
    }

    @Override
    public Product existById(Long id) {
        return Optional.ofNullable(this.getById(id))
                .orElseThrow(() -> new ServiceException("产品信息不存在"));
    }

    @Override
    public Product existByCode(String code) {
        return oneOptByCode(code)
                .orElseThrow(() -> new ServiceException("产品信息不存在"));
    }

    @Override
    public Map<Long, Product> groupById() {
		return this.lambdaQuery().list().stream().collect(Collectors.toMap(Product::getId, Function.identity()));
    }

    @Override
    public List<Product> listByNodeType(String nodeType) {
        return this.lambdaQuery().eq(Product::getNodeType, nodeType).list();
    }

    @Override
    public List<Long> idsByNodeType(String nodeType) {
        return listByNodeType(nodeType).stream().map(Product::getId).collect(Collectors.toList());
    }

    public Optional<Product> oneOptByCode(String code) {
        return this.lambdaQuery().eq(Product::getCode, code).oneOpt();
    }

    private boolean checkCodeRepeat(String code, Long id) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", BladeConstant.DB_NOT_DELETED).eq("code", code);
        if (Func.isNotEmpty(id)) {
            queryWrapper.ne("id", id);
        }
        List<Product> productList = this.baseMapper.selectList(queryWrapper);
        return Func.isEmpty(productList);
    }

}
