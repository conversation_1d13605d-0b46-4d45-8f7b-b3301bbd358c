package com.lecent.device.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.UpLogDTO;
import com.lecent.device.entity.UpLog;
import com.lecent.device.vo.DeviceLogVO;
import com.lecent.device.vo.UpLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备上发日志 Mapper 接口
 *
 * <AUTHOR>
 */
public interface UpLogMapper extends BaseMapper<UpLog> {

	/**
	 * 自定义分页
	 *
	 * @param page     分页参数
	 * @param upLogDTO 条件
	 * @return List<UpLogVO>
	 */
	List<UpLogVO> selectUpLogPage(IPage<UpLogVO> page, @Param("upLog") UpLogDTO upLogDTO);

	/**
	 * 获取最近一条数据
	 * @param upLogDTO
	 * @param orderBy
	 * @return
	 */
	UpLogVO selectRecentlyUpLog( @Param("upLog") UpLogDTO upLogDTO,@Param("orderBy")Integer orderBy);

	/**
	 * 查询需要删除的日志id
	 *
	 * @return List<Long>
	 */
	@DS("doris")
	List<Long> findRemoveUpLogId();

	List<DeviceLogVO> selectDeviceLogPage(IPage<DeviceLogVO> page,@Param("model") UpLogDTO upLogDTO);
}
