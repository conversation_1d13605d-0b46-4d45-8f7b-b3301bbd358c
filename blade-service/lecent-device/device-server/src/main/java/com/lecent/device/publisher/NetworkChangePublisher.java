package com.lecent.device.publisher;

import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.device.entity.Device;
import com.lecent.park.core.notify.api.IMsgSender;
import com.lecent.park.core.notify.domain.MsgRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备网络状态变更发布器（推送到 MQ）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NetworkChangePublisher {

	private final IMsgSender sender;

	public NetworkChangePublisher(IMsgSender sender) {
		this.sender = sender;
	}

	public void publish(List<Device> devices) {
		log.info("MQ push devices, network status has changed, size ={}, devices = {}", devices.size(), devices);
		sender.send(MsgRequest.<List<Device>>builder()
			.code(DeviceMQConstant.NETWORK_CHANGE_EVENT_CODE)
			.body(devices)
			.bodyClass(Device.class)
			.build()
		);
	}
}
