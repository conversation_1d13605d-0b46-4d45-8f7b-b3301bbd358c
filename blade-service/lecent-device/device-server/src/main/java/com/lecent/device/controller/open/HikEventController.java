package com.lecent.device.controller.open;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.service.IHikVideoPileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 海康tcl视频桩事件接受处理类
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/hik/tcl/event")
@Api(value = "海康tcl视频桩事件接收接口", tags = "海康tcl视频桩事件接收接口")
public class HikEventController {

    @Resource
    private IHikVideoPileService hikVideoPileService;


    @PostMapping(value = "/rcv")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "海康tcl视频桩接收接口")
    public R<Boolean> hkEventRcv(HttpServletRequest httpServletRequest) {
        try {
			this.hikVideoPileService.process(httpServletRequest);
		} catch (Exception e) {
			log.error("接收海康视频桩数据处理失败", e);
            return R.fail("接收数据处理失败");
		}

        return R.status(true);
    }


}

