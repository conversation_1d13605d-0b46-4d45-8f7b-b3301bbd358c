package com.lecent.device.controller.ebo.ops;

import com.lecent.device.dto.DeviceDTO;
import com.lecent.device.entity.Device;
import com.lecent.device.service.IDeviceService;
import com.lecent.device.vo.DeviceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备管理表 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ebo/ops/device")
@Api(value = "e泊运维端接口", tags = "e泊运维端接口")
public class EboOpsDeviceController extends BladeController {

	private final IDeviceService deviceService;

	@PostMapping(value = "v1/save-if-absent")
	public R<Device> saveIfAbsent(@RequestBody DeviceDTO deviceDTO) {
		return R.data(deviceService.saveIfAbsent(deviceDTO));
	}

	/**
	 * 详情
	 */
	@GetMapping("/v1/detail")
	@ApiOperation(value = "详情", notes = "传入device")
	public R<DeviceVO> detail(Device device) {
		DeviceVO detail = deviceService.detail(device);
		return R.data(detail);
	}

	/**
	 * 获取在线设备运行状况
	 */
	@GetMapping("/v1/status")
	@ApiOperation(value = "获取在线设备运行状况", notes = "获取在线设备运行状况")
	public R<Map<String, Object>> getRuntimeStatus(String sn) {
		return R.data(deviceService.getRuntimeStatus(sn));
	}

	@PostMapping("/upload/coord")
	@ApiOperation(value = "上传设备坐标", notes = "上传设备坐标")
	public R<Boolean> uploadCoord(@RequestBody Device device) {
		return R.status(deviceService.uploadCoord(device.getSn(), device.getLng(), device.getLat()));
	}

	@GetMapping("/coord/list")
	@ApiOperation(value = "获取设备坐标列表", notes = "获取设备坐标列表")
	public R<List<Object>> getCoordList() {
		return R.data(deviceService.getCoordList());
	}
}
