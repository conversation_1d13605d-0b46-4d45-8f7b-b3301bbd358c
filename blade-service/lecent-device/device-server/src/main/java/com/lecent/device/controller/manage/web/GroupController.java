package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.entity.Group;
import com.lecent.device.service.IGroupService;
import com.lecent.device.vo.GroupVO;
import com.lecent.device.wrapper.GroupWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.node.INode;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备分组 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/group")
@Api(value = "设备分组", tags = "设备分组接口")
public class GroupController extends BladeController {

	private final IGroupService groupService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入group")
	public R<Group> detail(Group group) {
		Group detail = groupService.getOne(Condition.getQueryWrapper(group));
		return R.data(detail);
	}

	/**
	 * 分页 设备分组
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入group")
	public R<IPage<Group>> list(Group group, Query query) {
		IPage<Group> pages = groupService.page(Condition.getPage(query), Condition.getQueryWrapper(group));
		return R.data(pages);
	}

	/**
	 * 自定义分页 设备分组
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入group")
	public R<IPage<GroupVO>> page(GroupVO group, Query query) {
		IPage<GroupVO> pages = groupService.selectGroupPage(Condition.getPage(query), group);
		return R.data(pages);
	}

	/**
	 * 新增 设备分组
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入group")
	public R save(@Valid @RequestBody Group group) {
		return R.status(groupService.save(group));
	}

	/**
	 * 修改 设备分组
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入group")
	public R update(@Valid @RequestBody Group group) {
		return R.status(groupService.updateById(group));
	}

	/**
	 * 新增或修改 设备分组
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入group")
	public R submit(@Valid @RequestBody Group group) {
		return R.status(groupService.submit(group));
	}


	/**
	 * 删除 设备分组
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(groupService.deleteLogic(Func.toLongList(ids)));
	}

	@GetMapping("/lazy-tree")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "懒加载树形结构", notes = "树形结构")
	public R<List<GroupVO>> lazyTree(String tenantId, Long parentId, BladeUser bladeUser) {
		List<GroupVO> tree = groupService.lazyTree(Func.toStrWithEmpty(tenantId, bladeUser.getTenantId()), parentId);
		return R.data(tree);
	}

	/**
	 * 懒加载列表
	 */
	@GetMapping("/lazy-list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入group")
	public R<List<INode>> lazyList(@ApiIgnore @RequestParam Map<String, Object> group, Long parentId, BladeUser bladeUser) {
		List<GroupVO> list = groupService.lazyList(bladeUser.getTenantId(), parentId, group);
		return R.data(GroupWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * 同步设备分组到视频流平台
	 */
	@GetMapping("/sync-group")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "同步设备分组到视频流平台", notes = "传入一级分组ID")
	public R syncGroup(@RequestParam Long id) {
		return R.status(groupService.syncGroup(id));
	}

	/**
	 * 获取海康配置
	 */
	@GetMapping("/get-hik-config")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "同步设备到视频流平台", notes = "传入设备真实编号")
	public R getHikConfig(@RequestParam String deviceSn) {
		return R.data(groupService.getHikConfig(deviceSn));
	}

	/**
	 * 获取视频流地址
	 */
	@GetMapping("/get-video-url")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "获取视频流地址", notes = "传入设备ID")
	public R getVideoUrl(@RequestParam Long id) {
		return R.data(groupService.getVideoUrl(id));
	}
}
