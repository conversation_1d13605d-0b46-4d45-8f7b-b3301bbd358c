package com.lecent.device.service.impl;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.Group;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.mapper.GroupMapper;
import com.lecent.device.service.IDeviceService;
import com.lecent.device.service.IGroupService;
import com.lecent.device.vo.GroupVO;
import com.lecent.park.core.videostream.strategy.config.VideoStreamContext;
import com.lecent.park.core.videostream.strategy.dto.ResultDTO;
import com.lecent.park.core.videostream.strategy.enums.VideoStreamTypeEnum;
import com.tencentcloudapi.iotvideoindustry.v20201201.models.ChannelDetail;
import com.tencentcloudapi.iotvideoindustry.v20201201.models.DescribeDeviceStreamsData;
import com.tencentcloudapi.iotvideoindustry.v20201201.models.DescribeGroupDevicesResponse;
import com.tencentcloudapi.iotvideoindustry.v20201201.models.GroupDeviceItem;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备分组 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Service
@AllArgsConstructor
public class GroupServiceImpl extends BaseServiceImpl<GroupMapper, Group> implements IGroupService {

	public static final VideoStreamTypeEnum type = VideoStreamTypeEnum.TENCENT_IOT_VIDEO_INDUSTRY;

	private final VideoStreamContext videoStreamContext;
	@Lazy
	private final IDeviceService deviceService;

	@Slave
	@Override
	public IPage<GroupVO> selectGroupPage(IPage<GroupVO> page, GroupVO group) {
		return page.setRecords(baseMapper.selectGroupPage(page, group));
	}

	@Override
	public List<GroupVO> lazyList(String tenantId, Long parentId, Map<String, Object> param) {
		return baseMapper.lazyList(tenantId, parentId, param);
	}

	@Override
	public List<GroupVO> lazyTree(String tenantId, Long parentId) {
		return ForestNodeMerger.merge(baseMapper.lazyTree(tenantId, parentId));
	}

	@Override
	public boolean submit(Group group) {
		if (Func.isEmpty(group.getParentId())) {
			group.setTenantId(AuthUtil.getTenantId());
			Long parentId = BladeConstant.TOP_PARENT_ID;
			group.setParentId(parentId);
			group.setAncestors(String.valueOf(BladeConstant.TOP_PARENT_ID));
		}
		if (group.getParentId() > 0) {
			Group parent = getById(group.getParentId());
			if (Func.toLong(group.getParentId()) == Func.toLong(group.getId())) {
				throw new ServiceException("父节点不可选择自身!");
			}
			group.setTenantId(parent.getTenantId());
			String ancestors = parent.getAncestors() + StringPool.COMMA + group.getParentId();
			group.setAncestors(ancestors);
		}
		group.setIsDeleted(BladeConstant.DB_NOT_DELETED);
		return saveOrUpdate(group);
	}

	@Override
	public boolean syncGroup(Long id) {
		// 一级项目
		Group group = baseMapper.selectById(id);
		handleSyncGroup(group);
		// 二级车场
		List<Group> parkGroupList = baseMapper.selectList(new LambdaQueryWrapper<Group>().eq(Group::getParentId, id));
		if (CollectionUtils.isEmpty(parkGroupList)) {
			return true;
		}
		for (Group parkGroup : parkGroupList) {
			handleSyncGroup(parkGroup);
			// 三级设备，只同步低拍杆
			List<Device> deviceList = deviceService.list(new LambdaQueryWrapper<Device>()
				.eq(Device::getGroupId, parkGroup.getId())
				.in(Device::getType, Arrays.asList(DeviceType.VIDEO_PILE.getValue(), DeviceType.HIGH_VIDEO_CAMERA.getValue())));
			if (CollectionUtils.isEmpty(deviceList)) {
				continue;
			}
			for (Device device : deviceList) {
				device.setOutGroupId(parkGroup.getOutGroupId());
			}
			// 根据外部设备序列号分组
			Map<String, List<Device>> deviceMap = deviceList.stream()
				.filter(e -> StrUtil.isNotEmpty(e.getDeviceSn()))
				.collect(Collectors.groupingBy(Device::getDeviceSn));
			for (Map.Entry<String, List<Device>> entry : deviceMap.entrySet()) {
				List<Device> list = entry.getValue();
				if (CollectionUtils.isEmpty(list)) {
					continue;
				}
				// 同一个序列号只创建一个设备
				Device initDevice = handleSyncDevice(list.get(0));
				for (int i = 0; i < list.size(); i++) {
					list.get(i).setOutGroupId(initDevice.getOutGroupId());
					list.get(i).setOutDeviceId(initDevice.getOutDeviceId());
					list.get(i).setOutDevicePwd(initDevice.getOutDevicePwd());
					// 更新通道ID
					if (!StrUtil.isEmpty(list.get(i).getOutDeviceId())) {
						ResultDTO resultDTO = videoStreamContext.getDeviceStrategy(type).listChannelByDeviceId(list.get(i).getOutDeviceId());
						LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
						JSONObject jsonData = JSONUtil.parseObj(resultDTO.getData());
						Integer totalCount = jsonData.getInt("TotalCount");
						List<ChannelDetail> channelDetailList = jsonData.getBeanList("Channels", ChannelDetail.class);
						if (CompareUtil.compare(totalCount, 0) > 0 && !CollectionUtils.isEmpty(channelDetailList)) {
							list.get(i).setOutDeviceChannelId(channelDetailList.get(i).getChannelId());
						}
					}
					deviceService.updateById(list.get(i));
				}
			}
		}
		return true;
	}

	@Override
	public Map<String, Object> getHikConfig(String deviceSn) {
		List<Device> deviceList = deviceService.list(new LambdaQueryWrapper<Device>().eq(Device::getDeviceSn, deviceSn));
		LecentAssert.isTrue(!CollectionUtils.isEmpty(deviceList), "还未同步到管理平台");
		Device device = deviceList.get(0);
		LecentAssert.notNull(device.getOutDeviceId(), "还未同步到管理平台");
		ResultDTO resultDTO = videoStreamContext.getConfigStrategy(type).getHikConfig(device.getOutDeviceId());
		LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
		return JSONUtil.parseObj(resultDTO.getData());
	}

	@Override
	@TenantIgnore(tenants = TenantConstant.ADMIN_CODE)
	public Map<String, Object> getVideoUrl(Long id) {
		Device device = deviceService.getById(id);
		LecentAssert.notNull(device, "设备不存在");
		LecentAssert.notNull(device.getOutDeviceId(), "未同步到管理平台");
		LecentAssert.notNull(device.getOutDeviceChannelId(), "未同步到管理平台，通道ID不存在");
		ResultDTO resultDTO = videoStreamContext.getMonitorStrategy(type).getVideoUrl(device.getOutDeviceId(), device.getOutDeviceChannelId());
		LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
		JSONObject data = JSONUtil.parseObj(resultDTO);
		DescribeDeviceStreamsData streamsData = JSONUtil.parseObj(data.get("data")).get("Data", DescribeDeviceStreamsData.class);
		Map<String, Object> result = new HashMap<>(16);
		result.put("device", device);
		result.put("address", streamsData);
		return result;
	}

	/**
	 * 处理视频流分组，不存在，则创建， 存在则更新
	 *
	 * @param group
	 */
	private void handleSyncGroup(Group group) {
		ResultDTO resultDTO;
		if (StrUtil.isEmpty(group.getOutParentId())) {
			// 创建分组
			String outParentId = "";
			if (CompareUtil.compare(group.getParentId(), 0L) == 0) {
				if (StrUtil.isEmpty(group.getOutParentId())) {
					outParentId = "group_root";
				}
			} else {
				Group parentGroup = baseMapper.selectById(group.getParentId());
				outParentId = parentGroup.getOutGroupId();
			}
			resultDTO = videoStreamContext.getDeviceGroupStrategy(type).createDeviceGroup(outParentId, group.getName(), group.getRemark());
			LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			JSONObject jsonObject = JSONUtil.parseObj(resultDTO.getData());
			String groupId = jsonObject.getStr("GroupId");
			group.setOutParentId(outParentId);
			group.setOutGroupId(groupId);
			baseMapper.updateById(group);
		} else {
			// 更新分组
			resultDTO = videoStreamContext.getDeviceGroupStrategy(type).getGroupById(group.getOutGroupId());
			LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			JSONObject jsonObject = JSONUtil.parseObj(resultDTO.getData()).getJSONObject("Group");
			// 父节点/分组名称有一个不相等，则更新设备信息
			if ((!StrUtil.equals(jsonObject.getStr("ParentId"), group.getOutParentId())
				|| !StrUtil.equals(jsonObject.getStr("GroupName"), group.getName()))
				&& !StrUtil.equals(jsonObject.getStr("ParentId"), group.getOutParentId())) {
				resultDTO = videoStreamContext.getDeviceGroupStrategy(type).updateDeviceGroup(group.getOutParentId(), group.getName(), group.getRemark(), group.getOutParentId());
				LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			}
			// 查询分组下的设备
			resultDTO = videoStreamContext.getDeviceGroupStrategy(type).listDeviceByGroupId(group.getOutGroupId());
			LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			DescribeGroupDevicesResponse resp = (DescribeGroupDevicesResponse) resultDTO.getData();
			if (CompareUtil.compare(resp.getTotalCount(), 0L) > 0) {
				// 把当前分组的设备全部移动到默认分组
				String[] array = Arrays.stream(resp.getDeviceList()).map(GroupDeviceItem::getDeviceId).toArray(String[]::new);
				videoStreamContext.getDeviceGroupStrategy(type).bindDeviceList("gro-jxobxna7", array);
			}
		}
	}

	/**
	 * 处理视频流设备，不存在，则创建， 存在则更新
	 *
	 * @param device
	 */
	private Device handleSyncDevice(Device device) {
		ResultDTO resultDTO;
		// 腾讯云设备不存在
		resultDTO = videoStreamContext.getDeviceStrategy(type).getInfoByDeviceId(device.getOutDeviceId());
		if (StrUtil.equals(resultDTO.getMsg(), "ResourceNotFound.DeviceNotExist")) {
			device.setOutDeviceId("");
			device.setOutDevicePwd("");
			device.setOutDeviceChannelId("");
			deviceService.updateById(device);
		}
		String defaultPwd = "lc123456";
		if (StrUtil.isEmpty(device.getOutDevicePwd())) {
			device.setOutDevicePwd(defaultPwd);
		}
		if (StrUtil.isEmpty(device.getOutDeviceId())) {
			resultDTO = videoStreamContext.getDeviceStrategy(type).createDevice(device.getOutGroupId(), Long.valueOf(2), device.getDeviceSn(), defaultPwd);
			LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			JSONObject jsonObject = JSONUtil.parseObj(resultDTO.getData());
			device.setOutDeviceId(jsonObject.getStr("DeviceId"));
			deviceService.updateById(device);
		} else {
			JSONObject jsonObject = JSONUtil.parseObj(resultDTO.getData()).getJSONObject("Device");
			// 更新设备分组
			if (!StrUtil.equals(jsonObject.getStr("GroupId"), device.getOutGroupId())) {
				String[] deviceList = {device.getOutDeviceId()};
				resultDTO = videoStreamContext.getDeviceGroupStrategy(type).bindDeviceList(device.getOutGroupId(), deviceList);
				LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			}
			// 更新设备昵称：视频桩编号
			if (!StrUtil.equals(jsonObject.getStr("NickName"), device.getDeviceSn())) {
				resultDTO = videoStreamContext.getDeviceStrategy(type).updateDevice(device.getOutDeviceId(), device.getDeviceSn());
				LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			}
			// 更新设备密码
			resultDTO = videoStreamContext.getDeviceStrategy(type).getPwdByDeviceId(device.getOutDeviceId());
			LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			if (!StrUtil.equals(jsonObject.getStr("PassWord"), device.getOutDevicePwd())) {
				resultDTO = videoStreamContext.getDeviceStrategy(type).updateDevicePwd(device.getOutDeviceId(), device.getOutDevicePwd());
				LecentAssert.isTrue(resultDTO.getIsSuccess(), resultDTO.getMsg());
			}
		}
		return device;
	}
}
