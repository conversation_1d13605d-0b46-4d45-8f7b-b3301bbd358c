package com.lecent.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lecent.device.enums.EventAlarmType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 设备告警实体类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Data
@TableName("dev_alarm")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Alarm对象", description = "设备告警")
public class Alarm extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备ID
	 */
	@ApiModelProperty(value = "设备ID")
	private Long deviceId;

	@TableField(exist = false)
	@ApiModelProperty(value = "设备名称")
	private String deviceName;

	@TableField(exist = false)
	@ApiModelProperty(value = "设备序列号")
	private String deviceSn;

	@TableField(exist = false)
	@ApiModelProperty(value = "设备类型")
	private Integer deviceType;

	/**
	 * 告警名称
	 */
	@ApiModelProperty(value = "告警名称")
	private String name;

	/**
	 * 告警名称
	 */
	@ApiModelProperty(value = "告警类型")
	private EventAlarmType type;

	/**
	 * 告警原因
	 */
	@ApiModelProperty(value = "告警原因")
	private String reason;

	@ApiModelProperty(value = "告警原因")
	private Integer reasonType;
	/**
	 * 告警级别;1紧急告警、2重要告警、3次要告警、4提示告警、5不确定告警和清除告警
	 */
	@ApiModelProperty(value = "告警级别;1紧急告警、2重要告警、3次要告警、4提示告警、5不确定告警和清除告警")
	private Integer level;
	/**
	 * 分组ID
	 */
	@ApiModelProperty(value = "分组ID")
	private Long groupId;
	/**
	 * 告警规则ID
	 */
	@ApiModelProperty(value = "告警规则ID")
	private Long ruleId;
	/**
	 * 告警状态;0未处理、1已处理、2已清除
	 */
	@ApiModelProperty(value = "告警状态;0未处理、1已处理、2已清除")
	private Integer status;


}
