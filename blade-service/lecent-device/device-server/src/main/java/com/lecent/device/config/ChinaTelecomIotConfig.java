package com.lecent.device.config;

import com.lecent.chinatelecom.iot.api.ChinaTelecomIotAPI;
import com.lecent.chinatelecom.iot.api.IChinaTelecomIotAPI;
import com.lecent.chinatelecom.iot.model.AccessToken;
import com.lecent.device.config.props.ChinaTelecomIotProperties;
import org.springblade.common.utils.CacheUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.time.Duration;

@Configuration
@ConditionalOnClass(value = {IChinaTelecomIotAPI.class})
public class ChinaTelecomIotConfig {

	private static final String CACHE_NAME_TOKEN = "lecent:device:cache:chinaTelecom:iot:accessToken";
	@Resource
	private ChinaTelecomIotProperties properties;

	@Bean
	public IChinaTelecomIotAPI chinaTelecomIotAPI() {
		return ChinaTelecomIotAPI.builder()
			.domain(properties.getDomain())
			.appId(properties.getAppId())
			.secret(properties.getSecret())
			.cacheFunc(login -> CacheUtils.get(CACHE_NAME_TOKEN, AccessToken.class, login, Duration.ofSeconds(3300)))
			.build();
	}

}
