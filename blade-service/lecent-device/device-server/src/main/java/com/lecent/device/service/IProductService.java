package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.ProductDTO;
import com.lecent.device.entity.Product;
import com.lecent.device.vo.ProductVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 设备产品 服务类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface IProductService extends BaseService<Product> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param product
	 * @return
	 */
	IPage<ProductVO> selectProductPage(IPage<ProductVO> page, ProductVO product);

	/**
	 * 保存或更新
	 *
	 * @param product
	 * @return
	 */
	Boolean customSaveOrUpdate(ProductDTO product);

	/**
	 * 根据id查询产品信息
	 * 受检
	 * @param id 产品id
	 * @return Product
	 */
	Product existById(Long id);

	/**
	 * 根据code查询产品信息
	 * 受检
	 * @param code 产品id
	 * @return Product
	 */
	Product existByCode(String code);

	/**
	 * 获取产品Map, 根据id分组
	 *
	 * @return Map<String, Product>
	 */
	Map<Long, Product> groupById();

	/**
	 * 根据节点类型查询产品信息
	 * @param nodeType 节点类型
	 * @return List<Product>
	 */
	List<Product> listByNodeType(String nodeType);

	/**
	 * 根据节点类型查询产品Ids
	 * @param nodeType 节点类型
	 * @return List<Long>
	 */
	List<Long> idsByNodeType(String nodeType);
}
