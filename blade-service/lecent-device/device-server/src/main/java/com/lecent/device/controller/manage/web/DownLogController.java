package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.dto.DownLogDTO;
import com.lecent.device.entity.DownLog;
import com.lecent.device.service.IDownLogService;
import com.lecent.device.vo.DownLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备下发日志 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/downLog")
@Api(value = "设备下发日志", tags = "设备下发日志接口")
public class DownLogController extends BladeController {

	private final IDownLogService downLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入downLog")
	public R<DownLog> detail(DownLog downLog) {
		DownLog detail = downLogService.getOne(Condition.getQueryWrapper(downLog));
		return R.data(detail);
	}

	/**
	 * 自定义分页 设备下发日志
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入downLog")
	public R<IPage<DownLogVO>> page(DownLogDTO downLog, Query query) {
		IPage<DownLogVO> pages = downLogService.selectDownLogPage(Condition.getPage(query), downLog);
		return R.data(pages);
	}

}
