package com.lecent.device.common.utils;

import org.springblade.core.log.exception.ServiceException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

public class HmacSHA1Signature extends ServiceSignature {

	/* Signature method. */
	private static final String ALGORITHM = "HmacSHA1";

	private static final Object LOCK = new Object();

	/* Prototype of the Mac instance. */
	private static Mac macInstance;

	@Override
	public String computeSignature(String key, String data) {
		byte[] signData = sign(key.getBytes(StandardCharsets.UTF_8), data.getBytes(StandardCharsets.UTF_8));
		return BinaryUtil.toBase64String(signData);
	}

	private byte[] sign(byte[] key, byte[] data) {
		try {
			if (macInstance == null) {
				synchronized (LOCK) {
					if (macInstance == null) {
						macInstance = Mac.getInstance(ALGORITHM);
					}
				}
			}

			Mac mac;
			try {
				mac = (Mac) macInstance.clone();
			} catch (CloneNotSupportedException e) {
				// If it is not clonable, create a new one.
				mac = Mac.getInstance(ALGORITHM);
			}
			mac.init(new SecretKeySpec(key, ALGORITHM));
			return mac.doFinal(data);
		} catch (NoSuchAlgorithmException ex) {
			ex.printStackTrace();
			throw new ServiceException("Unsupported algorithm: " + ALGORITHM + ex);
		} catch (InvalidKeyException ex) {
			ex.printStackTrace();
			throw new ServiceException("Invalid key: " + Arrays.toString(key) + ex.getMessage());
		}
	}
}
