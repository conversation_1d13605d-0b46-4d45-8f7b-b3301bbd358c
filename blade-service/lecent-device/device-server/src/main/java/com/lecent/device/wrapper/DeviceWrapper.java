package com.lecent.device.wrapper;

import cn.hutool.core.bean.BeanUtil;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.Product;
import com.lecent.device.vo.DeviceVO;
import org.springblade.core.mp.support.BaseEntityWrapper;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class DeviceWrapper extends BaseEntityWrapper<Device, DeviceVO> {

	public static DeviceWrapper build() {
		return new DeviceWrapper();
	}

	@Override
	public DeviceVO entityVO(Device device) {
		return BeanUtil.copyProperties(device, DeviceVO.class);
	}

	public List<DeviceVO> listVO(List<Device> list, Map<Long, Product> productMap) {
		return listVO(list, t -> {
			DeviceVO deviceVO = entityVO(t);
			Product product = productMap.get(deviceVO.getProductId());
			Optional.ofNullable(product).ifPresent(p -> deviceVO.setProductName(p.getName()));
			deviceVO.setParams(DeviceCache.getDeviceParamsFor(t.getId()));
			return deviceVO;
		});
	}

	public List<DeviceVO> listVO(List<Device> list, Function<Device, DeviceVO> function) {
		return list.stream().map(function).collect(Collectors.toList());
	}

}
