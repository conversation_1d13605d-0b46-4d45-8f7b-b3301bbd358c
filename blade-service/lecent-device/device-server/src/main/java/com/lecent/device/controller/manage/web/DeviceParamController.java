package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.entity.DeviceParam;
import com.lecent.device.service.IDeviceParamService;
import com.lecent.device.vo.DeviceParamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 设备管理参数 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/device/param")
@Api(value = "设备管理参数", tags = "设备管理参数接口")
public class DeviceParamController extends BladeController {

	private final IDeviceParamService deviceParamService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceParam")
	public R<DeviceParam> detail(DeviceParam deviceParam) {
		DeviceParam detail = deviceParamService.getOne(Condition.getQueryWrapper(deviceParam));
		return R.data(detail);
	}

	/**
	 * 分页 设备管理参数
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "设备参数列表", notes = "传入deviceId")
	public R<List<DeviceParam>> list(Long deviceId) {
		List<DeviceParam> list = deviceParamService.listByDeviceId(deviceId);
		return R.data(list);
	}

	/**
	 * 自定义分页 设备管理参数
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceParam")
	public R<IPage<DeviceParamVO>> page(DeviceParamVO deviceParam, Query query) {
		IPage<DeviceParamVO> pages = deviceParamService.selectDeviceParamPage(Condition.getPage(query), deviceParam);
		return R.data(pages);
	}

	/**
	 * 新增 设备管理参数
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceParam")
	public R save(@Valid @RequestBody DeviceParam deviceParam) {
		return R.status(deviceParamService.save(deviceParam));
	}

	/**
	 * 修改 设备管理参数
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceParam")
	public R update(@Valid @RequestBody DeviceParam deviceParam) {
		return R.status(deviceParamService.updateById(deviceParam));
	}

	/**
	 * 新增或修改 设备管理参数
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceParam")
	public R submit(@Valid @RequestBody DeviceParam deviceParam) {
		return R.status(deviceParamService.saveOrUpdateParams(Collections.singletonList(deviceParam)));
	}


	/**
	 * 删除 设备管理参数
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceParamService.deleteLogic(Func.toLongList(ids)));
	}


}
