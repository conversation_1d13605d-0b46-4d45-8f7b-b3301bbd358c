package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.dto.ProductDTO;
import com.lecent.device.entity.Product;
import com.lecent.device.service.IProductService;
import com.lecent.device.vo.ProductVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 设备产品 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/product")
@Api(value = "设备产品", tags = "设备产品接口")
public class ProductController extends BladeController {

	private final IProductService productService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入product")
	public R<Product> detail(Product product) {
		Product detail = productService.getOne(Condition.getQueryWrapper(product));
		return R.data(detail);
	}

	/**
	 * 自定义分页 设备产品
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入product")
	public R<IPage<ProductVO>> page(ProductVO product, Query query) {
		IPage<ProductVO> pages = productService.selectProductPage(Condition.getPage(query), product);
		return R.data(pages);
	}


	/**
	 * 产品列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "产品列表", notes = "")
	public R<List<Product>> getList() {
		List<Product> list = productService.list();
		return R.data(list);
	}

	/**
	 * 新增 设备产品
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入product")
	public R save(@Valid @RequestBody ProductDTO product) {
		return R.status(productService.customSaveOrUpdate(product));
	}

	/**
	 * 修改 设备产品
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入product")
	public R update(@Valid @RequestBody ProductDTO product) {
		return R.status(productService.customSaveOrUpdate(product));
	}

	/**
	 * 删除 设备产品
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(productService.deleteLogic(Func.toLongList(ids)));
	}


}
