package com.lecent.device.listener.spring.vz;

import com.lecent.device.dto.inspectionvehicle.EventData;
import com.lecent.device.domain.vz.event.VZEvent;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 臻识巡检车未知事件监听
 */
@Slf4j
@Component("vzUnknownEventListener")
public class UnknownEventListener {

    @EventListener(classes = VZEvent.class, condition = "#event.eventType == T(com.lecent.device.domain.vz.event.VZEventType).UNKNOWN")
    public void onEvent(VZEvent<?> event) {
        if (event.getPayload() instanceof EventData) {
            log.info("Unknown VZEvent: \n{}", Func.toJson(event.getPayload()));
        }
    }
}
