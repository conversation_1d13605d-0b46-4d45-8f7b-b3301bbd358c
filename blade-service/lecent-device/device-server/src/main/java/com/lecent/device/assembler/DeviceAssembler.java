package com.lecent.device.assembler;

import com.lecent.device.dto.SimpleDeviceDTO;
import com.lecent.device.entity.Device;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 设备装配器
 *
 * <AUTHOR>
 * @since 2024/11/12
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DeviceAssembler {

    public static SimpleDeviceDTO toSimpleDeviceDTO(Device device) {
        return SimpleDeviceDTO.builder()
            .id(device.getId())
            .sn(device.getSn())
            .name(device.getName())
            .type(device.getType())
            .build();
    }
}
