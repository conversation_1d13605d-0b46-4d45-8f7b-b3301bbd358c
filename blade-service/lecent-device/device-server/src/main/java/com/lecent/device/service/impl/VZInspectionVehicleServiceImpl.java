package com.lecent.device.service.impl;

import com.lecent.device.domain.vz.event.VZEvent;
import com.lecent.device.domain.vz.event.VZEventType;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.device.dto.inspectionvehicle.EventData;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.enums.ImageTypeEnum;
import com.lecent.device.enums.PlateColorEnum;
import com.lecent.device.publisher.ParkingPublisher;
import com.lecent.device.service.IVZInspectionVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.resource.feign.IOssClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 臻识巡检车
 */
@Slf4j
@Service
public class VZInspectionVehicleServiceImpl implements IVZInspectionVehicleService {

    @Resource
    private IOssClient ossClient;

    @Resource
    private ParkingPublisher enterOrExitPublisher;

    @Override
    public void process(EventData data) {
        uploadImageToLocalOss(data);
        SpringUtil.publishEvent(new VZEvent<>(this, VZEventType.resolve(data.getEventType()), data));
    }

    /**
     * 上传图片到本地oss
     *
     * @param data 数据
     */
    private void uploadImageToLocalOss(EventData data) {
        try {
            if (Func.isNotEmpty(data.getImages())) {
                for (EventData.Images image : data.getImages()) {
                    String imageUrl = image.getImageUrl();
                    // 上传oss
                    R<BladeFile> r = ossClient.putFile(imageUrl, "leliven-park-roadside");
                    if (r.isSuccess() && Func.isNotEmpty(r.getData())) {
                        image.setImageUrl(r.getData().getLink());
                    }
                }
            }
        } catch (Exception e) {
            log.info("上传图片失败 error:", e);
        }
    }

    @Override
    public void handle(EventData data) {
        try {
            checkRcvData(data);

            DeviceParkingEventDTO eventDTO = DeviceParkingEventDTO.builder()
                    .deviceType(DeviceType.PATROL_VEHICLE)
                    .triggerTime(new Date(data.getOccurTime() * 1000))
                    .type(VZEventType.resolve(data.getEventType()).getParkingStatus())
                    .plate(data.getPlate().getPlateNumber().replace(" ", ""))
                    .plateType(Func.toStr(data.getPlate().getPlateType()))
                    .plateColor(PlateColorEnum.getByValue(data.getPlate().getPlateColor()).name().toLowerCase(Locale.ROOT))
                    .detectionPicUrl(getDetectionPicUrl(data.getImages()))
                    .imageUrlMap(buildImageTypeMap(data.getImages()))
                    .payCode(Func.toStr(data.getBerthUnique()))
                    .build();
            enterOrExitPublisher.publish(eventDTO);
        } catch (Exception e) {
            log.info("巡检车事件处理失败 error:", e);
        }
    }

    private void checkRcvData(EventData data) {
        if (Func.isNull(data)) {
            throw new ServiceException("巡检车上发数据为空");
        }
        if (Func.isNull(data.getEventType())) {
            throw new ServiceException("巡检车上发事件类型为空");
        }
        if (Func.isNull(data.getBerthUnique())) {
            throw new ServiceException("巡检车上发车位号为空");
        }
        if (Func.isNull(data.getPlate()) || Func.isBlank(data.getPlate().getPlateNumber())) {
            throw new ServiceException("巡检车上发车牌为空");
        }
        if (Func.isEmpty(data.getImages())) {
            log.info("巡检车上发图片为空");
        }
        if (Func.isNull(data.getPlate().getPlateType())) {
            log.info("巡检车上发车牌类型为空");
        }
        if (Func.isNull(data.getPlate().getPlateColor())) {
            log.info("巡检车上发车牌颜色为空");
        }
    }

    /**
     * 构建图像类型Map
     *
     * @param imagesList 图像列表
     * @return {@link Map}<{@link Integer}, {@link List}<{@link String}>>
     */
    private Map<Integer, List<String>> buildImageTypeMap(List<EventData.Images> imagesList) {
        if (Func.isEmpty(imagesList)) {
            return new HashMap<>();
        }
        return imagesList.stream()
                .sorted(Comparator.comparing(EventData.Images::getImageType).reversed())
                .filter(images -> {
                    int parkType = ImageTypeEnum.transParkType(images.getImageType());
                    images.setImageType(parkType);
                    return parkType != 0;
                })
                .collect(
                        Collectors.collectingAndThen(
                                Collectors.groupingBy(
                                        EventData.Images::getImageType,
                                        Collectors.mapping(EventData.Images::getImageUrl, Collectors.toList())
                                ),
                                map -> {
                                    // 仅保留一张小图
                                    map.put(ImageTypeEnum.LICENSE_PLATE_SMALL_IMAGE.getParkType(),
                                            Collections.singletonList(map.get(ImageTypeEnum.LICENSE_PLATE_SMALL_IMAGE.getParkType()).get(0)));
                                    return map;
                                }
                        )
                );
    }

    /**
     * 获取场景图片
     *
     * @param imagesList 图像列表
     * @return {@link String}
     */
    private String getDetectionPicUrl(List<EventData.Images> imagesList) {
        String imageUrl = "";
        if (Func.isNotEmpty(imagesList)) {
            for (EventData.Images images : imagesList) {
                if (images.getImageType().equals(ImageTypeEnum.PICTURE_PUZZLE.getValue())) {
                    imageUrl = images.getImageUrl();
                    break;
                } else if (images.getImageType().equals(ImageTypeEnum.CAR_BIG_IMAGE.getValue())) {
                    imageUrl = images.getImageUrl();
                }
            }
        }
        return imageUrl;
    }
}
