package com.lecent.device.listener.spring.route;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONValidator;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.domain.device.DeviceParkingLock;
import com.lecent.device.dto.MsgBody;
import com.lecent.device.entity.Device;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.DeviceRuntimeStatusEvent;
import com.lecent.device.event.UpEvent;
import com.lecent.device.event.payload.ParkingLockRuntimeStatus;
import com.lecent.device.service.IDeviceHeartbeatService;
import com.lecent.device.service.IDeviceRuntimeStatusService;
import com.lecent.device.service.IDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 心跳消息处理器
 *
 * <AUTHOR> zxr
 * @date : 2022/9/14
 */
@Slf4j
@Component
public class HrtMsgRouteHandler {
	/**
	 * 心跳命令
	 */
	public static final String CMD = UpCmd.hrt.name();

	private final IDeviceService deviceService;
	private final IDeviceHeartbeatService deviceHeartbeatService;
	private final IDeviceRuntimeStatusService deviceRuntimeStatusService;

	public HrtMsgRouteHandler(IDeviceService deviceService, IDeviceHeartbeatService deviceHeartbeatService,
                              IDeviceRuntimeStatusService deviceRuntimeStatusService) {
        this.deviceService = deviceService;
        this.deviceHeartbeatService = deviceHeartbeatService;
		this.deviceRuntimeStatusService = deviceRuntimeStatusService;
	}

	/**
	 * 接收设备心跳事件
	 *
	 * @param event 事件
	 */
	@EventListener(classes = UpEvent.class,
		condition = "#event.cmd ==T(com.lecent.device.listener.spring.route.HrtMsgRouteHandler).CMD")
	public void receive(UpEvent event) {
		try {
			Device device = event.getDevice();

			DeviceParkingLock parkingLock = new DeviceParkingLock(device);
			if (parkingLock.hasParentGateway()) {
				String parentGatewaySn = parkingLock.getParentGatewaySn();
				Device gatewayDevice = Func.isNotBlank(parentGatewaySn) ? DeviceCache.getBySn(parentGatewaySn) : null;
				Optional.ofNullable(gatewayDevice).ifPresent(deviceHeartbeatService::heartbeat);
			}

			deviceHeartbeatService.heartbeat(device);

			String content = event.getPayload().getContent();
			if (Func.isBlank(content) || !JSONValidator.from(content).validate()) {
				log.error("content内容为空或JSON格式校验失败，content={}", content);
				return;
			}

			DeviceRuntimeStatusEvent deviceRuntimeStatusEvent = new DeviceRuntimeStatusEvent(content, device);

			deviceRuntimeStatusService.runtimeStatus(deviceRuntimeStatusEvent);
			ParkingLockRuntimeStatus lockRuntimeStatus = (ParkingLockRuntimeStatus) deviceRuntimeStatusEvent.getRuntimeStatus();
			deviceService.updateIfFirmwareVersionDiff(device, lockRuntimeStatus.getVersion());
		} catch (Exception e) {
			log.error("HrtMsgRouteHandler.receive", e);
		}
	}


}
