package com.lecent.device;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springblade.common.constant.LecentAppConstant;
import org.springblade.core.cloud.feign.EnableBladeFeign;
import org.springblade.core.launch.BladeApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 用户启动器
 *
 * <AUTHOR>
 */
@EnableBladeFeign
@MapperScan(basePackages = {"com.leliven.device.**.mapper"})
@EnableFeignClients(basePackages = {"org.springblade.*", "com.lecent.*", "com.leliven.*"})
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@ComponentScan({"org.springblade.system.*", "org.springblade.common.*", "com.lecent.*", "com.leliven.*"})
@SpringCloudApplication
public class DeviceApplication {

	public static void main(String[] args) {
		BladeApplication.run(LecentAppConstant.APPLICATION_DEVICE_NAME, DeviceApplication.class, args);
	}
}
