package com.lecent.device.domain.vz.event;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lecent.device.domain.common.service.AlarmEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 臻识高拍杆告警事件类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VZHigVideoCameraAlarmEventType implements AlarmEventType {

    NORMAL_ORDER(0, "正常订单，无报警"),
    VIDEO_BLURRING(1, "视频模糊"),
    VIDEO_LOSS(2, "视频丢失"),
    LINE_PERSSING_PARKING(3, "占用双车位/压线停车"),
    NON_MOTOR_VEHICLE_OR_MOTORCYCLE(4, "非机动车/摩托车"),
    FRONT_AND_REAR_DIRECTION_INCONSISTENCY(5, "车头/车尾方向不一致"),
    LICENSE_PLATE_OCCLUSION(6, "车牌遮挡"),
    UNLICENSED_VEHICLE(7, "无牌车"),
    FLASH_PARTITION_DAMAGE(100, "FLASH分区损坏"),
    SD_CARD_UNAVAILABLE(101, "SD卡不可用");


    @JsonValue
    @EnumValue
    private final int code;
    private final String desc;

    public static VZHigVideoCameraAlarmEventType resolve(int code) {
        return Arrays.stream(VZHigVideoCameraAlarmEventType.values())
                .filter(t->t.code == code)
                .findFirst()
                .orElse(null);
    }

    @Override
    public VZHigVideoCameraAlarmEventType getValue() {
        return this;
    }

}
