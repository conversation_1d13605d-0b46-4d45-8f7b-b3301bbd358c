package com.lecent.device.domain.hik;

import com.lecent.device.domain.hik.event.HikEventType;
import com.lecent.device.dto.TCLRcvDTO;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.converters.basic.DateConverter;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.io.xml.XmlFriendlyNameCoder;
import com.thoughtworks.xstream.security.AnyTypePermission;
import lombok.NoArgsConstructor;
import org.springblade.core.log.exception.ServiceException;

import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;

/**
 * HikXStream
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class HikXStream {

	private static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss.S z";
	private static final String[] ACCEPTABLE_FORMATS = new String[]{
		"yyyy-MM-dd'T'HH:mm:ss.SSS",
		"yyyy-MM-dd'T'HH:mm:ss"
	};
	private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("GMT+8");
	private static final ConcurrentHashMap<String, XStream> X_STREAM_MAP = new ConcurrentHashMap<>();

	/**
	 * 获取XStream
	 *
	 * @param eventType 事件类型
	 * @return XStream
	 */
	public static XStream of(HikEventType eventType) {
		return Optional.ofNullable(eventType)
			.map(t -> of(eventType.name()))
			.orElseThrow(() -> new ServiceException("eventType must not null"));
	}

	/**
	 * 获取XStream
	 *
	 * @param eventTypeName 事件类型名称
	 * @return XStream
	 */
	private static XStream of(String eventTypeName) {
		return X_STREAM_MAP.computeIfAbsent(eventTypeName, key -> {
			XStream xStreamConfig = getDefaultConfig();
			xStreamConfig.aliasField(eventTypeName, TCLRcvDTO.class, "tfs");
			return xStreamConfig;
		});
	}

	/**
	 * 获取默认配置
	 *
	 * @return XStream
	 */
	public static XStream getDefaultConfig() {
		XStream xStream = new XStream(new DomDriver("UTF-8", new XmlFriendlyNameCoder("_-", "_")));
		xStream.autodetectAnnotations(true);
		xStream.ignoreUnknownElements();
		xStream.autodetectAnnotations(true);
		xStream.processAnnotations(TCLRcvDTO.class);
		xStream.addPermission(AnyTypePermission.ANY);
		// 兼容海康不同版本，时间格式有区别
		xStream.registerConverter(new DateConverter(DEFAULT_PATTERN, ACCEPTABLE_FORMATS, TIME_ZONE), 1);
		return xStream;
	}
}
