package com.lecent.device.service.impl;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.UpdatePackage;
import com.lecent.device.mapper.UpdatePackageMapper;
import com.lecent.device.service.IUpdatePackageService;
import com.lecent.device.vo.UpdatePackageVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 升级包管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Service
public class UpdatePackageServiceImpl extends BaseServiceImpl<UpdatePackageMapper, UpdatePackage> implements IUpdatePackageService {

	@Slave
	@Override
	public IPage<UpdatePackageVO> selectUpdatePackagePage(IPage<UpdatePackageVO> page, UpdatePackageVO updatePackage) {
		return page.setRecords(baseMapper.selectUpdatePackagePage(page, updatePackage));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(UpdatePackage updatePackage) {
		return saveOrUpdate(updatePackage);
	}
}
