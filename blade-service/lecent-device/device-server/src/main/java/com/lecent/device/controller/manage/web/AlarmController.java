
package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.dto.AlarmDTO;
import com.lecent.device.service.IAlarmService;
import com.lecent.device.vo.AlarmVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备告警 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */

@RestController
@AllArgsConstructor
@RequestMapping("/alarm")
@Api(value = "设备告警", tags = "设备告警接口")
public class AlarmController extends BladeController {

	private final IAlarmService alarmService;

	/**
	 * 自定义分页 设备告警
	 */

	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入alarm")
	public R<IPage<AlarmVO>> page(AlarmDTO dto, Query query) {
		IPage<AlarmVO> pages = alarmService.selectAlarmPage(Condition.getPage(query), dto);
		return R.data(pages);
	}


}

