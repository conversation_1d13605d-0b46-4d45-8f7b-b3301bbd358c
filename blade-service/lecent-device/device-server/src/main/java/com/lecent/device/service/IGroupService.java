package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Group;
import com.lecent.device.vo.GroupVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 设备分组 服务类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface IGroupService extends BaseService<Group> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param group
	 * @return
	 */
	IPage<GroupVO> selectGroupPage(IPage<GroupVO> page, GroupVO group);

	/**
	 * 懒加载分组列表
	 *
	 * @param tenantId
	 * @param parentId
	 * @param param
	 * @return
	 */
	List<GroupVO> lazyList(String tenantId, Long parentId, Map<String, Object> param);

	/**
	 * 树形结构
	 *
	 * @param tenantId
	 * @param parentId
	 * @return
	 */
	List<GroupVO> lazyTree(String tenantId, Long parentId);

	/**
	 * 新增或修改分组
	 *
	 * @param group
	 * @return
	 */
	boolean submit(Group group);

	/**
	 * 同步设备到视频流平台
	 *
	 * @param id
	 * @return
	 */
	boolean syncGroup(Long id);

	/**
	 * 获取海康配置
	 *
	 * @param deviceSn
	 * @return
	 */
	Map<String, Object> getHikConfig(String deviceSn);

	/**
	 * 获取视频流地址
	 *
	 * @param id
	 * @return
	 */
	Map<String, Object> getVideoUrl(Long id);

}
