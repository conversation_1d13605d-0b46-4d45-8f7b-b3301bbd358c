package com.lecent.device.controller.manage.web;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.enums.EnterExitType;
import com.lecent.device.publisher.ParkingPublisher;
import com.leliven.vehicle.validator.PlateValidator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 设备管理表 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/simulate")
@Api(value = "设备模拟接口", tags = "设备模拟接口")
public class DeviceSimulateController extends BladeController {

	@Resource
	private ParkingPublisher enterOrExitPublisher;

	@PostMapping("/action")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模拟设备进出场事件", notes = "action")
	public R<Object> action(@Valid @RequestBody DeviceParkingEventDTO deviceTriggerDTO) {
		LecentAssert.notNull(EnterExitType.resolve(deviceTriggerDTO.getType()), "invalid type.");
		LecentAssert.isFalse(DeviceType.VIDEO_PILE.equals(deviceTriggerDTO.getDeviceType())
				&& !PlateValidator.isPlate(deviceTriggerDTO.getPlate()), "invalid plate");
		DeviceCache.existBySn(deviceTriggerDTO.getDeviceSn());
		enterOrExitPublisher.publish(deviceTriggerDTO);
		return R.status(true);
	}






}
