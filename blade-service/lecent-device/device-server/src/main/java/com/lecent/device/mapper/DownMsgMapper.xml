<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.DownMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="downMsgResultMap" type="com.lecent.device.entity.DownMsg">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="request_id" property="requestId"/>
        <result column="device_id" property="deviceId"/>
        <result column="cmd" property="cmd"/>
        <result column="content" property="content"/>
        <result column="success" property="success"/>
        <result column="retry_time" property="retryTime"/>
        <result column="retry_num" property="retryNum"/>
        <result column="retry_next_time" property="retryNextTime"/>
        <result column="exception" property="exception"/>
    </resultMap>


    <select id="selectDownMsgPage" resultMap="downMsgResultMap">
        select *
        from d_down_msg
        where is_deleted = 0
    </select>

</mapper>
