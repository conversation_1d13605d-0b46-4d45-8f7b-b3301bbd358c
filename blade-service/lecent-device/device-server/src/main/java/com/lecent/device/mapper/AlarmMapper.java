package com.lecent.device.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.AlarmDTO;
import com.lecent.device.entity.Alarm;
import com.lecent.device.vo.AlarmVO;

import java.util.List;

/**
 * 设备告警 Mapper 接口
 */
public interface AlarmMapper extends BaseMapper<Alarm> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param alarm
	 * @return
	 */
	@InterceptorIgnore(tenantLine = "true")
	List<AlarmVO> selectAlarmPage(IPage page, AlarmDTO alarm);

}
