package com.lecent.device.domain.hik.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.PayloadApplicationEvent;

import java.io.Serializable;

/**
 * 海康事件
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class HikEvent<T> extends PayloadApplicationEvent<T> implements Serializable {

	private static final long serialVersionUID = -6996392823755183659L;

	private String eventTypeName;

	private HikEventType eventType;

	public HikEvent(Object source, String eventTypeName, T payload) {
		super(source, payload);
		this.eventTypeName = eventTypeName;
		this.eventType = HikEventType.resolve(eventTypeName);
	}

	public HikEvent(Object source, HikEventType hikEventType, T payload) {
		super(source, payload);
		this.eventTypeName = hikEventType.name();
		this.eventType = hikEventType;
	}

	public boolean isUnknownEvent() {
		return HikEventType.UNKNOWN.equals(eventType);
	}
}
