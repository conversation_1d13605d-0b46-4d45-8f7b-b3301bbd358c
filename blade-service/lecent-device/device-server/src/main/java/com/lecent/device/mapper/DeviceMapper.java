package com.lecent.device.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.DeviceDTO;
import com.lecent.device.entity.Device;
import com.lecent.device.vo.DeviceStatisticsVO;
import com.lecent.device.vo.DeviceVO;
import com.lecent.device.vo.TempDeviceAggregationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备管理表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface DeviceMapper extends BaseMapper<Device> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param device
	 * @param groupIdList
	 * @return
	 */
	List<DeviceVO> selectDevicePage(IPage page, @Param("device") DeviceVO device, @Param("groupIdList") List<Long> groupIdList);

	/**
	 * 设备数量统计
	 *
	 * @return DeviceStatisticsVO
	 */
	DeviceStatisticsVO statistics(@Param("device") DeviceDTO device, @Param("groupIdList") List<Long> groupIdList);

	String getMaxSnByParentId(@Param("parentId") Long parentId);

	@InterceptorIgnore(tenantLine = "true")
	Device getStatus(String id);

	/**
	 * 数据大屏根据sn查询设备信息
	 *
	 * @param sn 设备序列号
	 * @return
	 */
	@InterceptorIgnore(tenantLine = "true")
	Device getDeviceStatus(@Param("sn") String sn);

	/**
	 * 数据大屏根据sn查询设备信息
	 *
	 * @param id 设备id
	 * @return
	 */
	@InterceptorIgnore(tenantLine = "true")
    Device getDeviceInfoById(@Param("id") String id);

	List<TempDeviceAggregationVO> listAggregation(@Param("parklotId") Long parklotId, @Param("deviceType") Integer deviceType);

	List<TempDeviceAggregationVO> listAggregationBySn(@Param("parklotId") Long parklotId, @Param("deviceType") Integer deviceType);

}
