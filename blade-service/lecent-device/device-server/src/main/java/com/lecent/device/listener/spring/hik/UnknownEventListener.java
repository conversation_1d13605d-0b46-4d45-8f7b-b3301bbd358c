package com.lecent.device.listener.spring.hik;

import com.lecent.device.domain.hik.HikEventXmlFileResolver;
import com.lecent.device.domain.hik.HikMultipartFileWrapper;
import com.lecent.device.domain.hik.HikXStream;
import com.lecent.device.domain.hik.event.HikEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 未知事件监听
 * <AUTHOR>
 */
@Slf4j
@Component
public class UnknownEventListener {

	@EventListener(classes = HikEvent.class, condition = "#event.eventType == T(com.lecent.device.domain.hik.event.HikEventType).UNKNOWN")
	public void onEvent(HikEvent<?> event) {
		if (event.getPayload() instanceof HikMultipartFileWrapper) {
			HikMultipartFileWrapper multipartFileWrapper = (HikMultipartFileWrapper) event.getPayload();
			HikEventXmlFileResolver hikEventResolver = new HikEventXmlFileResolver(HikXStream.of(event.getEventType()));
			log.info("Unknown Event Listener: \n{}", hikEventResolver.read(multipartFileWrapper.getXmlFile()));
		}
	}
}
