package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.AlarmDTO;
import com.lecent.device.entity.Alarm;
import com.lecent.device.entity.Device;
import com.lecent.device.enums.EventAlarmType;
import com.lecent.device.vo.AlarmVO;
import org.springblade.core.mp.base.BaseService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 设备告警 服务类
 */
public interface IAlarmService extends BaseService<Alarm> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param alarm
	 * @return
	 */
	IPage<AlarmVO> selectAlarmPage(IPage<AlarmVO> page, AlarmDTO alarm);

	void alarm(Device device, EventAlarmType alarmType);

	void alarm(Alarm alarm);

	void alarm(List<Alarm> alarms);
}
