package com.lecent.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Product;
import com.lecent.device.vo.ProductVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备产品 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface ProductMapper extends BaseMapper<Product> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param product
	 * @return
	 */
	List<ProductVO> selectProductPage(IPage page, @Param("product") ProductVO product);

}
