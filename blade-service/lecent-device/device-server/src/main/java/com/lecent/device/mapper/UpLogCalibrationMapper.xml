<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.UpLogCalibrationMapper">



    <delete id="delUpLogCalibration" >
        DELETE  from dev_up_log_calibration
        <where>
            <if test="model.parklotIds != null and model.parklotIds.size()>0">
                and parklot_id in
                <foreach collection="model.parklotIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="model.placeIds != null and model.placeIds.size()>0">
                and place_id in
                <foreach collection="model.placeIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="model.startDate != null">
                AND trigger_time &gt;= #{model.startDate}
            </if>
            <if test="model.endDate != null">
                AND trigger_time &lt;= #{model.endDate}
            </if>
        </where>
    </delete>
    <select id="findUpLogCalibrationPage" resultType="com.lecent.device.vo.UpLogCalibrationVO">
        select * from  dev_up_log_calibration where is_deleted =0
        <if test="model.parklotIds != null and model.parklotIds.size()>0">
            and parklot_id in
            <foreach collection="model.parklotIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="model.placeIds != null and model.placeIds.size()>0">
            and place_id in
            <foreach collection="model.placeIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="model.startDate != null">
            AND trigger_time &gt;= #{model.startDate}
        </if>
        <if test="model.endDate != null">
            AND trigger_time &lt;= #{model.endDate}
        </if>
        order by trigger_time desc
    </select>



</mapper>
