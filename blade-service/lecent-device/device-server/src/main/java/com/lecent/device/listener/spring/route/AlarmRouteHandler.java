package com.lecent.device.listener.spring.route;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONValidator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lecent.device.dto.MsgBody;
import com.lecent.device.entity.Device;
import com.lecent.device.enums.EventAlarmType;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.UpEvent;
import com.lecent.device.service.IAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;

/**
 * 事件警告处理
 * <AUTHOR>
 */
@Slf4j
@Component
public class AlarmRouteHandler {
	public static final String CMD_ALARM = UpCmd.alarm.name();

	@Resource
	private IAlarmService alarmService;

	/**
	 * 接收事件
	 *
	 * @param event 事件
	 */
	@EventListener(classes = UpEvent.class,
		condition = "#event.cmd ==T(com.lecent.device.listener.spring.route.AlarmRouteHandler).CMD_ALARM")
	public void receive(UpEvent event) {
		try {
            if (log.isDebugEnabled()) {
				log.debug("alarm: {}", event);
			}
			handler(event);
		} catch (Exception e) {
			log.error("AlarmRouteHandler.receive", e);
		}
	}

	private void handler(UpEvent event) {
		final Device device = event.getDevice();
		final MsgBody payload = event.getPayload();
		final String content = payload.getContent();

		if (StrUtil.isBlank(content) || !JSONValidator.from(content).validate()) {
			log.warn("参数为空 || JSON格式校验失败");
			return;
		}

		final HashMap<String, Object> params = Func.readJson(content, new TypeReference<HashMap<String, Object>>() {
		});
		Integer warn = null ;
		if(params.get("warn") !=null){
			warn = Integer.parseInt(params.get("warn").toString());
		}
		if (Objects.isNull(warn)) {
			log.warn("参数warn为空");
			return;
		}

		this.alarmService.alarm(device, EventAlarmType.resolve(warn));
	}
}
