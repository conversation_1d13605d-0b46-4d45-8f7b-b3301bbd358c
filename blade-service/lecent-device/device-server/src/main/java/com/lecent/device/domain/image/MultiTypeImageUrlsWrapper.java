package com.lecent.device.domain.image;

import com.google.common.collect.Lists;
import lombok.Getter;

import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 多类型图片URL包装类
 *
 * <AUTHOR>
 */
@Getter
public class MultiTypeImageUrlsWrapper {

	/**
	 * 图片类型 - 车牌
	 */
	public static final int IMAGE_TYPE_PLATE = 1;
	/**
	 * 图片类型 - 场景
	 */
	public static final int IMAGE_TYPE_SCENE = 2;
	/**
	 * 存放多种类型图片
	 * key - 图片类型
	 * value - 多个图片URL
	 */
	private final MultiValueMap<Integer, String> multiImageUrls;

	public MultiTypeImageUrlsWrapper() {
		this.multiImageUrls = new LinkedMultiValueMap<>();
	}

	public MultiTypeImageUrlsWrapper(MultiValueMap<Integer, String> multiImageUrls) {
		this.multiImageUrls = multiImageUrls;
	}

	public MultiTypeImageUrlsWrapper of(MultiValueMap<Integer, String> multiImageUrls) {
		return new MultiTypeImageUrlsWrapper(multiImageUrls);
	}

	public void addPlateImageUrl(String plainImageUrl) {
		this.multiImageUrls.computeIfAbsent(IMAGE_TYPE_PLATE, ArrayList::new).add(plainImageUrl);
	}

	public void addPlateImageUrls(List<String> plainImageUrls) {
		this.multiImageUrls.put(IMAGE_TYPE_PLATE, plainImageUrls);
	}

	public void addSceneImageUrl(String plainImageUrl) {
		this.multiImageUrls.computeIfAbsent(IMAGE_TYPE_SCENE, ArrayList::new).add(plainImageUrl);
	}

	public void addSceneImageUrls(List<String> sceneImageUrls) {
		multiImageUrls.put(IMAGE_TYPE_SCENE, sceneImageUrls);
	}

	public List<String> getPlateImageUrls() {
		return Optional.ofNullable(multiImageUrls).map(t -> t.get(IMAGE_TYPE_PLATE)).orElse(Lists.newArrayList());
	}

	public List<String> getSceneImageUrls() {
		return Optional.ofNullable(multiImageUrls).map(t -> t.get(IMAGE_TYPE_SCENE)).orElse(Lists.newArrayList());
	}

	public String getLastSceneImageUrl() {
		List<String> sceneImageUrls = getSceneImageUrls();
		return sceneImageUrls.isEmpty() ? StringPool.EMPTY : sceneImageUrls.get(sceneImageUrls.size() - 1);
	}

	public String getLastPlateImageUrl() {
		List<String> plateImageUrls = getPlateImageUrls();
		return plateImageUrls.isEmpty() ? StringPool.EMPTY : plateImageUrls.get(plateImageUrls.size() - 1);
	}

	public String getLastImageUrl() {
		String lastSceneImageUrl = getLastSceneImageUrl();
		if (Func.isNotBlank(lastSceneImageUrl)) {
			return lastSceneImageUrl;
		}
		return getLastPlateImageUrl();
	}

}
