package com.lecent.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.DeviceSnapEventDTO;
import com.lecent.device.dto.UpLogCalibrationDTO;
import com.lecent.device.dto.UpLogDTO;
import com.lecent.device.entity.UpLogCalibration;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.mapper.UpLogCalibrationMapper;
import com.lecent.device.service.IUpLogCalibrationService;
import com.lecent.device.service.IUpLogService;
import com.lecent.device.vo.UpLogCalibrationExcel;
import com.lecent.device.vo.UpLogCalibrationVO;
import com.lecent.device.vo.UpLogVO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotDeviceRet;
import com.lecent.park.feign.IParkClient;
import com.lecent.park.feign.IParkingPlaceClient;
import com.lecent.park.feign.IParklotDeviceRetClient;
import com.lecent.park.vo.ParkingOrderVO;
import com.lecent.park.vo.ParkingPlaceVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024/8/15 9:57
 */
@Slf4j
@Service
public class UpLogCalibrationServiceImpl extends BaseServiceImpl<UpLogCalibrationMapper, UpLogCalibration> implements IUpLogCalibrationService {

	@Resource
	private IParkingPlaceClient parkingPlaceClient;

	@Resource
	private IParklotDeviceRetClient parklotDeviceRetClient;
	@Resource
	private IParkClient parkClient;
	@Resource
	private IUpLogService upLogService;


	/**
	 * 分页查询
	 * @param page
	 * @param upLogCalibrationDTO
	 * @return
	 */
	@Override
	public IPage<UpLogCalibrationVO>  findUpLogCalibrationPage(IPage<UpLogCalibrationVO> page, UpLogCalibrationDTO upLogCalibrationDTO){
		List<UpLogCalibrationVO> list = this.baseMapper.findUpLogCalibrationPage(page,upLogCalibrationDTO);
		page.setRecords(list);
		return page;
	}



	@Override
	@Async
	public void createUpLogCalibration(UpLogCalibrationDTO upLogCalibrationDTO){
		log.info("createUpLogCalibration start");
		/**
		 * 先删除
		 */
		baseMapper.delUpLogCalibration(upLogCalibrationDTO);
		try {



			//获取车位相关信息
			Map<Long,ParkingPlaceVO> data =findParkingPlace(upLogCalibrationDTO);
			for (Map.Entry<Long, ParkingPlaceVO> entry : data.entrySet()) {
				createUpLogCalibration(entry.getValue(),upLogCalibrationDTO);
			}
		}catch (Exception e){
			log.error("createUpLogCalibration",e);
		}

		log.info("createUpLogCalibration end");
	}

	private void createUpLogCalibration(ParkingPlaceVO placeVO,UpLogCalibrationDTO upLogCalibrationDTO){
		try {
			//查询设备
			R<List<ParklotDeviceRet>> devicesR = parklotDeviceRetClient.listByParkPlaceId(placeVO.getId());
			if (devicesR == null || Func.isEmpty(devicesR.getData())) {
				log.info("createUpLogCalibration 车位id={},未查询到设备信息", placeVO.getId());
				return;
			}
			Long devLockId = null;
			Long devVideoId = null;
			for (ParklotDeviceRet ret : devicesR.getData()) {
				if (ret.getDeviceType().equals(DeviceType.VIDEO_PILE.getValue())) {
					devVideoId = ret.getDeviceId();
					continue;
				}
				if (ret.getDeviceType().equals(DeviceType.PARKING_LOCK.getValue())) {
					devLockId = ret.getDeviceId();
				}
			}
			if (devLockId == null || devVideoId == null) {
				log.info("createUpLogCalibration 车位id={} devLockId={},devVideoId={} 设备不全", placeVO.getId(), devLockId, devVideoId);
				return;
			}
			//查询秒图
			UpLogDTO upLogDTO = new UpLogDTO();
			upLogDTO.setDeviceId(devVideoId);
			upLogDTO.setStartTime(upLogCalibrationDTO.getStartDate());
			upLogDTO.setEndTime(upLogCalibrationDTO.getEndDate());
			List<DeviceSnapEventDTO> eventDTOS = new ArrayList<>();
			if (Func.isEmpty(eventDTOS)) {
				log.info("createUpLogCalibration 车位id={},没有查询到秒图", placeVO.getId());
				return;
			}
			/**
			 * 查询停车记录
			 */
			ParkingOrderDTO parkingOrderDTO = new ParkingOrderDTO();
			parkingOrderDTO.setParklotId(placeVO.getParklotId());
			parkingOrderDTO.setPlaceId(placeVO.getId());
			parkingOrderDTO.setEnterStartTime(upLogCalibrationDTO.getStartDate());
			parkingOrderDTO.setEnterEndTime(upLogCalibrationDTO.getEndDate());
			R<List<ParkingOrderVO>> parkingOrderR = parkClient.findOccupyParkingOrderVO(parkingOrderDTO);
			List<ParkingOrderVO> parkingOrderVOS = null;
			if (parkingOrderR != null && Func.isNotEmpty(parkingOrderR.getData())) {
				parkingOrderVOS = parkingOrderR.getData();
			}
			for (DeviceSnapEventDTO eventDTO : eventDTOS) {
				createUpLogCalibration(eventDTO, placeVO, upLogCalibrationDTO, parkingOrderVOS, devLockId);
				Thread.sleep(10L);
			}
		}catch (Exception e){
			log.error("",e);
		}
	}

	private void createUpLogCalibration(DeviceSnapEventDTO eventDTO,ParkingPlaceVO placeVO,UpLogCalibrationDTO upLogCalibrationDTO,List<ParkingOrderVO> parkingOrderVOS,Long devLockId){
		try {
			Integer fetchDataType = 1;
			if (upLogCalibrationDTO.getFetchDataType() != null) {
				fetchDataType = upLogCalibrationDTO.getFetchDataType();
			}
			UpLogCalibration upLogCalibration = new UpLogCalibration();
			upLogCalibration.setParklotId(placeVO.getParklotId());
			upLogCalibration.setParklotName(placeVO.getParklotName());
			upLogCalibration.setPlaceId(placeVO.getId());
			upLogCalibration.setPlaceCode(placeVO.getPlaceCode());
			upLogCalibration.setTriggerTime(eventDTO.getTriggerTime());
			upLogCalibration.setFetchDataType(fetchDataType);
			upLogCalibration.setSecondImageUrl(eventDTO.getImagesUrl());
			upLogCalibration.setOrderStatus("无");
			//查询停车记录
			if (Func.isNotEmpty(parkingOrderVOS)) {
				for (ParkingOrderVO orderVO : parkingOrderVOS) {
					if (upLogCalibration.getTriggerTime().compareTo(orderVO.getEnterTime()) >= 0 &&
						(orderVO.getParkingStatus() == 2 || orderVO.getExitTime().compareTo(upLogCalibration.getTriggerTime()) >= 0)) {
						upLogCalibration.setOrderStatus("有");
						upLogCalibration.setParkingId(orderVO.getId());
						break;
					}
				}
			}
			//查询设备日志
			UpLogDTO upLogDTO = new UpLogDTO();
			upLogDTO.setDeviceId(devLockId);
			Date startTime = null;
			Date endTime = null;
			Integer orderBy = 1;
			if (fetchDataType == 1) {
				startTime = upLogCalibration.getTriggerTime();
				endTime = DateUtil.plusMinutes(startTime, 5);
			} else {
				endTime = upLogCalibration.getTriggerTime();
				startTime = DateUtil.plusMinutes(endTime, -5);
				orderBy = 2;
			}
			upLogDTO.setStartTime(startTime);
			upLogDTO.setEndTime(endTime);
			UpLogVO upLogVO = upLogService.selectRecentlyUpLog(upLogDTO, orderBy);
			if (upLogVO != null && Func.isNotBlank(upLogVO.getContent())) {
				JSONObject content = JSON.parseObject(upLogVO.getContent());
				upLogCalibration.setLogId(upLogVO.getId());
				upLogCalibration.setLogUploadTime(upLogVO.getCreateTime());
				String park = content.getString("park");
				upLogCalibration.setPark(park);
				if ("used".equals(park)) {
					upLogCalibration.setPark("占用");
				} else if ("idle".equals(park)) {
					upLogCalibration.setPark("空闲");
				}

				String coil1 = content.getString("coil1");
				upLogCalibration.setCoil1(coil1);
				if ("true".equals(coil1)) {
					upLogCalibration.setCoil1("占用");
				} else if ("false".equals(coil1)) {
					upLogCalibration.setCoil1("空闲");
				}


				String magn = content.getString("magn");
				upLogCalibration.setMagn(magn);
				if ("1".equals(magn)) {
					upLogCalibration.setMagn("占用");
				} else if ("0".equals(magn)) {
					upLogCalibration.setMagn("空闲");
				}

				String ipc_status = content.getString("ipc_status");
				upLogCalibration.setIpcStatus(ipc_status);
				if ("1".equals(ipc_status)) {
					upLogCalibration.setIpcStatus("占用");
				} else if ("0".equals(ipc_status)) {
					upLogCalibration.setIpcStatus("空闲");
				}
				upLogCalibration.setMagnz(content.getBigDecimal("magnz"));
			}
			this.save(upLogCalibration);
		}catch (Exception e){
			log.error("",e);
		}
	}



	private Map<Long,String> findParklot(List<Long> ids){
		R<List<Parklot>>  parkLotR= parkClient.queryParklotByIds(ids);
		if(parkLotR !=null && Func.isNotEmpty(parkLotR.getData())){
			Map<Long, String> map = parkLotR.getData().stream()
				.collect(Collectors.toMap(Parklot::getId, Parklot::getName));
			return map;
		}
		return null;
	}

	private Map<Long, ParkingPlaceVO> findParkingPlace(UpLogCalibrationDTO upLogCalibrationDTO){
		Map<Long,String> parkLotMap = findParklot(upLogCalibrationDTO.getParklotIds());
		if(parkLotMap ==null){
			log.info("findDevices 未查询到车场信息");
			throw new ServiceException("未查询到车场信息");
		}
		List<Long> placeIds= upLogCalibrationDTO.getPlaceIds();
		Map<Long, ParkingPlaceVO> data =new HashMap<>();
		R<List<ParkingPlace>> placeR =null;
		if(Func.isEmpty(placeIds)){
			//查询车位信息
			 placeR = parkingPlaceClient.queryParkingPlaceByParklotIds(upLogCalibrationDTO.getParklotIds());

		}else{
			placeR =parkingPlaceClient.getByIdsExcludeTenantId(placeIds);
		}
		if(placeR ==null || Func.isEmpty(placeR.getData())){
			log.info("findDevices 未查询到车位id");
			throw new ServiceException("未查询到车位id");
		}
		ParkingPlaceVO placeVO = null;
		for(ParkingPlace parkingPlace : placeR.getData()){
			placeVO = new ParkingPlaceVO();
			placeVO.setId(parkingPlace.getId());
			placeVO.setPlaceCode(parkingPlace.getPlaceCode());
			placeVO.setParklotId(parkingPlace.getParklotId());
			placeVO.setParklotName(parkLotMap.get(parkingPlace.getParklotId()));
			data.put(parkingPlace.getId(),placeVO);
		}
		return data;
	}

	public void upLogCalibrationExport(UpLogCalibrationDTO upLogCalibrationDTO, HttpServletResponse response){
         List<UpLogCalibrationVO> datas =this.baseMapper.findUpLogCalibrationPage(null,upLogCalibrationDTO);
		 List<UpLogCalibrationExcel> excels =new ArrayList<>();
		 if(Func.isNotEmpty(datas)){
			 excels = BeanUtil.copyToList(datas,UpLogCalibrationExcel.class);
		 }

		ExcelUtil.export(response, "地锁设备日志校验导出", "地锁设备日志校验导出", excels, UpLogCalibrationExcel.class);
	}



}
