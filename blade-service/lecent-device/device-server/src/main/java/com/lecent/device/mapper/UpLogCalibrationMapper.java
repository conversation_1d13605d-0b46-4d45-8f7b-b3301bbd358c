package com.lecent.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.UpLogCalibrationDTO;
import com.lecent.device.entity.UpLogCalibration;
import com.lecent.device.vo.UpLogCalibrationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UpLogCalibrationMapper extends BaseMapper<UpLogCalibration> {


	Integer delUpLogCalibration(@Param("model") UpLogCalibrationDTO upLogCalibrationDTO);

	List<UpLogCalibrationVO> findUpLogCalibrationPage(IPage<UpLogCalibrationVO> page, @Param("model") UpLogCalibrationDTO upLogCalibrationDTO);

}
