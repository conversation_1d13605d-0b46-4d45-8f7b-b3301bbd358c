package com.lecent.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Group;
import com.lecent.device.vo.GroupVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备分组 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface GroupMapper extends BaseMapper<Group> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param group
	 * @return
	 */
	List<GroupVO> selectGroupPage(IPage page, GroupVO group);


	/**
	 * 懒加载分组列表
	 *
	 * @param tenantId
	 * @param parentId
	 * @param param
	 * @return
	 */
	List<GroupVO> lazyList(@Param("tenantId") String tenantId, @Param("parentId") Long parentId, @Param("queryMap") Map<String, Object> queryMap);

	/**
	 *
	 * @param tenantId
	 * @param parentId
	 * @return
	 */
	List<GroupVO> lazyTree(@Param("tenantId") String tenantId,  @Param("parentId") Long parentId);
}
