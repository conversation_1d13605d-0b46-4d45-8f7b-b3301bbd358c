package com.lecent.device.domain.device;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.entity.Device;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;

import java.util.Map;
import java.util.Objects;

public class DeviceParkingLock {

	private final Device device;

	public DeviceParkingLock(Device device) {
		this.device = device;
	}

	public Map<String, Object> getRuntimeStatus() {
		String runtimeStatusJson = DeviceCache.getRuntimeStatus(device.getSn());
		return JsonUtil.readMap(runtimeStatusJson, String.class, String.class);
	}


	@JsonIgnore
	public String getParentGatewaySn() {
		String[] split = device.getSn().split(StringPool.AT);
		if (split.length > 0 && Func.isNoneBlank(split[0])) {
			return split[0];
		}

		return StringPool.EMPTY;
	}

	public boolean hasParentGateway() {
		return Objects.nonNull(device.getParentId()) && device.getParentId() > 0;
	}
}
