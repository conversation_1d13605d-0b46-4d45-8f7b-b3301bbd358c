package com.lecent.device.common.utils;

import org.springblade.core.log.exception.ServiceException;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class BinaryUtil {

	private BinaryUtil() {
	}

	public static String toBase64String(byte[] binaryData) {
		return new String(Base64.getEncoder().encode(binaryData));
	}

	public static byte[] calculateMd5(byte[] binaryData) {
		MessageDigest messageDigest;
		try {
			messageDigest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			throw new ServiceException("MD5 algorithm not found.");
		}
		messageDigest.update(binaryData);
		return messageDigest.digest();
	}

}
