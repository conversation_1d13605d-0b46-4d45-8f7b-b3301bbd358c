package com.lecent.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.DownMsg;
import com.lecent.device.vo.DownMsgVO;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
public interface DownMsgMapper extends BaseMapper<DownMsg> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param downMsg
	 * @return
	 */
	List<DownMsgVO> selectDownMsgPage(IPage page, DownMsgVO downMsg);

}
