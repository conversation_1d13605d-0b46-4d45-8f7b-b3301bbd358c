package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.UpLogDTO;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.UpLog;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.DeviceEvent;
import com.lecent.device.vo.DeviceLogVO;
import com.lecent.device.vo.UpLogVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 设备上发日志 服务类
 *
 * <AUTHOR>
 */
public interface IUpLogService extends BaseService<UpLog> {

	/**
	 * 自定义分页
	 *
	 * @param page     分页
	 * @param upLogDTO 参数条件
	 * @return IPage<DownLogVO>
	 */
	IPage<UpLogVO> selectUpLogPage(IPage<UpLogVO> page, UpLogDTO upLogDTO);

	/**
	 * 自定义分页
	 *
	 * @param page     分页
	 * @param upLogDTO 参数条件
	 * @return IPage<DeviceLogVO>
	 */
	IPage<DeviceLogVO> selectDeviceLogPage(IPage<DeviceLogVO> page, UpLogDTO upLogDTO);

	/**
	 * 根据设备上发的数据异步保存上发日志
	 *
	 * @param upLog 上发日志
	 */
	void asyncSave(UpLog upLog);

	/**
	 * 根据设备上发的数据异步保存上发日志
	 *
	 * @param event 设备上发的数据
	 * @param upCmd 上发命令
	 */
	void asyncSave(DeviceEvent<?> event, UpCmd upCmd);

	/**
	 * 根据设备上发的数据异步保存上发日志
	 *
	 * @param device 设备
	 * @param content 上发内容
	 */
	void asyncSave(Device device, String content);

	/**
	 * 获取最近一条数据
	 *
	 * @param upLogDTO 上发日志
	 * @param orderBy  排序
	 * @return 上发日志
	 */
	UpLogVO selectRecentlyUpLog(UpLogDTO upLogDTO,Integer orderBy);

	/**
	 * 查询需要删除的日志id
	 *
	 * @return List<Long>
	 */
	List<Long> findRemoveUpLogId();
}
