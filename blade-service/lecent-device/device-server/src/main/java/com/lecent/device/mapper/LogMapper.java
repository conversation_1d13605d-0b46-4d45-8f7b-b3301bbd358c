package com.lecent.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Log;
import com.lecent.device.vo.LogVO;

import java.util.List;

/**
 * 设备操作日志 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface LogMapper extends BaseMapper<Log> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param log
	 * @return
	 */
	List<LogVO> selectLogPage(IPage page, LogVO log);

}
