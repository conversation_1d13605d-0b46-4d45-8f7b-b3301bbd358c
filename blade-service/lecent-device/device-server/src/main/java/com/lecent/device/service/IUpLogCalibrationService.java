package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.UpLogCalibrationDTO;
import com.lecent.device.entity.UpLogCalibration;
import com.lecent.device.vo.UpLogCalibrationVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024/8/15 9:56
 */
public interface IUpLogCalibrationService extends BaseService<UpLogCalibration> {

	public void createUpLogCalibration(UpLogCalibrationDTO upLogCalibrationDTO);
	public IPage<UpLogCalibrationVO> findUpLogCalibrationPage(IPage<UpLogCalibrationVO> page, UpLogCalibrationDTO upLogCalibrationDTO);

	public void upLogCalibrationExport(UpLogCalibrationDTO upLogCalibrationDTO, HttpServletResponse response);
}
