package com.lecent.device.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.entity.DeviceParam;
import com.lecent.device.mapper.DeviceParamMapper;
import com.lecent.device.service.IDeviceParamService;
import com.lecent.device.vo.DeviceParamVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备管理参数 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Service
public class DeviceParamServiceImpl extends BaseServiceImpl<DeviceParamMapper, DeviceParam> implements IDeviceParamService {

    @Override
    public IPage<DeviceParamVO> selectDeviceParamPage(IPage<DeviceParamVO> page, DeviceParamVO deviceParam) {
        return page.setRecords(baseMapper.selectDeviceParamPage(page, deviceParam));
    }

    @Override
    public List<DeviceParam> listByDeviceId(Long deviceId) {
        return list(Wrappers.<DeviceParam>lambdaQuery()
                .eq(DeviceParam::getDeviceId, deviceId)
                .eq(DeviceParam::getStatus, 1));
    }


    @Override
    public boolean saveOrUpdateParams(List<DeviceParam> params) {
        final List<DeviceParam> deviceParams = params.stream()
                .filter(p -> Func.isNoneBlank(p.getK()) && Func.isNoneBlank(p.getV()))
                .map(this::updateIfPresent)
                .collect(Collectors.toList());
        boolean success = this.saveOrUpdateBatch(deviceParams);

        Map<Long, List<DeviceParam>> groupByDeviceId = deviceParams.stream().collect(Collectors.groupingBy(DeviceParam::getDeviceId));
        groupByDeviceId.forEach((k, v) -> DeviceCache.delDeviceParamsFor(k));

        return success;
    }

    /**
     * 如果存在key,则更新对应key的value值
     *
     * @param param 设备参数
     * @return DeviceParam
     */
    private DeviceParam updateIfPresent(DeviceParam param) {
        return getOneOptByDevIdAndKey(param.getDeviceId(), param.getK())
                .map(p -> {
                    p.setV(param.getV());
                    return p;
                })
                .orElse(param);
    }

    private Optional<DeviceParam> getOneOptByDevIdAndKey(Long deviceId, String key) {
        return this.lambdaQuery()
                .eq(DeviceParam::getDeviceId, deviceId)
                .eq(DeviceParam::getK, key)
                .oneOpt();
    }
}
