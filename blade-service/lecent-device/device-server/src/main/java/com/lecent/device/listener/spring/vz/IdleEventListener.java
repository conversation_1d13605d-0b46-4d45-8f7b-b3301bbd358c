package com.lecent.device.listener.spring.vz;

import com.lecent.device.domain.vz.event.VZEvent;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.device.dto.inspectionvehicle.EventData;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.enums.ImageTypeEnum;
import com.lecent.device.publisher.ParkingPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 臻识巡检车空闲事件监听器
 */
@Slf4j
@Component
public class IdleEventListener {

    @Resource
    private ParkingPublisher enterOrExitPublisher;

    @EventListener(
            classes = VZEvent.class,
            condition = "#event.eventType == T(com.lecent.device.domain.vz.event.VZEventType).PARKING_EVENT_IDLE"
    )
    public void onEvent(VZEvent<EventData> event) {
        log.info("VZEvent: {}", event.getEventType());
        EventData data = event.getPayload();
		Map<Integer, List<String>> imageUrlMap =new HashMap<>();
		if(Func.isNotEmpty(data.getImages())){
			List<String> images = new ArrayList<>();
			for (EventData.Images image : data.getImages()) {
				// 不保存车牌图片日志
				ImageTypeEnum imageType = ImageTypeEnum.resolve(image.getImageType());
				if (imageType.isPlateImageType()) {
					continue;
				}
				images.add(image.getImageUrl());
			}
			imageUrlMap.put(2,images);
		}

        DeviceParkingEventDTO eventDTO = DeviceParkingEventDTO.builder()
                .deviceType(DeviceType.PATROL_VEHICLE)
                .triggerTime(new Date(data.getOccurTime() * 1000))
                .payCode(Func.toStr(data.getBerthUnique()))
				.deviceSn(data.getSn())
				.imageUrlMap(imageUrlMap)
                .build();
        enterOrExitPublisher.publishIdleEvent(eventDTO);
    }
}
