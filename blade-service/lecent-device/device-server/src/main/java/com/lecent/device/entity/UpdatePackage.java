package com.lecent.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 升级包管理实体类
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Data
@TableName("dev_update_package")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UpdatePackage对象", description = "升级包管理")
public class UpdatePackage extends TenantEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 包名
     */
    @NotBlank(message = "包名不能为空")
    @ApiModelProperty(value = "包名")
    private String name;
    /**
     * 升级包类型
     */
    @NotNull(message = "升级包类型不能为空")
    @ApiModelProperty(value = "升级包类型")
    private Integer type;
    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    private String productCode;
    /**
     * 开始硬件版本
     */
    @ApiModelProperty(value = "开始硬件版本")
    private String startHwRev;
    /**
     * 结束硬件版本
     */
    @ApiModelProperty(value = "结束硬件版本")
    private String endHwRev;
    /**
     * 升级包版本号
     */
    @NotBlank(message = "升级包版本号不能为空")
    @ApiModelProperty(value = "升级包版本号")
    private String version;
    /**
     * 签名算法
     */
    @ApiModelProperty(value = "签名算法")
    private String signAlgorithm;
    /**
     * 包链接
     */
    @ApiModelProperty(value = "包链接")
    private String packageUrl;
    /**
     * 平台验证
     */
    @ApiModelProperty(value = "平台验证")
    private Boolean platformVerify;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 推送消息
     */
    @ApiModelProperty(value = "推送消息")
    private String pushMessage;
}
