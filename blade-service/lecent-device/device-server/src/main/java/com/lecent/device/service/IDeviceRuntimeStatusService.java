package com.lecent.device.service;

import com.lecent.device.entity.Device;
import com.lecent.device.event.DeviceRuntimeStatusEvent;
import org.springframework.scheduling.annotation.Async;

import java.util.Map;

public interface IDeviceRuntimeStatusService {

	/**
	 * 运行状况处理
	 *
	 * @param device        设备信息
	 * @param runtimeStatus 运行状况
	 */
	void runtimeStatus(Device device, Map<String, Object> runtimeStatus);

	/**
	 * 运行状况处理
	 *
	 * @param device            设备信息
	 * @param runtimeStatusJson 运行状况Json
	 */
	void runtimeStatus(Device device, String runtimeStatusJson);

	@Async
	void runtimeStatus(DeviceRuntimeStatusEvent event);
}
