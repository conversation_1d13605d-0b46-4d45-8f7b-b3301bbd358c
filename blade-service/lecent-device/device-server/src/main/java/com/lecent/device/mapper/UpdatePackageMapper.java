package com.lecent.device.mapper;

import com.lecent.device.entity.UpdatePackage;
import com.lecent.device.vo.UpdatePackageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 升级包管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
public interface UpdatePackageMapper extends BaseMapper<UpdatePackage> {

	/**
	 * 自定义分页
	 *
	 * @param page          页
	 * @param updatePackage 更新包
	 * @return {@link List }<{@link UpdatePackageVO }>
	 */
	List<UpdatePackageVO> selectUpdatePackagePage(IPage<UpdatePackageVO> page, @Param("package") UpdatePackageVO updatePackage);

}
