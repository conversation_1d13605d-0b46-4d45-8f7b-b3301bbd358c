package com.lecent.device.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.dto.DownLogDTO;
import com.lecent.device.entity.DownLog;
import com.lecent.device.vo.DownLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备下发日志 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface DownLogMapper extends BaseMapper<DownLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param downLog
	 * @return
	 */
	List<DownLogVO> selectDownLogPage(IPage<DownLogVO> page, @Param("downLog") DownLogDTO downLog);

	@DS("doris")
    List<Long> selectRemoveDownLogId();
}
