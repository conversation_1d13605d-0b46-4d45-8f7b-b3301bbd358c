package com.lecent.device.wrapper;

import com.lecent.device.entity.Group;
import com.lecent.device.vo.GroupVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.node.INode;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.cache.DictCache;
import org.springblade.system.cache.SysCache;
import org.springblade.system.entity.Dept;
import org.springblade.system.vo.DeptVO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class GroupWrapper extends BaseEntityWrapper<Group, GroupVO> {

	public static GroupWrapper build() {
		return new GroupWrapper();
	}

	@Override
	public GroupVO entityVO(Group group) {
		GroupVO groupVO = Objects.requireNonNull(BeanUtil.copy(group, GroupVO.class));
		if (Func.equals(groupVO.getParentId(), BladeConstant.TOP_PARENT_ID)) {
			groupVO.setParentName(BladeConstant.TOP_PARENT_NAME);
		} else {
			Dept parent = SysCache.getDept(group.getParentId());
			groupVO.setParentName(parent.getDeptName());
		}
		return groupVO;
	}


	public List<INode> listNodeVO(List<Group> list) {
		List<INode> collect = list.stream().map(group -> {
			GroupVO groupVO = BeanUtil.copy(group, GroupVO.class);
			return groupVO;
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

	public List<INode> listNodeLazyVO(List<GroupVO> list) {
		List<INode> collect = list.stream().peek(group -> {
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

}
