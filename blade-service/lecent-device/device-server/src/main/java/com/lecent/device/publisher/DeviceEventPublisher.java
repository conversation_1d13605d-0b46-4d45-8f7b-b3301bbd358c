package com.lecent.device.publisher;

import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.device.constant.DeviceMQConstantV2;
import com.lecent.device.event.DeviceCameraCaptureEvent;
import com.lecent.device.event.DeviceLockStatusChangedEvent;
import com.lecent.device.event.DeviceRuntimeStatusEvent;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.stereotype.Service;

/**
 * 停车事件发布器（推送到 MQ）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceEventPublisher {

    private final MqSender mqSender;

    public DeviceEventPublisher(MqSender mqSender) {
        this.mqSender = mqSender;
    }

    /**
     * 摄像头定时抓拍事件发布
     *
     * @param event 摄像头定时抓拍事件
     */
    public void publishCameraCaptureTimedEvent(DeviceCameraCaptureEvent event) {
        String json = JsonUtil.toJson(event);
        if (log.isDebugEnabled()) {
            log.debug("publish camera capture event, event = {}", json);
        }
        mqSender.sendMessage(json, DeviceMQConstant.RoutingKey.CAMERA_CAPTURE_TIMED, DeviceMQConstant.EXCHANGE);
    }

    /**
     * 设备运行状况事件发布
     *
     * @param event 设备运行状况事件
     */
    public void publishDeviceRuntimeStatusEvent(DeviceRuntimeStatusEvent event) {
        String json = JsonUtil.toJson(event);
        if (log.isDebugEnabled()) {
            log.debug("publish device parking event, device runtime status event = {}", json);
        }
        mqSender.sendMessage(json, DeviceMQConstant.ROUTING_KEY_DEVICE_RUNTIME_STATUS, DeviceMQConstant.EXCHANGE);
    }

    /**
     * 设备锁状态变更事件发布
     *
     * @param event 设备锁状态变更事件
     */
    public void publishDeviceLockStatusChangeEvent(DeviceLockStatusChangedEvent event) {
        String json = JsonUtil.toJson(event);
        if (log.isDebugEnabled()) {
            log.debug("publish device lock status change event, event json = {}", json);
        }
        mqSender.sendMessage(json, DeviceMQConstantV2.StatusChanged.ROUTING_KEY_LOCK_STATUS, DeviceMQConstantV2.EXCHANGE);
    }

}
