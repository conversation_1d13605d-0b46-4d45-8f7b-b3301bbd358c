package com.lecent.device.domain.dahua.support;

import com.lecent.device.domain.common.support.convertor.ParkingStatusConvertor;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;

/**
 * 大华停车状态转换器
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DaHuaParkingStatusConvertor implements ParkingStatusConvertor<Integer> {

    private static final DaHuaParkingStatusConvertor INSTANCE = new DaHuaParkingStatusConvertor();

    public static DaHuaParkingStatusConvertor getInstance() {
        return INSTANCE;
    }

    @Override
    @Nonnull
    public Integer get3rdEnterTypeValue() {
        return 0;
    }

    @Override
    @Nonnull
    public Integer get3rdExitTypeValue() {
        return 1;
    }

}
