package com.lecent.device.controller.manage.web;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.dto.ParkSpaceLockCtlDTO;
import com.lecent.device.service.IIotCommandService;
import com.lecent.device.service.IParkingLockDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * IOT设备命令 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/command")
@Api(value = "IOT设备命令", tags = "IOT设备命令")
public class IotCommandController extends BladeController {

	private final IIotCommandService iotCommandService;

	private final IParkingLockDeviceService parkingLockDeviceService;

	/**
	 * 车位锁升锁
	 *
	 * @param sn 设备序列号
	 */
	@GetMapping("/lock/{sn}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "车位锁升锁", notes = "传入sn")
	public R<String> lock(@PathVariable("sn") String sn) {
		return R.data(iotCommandService.lock(ParkSpaceLockCtlDTO.builder().sn(sn).build()));
	}

	/**
	 * 车位锁降锁
	 *
	 * @param sn 设备序列号
	 */
	@GetMapping("/unlock/{sn}")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "车位锁降锁", notes = "传入sn")
	public R<String> unlock(@PathVariable("sn") String sn) {
		return R.data(iotCommandService.unlock(sn, null, 0L));
	}

	/**
	 * 车位锁同步升锁
	 *
	 * @param sn 设备序列号
	 */
	@GetMapping("/sync-lock/{sn}")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "车位锁什锁", notes = "传入sn")
	public R<Boolean> syncLock(@PathVariable("sn") String sn) {
		return R.data(parkingLockDeviceService.manualSyncLock(sn));
	}

	/**
	 * 车位锁同步降锁
	 *
	 * @param sn 设备序列号
	 */
	@GetMapping("/sync-unlock/{sn}")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "车位锁降锁", notes = "传入sn")
	public R<Boolean> syncUnlock(@PathVariable("sn") String sn) {
		return R.data(parkingLockDeviceService.manualSyncUnlock(sn));
	}




}
