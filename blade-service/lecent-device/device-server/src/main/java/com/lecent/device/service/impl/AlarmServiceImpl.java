package com.lecent.device.service.impl;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.dto.AlarmDTO;
import com.lecent.device.entity.Alarm;
import com.lecent.device.entity.Device;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.enums.EventAlarmType;
import com.lecent.device.mapper.AlarmMapper;
import com.lecent.device.publisher.AlarmPublisher;
import com.lecent.device.service.IAlarmService;
import com.lecent.device.vo.AlarmVO;
import com.leliven.csc.alarm.dto.AlarmEvent;
import com.leliven.csc.alarm.enums.AlarmClassEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备告警 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Service
public class AlarmServiceImpl extends BaseServiceImpl<AlarmMapper, Alarm> implements IAlarmService {

	@Resource
	private AlarmPublisher alarmPublisher;

	@Slave
	@Override
	public IPage<AlarmVO> selectAlarmPage(IPage<AlarmVO> page, AlarmDTO alarm) {
		return page.setRecords(baseMapper.selectAlarmPage(page, alarm));
	}

	@Override
	public void alarm(Device device, EventAlarmType alarmType) {
		this.alarm(Collections.singletonList(device), alarmType);
	}

	public void alarm(List<Device> devices, EventAlarmType alarmType) {
        if (Objects.isNull(alarmType) || alarmType.isOther()) {
            return;
        }

		List<Alarm> alarms = devices.stream()
			.map(t -> {
				AlarmDTO alarm = new AlarmDTO();
				alarm.setDeviceId(t.getId());
				alarm.setDeviceSn(t.getSn());
				alarm.setType(alarmType);
				return alarm;
			}).collect(Collectors.toList());
		this.alarm(alarms);
	}

	@Override
	public void alarm(Alarm alarm) {
		this.alarm(Collections.singletonList(alarm));
	}

	@Override
	public void alarm(List<Alarm> alarms) {
		alarms.stream()
			.filter(t -> Func.isNotBlank(t.getDeviceSn()))
			.forEach(t -> {
				Device device = DeviceCache.existBySn(t.getDeviceSn());
				t.setDeviceId(device.getId());
				t.setDeviceName(device.getName());
				t.setDeviceType(device.getType());
				alarmPublisher.publish(buildAlarmEvent(t));
			});
		this.saveBatch(alarms);
	}

	private AlarmEvent buildAlarmEvent(Alarm alarm) {
		if (Objects.isNull(alarm) || Objects.isNull(alarm.getType())) {
			return null;
		}

		return AlarmEvent.builder()
			.alarmType(1)
			.alarmClass(alarmClass(alarm.getDeviceType(), alarm.getType()))
			.alarmTime(alarm.getCreateTime())
			.deviceId(alarm.getDeviceId())
			//.deviceSn(alarm.getDeviceSn())
			//.deviceName(alarm.getDeviceName())
			//.deviceType(alarm.getDeviceType())
			.build();
	}

	private String alarmClass(Integer deviceTypeValue, EventAlarmType alarmType) {
		DeviceType deviceType = DeviceType.get(deviceTypeValue);
		String alarmClass;
		switch (deviceType) {
			case PARKING_LOCK:
				//alarmClass = AlarmClassEnum.DEVICE_LOCK_OFFLINE.getKey();
				if (alarmType.type.equals(EventAlarmType.NETWORK_OFFLINE.type)) {
					alarmClass = AlarmClassEnum.DEVICE_LOCK_OFFLINE.getKey();
				} else {
					alarmClass = alarmType.type.toLowerCase();
				}
				break;
			case VIDEO_PILE:
			case HIGH_VIDEO_CAMERA:
				//alarmClass = AlarmClassEnum.DEVICE_VIDEO_OFFLINE.getKey();
				if (alarmType.type.equals(EventAlarmType.NETWORK_OFFLINE.type)) {
					alarmClass = AlarmClassEnum.DEVICE_VIDEO_OFFLINE.getKey();
				} else {
					alarmClass = alarmType.type.toLowerCase();
				}
				break;
			default:
				alarmClass = "";
		}
		return alarmClass;
	}

}
