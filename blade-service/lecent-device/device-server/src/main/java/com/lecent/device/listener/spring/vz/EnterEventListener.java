package com.lecent.device.listener.spring.vz;

import com.lecent.device.domain.vz.event.VZEvent;
import com.lecent.device.dto.inspectionvehicle.EventData;
import com.lecent.device.service.IVZInspectionVehicleService;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import com.lecent.park.core.mq.rabbitmq.routing.RoutingKeys;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

import static com.lecent.park.core.mq.rabbitmq.exchange.Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE;
import static com.lecent.park.core.mq.rabbitmq.queue.Queues.LECENT_PARK_INSPECTION_VEHICLE_ENTER_DELAY_QUEUE;

/**
 * 臻识巡检车进场事件监听器
 */
@Slf4j
@Component
public class EnterEventListener {

    @Resource
    private IVZInspectionVehicleService inspectionVehicleService;

    @Resource
    private MqSender mqSender;

    @EventListener(
            classes = VZEvent.class,
            condition = "#event.eventType == T(com.lecent.device.domain.vz.event.VZEventType).PARKING_EVENT_ENTRY" +
				" || #event.eventType == T(com.lecent.device.domain.vz.event.VZEventType).PARKING_EVENT_STOP"
    )
    public void onEvent(VZEvent<EventData> event) {
        log.info("VZEvent: {}", event.getEventType());

        mqSender.sendDelayMessage(
                Func.toJson(event.getPayload()),
                DateUtil.plusMinutes(new Date(), 3).getTime(),
                Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE,
                RoutingKeys.LECENT_PARK_INSPECTION_VEHICLE_ENTER
        );
    }

    @RabbitListener(bindings = {
            @QueueBinding(
                    value = @Queue(value = LECENT_PARK_INSPECTION_VEHICLE_ENTER_DELAY_QUEUE, durable = StringPool.TRUE),
                    exchange = @Exchange(value = LECENT_PARK_DELAY_QUEUE_EXCHANGE, type = "x-delayed-message"),
                    key = RoutingKeys.LECENT_PARK_INSPECTION_VEHICLE_ENTER
            )
    })
    public void delayEnterListener(Message message) {
        EventData data = Func.readJson(message.getBody(), EventData.class);
        log.info("delayEnterListener event={}", data);
        inspectionVehicleService.handle(data);
    }

}
