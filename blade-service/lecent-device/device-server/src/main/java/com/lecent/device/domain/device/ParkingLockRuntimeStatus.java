package com.lecent.device.domain.device;

import lombok.Data;
import org.springblade.core.tool.jackson.JsonUtil;

import java.math.BigDecimal;

@Data
public class ParkingLockRuntimeStatus {

	private String lock;
	private String park;
	private Boolean block;
	private Boolean coil1;
	private Boolean coil2;
	private BigDecimal temperature;
	private BigDecimal voltage;
	private BigDecimal angle;

	public static void main(String[] args) {
		String json = "{\"lock\":\"down\",\"park\":\"idle\",\"block\":false,\"coil1\":false,\"coil2\":false,\"temperature\":\"29.75\",\"voltage\":\"13.84\",\"angle\":\"-26.95\"}";
		System.out.println(JsonUtil.readValue(json, ParkingLockRuntimeStatus.class));
	}

}
