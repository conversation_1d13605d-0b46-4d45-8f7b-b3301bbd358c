package com.lecent.device.domain.bigarea.event;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.tool.utils.Func;

@Getter
@AllArgsConstructor
public enum BigAreaEventType {
	/**
	 * 车辆检测事件
	 */
	PARKING("VehicleDetectorBasic"),
	/**
	 * 电量上报事件
	 */
	BATTERY("Battery");

	private final String eventName;

	public static BigAreaEventType resolve(String eventName) {
		if (Func.isNotBlank(eventName)) {
			for (BigAreaEventType value : BigAreaEventType.values()) {
				if (value.eventName.equals(eventName)) {
					return value;
				}
			}
		}
		return null;
	}
}
