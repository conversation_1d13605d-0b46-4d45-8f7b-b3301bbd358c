package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.dto.UpLogCalibrationDTO;
import com.lecent.device.dto.UpLogDTO;
import com.lecent.device.service.IUpLogCalibrationService;
import com.lecent.device.vo.UpLogCalibrationVO;
import com.lecent.device.vo.UpLogVO;
import com.lecent.park.dto.CardDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.StrUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024/8/15 9:33
 */
@RestController
@AllArgsConstructor
@RequestMapping("/up-log-calibration")
@Api(value = "设备上发日志", tags = "设备上发日志接口")
public class UpLogCalibrationController {

	private IUpLogCalibrationService upLogCalibrationService;

	@GetMapping("/create")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "创建", notes = "upLogCalibrationDTO")
	public R<Boolean> createUpLogCalibration(UpLogCalibrationDTO upLogCalibrationDTO) {
		if (upLogCalibrationDTO.getStartDate() == null || upLogCalibrationDTO.getEndDate() == null || Func.isEmpty(upLogCalibrationDTO.getParklotIds())) {
			return R.fail("必要参数不能为空");
		}
		upLogCalibrationService.createUpLogCalibration(upLogCalibrationDTO);
		return R.data(true);
	}

	/**
	 * 自定义分页 设备上发日志
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "upLogCalibrationDTO")
	public R<IPage<UpLogCalibrationVO>> page(UpLogCalibrationDTO upLogCalibrationDTO, Query query) {
		IPage<UpLogCalibrationVO> pages = upLogCalibrationService.findUpLogCalibrationPage(Condition.getPage(query), upLogCalibrationDTO);
		return R.data(pages);
	}

	/**
	 * 月卡导出
	 */
	@PostMapping("/export")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "导出", notes = "upLogCalibrationDTO")
	public void upLogCalibrationExport(UpLogCalibrationDTO upLogCalibrationDTO, HttpServletResponse response) {
		upLogCalibrationService.upLogCalibrationExport(upLogCalibrationDTO, response);
	}


}
