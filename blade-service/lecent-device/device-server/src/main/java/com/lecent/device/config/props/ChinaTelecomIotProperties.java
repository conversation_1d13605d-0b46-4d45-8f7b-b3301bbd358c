package com.lecent.device.config.props;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 中国电信IOT平台配置属性
 */
@Data
@Slf4j
@Component
@RefreshScope
@ConfigurationProperties(prefix = "china-telecom.iot")
public class ChinaTelecomIotProperties {

	/**
	 * 域名
	 */
	private String domain = "https://device.api.ct10649.com:8743";
	/**
	 * 应用ID
	 */
	private String appId = "24283a0eae9047b9a856fa93f0429866";
	/**
	 * 应用密钥
	 */
	private String secret = "7af28ac05c3d46c2adc5aaf53339b0bd";
}
