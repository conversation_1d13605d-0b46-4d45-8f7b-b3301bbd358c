package com.lecent.device.controller.manage.web;

import com.lecent.device.dto.BaseVideoStreamData;
import com.lecent.device.service.IVideoStreamServer;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/video/stream")
@Api(value = "视频流管理", tags = "视频流管理接口")
public class VideoStreamController {

    private final IVideoStreamServer highVideoCameraService;

    /**
     * 获取播放地址
     */
    @GetMapping("/play/url")
    public R<BaseVideoStreamData> getPlayUrl(@RequestParam String sn) {
        return R.data(highVideoCameraService.getPlayUrl(sn));
    }
}
