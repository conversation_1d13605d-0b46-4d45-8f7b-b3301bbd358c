package com.lecent.device.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lecent.device.config.props.VZVideoStreamProperties;
import com.lecent.device.dto.BaseVideoStreamData;
import com.lecent.device.dto.vzentith.VZHighVideoStreamData;
import com.lecent.device.service.IVideoStreamServer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.http.HttpRequest;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.UrlUtil;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Slf4j
@Service
@AllArgsConstructor
public class VideoStreamServerImpl implements IVideoStreamServer {

    private final VZVideoStreamProperties deviceStreamProperties;

    @Override
    public BaseVideoStreamData getPlayUrl(String sn) {
        verifyDevice(sn);

        String params = "{\"sn\": \"" + sn + "\"}";

        String verb = "POST";
        String contentMd5 = Base64Util.encodeToString(DigestUtil.md5Digest(params.getBytes(StandardCharsets.UTF_8)));
        String contentType = "application/json;charset=UTF-8";
        String expires = System.currentTimeMillis() / 1000 + 3600 + "";
        String data = verb + "\n" + contentMd5 + "\n" + contentType + "\n" + expires + "\n" + deviceStreamProperties.getPath();
        String signature = UrlUtil.encode(Base64Util.encodeToString(DigestUtil.decodeHex(
            DigestUtil.hmacSha1Hex(data, deviceStreamProperties.getSecretKey()))));

        String paramAccessKey = "accesskey_id=" + deviceStreamProperties.getAccessKey();
        String paramExpires = "expires=" + expires;
        String paramSignature = "signature=" + signature;

        R<VZHighVideoStreamData> r = HttpRequest.post(deviceStreamProperties.getUrl() + deviceStreamProperties.getPath() + "?"
                + paramAccessKey + "&" + paramExpires + "&" + paramSignature)
            .bodyString(params).execute()
            .onFailed((request, e) -> log.error("获取视频流地址失败:", e))
            .onSuccess(responseSpec -> responseSpec.asValue(new TypeReference<R<VZHighVideoStreamData>>() {
            }));

        return r != null && Func.isNotEmpty(r.getData()) ? r.getData() : null;
    }

    private void verifyDevice(String sn) {
        LecentAssert.notBlank(sn, "设备序列号不能为空");

        if (isSupport(sn)) {
            throw new ServiceException("设备["+ sn + "]暂不支持视频流");
        }
    }

    private boolean isSupport(String sn) {
        return sn.length() != 17 || sn.split("-").length != 2;
    }
}
