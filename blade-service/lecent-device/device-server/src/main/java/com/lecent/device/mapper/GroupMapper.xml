<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.GroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="groupResultMap" type="com.lecent.device.entity.Group">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <select id="selectGroupPage" resultMap="groupResultMap">
        select *
        from dev_group
        where is_deleted = 0
    </select>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <resultMap id="groupVOResultMap" type="com.lecent.device.vo.GroupVO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="name" property="name"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <select id="lazyTree" resultMap="treeNodeResultMap">
        select id,
        parent_id,
        name as title,
        id as "value",
        id as "key"
        from dev_group
        where is_deleted = 0
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>
        <if test="tenantId!=null and tenantId!=''">
            and tenant_id = #{tenantId}
        </if>
        ORDER BY sort
    </select>

    <select id="lazyList" resultMap="groupVOResultMap">
        SELECT
        gp.* ,
        (
        SELECT
        CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
        FROM
        dev_group
        WHERE
        parent_id = gp.id and is_deleted = 0
        ) AS "has_children"
        FROM
        dev_group gp
        WHERE gp.is_deleted = 0
        <if test="tenantId!=null and tenantId!=''">
            and gp.tenant_id = #{tenantId}
        </if>
        <if test="parentId!=null and parentId!=''">
            and gp.parent_id = #{parentId}
        </if>
        <if test="queryMap.name!=null and queryMap.name!=''">
            and gp.name like concat(concat('%', #{queryMap.name}),'%')
        </if>
        ORDER BY gp.sort
    </select>

</mapper>
