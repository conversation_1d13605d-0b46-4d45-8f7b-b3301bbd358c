package com.lecent.device.service;

import com.lecent.chinatelecom.iot.model.DeviceDataInfoNotify;
import com.lecent.chinatelecom.iot.model.DeviceDataChangedNotify;
import com.lecent.chinatelecom.iot.model.NotifyType;

/**
 * 大域设备服务接口
 */
public interface IBigAreaDeviceService {

	/**
	 * 设备数据变化回调
	 *
	 * @param deviceDataChanged 设备数据变化
	 */
	void deviceDataChangedCallback(DeviceDataChangedNotify deviceDataChanged);

	/**
	 * 设备信息变化回调
	 *
	 * @param deviceDataInfoNotify 设备信息变化通知
	 */
	void deviceInfoChangedCallback(DeviceDataInfoNotify deviceDataInfoNotify);

	/**
	 * 订阅通知
	 *
	 * @param notifyType 订阅通知类型
	 * @return true-订阅成功 false-订阅失败
	 */
	boolean subscribe(NotifyType notifyType);
}
