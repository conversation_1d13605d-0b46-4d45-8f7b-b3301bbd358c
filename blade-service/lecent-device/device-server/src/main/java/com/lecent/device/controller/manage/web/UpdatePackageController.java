package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.UpdatePackage;
import com.lecent.device.service.IUpdatePackageService;
import com.lecent.device.vo.UpdatePackageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 升级包管理 控制器
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/update-package")
@Api(value = "升级包管理", tags = "升级包管理接口")
public class UpdatePackageController extends BladeController {

    private final IUpdatePackageService updatePackageService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情", notes = "传入updatePackage")
    public R<UpdatePackage> detail(UpdatePackage updatePackage) {
        UpdatePackage detail = updatePackageService.getOne(Condition.getQueryWrapper(updatePackage));
        return R.data(detail);
    }

    /**
     * 分页 升级包管理
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页", notes = "传入updatePackage")
    public R<IPage<UpdatePackage>> list(UpdatePackage updatePackage, Query query) {
        IPage<UpdatePackage> pages = updatePackageService.page(Condition.getPage(query), Condition.getQueryWrapper(updatePackage));
        return R.data(pages);
    }

    /**
     * 自定义分页 升级包管理
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页", notes = "传入updatePackage")
    public R<IPage<UpdatePackageVO>> page(UpdatePackageVO updatePackage, Query query) {
        IPage<UpdatePackageVO> pages = updatePackageService.selectUpdatePackagePage(Condition.getPage(query), updatePackage);
        return R.data(pages);
    }

    /**
     * 新增 升级包管理
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增", notes = "传入updatePackage")
    public R<Boolean> save(@Valid @RequestBody UpdatePackage updatePackage) {
        return R.status(updatePackageService.save(updatePackage));
    }

    /**
     * 修改 升级包管理
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "传入updatePackage")
    public R<Boolean> update(@Valid @RequestBody UpdatePackage updatePackage) {
        return R.status(updatePackageService.updateById(updatePackage));
    }

    /**
     * 新增或修改 升级包管理
     */
    @PostMapping("/submit")
    @ApiOperation(value = "新增或修改", notes = "传入updatePackage")
    public R<Boolean> submit(@Valid @RequestBody UpdatePackage updatePackage) {
        return R.status(updatePackageService.submit(updatePackage));
    }

    /**
     * 删除 升级包管理
     */
    @PostMapping("/remove")
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(updatePackageService.deleteLogic(Func.toLongList(ids)));
    }
}
