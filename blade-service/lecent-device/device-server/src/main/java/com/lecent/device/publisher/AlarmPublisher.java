package com.lecent.device.publisher;

import com.leliven.csc.alarm.dto.AlarmEvent;
import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.park.core.notify.api.IMsgSender;
import com.lecent.park.core.notify.domain.MsgRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 告警事件发布器（推送到 MQ）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlarmPublisher {

	private final IMsgSender sender;

	public AlarmPublisher(IMsgSender mqSender) {
		this.sender = mqSender;
	}

	/**
	 * 告警事件发布
	 *
	 * @param alarmEvent 告警事件
	 */
	public void publish(AlarmEvent alarmEvent) {
		if (Objects.nonNull(alarmEvent)) {
			log.info("publish device alarm event, deviceTriggerDTO = {}", alarmEvent);
			sender.send(
				MsgRequest.<AlarmEvent>builder()
					.code(DeviceMQConstant.DEVICE_ALARM_EVENT_CODE)
					.body(alarmEvent)
					.build()
			);
		}
	}
}
