<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.ProductParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="productParamResultMap" type="com.lecent.device.entity.ProductParam">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="key" property="key"/>
        <result column="v_type" property="vType"/>
        <result column="value" property="value"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectProductParamPage" resultMap="productParamResultMap">
        select *
        from dev_product_param
        where is_deleted = 0
    </select>

</mapper>
