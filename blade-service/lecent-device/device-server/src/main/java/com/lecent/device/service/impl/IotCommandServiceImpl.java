package com.lecent.device.service.impl;

import com.lecent.device.dto.ParkSpaceLockCtlDTO;
import com.lecent.device.dto.ReqDownMsg;
import com.lecent.device.enums.DownCmd;
import com.lecent.device.service.IEventDownService;
import com.lecent.device.service.IIotCommandService;
import com.lecent.device.service.IParkingLockDeviceService;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.stereotype.Service;

/*********************************
 * IOT 指令业务功能
 * <AUTHOR>
 * @date 2022/11/23 19:03
 *********************************/
@Service
@RequiredArgsConstructor
public class IotCommandServiceImpl implements IIotCommandService {

    private final IEventDownService eventDownService;
    private final IParkingLockDeviceService parkingLockDeviceService;

    @Override
    public String lock(ParkSpaceLockCtlDTO dto) {
        return this.parkingLockDeviceService.lock(dto);
    }

    @Override
    public String lockWithCheckCondition(ParkSpaceLockCtlDTO dto) {
        return this.parkingLockDeviceService.lockWithCheckCondition(dto);
    }

    /**
     * 车位锁降锁
     *
     * @param deviceSn     设备序列号
     * @param delaySeconds 延迟时间 单位：秒
     */
    @Override
    public String unlock(String deviceSn, String requestId, Long delaySeconds) {
        return this.parkingLockDeviceService.unlock(
            ParkSpaceLockCtlDTO.builder().sn(deviceSn)
                .requestId(requestId)
                .delaySeconds(delaySeconds)
                .build()
        );
    }

    /**
     * 设备重启
     *
     * @param deviceSn 设备序列号
     */
    @Override
    public String reboot(String deviceSn) {
        ReqDownMsg reqDownMsg = ReqDownMsg.builder()
            .sn(deviceSn)
            .cmd(DownCmd.reboot.name())
            .content(StringPool.EMPTY)
            .build();
        return this.eventDownService.downMsgV1(reqDownMsg);
    }

    /**
     * 设备刷新配置
     *
     * @param deviceSn 设备序列号
     */
    @Override
    public String refreshConfig(String deviceSn) {
        ReqDownMsg reqDownMsg = ReqDownMsg.builder()
            .sn(deviceSn)
            .cmd(DownCmd.refreshConfig.name())
            .content(StringPool.EMPTY)
            .build();
        return this.eventDownService.downMsgV1(reqDownMsg);
    }

}
