package com.lecent.device.listener.spring.hik;

import com.lecent.device.domain.device.model.ShelterAlarm;
import com.leliven.csc.alarm.dto.AlarmEvent;
import com.leliven.csc.alarm.enums.AlarmClassEnum;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.common.utils.HikCheckUtils;
import com.lecent.device.domain.hik.HikEventXmlFileResolver;
import com.lecent.device.domain.hik.HikMultipartFileWrapper;
import com.lecent.device.domain.hik.HikXStream;
import com.lecent.device.domain.hik.event.HikEvent;
import com.lecent.device.dto.TCLRcvDTO;
import com.lecent.device.entity.Device;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.DeviceEvent;
import com.lecent.device.publisher.AlarmPublisher;
import com.lecent.device.service.IUpLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 遮挡告警事件监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class ShelterAlarmEventListener {

	@Resource
	private IUpLogService upLogService;
	@Resource
	private AlarmPublisher alarmPublisher;

	@Resource
	private BladeRedis bladeRedis;

	@EventListener(
		classes = HikEvent.class,
		condition = "#event.eventType == T(com.lecent.device.domain.hik.event.HikEventType).SHELTER_ALARM"
	)
	public void onEvent(HikEvent<HikMultipartFileWrapper> event) {
		HikEventXmlFileResolver resolver = HikEventXmlFileResolver.build(HikXStream.of(event.getEventType()));
		String xmlContent = resolver.read(event.getPayload().getXmlFile());
		log.info("ShelterAlarmEvent: {}", xmlContent);
		TCLRcvDTO tclRcvDTO = resolver.resolve(xmlContent);

		Device device = DeviceCache.existBySn(HikCheckUtils.deviceSn(tclRcvDTO.getChannelName(), tclRcvDTO.getChannelID()));
		List<String> imageUrlList = HikCheckUtils.uploadImages(event.getPayload().getImageFiles());

		ShelterAlarm shelterAlarm = ShelterAlarm.builder()
			.dateTime(tclRcvDTO.getDateTime())
			.imageUrls(Func.join(imageUrlList))
			.build();
		DeviceEvent<ShelterAlarm> deviceEvent = new DeviceEvent<>(shelterAlarm, device);
		// 保存上发日志
		upLogService.asyncSave(deviceEvent, UpCmd.alarm);
		// 发布告警事件
		publishShelterAlarmEvent(deviceEvent);
		//保存缓存
		saveRedis(deviceEvent);
	}


	/**
	 * 将设备id放入缓存 做自动恢复工单用
	 *
	 * @param deviceEvent
	 */
	private void saveRedis(DeviceEvent<ShelterAlarm> deviceEvent) {
		try {
			bladeRedis.getRedisTemplate().opsForSet().add(CacheNames.SHELTER_ALARM_DEVICE, deviceEvent.getSource().getId());
		} catch (Exception e) {
			log.error("saveRedis", e);
		}
	}

	/**
	 * 视频桩遮挡告警事件
	 *
	 * @param deviceEvent 设备事件
	 */
	private void publishShelterAlarmEvent(DeviceEvent<ShelterAlarm> deviceEvent) {
		alarmPublisher.publish(AlarmEvent.builder()
			.alarmType(1)
			.alarmClass(AlarmClassEnum.DEVICE_VIDEO_SHELTER.getKey())
			.alarmTime(deviceEvent.getPayload().getDateTime())
			.deviceId(deviceEvent.getSource().getId())
			.alarmPics(deviceEvent.getPayload().getImageUrls())
			.build()
		);
	}

}
