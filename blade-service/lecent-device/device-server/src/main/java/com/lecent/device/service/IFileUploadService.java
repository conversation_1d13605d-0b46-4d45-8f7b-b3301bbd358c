package com.lecent.device.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件上传服务
 *
 * <AUTHOR>
 * @since 2024/6/12
 */
public interface IFileUploadService {


    /**
     * 上传一堆图片
     *
     * @param imageFiles 一堆图片文件
     * @return 图片链接列表
     */
    List<String> uploadImages(List<MultipartFile> imageFiles);

    /**
     * 上传一张图片
     * @param imageFile 图片文件
     * @return 图片链接
     */
    String uploadImage(MultipartFile imageFile);

    /**
     * 上传一张图片
     * @param imageUrl 图片文件
     * @return 图片链接
     */
    String uploadImage(String imageUrl);

}
