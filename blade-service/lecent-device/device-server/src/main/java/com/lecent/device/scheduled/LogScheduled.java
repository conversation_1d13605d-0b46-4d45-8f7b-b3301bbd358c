package com.lecent.device.scheduled;

import com.google.common.collect.Lists;
import com.lecent.device.service.IDownLogService;
import com.lecent.device.service.IUpLogService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备定时器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LogScheduled {

    @Resource
    private IUpLogService upLogService;

    @Resource
    private IDownLogService downLogService;

    @XxlJob("removeUpLogBatch")
    public void removeUpLogBatch() {
        log.debug("[批量删除历史上发日志]任务开始==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Long> ids = upLogService.findRemoveUpLogId();
        if (Func.isNotEmpty(ids)) {
            List<List<Long>> partition = Lists.partition(ids, 1000);
			partition.forEach(t -> upLogService.removeByIds(t));
        }
        stopWatch.stop();
        log.info("[批量删除历史上发日志]任务结束, 条数: {}, 耗时:[{}s]==============", ids.size(), stopWatch.getTotalTimeSeconds());
    }

    @XxlJob("removeDownLogBatch")
    public void removeDownLogBatch() {
        log.debug("[批量删除历史下发日志]任务开始==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Long> ids = downLogService.findRemoveDownLogId();
        if (Func.isNotEmpty(ids)) {
            List<List<Long>> partition = Lists.partition(ids, 1000);
			partition.forEach(t -> downLogService.removeByIds(t));
        }
        stopWatch.stop();
        log.info("[批量删除历史下发日志]任务结束, 条数: {}, 耗时:[{}s]==============", ids.size(), stopWatch.getTotalTimeSeconds());
    }

}
