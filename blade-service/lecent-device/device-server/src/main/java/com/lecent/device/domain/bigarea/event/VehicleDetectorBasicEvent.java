package com.lecent.device.domain.bigarea.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class VehicleDetectorBasicEvent {

	/**
	 * 序列号
	 * 设备每次上报一条数据时序列号都 会自动加 1。
	 * 当序列号增加到 65535 或者设备复位时，序列号从 1开始。最小值 1，最大 值65535。
	 * 注:应用服务器对设备状态的更新请务必以序列号为准
	 */
	private Integer sequence;

	/**
	 * 车位状态
	 * ‘0’表示车位无车，‘1’表示 车位有车
	 */
	private String status;

	/**
	 * NB 信号强度
	 * 信号最弱为 0，信号最强为 31
	 */
	private Integer signalStrength;

	/**
	 * 设备编号
	 * 与二维码 ID 一致。
	 * 长度为 13 位字符。也可以用来设置车位编号
	 */
	@JsonProperty("NO")
	private String no;
}
