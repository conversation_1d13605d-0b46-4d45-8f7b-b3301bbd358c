package com.lecent.device.listener.spring.route;

import com.lecent.device.dto.MsgBody;
import com.lecent.device.event.UpEvent;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.service.IEventDownService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 设备应答消息处理器
 *
 * <AUTHOR> zxr
 * @date : 2022/9/14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AckMsgRouteHandler {
	/**
	 * 应答命令
	 */
	public static final String CMD = UpCmd.ack.name();

	private final IEventDownService eventDownService;

	/**
	 * 接收事件
	 *
	 * @param event 事件
	 */
	@EventListener(classes = UpEvent.class,
		condition = "#event.cmd ==T(com.lecent.device.listener.spring.route.AckMsgRouteHandler).CMD")
	public void receive(UpEvent event) {
		try {
			MsgBody msgBody = event.getPayload();
			this.eventDownService.ack(msgBody.getContent());
		} catch (Exception e) {
			log.error("AckMsgRouteHandler.receive", e);
		}
	}
}
