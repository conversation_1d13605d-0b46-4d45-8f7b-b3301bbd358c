package com.lecent.device.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.lecent.chinatelecom.iot.api.IChinaTelecomIotAPI;
import com.lecent.chinatelecom.iot.model.*;
import com.lecent.device.domain.bigarea.event.BatteryEvent;
import com.lecent.device.domain.bigarea.event.BigAreaEventType;
import com.lecent.device.domain.bigarea.event.VehicleDetectorBasicEvent;
import com.lecent.device.domain.common.support.convertor.ParkingStatusConvertor;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.DeviceParam;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.publisher.ParkingPublisher;
import com.lecent.device.service.*;
import com.leliven.vehicle.enums.PlateColor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;

@Slf4j
@Service
public class BigAreaDeviceServiceImpl implements IBigAreaDeviceService {

	private final Map<BigAreaEventType, BiConsumer<Device, DeviceServiceData>> handlerMap = new HashMap<>();

	private final Map<NotifyType, String> NOTIFY_TYPE_MAP = new HashMap<NotifyType, String>() {{
		put(NotifyType.DEVICE_INFO_CHANGED, "/lecent-device/big-area/event/info-changed/callback");
		put(NotifyType.DEVICE_DATA_CHANGED, "/lecent-device/big-area/event/data-changed/callback");
	}};

	@Value("${lecent.gateway.domain:http://test.gateway.park.leuplus.com}")
	private String gatewayDomain;
	@Resource
	private ParkingPublisher publisher;
	@Resource
	private IChinaTelecomIotAPI chinaTelecomIotAPI;
	@Resource
	private IDeviceHeartbeatService deviceHeartbeatService;
	@Resource
	private IDeviceService iDeviceService;
	@Resource
	private IDeviceParamService iDeviceParamService;
	@Resource
	private IUpLogService iUpLogService;

	@PostConstruct
	private void init() {
		handlerMap.put(BigAreaEventType.PARKING, this::vehicleDetectorBasicEvent);
		handlerMap.put(BigAreaEventType.BATTERY, this::batteryEvent);
	}

	@Override
	public void deviceInfoChangedCallback(DeviceDataInfoNotify deviceDataInfoNotify) {
		log.info("deviceInfoChanged: {}", Func.toJson(deviceDataInfoNotify));
		DeviceInfo serviceData = deviceDataInfoNotify.getDeviceInfo();
	}

	@Override
	public void deviceDataChangedCallback(DeviceDataChangedNotify deviceDataNotify) {
		log.info("deviceDataChanged: {}", Func.toJson(deviceDataNotify));
		Device device = getDevice(deviceDataNotify);
		if (Objects.nonNull(device)) {
			// 异步更新外部设备 id
			CompletableFuture.runAsync(() -> {
				if (Func.isBlank(device.getOutDeviceId())) {
					device.setOutDeviceId(deviceDataNotify.getDeviceId());
					iDeviceService.updateOutDeviceId(device);
				}
			});
			// 事件处理
			DeviceServiceData serviceData = deviceDataNotify.getService();
			handlerMap.getOrDefault(BigAreaEventType.resolve(serviceData.getServiceType()), this::handleUnknownEvent)
				.accept(device, serviceData);
		}
	}

	/**
	 * 根据设备数据变化通知获取设备信息
	 * @param deviceDataNotify
	 * @return
	 */
	private Device getDevice(DeviceDataChangedNotify deviceDataNotify) {
		// 根据外部编号获取，地磁只会有一个设备
		List<Device> devices = iDeviceService.getByOutDeviceId(deviceDataNotify.getDeviceId());
		if (!CollectionUtils.isEmpty(devices)) {
			return devices.get(0);
		}
		// 根据设备编号获取
		JsonNode node = deviceDataNotify.getService().getData().get("NO");
		if (Objects.nonNull(node)) {
			return iDeviceService.getBySn(node.asText());
		}
		return null;
	}

	@Override
	public boolean subscribe(NotifyType notifyType) {
		LecentAssert.notNull(notifyType, "notifyType must not be null");
		String subscribeUrl = gatewayDomain + NOTIFY_TYPE_MAP.get(notifyType);
		boolean result = chinaTelecomIotAPI.subscribe(notifyType, subscribeUrl);
		log.info("subscribe url[{}], result[{}]", subscribeUrl, result);
		return result;
	}

	/**
	 * 车辆检测事件（地磁进出场事件）
	 *
	 * @param serviceData 业务数据
	 */
	private void vehicleDetectorBasicEvent(Device device, DeviceServiceData serviceData) {
		VehicleDetectorBasicEvent event = JsonUtil.treeToValue(serviceData.getData(), VehicleDetectorBasicEvent.class);

		// 推送停车事件
		publisher.publish(
			DeviceParkingEventDTO.builder()
				.deviceSn(device.getSn())
				.deviceType(DeviceType.MAGNETIC)
				.triggerTime(serviceData.getEventTime())
				.plateColor(PlateColor.UNKNOWN.code())
				.type(BigAreaParkingStatusConvertor.getInstance().convert2Value(event.getStatus()))
				.build()
		);
		// 推送心跳
		this.deviceHeartbeatService.heartbeat(device);
		// 记录上发日志
		iUpLogService.asyncSave(device, Func.toJson(event));
		// 保存设备信号量参数
		if (Objects.nonNull(event.getSignalStrength())) {
			DeviceParam signalStrengthParam = new DeviceParam();
			signalStrengthParam.setDeviceId(device.getId());
			signalStrengthParam.setName("地磁信号量");
			signalStrengthParam.setK("signalStrength");
			signalStrengthParam.setVType("int");
			signalStrengthParam.setV(String.valueOf(event.getSignalStrength()));
			signalStrengthParam.setTenantId(device.getTenantId());
			updateDeviceParams(signalStrengthParam);
		}
	}

	/**
	 * 电量上报事件（电量上报事件）
	 *
	 * @param serviceData 业务数据
	 */
	private void batteryEvent(Device device, DeviceServiceData serviceData) {
		BatteryEvent batteryEvent = JsonUtil.treeToValue(serviceData.getData(), BatteryEvent.class);
		log.info("batteryEvent: {}", batteryEvent);
		if (Objects.nonNull(batteryEvent.getBatteryLevel())) {
			DeviceParam deviceParam = new DeviceParam();
			deviceParam.setDeviceId(device.getId());
			deviceParam.setName("地磁电量");
			deviceParam.setK("batteryLevel");
			deviceParam.setVType("int");
			deviceParam.setV(String.valueOf(batteryEvent.getBatteryLevel()));
			deviceParam.setTenantId(device.getTenantId());
			updateDeviceParams(deviceParam);
		}
	}

	private void updateDeviceParams(DeviceParam param) {
		if (Objects.isNull(param)) {
			return;
		}
		List<DeviceParam> params = Collections.singletonList(param);
		iDeviceParamService.saveOrUpdateParams(params);
	}

	/**
	 * 处理未知事件
	 *
	 * @param serviceData 业务数据
	 */
	private void handleUnknownEvent(Device device, DeviceServiceData serviceData) {
		log.warn("unknown event type: {}", serviceData.getServiceType());
	}

	private static class BigAreaParkingStatusConvertor implements ParkingStatusConvertor<String> {

		private static final BigAreaParkingStatusConvertor INSTANCE = new BigAreaParkingStatusConvertor();

		public static BigAreaParkingStatusConvertor getInstance() {
			return INSTANCE;
		}

		@Override
		@Nonnull
		public String get3rdEnterTypeValue() {
			return StringPool.ONE;
		}

		@Override
		@Nonnull
		public String get3rdExitTypeValue() {
			return StringPool.ZERO;
		}


	}


}
