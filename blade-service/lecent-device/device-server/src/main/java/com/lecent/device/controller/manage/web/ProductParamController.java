package com.lecent.device.controller.manage.web;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.device.entity.ProductParam;
import com.lecent.device.service.IProductParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 设备产品参数 控制器
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/product/param")
@Api(value = "设备产品参数", tags = "设备产品参数接口")
public class ProductParamController extends BladeController {
	private final IProductParamService productParamService;

	/**
	 * 分页 设备产品参数
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据产品ID查询参数列表", notes = "传入productId")
	public R<List<ProductParam>> list(@RequestParam Long productId) {
		List<ProductParam> list = productParamService.list(Wrappers.<ProductParam>lambdaQuery().eq(ProductParam::getProductId, productId));
		return R.data(list);
	}

	/**
	 * 新增 设备产品参数
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入productParam")
	public R save(@Valid @RequestBody ProductParam productParam) {
		return R.status(productParamService.save(productParam));
	}

	/**
	 * 修改 设备产品参数
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入productParam")
	public R update(@Valid @RequestBody ProductParam productParam) {
		return R.status(productParamService.customSaveOrUpdate(productParam));
	}


	/**
	 * 删除 设备产品参数
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(productParamService.deleteLogic(Func.toLongList(ids)));
	}


}
