package com.lecent.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.ProductParam;
import com.lecent.device.vo.ProductParamVO;

import java.util.List;

/**
 * 设备产品参数 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface ProductParamMapper extends BaseMapper<ProductParam> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param productParam
	 * @return
	 */
	List<ProductParamVO> selectProductParamPage(IPage page, ProductParamVO productParam);

}
