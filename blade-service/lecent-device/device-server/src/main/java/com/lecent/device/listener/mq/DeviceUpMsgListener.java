package com.lecent.device.listener.mq;

import com.lecent.device.cache.DeviceCache;
import com.lecent.device.constant.DeviceConstant;
import com.lecent.device.dto.MsgBody;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.UpLog;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.UpEvent;
import com.lecent.device.service.IUpLogService;
import com.lecent.device.common.utils.SignUtils;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Exceptions;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 设备消息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Order
public class DeviceUpMsgListener {

	private final IUpLogService upLogService;

	public DeviceUpMsgListener(IUpLogService upLogService) {
		this.upLogService = upLogService;
	}

	/**
	 * 设备上传消息接收
	 */
	@RabbitListener(bindings = {
		@QueueBinding(
			value = @Queue(
				value = DeviceConstant.PARKING_DEVICE_UP_QUEUE,
				durable = "true"),
			exchange = @Exchange(
				value = Exchanges.AMQ_TOPIC,
				type = ExchangeTypes.TOPIC),
			key = DeviceConstant.DEVICE_MQTT_UP_PREFIX + StringPool.HASH
		)
	})
	public void receiveMsg(Message message) {
		String msg = new String(message.getBody());
		log.debug("接收设备上发消息为：: jsonStr = {}", msg);

		UpLog upLog = new UpLog();

		try {
			// 校验消息
			checkMessage(msg);
			// Json转MsgBody
			MsgBody upBody = JsonUtil.readValue(msg, MsgBody.class);
			LecentAssert.notNull(upBody, "消息体JSON转换对象失败, msg={}", msg);
			// 复制MsgBody到UpLog
			copyMsgBody2UpLog(upBody, upLog);
			// 检查CMD
			LecentAssert.isTrue(UpCmd.contains(upBody.getCmd()), "未知命令 cmd={}", upBody.getCmd());
			// 验证签名
			verifySign(upBody);
			// 检查设备是否存在
			Device device = DeviceCache.existBySn(upBody.getSn());
			upLog.setDeviceId(device.getId());
			// 推送事件路由
			SpringUtil.publishEvent(new UpEvent(this, upBody.getRequestId(), upBody.getCmd(), upBody, device));
		} catch (Exception e) {
			log.error("接收设备消息失败:", e);
			upLog.fail(Exceptions.getStackTraceAsString(e));
		} finally {
			this.asyncSaveUpLog(upLog);
		}

	}

	/**
	 * 校验msg
	 *
	 * @param msg 上传msg
	 */
	private void checkMessage(String msg) {
		if (Func.isBlank(msg)) {
			throw new ServiceException("up message is blank");
		}
	}

	/**
	 * 验证签名
	 *
	 * @param req 事件
	 */
	private void verifySign(MsgBody req) {
		if (!SignUtils.verifySign(req, SignUtils.DOWN_MSG_SALT)) {
			throw new ServiceException("签名验证失败");
		}
	}

	/**
	 * 异步保存上发日志
	 *
	 * @param upLog 上发日志
	 */
	private void asyncSaveUpLog(UpLog upLog) {
		Optional.ofNullable(upLog)
			// 排除ack日志
			.filter(t -> !UpCmd.ack.equals(UpCmd.get(t.getCmd())))
			.ifPresent(this.upLogService::asyncSave);
	}

	/**
	 * 复制MsgBody到UpLog
	 *
	 * @param msgBody 消息体
	 * @param upLog   上发日志
	 */
	private void copyMsgBody2UpLog(MsgBody msgBody, UpLog upLog) {
		upLog.setRequestId(msgBody.getRequestId());
		upLog.setSn(msgBody.getSn());
		upLog.setCmd(msgBody.getCmd());
		upLog.setContent(msgBody.getContent());
		upLog.setTimestamp(msgBody.getTimestamp());
		upLog.setSign(msgBody.getSign());
		upLog.setSuccess(UpLog.STATUS_SUCCESS);
	}

}
