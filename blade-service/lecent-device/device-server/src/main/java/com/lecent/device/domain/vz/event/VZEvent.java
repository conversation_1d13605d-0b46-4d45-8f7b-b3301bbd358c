package com.lecent.device.domain.vz.event;

import com.lecent.device.domain.common.service.AlarmEventType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.PayloadApplicationEvent;

import java.io.Serializable;

/**
 * 臻识巡检车事件
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class VZEvent<T> extends PayloadApplicationEvent<T> implements Serializable {

    private static final long serialVersionUID = -6996392823755183659L;

    private AlarmEventType eventType;

    public VZEvent(Object source, AlarmEventType eventType, T payload) {
        super(source, payload);
        this.eventType = eventType;
    }
}
