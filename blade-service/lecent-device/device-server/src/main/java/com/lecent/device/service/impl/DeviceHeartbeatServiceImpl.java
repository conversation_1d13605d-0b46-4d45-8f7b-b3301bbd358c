package com.lecent.device.service.impl;

import com.lecent.device.cache.DeviceCache;
import com.lecent.device.config.props.DeviceNetworkProperties;
import com.lecent.device.constant.DeviceConstant;
import com.lecent.device.dto.AlarmDTO;
import com.lecent.device.dto.DeviceDTO;
import com.lecent.device.dto.DeviceNetworkDTO;
import com.lecent.device.entity.Alarm;
import com.lecent.device.entity.Device;
import com.lecent.device.enums.EventAlarmType;
import org.springblade.common.enums.NetworkStatus;
import com.lecent.device.publisher.NetworkChangePublisher;
import com.lecent.device.service.IAlarmService;
import com.lecent.device.service.IDeviceHeartbeatService;
import com.lecent.device.service.IDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备心跳
 * 业务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceHeartbeatServiceImpl implements IDeviceHeartbeatService {

    @Resource
	private IDeviceService deviceService;
	@Resource
	private IAlarmService alarmService;
	@Resource
	private DeviceNetworkProperties networkProperties;
	@Resource
	private NetworkChangePublisher networkChangePublisher;

	/**
	 * 心跳
	 *
	 * @param device 设备信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	@Async
	public void heartbeat(Device device) {
		if (log.isDebugEnabled()) {
			log.debug("heartbeat[{}]", device.getSn());
		}

		networkStatusHandler(device);
	}

	/**
	 * 批量同步最新设备网络信息
	 */
	@Override
	public void updateBatchFromRedis() {
		log.info("------ 准备从Redis获取在线设备数量 ------");
		final Set<String> onlineKeys = DeviceCache.scanOnlineKeys();
		log.info("------ 从Redis获取在线设备数量:{} ------", onlineKeys.size());


		List<DeviceDTO> latestOnlineDevices = latestOnlineDevices(onlineKeys);
		List<DeviceDTO> latestOfflineDevices = latestOfflineDevices(onlineKeys);

		List<DeviceDTO> updateDevices = new ArrayList<>();
		updateDevices.addAll(latestOnlineDevices);
		updateDevices.addAll(latestOfflineDevices);

		log.debug("------ 需要更新网络状态的设备数量:{} ------", updateDevices.size());
		log.info("------ 需要更新离线状态设备数量:{} ------", latestOfflineDevices.size());

		if (!updateDevices.isEmpty()) {
			if (log.isDebugEnabled()) {
				log.debug("need to update network status devices: {}", Func.toJson(updateDevices));
			}
			List<Device> devices= Func.copyProperties(updateDevices,Device.class);
			this.deviceService.updateBatchById(devices);
		}
		// 设备网络状态变化处理
		List<DeviceDTO> deviceHandlers=updateDevices.stream().filter(DeviceDTO::isNetworkStatusChanged).collect(Collectors.toList());
		if(Func.isNotEmpty(deviceHandlers)){
			List<Device> devices= Func.copyProperties(deviceHandlers,Device.class);
			this.networkChangeHandler(devices);
		}
	}


	/**
	 * 设备网络状态处理
	 *
	 * @param device 设备信息
	 */
	private void networkStatusHandler(Device device) {
		device.setNetworkStatus(NetworkStatus.ONLINE.getCode());
		device.setLastConnectTime(new Date());

		// 判断设备目前是否离线，离线则更新并推送最新在线信息
		if (isOffline(device.getSn())) {
			this.deviceService.updateNetworkStatus(device);
			// 推送
			this.networkChangeHandler(Collections.singletonList(device));
		}

		// 设置心跳key
		DeviceCache.setOnline(
			DeviceNetworkDTO.builder()
				.sn(device.getSn())
				.networkStatus(device.getNetworkStatus())
				.lastConnectTime(device.getLastConnectTime())
				.build()
			, networkProperties.getIntervalSeconds(device.getType()) * 3
		);
	}

	/**
	 * 从redis获取在线设备的最新网络状态信息
	 *
	 * @param onlineKeys 在线设备缓存keys
	 * @return List<Device>
	 */
	private List<DeviceDTO> latestOnlineDevices(Set<String> onlineKeys) {
		return this.deviceService.list().parallelStream()
			.filter(t -> isOnline(onlineKeys, t.getSn()))
			.map(t -> {
				final DeviceDTO device = new DeviceDTO();
				device.setId(t.getId());
				device.setSn(t.getSn());
				device.setNetworkStatus(NetworkStatus.ONLINE.getCode());
				device.setTenantId(t.getTenantId());
				// 针对设备网络状态在Redis中在线，RDB中离线的情况
				device.setNetworkStatusChanged(NetworkStatus.OFFLINE.getCode() == t.getNetworkStatus());
				Optional.ofNullable(DeviceCache.getOnline(t.getSn()))
					.ifPresent(networkDTO -> device.setLastConnectTime(networkDTO.getLastConnectTime()));
				return device;
			}).collect(Collectors.toList());
	}

	/**
	 * 获取最新离线设备
	 * 通过redis缓存的在线设备keys与数据库中在线设备对比
	 *
	 * @param onlineKeys 在线设备缓存keys
	 * @return List<Device>
	 */
	private List<DeviceDTO> latestOfflineDevices(Set<String> onlineKeys) {
		return this.deviceService.onlineList().parallelStream()
			.filter(t -> isOffline(onlineKeys, t.getSn()))
			.map(t -> {
				final DeviceDTO device = new DeviceDTO();
				device.setId(t.getId());
				device.setSn(t.getSn());
				device.setTenantId(t.getTenantId());
				device.setNetworkStatus(NetworkStatus.OFFLINE.getCode());
				device.setNetworkStatusChanged(true);
				return device;
			}).collect(Collectors.toList());
	}

	private boolean isOnline(Set<String> onlineKeys, String sn) {
		return onlineKeys.contains(DeviceConstant.CACHE_DEVICE_ONLINE_NAME + sn);
	}

	private boolean isOffline(Set<String> onlineKeys, String sn) {
		return !isOnline(onlineKeys, sn);
	}

	private boolean isOnline(String sn) {
		return Objects.nonNull(DeviceCache.getOnline(sn));
	}

	private boolean isOffline(String sn) {
		return !isOnline(sn);
	}

	/**
	 * 设备网络状态变更处理
	 *
	 * @param networkChangeDevices 网络状态变更设备
	 */
	private void networkChangeHandler(List<Device> networkChangeDevices) {
		log.info("------ 网络状态变化的设备数量: {} ------", networkChangeDevices.size());
		if (networkChangeDevices.isEmpty()) {
			return;
		}

		// 网络变化告警
		networkChangeAlarm(networkChangeDevices);
		// 网络状态变化推送
		this.networkChangePublisher.publish(networkChangeDevices);
	}

	private void networkChangeAlarm(List<Device> networkChangeDevices) {
		List<Alarm> alarms = networkChangeDevices.stream()
			.filter(t -> NetworkStatus.contains(t.getNetworkStatus()))
			.map(t -> {
				EventAlarmType alarmType = NetworkStatus.isOnline(t.getNetworkStatus()) ?
					EventAlarmType.NETWORK_ONLINE : EventAlarmType.NETWORK_OFFLINE;

				AlarmDTO alarm = new AlarmDTO();
				alarm.setDeviceId(t.getId());
				alarm.setDeviceSn(t.getSn());
				alarm.setType(alarmType);
				return alarm;
			}).collect(Collectors.toList());
		this.alarmService.alarm(alarms);
	}

}
