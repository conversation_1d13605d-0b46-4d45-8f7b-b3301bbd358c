package com.lecent.device.listener.spring.route;

import com.lecent.device.dto.MsgBody;
import com.lecent.device.dto.ReqDownMsg;
import com.lecent.device.dto.event.DeviceQueryCallbackEvent;
import com.lecent.device.dto.event.DeviceQueryEvent;
import com.lecent.device.entity.Device;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.enums.DownCmd;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.event.UpEvent;
import com.lecent.device.service.IEventDownService;
import com.lecent.device.service.IParkingLockDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 心跳消息处理器
 *
 * <AUTHOR> zxr
 * @date : 2022/9/14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QueryRouteHandler {
	/**
	 * 心跳命令
	 */
	public static final String CMD = UpCmd.query.name();

	private final IEventDownService eventDownService;
	private final IParkingLockDeviceService parkingLockDeviceService;

	/**
	 * 接收设备查询事件
	 *
	 * @param event 事件
	 */
	@EventListener(classes = UpEvent.class,
		condition = "#event.cmd ==T(com.lecent.device.listener.spring.route.QueryRouteHandler).CMD")
	public void receive(UpEvent event) {
		Device device = event.getDevice();
		MsgBody msgBody = event.getPayload();
		log.info("QueryRouteHandler event={}", event.getPayload());

		DeviceQueryCallbackEvent<?> queryCallbackEvent = null;
		String path = StringPool.EMPTY;
		try {
			DeviceQueryEvent deviceQueryEvent = Func.readJson(msgBody.getContent(), DeviceQueryEvent.class);
			path = deviceQueryEvent.getPath();
			if (DeviceType.PARKING_LOCK.getValue().equals(device.getType())) {
				queryCallbackEvent = parkingLockDeviceService.queryEvent(device, deviceQueryEvent, msgBody.getRequestId());
			}

		} catch (Exception e) {
			log.error("QueryRouteHandler.receive", e);
		}

		sendMessage(queryCallbackEvent, msgBody, path);
	}

	private void sendMessage(DeviceQueryCallbackEvent<?> queryCallbackEvent, MsgBody msgBody, String path) {
		if (Objects.nonNull(queryCallbackEvent)) {
			queryCallbackEvent.setPath(path);
			queryCallbackEvent.setRequestId(msgBody.getRequestId());
			this.eventDownService.downMsgV1(
				ReqDownMsg.builder()
					.sn(msgBody.getSn())
					.cmd(DownCmd.queryCallback.name())
					.content(Func.toJson(queryCallbackEvent))
					.build()
			);
		}
	}
}
