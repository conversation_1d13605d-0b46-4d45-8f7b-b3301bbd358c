package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.ProductParam;
import com.lecent.device.vo.ProductParamVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 设备产品参数 服务类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface IProductParamService extends BaseService<ProductParam> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param productParam
	 * @return
	 */
	IPage<ProductParamVO> selectProductParamPage(IPage<ProductParamVO> page, ProductParamVO productParam);

	/**
	 * 新增或保存
	 *
	 * @param productParam
	 * @return
	 */
	boolean customSaveOrUpdate(ProductParam productParam);

	/**
	 * 查询参数列表
	 *
	 * @param productId 产品ID
	 * @return
	 */
	List<ProductParam> selectParams(Long productId);
}
