package com.lecent.device.service;

import com.lecent.device.entity.Device;
import com.leliven.device.domain.shared.valueobject.DeviceType;

/*********************************
 * @Description:
 * <AUTHOR>
 * @date 2022/11/24 11:16
 * @version v1.0
 *********************************/
public interface IDeviceEventHandler {

	/**
	 * 事件触发
	 * @param device 设备信息
	 * @param content 消息内容
	 */
	void triggerEvent(Device device, String content);


	/**
	 * 获取设备类型
	 * @return DeviceType
	 */
	DeviceType getDeviceType();
}
