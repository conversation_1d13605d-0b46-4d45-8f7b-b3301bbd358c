package com.lecent.device.listener.spring.vz;

import com.lecent.device.domain.vz.event.VZEvent;
import com.lecent.device.dto.inspectionvehicle.EventData;
import com.lecent.device.service.IVZInspectionVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 臻识巡检车出场事件监听器
 */
@Slf4j
@Component
public class ExitEventListener {

    @Resource
    private IVZInspectionVehicleService inspectionVehicleService;

    @EventListener(
            classes = VZEvent.class,
            condition = "#event.eventType == T(com.lecent.device.domain.vz.event.VZEventType).PARKING_EVENT_EXIT"
    )
    public void onEvent(VZEvent<EventData> event) {
        log.info("VZEvent: {}", event.getEventType());

        inspectionVehicleService.handle(event.getPayload());
    }
}
