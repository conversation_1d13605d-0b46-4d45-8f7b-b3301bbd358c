package com.lecent.device.scheduled;

import com.google.common.collect.Lists;
import com.lecent.device.entity.Device;
import com.lecent.device.publisher.NetworkChangePublisher;
import com.lecent.device.service.IDeviceHeartbeatService;
import com.lecent.device.service.IDeviceService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.Func;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备定时器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeviceScheduled {

	@Resource
	private IDeviceHeartbeatService deviceHeartbeatService;
	@Resource
	private IDeviceService deviceService;
	@Resource
	private NetworkChangePublisher networkChangePublisher;

	/**
	 * 每3分种
	 * 批量同步最新设备信息
	 */
	@SneakyThrows
	@Scheduled(cron = "0 0/3 * * * ?")
	@RedisLock(value = "lecent:device::timedTask:lock:updateBatchFromRedis", waitTime = 1L, leaseTime = 3 * 60)
	public void updateBatchFromRedis() {
		Thread.sleep(2000);
		log.info("[执行批量同步最新设备信息]任务开始==============");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		this.deviceHeartbeatService.updateBatchFromRedis();
		stopWatch.stop();
		log.info("[执行批量同步最新设备信息]任务结束，耗时:[{}s]==============", stopWatch.getTotalTimeSeconds());
	}

	/**
	 * 同步更新车场设备关系表设备状态
	 */
	@SneakyThrows
	@Scheduled(cron = "0 0 4 * * ?")
	@RedisLock(value = "lecent:device::timedTask:lock:syncDeviceInfo", waitTime = 1L, leaseTime = 3 * 60)
	public void syncDeviceInfo() {
		Thread.sleep(2000);
		log.info("[执行同步更新车场设备关系表设备状态]任务开始==============");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		List<Device> deviceList = deviceService.list();
		if (Func.isNotEmpty(deviceList)) {
			List<List<Device>> partition = Lists.partition(deviceList, 200);
			partition.forEach(t -> networkChangePublisher.publish(t));
		}

		stopWatch.stop();
		log.info("[执行同步更新车场设备关系表设备状态]任务结束，同步数量: {},耗时:[{}s]==============",
			deviceList.size(), stopWatch.getTotalTimeSeconds());
	}
}
