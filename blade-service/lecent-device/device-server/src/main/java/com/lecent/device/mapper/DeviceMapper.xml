<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.DeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceResultMap" type="com.lecent.device.vo.DeviceVO">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="sn" property="sn"/>
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="ip" property="ip"/>
        <result column="mac" property="mac"/>
        <result column="lng" property="lng"/>
        <result column="lat" property="lat"/>
        <result column="region_code" property="regionCode"/>
        <result column="region_name" property="regionName"/>
        <result column="address" property="address"/>
        <result column="network_status" property="networkStatus"/>
        <result column="pic_urls" property="picUrls"/>
        <result column="last_connect_time" property="lastConnectTime"/>
        <result column="group_id" property="groupId"/>
        <result column="product_id" property="productId"/>
        <result column="parent_id" property="parentId"/>
        <result column="labels" property="labels"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="firmware_version" property="firmwareVersion"/>
        <result column="group_name" property="groupName"/>
    </resultMap>


    <select id="selectDevicePage" resultMap="deviceResultMap">
		SELECT A.*, B.name group_name
		FROM dev_device A
		left join dev_group B on A.group_id = B.id
		WHERE A.is_deleted = 0
		<include refid="pageCondition"/>
    </select>

    <select id="statistics" resultType="com.lecent.device.vo.DeviceStatisticsVO">
		SELECT
		COUNT( A.ID ) as totalNum,
		IFNULL(SUM( IF ( A.NETWORK_STATUS = 1, 1, 0 ) ),0) AS 'onlineNum',
		IFNULL(SUM( IF ( A.BIND_STATUS = 0, 1, 0 ) ),0) AS 'unbindNum'
		FROM
		dev_device A
		WHERE
		A.is_deleted = 0
		<include refid="pageCondition"/>
	</select>

	<sql id="pageCondition">
		<if test="device.hasGroup != null">
			AND A.has_group = #{device.hasGroup}
		</if>

		<if test="device.productId != null">
			AND A.product_id = #{device.productId}
		</if>

		<if test="device.name != null and device.name != ''">
            AND A.name like CONCAT('%',#{device.name},'%')
        </if>

		<if test="device.sn != null">
			AND A.sn like CONCAT('%',#{device.sn},'%')
		</if>

		<if test="device.bindStatus != null">
			AND A.bind_status = #{device.bindStatus}
		</if>
		<if test="device.networkStatus != null">
			AND A.network_status = #{device.networkStatus}
		</if>

        <if test="device.firmwareVersion != null and device.firmwareVersion != ''">
            AND A.firmware_version like CONCAT('%',#{device.firmwareVersion},'%')
        </if>

		<if test="groupIdList!=null and groupIdList.size>0">
			and A.group_id in
			<foreach collection="groupIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</sql>

    <select id="getMaxSnByParentId" resultType="java.lang.String">
        SELECT SN
        FROM DEV_DEVICE
        WHERE ID = (SELECT MAX(ID) AS ID
                    FROM DEV_DEVICE T
                    WHERE T.IS_DELETED = 0
                      AND T.PARENT_ID = #{parentId})
    </select>

    <select id="getStatus" resultType="com.lecent.device.entity.Device">
        select *
        from DEV_DEVICE
        where is_deleted = 0
          and id = #{id}
    </select>

    <select id="getDeviceStatus" resultType="com.lecent.device.entity.Device">
        select *
        from DEV_DEVICE
        where is_deleted = 0
          and sn = #{sn}
    </select>

    <select id="getDeviceInfoById" resultType="com.lecent.device.entity.Device">
        select *
        from DEV_DEVICE
        where is_deleted = 0
          and id = #{id}
    </select>

    <select id="listAggregation" resultType="com.lecent.device.vo.TempDeviceAggregationVO">
        SELECT
	        SUBSTRING_INDEX(d.sn, '@', 1) AS originalSn,
	        ret.parklot_id as parklotId,
            ret.park_place_id as parkingPlaceId,
            ret.id as bindingId,
            ret.create_time as bindingTime,
            dpp.pay_code as payCode,
	        d.*
        from
            dev_device d
        LEFT JOIN leliven_park.d_parklot_device_ret ret on d.id = ret.device_id
        LEFT JOIN leliven_park.d_parking_place dpp on ret.park_place_id = dpp.id
        where
            d.is_deleted = 0
            and d.bind_status = 1
            and d.type = #{deviceType}
            <if test="parklotId != null">
                and ret.parklot_id = #{parklotId}
            </if>
            ORDER BY originalSn
    </select>

    <select id="listAggregationBySn" resultType="com.lecent.device.vo.TempDeviceAggregationVO">
        SELECT
	        d.sn AS originalSn,
	        ret.parklot_id as parklotId,
            ret.park_place_id as parkingPlaceId,
            dpp.pay_code as payCode,
            ret.id as bindingId,
            ret.create_time as bindingTime,
            d.*
        from
            dev_device d
        LEFT JOIN leliven_park.d_parklot_device_ret ret on d.id = ret.device_id
        LEFT JOIN leliven_park.d_parking_place dpp on ret.park_place_id = dpp.id
        where
            d.is_deleted = 0
            and d.bind_status = 1
            and d.type = #{deviceType}
            <if test="parklotId != null">
                and ret.parklot_id = #{parklotId}
            </if>
            ORDER BY originalSn
    </select>

</mapper>
