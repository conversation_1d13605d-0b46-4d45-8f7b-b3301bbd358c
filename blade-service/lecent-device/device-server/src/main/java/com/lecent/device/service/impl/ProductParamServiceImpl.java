package com.lecent.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.Device;
import com.lecent.device.entity.ProductParam;
import com.lecent.device.mapper.ProductParamMapper;
import com.lecent.device.service.IProductParamService;
import com.lecent.device.vo.ProductParamVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备产品参数 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Service
public class ProductParamServiceImpl extends BaseServiceImpl<ProductParamMapper, ProductParam> implements IProductParamService {

	@Override
	public IPage<ProductParamVO> selectProductParamPage(IPage<ProductParamVO> page, ProductParamVO productParam) {
		return page.setRecords(baseMapper.selectProductParamPage(page, productParam));
	}

	@Override
	public boolean customSaveOrUpdate(ProductParam productParam) {
		if (!checkKeyRepeat(productParam.getK(), productParam.getId())) {
			throw new ServiceException("键值已存在");
		}
		return super.saveOrUpdate(productParam);
	}

	@TenantIgnore
	private boolean checkKeyRepeat(String key, Long id) {
		QueryWrapper<ProductParam> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("is_deleted", BladeConstant.DB_NOT_DELETED).eq("k", key);
		if (Func.isNotEmpty(id)) {
			queryWrapper.ne("id", id);
		}
		List<ProductParam> list = this.baseMapper.selectList(queryWrapper);
		return Func.isEmpty(list);
	}

	@Override
	public List<ProductParam> selectParams(Long productId) {
		QueryWrapper<ProductParam> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("is_deleted", BladeConstant.DB_NOT_DELETED).eq("product_id", productId);
		return this.baseMapper.selectList(queryWrapper);
	}

}
