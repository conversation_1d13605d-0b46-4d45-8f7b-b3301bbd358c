package com.lecent.device.controller.open;

import com.lecent.device.entity.UpdatePackage;
import com.lecent.device.service.IUpdatePackageService;
import com.lecent.device.vo.UpdatePackageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 升级包管理 控制器
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/open/update-package")
@Api(value = "升级包管理", tags = "升级包管理接口")
public class OpenUpdatePackageController extends BladeController {

    private final IUpdatePackageService updatePackageService;

    /**
     * 自定义分页 升级包管理
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页", notes = "传入updatePackage")
    public R<List<UpdatePackage>> list(UpdatePackageVO updatePackage) {
        return R.data(updatePackageService.list(Condition.getQueryWrapper(updatePackage)));
    }
}
