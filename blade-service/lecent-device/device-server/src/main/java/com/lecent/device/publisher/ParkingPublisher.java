package com.lecent.device.publisher;

import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.device.dto.DeviceQueryEventDTO;
import com.lecent.park.core.notify.api.IMsgSender;
import com.lecent.park.core.notify.domain.MsgRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 停车事件发布器（推送到 MQ）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParkingPublisher {

	private final IMsgSender sender;

    public ParkingPublisher(IMsgSender sender) {
		this.sender = sender;
    }

	/**
	 * 设备停车事件发布
	 *
	 * @param deviceTriggerDTO 设备进出场事件
	 */
	public void publish(DeviceParkingEventDTO deviceTriggerDTO) {
		log.info("publish device parking event, deviceTriggerDTO = {}", deviceTriggerDTO);
		sender.send(
			MsgRequest.<DeviceParkingEventDTO>builder()
				.code(DeviceMQConstant.ENTER_EXIT_EVENT_CODE)
				.body(deviceTriggerDTO)
				.build()
		);
	}

	/**
	 * 车位空闲事件发布
	 *
	 * @param deviceTriggerDTO 设备进出场事件
	 */
	public void publishIdleEvent(DeviceParkingEventDTO deviceTriggerDTO) {
		log.info("publish place idle event, deviceTriggerDTO = {}", deviceTriggerDTO);
		sender.send(
			MsgRequest.<DeviceParkingEventDTO>builder()
				.code(DeviceMQConstant.PARKING_IDLE_CODE)
				.body(deviceTriggerDTO)
				.build()
		);
	}

	/**
	 * 查询停车状态事件发布
	 *
	 * @param deviceTriggerDTO 查询停车状态事件
	 */
	public void publishQueryParkingStatusEvent(DeviceQueryEventDTO deviceTriggerDTO) {
		log.info("publish query parking status event, deviceTriggerDTO = {}", deviceTriggerDTO);
		sender.send(
				MsgRequest.<DeviceQueryEventDTO>builder()
						.code(DeviceMQConstant.QUERY_PARKING_STATUS_CODE)
						.body(deviceTriggerDTO)
						.build()
		);
	}

}
