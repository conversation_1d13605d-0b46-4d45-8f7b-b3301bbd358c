package com.lecent.device.listener.spring.route;

import com.lecent.device.dto.MsgBody;
import com.lecent.device.event.UpEvent;
import com.lecent.device.entity.Device;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.lecent.device.enums.UpCmd;
import com.lecent.device.service.IParkingLockDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 设备事件处理器
 * 设备：地感、车位锁
 *
 * <AUTHOR> zxr
 * @date : 2022/9/14
 */
@Slf4j
@Component
public class DeviceEventRouteHandler {
	/**
	 * 事件命令
	 */
	public static final String CMD = UpCmd.event.name();

	private final IParkingLockDeviceService parkingLockDeviceService;

	public DeviceEventRouteHandler(IParkingLockDeviceService parkingLockDeviceService) {
		this.parkingLockDeviceService = parkingLockDeviceService;
	}

	/**
	 * 接收事件
	 *
	 * @param event 事件
	 */
	@EventListener(classes = UpEvent.class,
		condition = "#event.cmd ==T(com.lecent.device.listener.spring.route.DeviceEventRouteHandler).CMD")
	public void receive(UpEvent event) {
		try {

			log.info("event: {}", event);
			MsgBody msgBody = event.getPayload();
			handle(msgBody, event.getDevice());

		} catch (Exception e) {
			log.error("DeviceEventRouteHandler.receive", e);
		}
	}

	/**
	 * 事件处理器
	 *
	 * @param msgBody 消息体
	 * @param device  设备
	 */
	private void handle(MsgBody msgBody, Device device) {
		if (Objects.isNull(device)) {
			log.error("处理失败，设备信息为空");
			return;
		}
		// 车位锁设备
		if (DeviceType.PARKING_LOCK.getValue().equals(device.getType())) {
			parkingLockDeviceService.triggerEvent(device, msgBody.getContent());
		}
	}
}
