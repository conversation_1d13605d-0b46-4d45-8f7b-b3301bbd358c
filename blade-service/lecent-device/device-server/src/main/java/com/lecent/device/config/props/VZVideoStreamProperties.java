package com.lecent.device.config.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 停车配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "device.vz.video")
public class VZVideoStreamProperties {

    private String url = "https://open.parkways.cn";

    private String path = "/v2/server_api/devices/pdns/video";

    private String accessKey = "GXJMfACP22K152H0eFJt8gkKqhI5KiGL";

    private String secretKey = "LKlhsWhIONHG1tQA4eTGwDnasb3xrRnx";
}
