package com.lecent.device.domain.common.support.convertor;

import com.lecent.device.enums.EnterExitType;
import org.springblade.core.log.exception.ServiceException;

import javax.annotation.Nonnull;

/**
 * 停车状态转换器
 *
 * <AUTHOR>
 */
public interface ParkingStatusConvertor<T> {

    /**
     * 将第三方停车状态转换为本地停车状态
     *
     * @param t 停车状态 {@link T} 第三方停车状态
     * @return 停车状态 {@link EnterExitType}
     */
    default EnterExitType convert(T t) {
        if (get3rdEnterTypeValue().equals(t)) {
            return EnterExitType.ENTER;
        } else if (get3rdExitTypeValue().equals(t)) {
            return EnterExitType.EXIT;
        } else {
            throw new ServiceException("invalid status: " + t);
        }
    }

    /**
     * 将第三方停车状态转换为本地停车状态
     *
     * @param t 停车状态 {@link T} 第三方停车状态
     * @return 停车状态 {@link Integer}
     */
    default Integer convert2Value(T t) {
        return convert(t).getValue();
    }

    /**
     * 获取第三方停车入场类型值
     *
     * @return 第三方停车状态值
     */
    @Nonnull
    T get3rdEnterTypeValue();

    /**
     * 获取第三方停车出场类型值
     *
     * @return 第三方停车状态值
     */
    @Nonnull
    T get3rdExitTypeValue();

}
