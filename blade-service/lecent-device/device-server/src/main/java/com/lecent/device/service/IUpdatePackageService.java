package com.lecent.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.device.entity.UpdatePackage;
import com.lecent.device.vo.UpdatePackageVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 升级包管理 服务类
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
public interface IUpdatePackageService extends BaseService<UpdatePackage> {

	/**
	 * 自定义分页
	 *
	 * @param page          页
	 * @param updatePackage 更新包
	 * @return {@link IPage }<{@link UpdatePackageVO }>
	 */
	IPage<UpdatePackageVO> selectUpdatePackagePage(IPage<UpdatePackageVO> page, UpdatePackageVO updatePackage);

	/**
	 * 提交
	 *
	 * @param updatePackage 更新包
	 * @return boolean
	 */
	boolean submit(UpdatePackage updatePackage);
}
