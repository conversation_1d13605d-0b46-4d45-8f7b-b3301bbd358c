package com.lecent.device.service;

import com.lecent.device.dto.ParkSpaceLockCtlDTO;

/*********************************
 * IOT 指令业务功能接口
 * <AUTHOR>
 * @date 2022/11/23 19:03S
 *********************************/
public interface IIotCommandService {

    /**
     * 升锁命令
     *
     * @param dto 车位锁控制DTO
     * @return 请求ID
     */
    String lock(ParkSpaceLockCtlDTO dto);

    /**
     * 升锁命令
     * 根据条件检查判断是否最终升锁
     *
     * @param dto 车位锁控制DTO
     * @return 请求ID
     */
    String lockWithCheckCondition(ParkSpaceLockCtlDTO dto);

    /**
     * 车位锁降锁指令
     *
     * @param deviceSn     设备序列号
     * @param requestId    请求ID
     * @param delaySeconds 延迟时间 单位：秒
     * @return 请求ID
     */
    String unlock(String deviceSn, String requestId, Long delaySeconds);

    /**
     * 设备重启
     *
     * @param deviceSn 设备序列号
     */
    String reboot(String deviceSn);

    /**
     * 设备刷新配置
     *
     * @param deviceSn 设备序列号
     */
    String refreshConfig(String deviceSn);
}
