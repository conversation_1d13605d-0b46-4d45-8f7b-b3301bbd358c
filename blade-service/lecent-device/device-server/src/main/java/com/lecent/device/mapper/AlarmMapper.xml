<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.AlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="alarmResultMap" type="com.lecent.device.entity.Alarm">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="device_id" property="deviceId"/>
        <result column="name" property="name"/>
        <result column="reason" property="reason"/>
        <result column="level" property="level"/>
        <result column="group_id" property="groupId"/>
        <result column="rule_id" property="ruleId"/>
        <result column="status" property="status"/>
    </resultMap>


    <select id="selectAlarmPage" resultType="com.lecent.device.vo.AlarmVO">
        SELECT *
        FROM DEV_ALARM
        WHERE
        IS_DELETED = 0
        <if test="alarm.deviceId != null">
            AND DEVICE_ID = #{alarm.deviceId}
        </if>
        <if test="alarm.statTime != null">
            AND CREATE_TIME &gt;= #{alarm.statTime}
        </if>
        <if test="alarm.endTime != null">
            AND CREATE_TIME &lt;= #{alarm.endTime}
        </if>
        <if test="alarm.level != null">
            AND LEVEL = #{alarm.level}
        </if>
        <if test="alarm.status != null">
            AND LEVEL = #{alarm.status}
        </if>
    </select>

</mapper>
