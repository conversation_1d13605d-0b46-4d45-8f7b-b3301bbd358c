<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lecent.device.mapper.UpLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="upLogResultMap" type="com.lecent.device.entity.UpLog">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="request_id" property="requestId"/>
        <result column="device_id" property="deviceId"/>
        <result column="name" property="name"/>
        <result column="cmd" property="cmd"/>
        <result column="content" property="content"/>
        <result column="success" property="success"/>
        <result column="exception" property="exception"/>
    </resultMap>


    <select id="selectUpLogPage" resultType="com.lecent.device.vo.UpLogVO">
        SELECT *
        FROM DEV_UP_LOG
        WHERE 1 = 1
        <if test="upLog.deviceId != null or ( upLog.payCode != null and upLog.payCode != '' )">
            AND DEVICE_ID IN ( #{upLog.deviceId}, #{upLog.payCode} )
        </if>
        <if test="upLog.startTime != null">
            AND CREATE_TIME &gt;= #{upLog.startTime}
        </if>
        <if test="upLog.endTime != null">
            AND CREATE_TIME &lt;= #{upLog.endTime}
        </if>
        <if test="upLog.cmd != null and upLog.cmd != '' ">
            AND CMD = #{upLog.cmd}
        </if>
        <if test="upLog.requestId != null and upLog.requestId != '' ">
            AND REQUEST_ID LIKE CONCAT('%', #{upLog.requestId}, '%')
        </if>
        <if test="upLog.success != null ">
            AND SUCCESS = #{upLog.success}
        </if>
    </select>

    <select id="selectRecentlyUpLog" resultType="com.lecent.device.vo.UpLogVO">
        SELECT *
        FROM DEV_UP_LOG
        WHERE 1 = 1
        <if test="upLog.deviceId != null ">
            AND DEVICE_ID = #{upLog.deviceId}
        </if>
        <if test="upLog.startTime != null">
            AND CREATE_TIME &gt;= #{upLog.startTime}
        </if>
        <if test="upLog.endTime != null">
            AND CREATE_TIME &lt;= #{upLog.endTime}
        </if>
        <if test="upLog.cmd != null and upLog.cmd != '' ">
            AND CMD = #{upLog.cmd}
        </if>
        <if test="upLog.requestId != null and upLog.requestId != '' ">
            AND REQUEST_ID LIKE CONCAT('%', #{upLog.requestId}, '%')
        </if>
        <if test="upLog.success != null ">
            AND SUCCESS = #{upLog.success}
        </if>
        <if test="orderBy==1">
            order by create_time asc
        </if>
        <if test="orderBy==2">
            order by create_time desc
        </if>
        limit 1
    </select>





    <select id="getLastCameraCaptureRecord" resultType="com.lecent.device.vo.UpLogVO">
        SELECT
            *
        FROM
            dev_up_log t
        WHERE
            t.device_id = #{query.deviceId}
          AND create_time &gt; #{query.startTime}
          AND create_time &lt;= #{query.endTime}
          AND cmd IN ('event', 'PARKING_EVENT_STOP', 'PARKING_EVENT_ENTRY', 'PARKING_EVENT_EXIT')
          AND JSON_EXTRACT(t.content, "$.plate") = #{query.plate}
        ORDER BY
            t.create_time DESC
        LIMIT 1
    </select>

    <select id="findRemoveUpLogId" resultType="java.lang.Long">
        SELECT 
            id
        FROM 
            dev_up_log
        WHERE 
            create_time &lt; DATE_SUB(NOW(), INTERVAL 3 DAY)
        LIMIT 5000
    </select>

    <select id="selectDeviceLogPage" resultType="com.lecent.device.vo.DeviceLogVO">
        select * from (
            <if test="model.type==null">
                <include refid="selectUpLogItem"/>
                 union all
                <include refid="selectDownLogItem"/>

            </if>
        <if test="model.type != null and model.type==1">
            <include refid="selectUpLogItem"/>
        </if>

        <if test="model.type != null and model.type==2">
            <include refid="selectDownLogItem"/>

        </if>
                   ) t


    </select>




    <sql id="selectDownLogItem">
        SELECT
            id,
            request_id,
            device_id,
            name,
            cmd,
            content,
            success,
            create_time,
            exception,
            ack_content,
            ack_time,
            retry_num,
            2 as type,
            create_user
        FROM DEV_DOWN_LOG
        WHERE IS_DELETED = 0
        <if test="model.deviceId != null">
            AND DEVICE_ID = #{model.deviceId}
        </if>
        <if test="model.startTime != null">
            AND CREATE_TIME &gt;= #{model.startTime}
        </if>
        <if test="model.endTime != null">
            AND CREATE_TIME &lt;= #{model.endTime}
        </if>
        <if test="model.cmd != null and model.cmd != '' ">
            AND CMD = #{model.cmd}
        </if>
        <if test="model.requestId != null and model.requestId != '' ">
            AND REQUEST_ID LIKE CONCAT('%', #{model.requestId}, '%')
        </if>
        <if test="model.success != null ">
            AND SUCCESS = #{model.success}
        </if>
    </sql>


    <sql id="selectUpLogItem">
        SELECT
        id,
        request_id,
        device_id,
        name,
        cmd,
        content,
        success,
        create_time,
        exception,
        null as ack_content,
        null as ack_time,
        null as retry_num,
        1 as type,
        create_user
        FROM DEV_UP_LOG
        WHERE 1 = 1
        <if test="model.deviceId != null or ( model.payCode != null and model.payCode != '' )">
            AND DEVICE_ID IN ( #{model.deviceId}, #{model.payCode} )
        </if>
        <if test="model.startTime != null">
            AND CREATE_TIME &gt;= #{model.startTime}
        </if>
        <if test="model.endTime != null">
            AND CREATE_TIME &lt;= #{model.endTime}
        </if>
        <if test="model.cmd != null and model.cmd != '' ">
            AND CMD = #{model.cmd}
        </if>
        <if test="model.requestId != null and model.requestId != '' ">
            AND REQUEST_ID LIKE CONCAT('%', #{model.requestId}, '%')
        </if>
        <if test="model.success != null ">
            AND SUCCESS = #{model.success}
        </if>

    </sql>

</mapper>
