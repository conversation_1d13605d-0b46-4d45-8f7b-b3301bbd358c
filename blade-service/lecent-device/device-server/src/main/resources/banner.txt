${AnsiColor.BLUE}                          _            _                                     _
${AnsiColor.BLUE}                         | |          (_)                                   (_)
${AnsiColor.BLUE}                       __| | _____   ___  ___ ___ ______ ___  ___ _ ____   ___  ___ ___
${AnsiColor.BLUE}                      / _` |/ _ \ \ / / |/ __/ _ \______/ __|/ _ \ '__\ \ / / |/ __/ _ \
${AnsiColor.BLUE}                     | (_| |  __/\ V /| | (_|  __/      \__ \  __/ |   \ V /| | (_|  __/
${AnsiColor.BLUE}                      \__,_|\___| \_/ |_|\___\___|      |___/\___|_|    \_/ |_|\___\___|

${AnsiColor.BLUE}:: BladeX ${blade.service.version} :: ${spring.application.name}:${AnsiColor.RED}${blade.env}${AnsiColor.BLUE} :: Running SpringBoot ${spring-boot.version} :: ${AnsiColor.BRIGHT_BLACK}
